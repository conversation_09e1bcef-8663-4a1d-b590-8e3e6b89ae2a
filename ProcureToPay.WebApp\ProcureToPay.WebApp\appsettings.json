{"ConnectionStrings": {"postgresdb": "Host=localhost;Port=5432;Database=procuretopaydb;Username=postgres;Password=localdevpassword"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "NamingConventionOptions": {"EnableSnakeCaseConversion": true, "ConvertTableNames": true, "ConvertColumnNames": true, "ConvertIndexNames": true, "ConvertKeyNames": true, "ConvertForeignKeyConstraintNames": true, "ConvertConstraintNames": true, "EnableLogging": false, "EnableValidation": true}}