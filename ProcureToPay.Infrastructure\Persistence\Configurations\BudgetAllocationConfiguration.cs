using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProcureToPay.Domain.Entities; // Assuming BudgetAllocation, Budget, Department entities exist
using ProcureToPay.Domain.Enums;   // Assuming AllocationStatus enum exists
using ProcureToPay.Domain.ValueObjects; // Assuming Money VO exists
using System;

namespace ProcureToPay.Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// EF Core Configuration for the BudgetAllocation entity targeting PostgreSQL.
    /// </summary>
    public class BudgetAllocationConfiguration : IEntityTypeConfiguration<BudgetAllocation>
    {
        private const string DefaultSchema = "public"; // Example schema

        public void Configure(EntityTypeBuilder<BudgetAllocation> builder)
        {
            // --- Table Mapping ---
            builder.ToTable("budget_allocations", DefaultSchema); // Use snake_case and schema

            // --- Primary Key ---
            // Assuming BudgetAllocation inherits from BaseEntity<Guid>
            builder.<PERSON><PERSON>ey(b => b.Id);
            // Note: UseIdentityByDefaultColumn() is for SERIAL types (int/long), not Guid PKs.
            // Guid PKs are typically generated client-side or via functions like uuid_generate_v4() if needed.

            // --- Concurrency Control (PostgreSQL xmin - EF Core 7+) ---
            builder.Property<uint>("xmin")
                   .HasColumnType("xid")
                   .ValueGeneratedOnAddOrUpdate()
                   .IsConcurrencyToken();

            // --- Property Mappings & Constraints ---

            // Foreign Keys (configure relationships below)
            builder.Property(b => b.BudgetId).IsRequired();
            builder.Property(b => b.DepartmentId).IsRequired(); // Assuming allocation to Department
            builder.Property(b => b.TenantId).IsRequired();

            // Dates (using timestamp without time zone)
            builder.Property(b => b.AllocationDate)
                   .HasColumnType("timestamp without time zone") // Common PostgreSQL practice
                   .IsRequired();
            // Default value like CURRENT_TIMESTAMP is often better handled by DB or BaseEntity logic

            builder.Property(b => b.FiscalPeriodIdentifier)
                   .HasMaxLength(50); // e.g., "2025-Q3"

            builder.Property(b => b.Description)
                   .HasColumnType("text"); // Use 'text' for potentially long descriptions in PostgreSQL

            // Status Enum (stored as string)
            builder.Property(b => b.Status)
                   .IsRequired()
                   .HasConversion<string>()
                   .HasMaxLength(50);


            // --- Value Object Mapping (Money) ---

            // AllocatedAmount (Money VO)
            builder.OwnsOne(b => b.AllocatedAmount, allocated =>
            {
                allocated.Property(m => m.Amount)
                    .HasColumnName("allocated_amount") // snake_case column name
                    .HasColumnType("numeric(18, 2)")   // PostgreSQL numeric type with precision/scale
                    .IsRequired();

                allocated.Property(m => m.CurrencyCode)
                    .HasColumnName("allocated_currency_code") // snake_case column name
                    .HasMaxLength(3)
                    .IsRequired();
            });
            builder.Navigation(b => b.AllocatedAmount).IsRequired();

            // ConsumedAmount (Money VO)
            builder.OwnsOne(b => b.ConsumedAmount, consumed =>
            {
                consumed.Property(m => m.Amount)
                    .HasColumnName("consumed_amount") // snake_case column name
                    .HasColumnType("numeric(18, 2)")  // PostgreSQL numeric type with precision/scale
                    .IsRequired()
                    .HasDefaultValue(0m); // Database default value for the amount

                consumed.Property(m => m.CurrencyCode)
                    .HasColumnName("consumed_currency_code") // snake_case column name
                    .HasMaxLength(3)
                    .IsRequired();
                // Default currency code often matches AllocatedAmount, handled in entity constructor
            });
            builder.Navigation(b => b.ConsumedAmount).IsRequired();


            // --- Relationships ---

            // To Budget (Many Allocations to One Budget)
            builder.HasOne(b => b.Budget)
                   .WithMany(bg => bg.BudgetAllocations) // Assumes Budget has ICollection<BudgetAllocation>
                   .HasForeignKey(b => b.BudgetId)
                   .IsRequired()
                   .OnDelete(DeleteBehavior.Restrict); // Prevent deleting Budget if Allocations exist

            // To Department (Many Allocations to One Department)
            builder.HasOne(b => b.Department)
                   .WithMany(d => d.BudgetAllocations) // Assumes Department has ICollection<BudgetAllocation>
                   .HasForeignKey(b => b.DepartmentId)
                   .IsRequired()
                   .OnDelete(DeleteBehavior.Restrict); // Prevent deleting Department if Allocations exist


            // --- Indexes ---
            builder.HasIndex(b => b.BudgetId);
            builder.HasIndex(b => b.DepartmentId); // Index on FK
            builder.HasIndex(b => b.TenantId);
            builder.HasIndex(b => b.Status);
            builder.HasIndex(b => b.FiscalPeriodIdentifier);
            // Example Composite Index
            builder.HasIndex(b => new { b.BudgetId, b.FiscalPeriodIdentifier });


            // --- Tenant Isolation ---
            // Note: Tenant filtering should be implemented at the DbContext level
            // Example:
            // builder.HasQueryFilter(b => b.TenantId == currentTenantId);
            // Where currentTenantId is obtained from the DbContext or a tenant provider service

        }
    }
}

