﻿using System;
using ProcureToPay.Domain.Enums;       // For AllocationStatus
using ProcureToPay.Domain.ValueObjects; // For Money

namespace ProcureToPay.Domain.Events
{
    // --- Placeholder: Define a base marker interface or class if desired ---
    // public interface IDomainEvent { }

    // --- BudgetAllocation Specific Events ---

    /// <summary>
    /// Raised when a new Budget Allocation is created.
    /// </summary>
    /// <param name="AllocationId">The ID of the new allocation.</param>
    /// <param name="BudgetId">The ID of the parent budget.</param>
    /// <param name="DepartmentId">The ID of the allocated department (or project/cost center).</param>
    /// <param name="AllocatedAmount">The amount allocated.</param>
    public record BudgetAllocationCreatedEvent(Guid AllocationId, Guid BudgetId, Guid DepartmentId, Money AllocatedAmount);

    /// <summary>
    /// Raised when an amount is consumed (committed) against a Budget Allocation.
    /// </summary>
    /// <param name="AllocationId">The ID of the allocation being consumed.</param>
    /// <param name="ConsumedAmount">The amount that was consumed in this specific transaction.</param>
    public record BudgetAllocationConsumedEvent(Guid AllocationId, Money ConsumedAmount);

    /// <summary>
    /// Raised when a previously consumed amount is released back to a Budget Allocation.
    /// </summary>
    /// <param name="AllocationId">The ID of the allocation having funds released.</param>
    /// <param name="ReleasedAmount">The amount that was released in this specific transaction.</param>
    public record BudgetAllocationReleasedEvent(Guid AllocationId, Money ReleasedAmount);

    /// <summary>
    /// Raised when the status of a Budget Allocation changes.
    /// </summary>
    /// <param name="AllocationId">The ID of the allocation whose status changed.</param>
    /// <param name="NewStatus">The new status of the allocation.</param>
    public record BudgetAllocationStatusChangedEvent(Guid AllocationId, AllocationStatus NewStatus);

}
