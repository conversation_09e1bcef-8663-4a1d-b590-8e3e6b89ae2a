using ProcureToPay.Application.Features.Categories.DTOs;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ProcureToPay.Application.Features.Categories.Services
{
    /// <summary>
    /// Service interface for managing categories.
    /// </summary>
    public interface ICategoryService
    {
        /// <summary>
        /// Gets all categories for the current tenant.
        /// </summary>
        /// <returns>A collection of category DTOs.</returns>
        Task<IEnumerable<CategoryDto>> GetCategoriesAsync();

        /// <summary>
        /// Gets a category by its ID for the current tenant.
        /// </summary>
        /// <param name="categoryId">The ID of the category to retrieve.</param>
        /// <returns>The category DTO if found, null otherwise.</returns>
        Task<CategoryDto?> GetCategoryByIdAsync(Guid categoryId);

        /// <summary>
        /// Creates a new category for the current tenant.
        /// </summary>
        /// <param name="request">The create category request.</param>
        /// <returns>A tuple containing the created category DTO and any validation errors.</returns>
        Task<(CategoryDto? Category, IEnumerable<string> Errors)> CreateCategoryAsync(CreateCategoryRequest request);

        /// <summary>
        /// Updates an existing category for the current tenant.
        /// </summary>
        /// <param name="request">The update category request.</param>
        /// <returns>A tuple containing the updated category DTO and any validation errors.</returns>
        Task<(CategoryDto? Category, IEnumerable<string> Errors)> UpdateCategoryAsync(UpdateCategoryRequest request);

        /// <summary>
        /// Deletes a category by its ID for the current tenant.
        /// </summary>
        /// <param name="categoryId">The ID of the category to delete.</param>
        /// <returns>True if the category was deleted, false otherwise.</returns>
        Task<bool> DeleteCategoryAsync(Guid categoryId);

        /// <summary>
        /// Gets all categories that can be used as parent categories for the current tenant.
        /// </summary>
        /// <param name="currentCategoryId">The ID of the current category to exclude from the results.</param>
        /// <returns>A collection of category DTOs.</returns>
        Task<IEnumerable<CategoryDto>> GetParentCategoryCandidatesAsync(Guid? currentCategoryId = null);
    }
}
