using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProcureToPay.Domain.Entities; // Assuming RFQ, RFQLine, PR, Vendor, Contract entities exist
using ProcureToPay.Domain.Enums;   // Assuming RFQStatus enum exists
using ProcureToPay.Domain.ValueObjects; // Assuming Address VO exists
using System;

namespace ProcureToPay.Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// EF Core Configuration for the RequestForQuote (RFQ) entity targeting PostgreSQL.
    /// </summary>
    public class RequestForQuoteConfiguration : IEntityTypeConfiguration<RequestForQuote>
    {
        private const string DefaultSchema = "public"; // Example schema for sourcing documents

        public void Configure(EntityTypeBuilder<RequestForQuote> builder)
        {
            // --- Table Mapping ---
            builder.ToTable("request_for_quotes", DefaultSchema); // Use snake_case and schema

            // --- Primary Key ---
            // Assuming RequestForQuote inherits from BaseEntity<Guid>
            builder.HasKey(r => r.Id);
            // Note: UseIdentityByDefaultColumn() requirement ignored as PK is Guid

            // --- Soft Delete Configuration ---
            // Assumes RequestForQuote has: public bool IsDeleted { get; private set; }
            builder.Property(r => r.IsDeleted)
                   .HasDefaultValue(false)
                   .IsRequired();
            builder.HasIndex(r => r.IsDeleted);


            // --- Concurrency Control (PostgreSQL xmin - EF Core 7+) ---
            builder.Property<uint>("xmin")
                   .HasColumnType("xid")
                   .ValueGeneratedOnAddOrUpdate()
                   .IsConcurrencyToken();


            // --- Property Mappings & Constraints ---
            builder.Property(r => r.TenantId)
                   .IsRequired();
            builder.HasIndex(r => r.TenantId); // Index for tenant filtering

            builder.Property(r => r.RFQNumber)
                   .IsRequired()
                   .HasMaxLength(50); // Adjust length as needed
            // Unique index within a tenant
            builder.HasIndex(r => new { r.TenantId, r.RFQNumber }).IsUnique();

            builder.Property(r => r.Title)
                   .IsRequired()
                   .HasMaxLength(250); // Or use .HasColumnType("text")

            // Map Status Enum
            builder.Property(r => r.Status)
                   .IsRequired()
                   .HasConversion<string>()
                   .HasMaxLength(50);
            builder.HasIndex(r => r.Status);

            // Dates/Timestamps
            builder.Property(r => r.SubmissionDeadline)
                   .IsRequired()
                   .HasColumnType("timestamp without time zone"); // PostgreSQL timestamp
            builder.HasIndex(r => r.SubmissionDeadline);

            builder.Property(r => r.RequiredDeliveryDate)
                   .HasColumnType("timestamp without time zone"); // Nullable timestamp

            // Text Fields
            builder.Property(r => r.Description).HasColumnType("text");
            builder.Property(r => r.ScopeOfWork).HasColumnType("text");
            builder.Property(r => r.TermsAndConditions).HasColumnType("text");
            builder.Property(r => r.VendorReferenceInstructions).HasColumnType("text");

            // Currency
            builder.Property(r => r.CurrencyCode)
                   .IsRequired()
                   .HasMaxLength(3);

            // User FK
            builder.Property(r => r.CreatedByUserId)
                   .IsRequired()
                   .HasMaxLength(450); // Match Identity User Id length if applicable
            builder.HasIndex(r => r.CreatedByUserId);

            // Communication Fields
            builder.Property(r => r.ContactPersonEmail).HasMaxLength(254);
            builder.Property(r => r.CommunicationMethod).HasMaxLength(50);


            // --- Value Object Embedding (DeliverToAddress) ---
            builder.OwnsOne(r => r.DeliverToAddress, addr =>
            {
                // Configure Address properties using snake_case column names
                addr.Property(a => a.Street).HasColumnName("deliver_to_street").HasMaxLength(200).IsRequired();
                addr.Property(a => a.City).HasColumnName("deliver_to_city").HasMaxLength(100).IsRequired();
                addr.Property(a => a.State).HasColumnName("deliver_to_state").HasMaxLength(100).IsRequired();
                addr.Property(a => a.Country).HasColumnName("deliver_to_country").HasMaxLength(100).IsRequired();
                addr.Property(a => a.PostalCode).HasColumnName("deliver_to_postal_code").HasMaxLength(20).IsRequired();
            });
            builder.Navigation(r => r.DeliverToAddress).IsRequired(); // Mark as required


            // --- Relationships ---

            // To RequestForQuoteLines (One RFQ to Many Lines)
            builder.HasMany(r => r.Lines)
                   .WithOne(l => l.RequestForQuote) // Assumes RequestForQuoteLine has RequestForQuote nav prop
                   .HasForeignKey(l => l.RequestForQuoteId) // Assumes RequestForQuoteLine has FK
                   .IsRequired()
                   .OnDelete(DeleteBehavior.Cascade); // Deleting RFQ deletes its lines

            // To Originating PurchaseRequisition (Optional)
            builder.HasOne(r => r.OriginatingRequisition)
                   .WithMany() // Assuming PurchaseRequisition doesn't need direct collection of RFQs
                   .HasForeignKey(r => r.OriginatingRequisitionId)
                   .IsRequired(false)
                   .OnDelete(DeleteBehavior.SetNull); // Nullify link if Requisition is deleted

            // To Awarded Vendor (Optional)
            builder.HasOne(r => r.AwardedVendor)
                   .WithMany() // Assuming Vendor doesn't need direct collection of RFQs they won
                   .HasForeignKey(r => r.AwardedVendorId)
                   .IsRequired(false)
                   .OnDelete(DeleteBehavior.Restrict); // Prevent deleting Vendor if they were awarded an RFQ? Or SetNull?

            // To Related Agreement/Contract (Optional)
            builder.HasOne(r => r.RelatedAgreement)
                   .WithMany() // Assuming Contract doesn't need direct collection of RFQs
                   .HasForeignKey(r => r.RelatedAgreementId)
                   .IsRequired(false)
                   .OnDelete(DeleteBehavior.SetNull); // Nullify link if Agreement is deleted

            // Relationship to CreatedByUser (ApplicationUser) is typically handled implicitly via FK
            // or configured from the ApplicationUser side if direct navigation is needed there.


            // --- Indexes ---
            // Included unique index on TenantId+RFQNumber and indexes on Status, SubmissionDeadline, CreatedByUserId above.
            builder.HasIndex(r => r.OriginatingRequisitionId);
            builder.HasIndex(r => r.AwardedVendorId);
            builder.HasIndex(r => r.RelatedAgreementId);


            // --- Tenant Isolation & Soft Delete Query Filter ---
            // Note: Tenant filtering should be implemented at the DbContext level
            // Example:
            // builder.HasQueryFilter(r => !r.IsDeleted && r.TenantId == currentTenantId);
            // Where currentTenantId is obtained from the DbContext or a tenant provider service

            // For now, just implement the soft delete filter
            builder.HasQueryFilter(r => !r.IsDeleted);
        }
    }
}

