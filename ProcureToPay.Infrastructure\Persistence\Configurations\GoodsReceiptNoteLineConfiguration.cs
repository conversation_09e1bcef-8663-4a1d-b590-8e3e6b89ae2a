using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProcureToPay.Domain.Entities;
using ProcureToPay.Domain.Enums; // Assuming UnitOfMeasure, QualityControlStatus enums exist
using ProcureToPay.Infrastructure.Persistence.Extensions;

namespace ProcureToPay.Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// Configuration for the GoodsReceiptNoteLine entity for EF Core and PostgreSQL.
    /// </summary>
    public class GoodsReceiptNoteLineConfiguration : IEntityTypeConfiguration<GoodsReceiptNoteLine>
    {
        public void Configure(EntityTypeBuilder<GoodsReceiptNoteLine> builder)
        {
            // Table Mapping
            builder.ToTable("goods_receipt_note_lines");

            // Primary Key (Composite Key)
            // Assuming GoodsReceiptNoteId is long (from GRN) and LineNumber is int
            builder.HasKey(l => new { l.GoodsReceiptNoteId, l.LineNumber });

            // Properties
            builder.Property(l => l.LineNumber)
                .ValueGeneratedNever(); // Part of composite key

            builder.Property(l => l.QuantityReceived)
                .HasColumnType("numeric(18, 4)")
                .IsRequired()
                .HasDefaultValue(0m);

            builder.Property(l => l.QuantityAccepted)
                .HasColumnType("numeric(18, 4)")
                .IsRequired()
                .HasDefaultValue(0m);

            builder.Property(l => l.QuantityRejected)
                .HasColumnType("numeric(18, 4)")
                .IsRequired()
                .HasDefaultValue(0m);

            builder.Property(l => l.UnitOfMeasure)
                .IsRequired()
                .HasConversion<string>()
                .HasMaxLength(20);

            builder.Property(l => l.QualityControlStatus)
                .IsRequired()
                .HasConversion<string>() // Or int
                .HasMaxLength(50); // Adjust size if string

            builder.Property(l => l.PutAwayLocation)
                .HasMaxLength(100)
                .IsRequired(false);

            builder.Property(l => l.BatchNumber)
                .HasColumnType("text")
                .IsRequired(false);

            builder.Property(l => l.LotNumber)
                .HasColumnType("text")
                .IsRequired(false);

            builder.Property(l => l.ExpiryDate)
                .HasColumnType("timestamp without time zone") // Or just "date" if time is irrelevant
                .IsRequired(false);

            // Foreign Keys (Assuming Guid or appropriate types)
            builder.Property(l => l.ProductId).IsRequired();
            builder.Property(l => l.PurchaseOrderLineId).IsRequired(); // GRN Line must map back to PO Line


            // --- Relationships ---
            // Line-Header (Many-to-One)
            builder.HasOne(l => l.GoodsReceiptNote)
                   .WithMany(grn => grn.Lines)
                   .HasForeignKey(l => l.GoodsReceiptNoteId)
                   .IsRequired() // Part of composite key
                   .OnDelete(DeleteBehavior.Cascade); // Matches header config

            // Link to Product/Service (Many-to-One)
            builder.HasOne(l => l.Product)
                   .WithMany() // Assuming ProductDefinition doesn't have direct nav back
                   .HasForeignKey(l => l.ProductId)
                   .IsRequired()
                   .OnDelete(DeleteBehavior.Restrict); // Prevent deleting product if used in GRN lines

            // Link to Origin Line (Many-to-One)
            builder.HasOne(l => l.PurchaseOrderLine)
                   .WithMany(pol => pol.GoodsReceiptNoteLines) // Assuming POLine has collection
                   .HasForeignKey(l => l.PurchaseOrderLineId)
                   .IsRequired()
                   .OnDelete(DeleteBehavior.Restrict); // Prevent deleting PO Line if linked GRN line exists


            // --- Indexes ---
            builder.HasIndex(l => l.ProductId);
            builder.HasIndex(l => l.PurchaseOrderLineId);
            builder.HasIndex(l => l.QualityControlStatus);
            builder.HasIndex(l => l.ExpiryDate);
            // Composite PK serves as an index for GoodsReceiptNoteId


            // --- TODO ---
            // TODO: Verify FK types (ProductId, PurchaseOrderLineId) match related entities.
            // TODO: Define QualityControlStatus enum if not already present.
        }
    }
}
