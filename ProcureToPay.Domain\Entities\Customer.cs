﻿using System;
using System.Collections.Generic;
using ProcureToPay.Domain.Enums;       // For CustomerType
using ProcureToPay.Domain.ValueObjects; // For Address, Money, <PERSON><PERSON>erson
using ProcureToPay.Domain.Exceptions;   // Assuming DomainStateException exists
using ProcureToPay.Domain.Events;       // Assuming Customer domain events namespace exists

// Assuming BaseEntity<Guid>, SalesOrder, Invoice entities exist
namespace ProcureToPay.Domain.Entities
{
    /// <summary>
    /// Represents a Customer entity within the system.
    /// </summary>
    public class Customer : BaseEntity<Guid> // Assuming BaseEntity<Guid>
    {
        /// <summary>
        /// Foreign Key for multi-tenancy.
        /// </summary>
        public Guid TenantId { get; private set; }

        /// <summary>
        /// Unique code identifying the customer within the tenant.
        /// </summary>
        public string CustomerCode { get; private set; } = null!;

        /// <summary>
        /// Primary or common name of the customer.
        /// </summary>
        public string Name { get; private set; } = null!;

        /// <summary>
        /// Official legal name of the customer entity, if different from Name.
        /// </summary>
        public string? LegalName { get; private set; }

        /// <summary>
        /// Tax identification number (e.g., VAT ID, EIN). Format varies by region.
        /// </summary>
        public string? TaxIdentifier { get; private set; }

        /// <summary>
        /// Indicates if the customer account is currently active.
        /// </summary>
        public bool IsActive { get; private set; }

        /// <summary>
        /// Credit limit assigned to the customer. Uses Money VO. Null if no limit set.
        /// </summary>
        public Money? CreditLimit { get; private set; }

        /// <summary>
        /// Default payment terms applicable to this customer (e.g., "Net 30").
        /// </summary>
        public string? DefaultPaymentTerms { get; private set; }

        /// <summary>
        /// Default currency code used for transactions with this customer.
        /// </summary>
        public string? DefaultCurrencyCode { get; private set; } // Stored directly for easier access

        /// <summary>
        /// Customer's primary website URL.
        /// </summary>
        public string? Website { get; private set; }

        /// <summary>
        /// Classification type of the customer.
        /// </summary>
        public CustomerType CustomerType { get; private set; }

        /// <summary>
        /// Billing address for the customer. Uses Address VO.
        /// </summary>
        public Address BillingAddress { get; private set; } = null!;

        /// <summary>
        /// Default shipping address for the customer. Uses Address VO.
        /// </summary>
        public Address ShippingAddress { get; private set; } = null!;

        /// <summary>
        /// Primary contact person details. Uses ContactPerson record/VO.
        /// </summary>
        public ContactPerson PrimaryContact { get; private set; } = null!; // Assuming ContactPerson record exists

        /// <summary>
        /// Optional Foreign Key (string representation) of the assigned salesperson. Links to ApplicationUser Id.
        /// </summary>
        public string? AssignedSalesRepId { get; private set; }

        /// <summary>
        /// Flag indicating if the customer has been soft-deleted.
        /// </summary>
        public bool IsDeleted { get; private set; }

        // --- Navigation Properties ---
        /// <summary>
        /// Sales orders associated with this customer.
        /// </summary>
        public virtual ICollection<SalesOrder> SalesOrders { get; private set; } = new List<SalesOrder>();

        /// <summary>
        /// Invoices issued to this customer.
        /// </summary>
        public virtual ICollection<Invoice> Invoices { get; private set; } = new List<Invoice>(); // Added optional relationship

        /// <summary>
        /// Return authorizations for this customer.
        /// </summary>
        public virtual ICollection<ReturnAuthorization> ReturnAuthorizations { get; private set; } = new List<ReturnAuthorization>();

        // No direct navigation to AssignedSalesRep (ApplicationUser)


        /// <summary>
        /// Private constructor for EF Core.
        /// </summary>
        private Customer() : base(Guid.NewGuid()) { }

        /// <summary>
        /// Creates a new Customer instance.
        /// </summary>
        public Customer(
            Guid id,
            Guid tenantId,
            string customerCode,
            string name,
            CustomerType customerType,
            Address billingAddress,
            Address shippingAddress,
            ContactPerson primaryContact, // Pass ContactPerson record/VO
            string? legalName = null,
            string? taxIdentifier = null,
            Money? creditLimit = null, // Pass Money VO
            string? defaultPaymentTerms = null,
            string? defaultCurrencyCode = null,
            string? website = null,
            string? assignedSalesRepId = null,
            bool isActive = true
            ) : base(id)
        {
            // Validation
            if (tenantId == Guid.Empty) throw new ArgumentException("TenantId cannot be empty.", nameof(tenantId));
            ArgumentException.ThrowIfNullOrWhiteSpace(customerCode);
            ArgumentException.ThrowIfNullOrWhiteSpace(name);
            ArgumentNullException.ThrowIfNull(billingAddress);
            ArgumentNullException.ThrowIfNull(shippingAddress);
            ArgumentNullException.ThrowIfNull(primaryContact);
            if (creditLimit?.Amount < 0) throw new ArgumentOutOfRangeException(nameof(creditLimit), "Credit limit cannot be negative.");
            if (!string.IsNullOrWhiteSpace(defaultCurrencyCode) && defaultCurrencyCode.Length != 3) throw new ArgumentException("Default currency code must be 3 characters.", nameof(defaultCurrencyCode));
            if (creditLimit != null && string.IsNullOrWhiteSpace(creditLimit.CurrencyCode)) throw new ArgumentException("Credit limit must have a currency code.", nameof(creditLimit));
            if (creditLimit != null && defaultCurrencyCode != null && creditLimit.CurrencyCode.ToUpperInvariant() != defaultCurrencyCode.ToUpperInvariant())
                throw new ArgumentException("Credit limit currency must match default currency code if both are specified.", nameof(creditLimit));


            TenantId = tenantId;
            CustomerCode = customerCode;
            Name = name;
            LegalName = legalName;
            TaxIdentifier = taxIdentifier;
            IsActive = isActive;
            CreditLimit = creditLimit;
            DefaultPaymentTerms = defaultPaymentTerms;
            // Set DefaultCurrencyCode - prefer from CreditLimit if provided, otherwise use parameter
            DefaultCurrencyCode = (creditLimit?.CurrencyCode ?? defaultCurrencyCode)?.ToUpperInvariant();
            Website = website;
            CustomerType = customerType;
            BillingAddress = billingAddress;
            ShippingAddress = shippingAddress;
            PrimaryContact = primaryContact;
            AssignedSalesRepId = assignedSalesRepId;
            IsDeleted = false;

            AddDomainEvent(new CustomerCreatedEvent(this.Id, this.TenantId));
        }

        // --- Domain Methods ---

        public void UpdateDetails(string name, string? legalName, string? taxIdentifier, string? website, CustomerType customerType)
        {
            ArgumentException.ThrowIfNullOrWhiteSpace(name);
            // Add other validation as needed

            Name = name;
            LegalName = legalName;
            TaxIdentifier = taxIdentifier;
            Website = website;
            CustomerType = customerType;

            AddDomainEvent(new CustomerDetailsUpdatedEvent(this.Id));
        }

        public void UpdateAddresses(Address billingAddress, Address shippingAddress)
        {
            ArgumentNullException.ThrowIfNull(billingAddress);
            ArgumentNullException.ThrowIfNull(shippingAddress);

            BillingAddress = billingAddress;
            ShippingAddress = shippingAddress;

            AddDomainEvent(new CustomerAddressUpdatedEvent(this.Id));
        }

        public void UpdatePrimaryContact(ContactPerson primaryContact)
        {
            ArgumentNullException.ThrowIfNull(primaryContact);
            PrimaryContact = primaryContact;
            AddDomainEvent(new CustomerContactUpdatedEvent(this.Id));
        }

        public void UpdateDefaults(string? paymentTerms, string? currencyCode)
        {
            if (!string.IsNullOrWhiteSpace(currencyCode) && currencyCode.Length != 3) throw new ArgumentException("Default currency code must be 3 characters.", nameof(currencyCode));
            // If changing currency, ensure CreditLimit currency also changes or is cleared? Requires business rule.
            if (CreditLimit != null && !string.IsNullOrWhiteSpace(currencyCode) && CreditLimit.CurrencyCode.ToUpperInvariant() != currencyCode.ToUpperInvariant())
                throw new DomainStateException("Cannot change default currency while a credit limit with a different currency exists.");

            DefaultPaymentTerms = paymentTerms;
            DefaultCurrencyCode = currencyCode?.ToUpperInvariant();

            AddDomainEvent(new CustomerDefaultsUpdatedEvent(this.Id));
        }

        public void SetCreditLimit(Money? newLimit)
        {
            if (newLimit?.Amount < 0) throw new ArgumentOutOfRangeException(nameof(newLimit), "Credit limit cannot be negative.");
            // Ensure currency matches DefaultCurrencyCode if set
            if (newLimit != null && !string.IsNullOrWhiteSpace(DefaultCurrencyCode) && newLimit.CurrencyCode.ToUpperInvariant() != DefaultCurrencyCode)
                throw new DomainStateException($"Credit limit currency '{newLimit.CurrencyCode}' must match customer default currency '{DefaultCurrencyCode}'.");
            if (newLimit != null && string.IsNullOrWhiteSpace(DefaultCurrencyCode))
                DefaultCurrencyCode = newLimit.CurrencyCode; // Set default currency if setting limit first

            if (CreditLimit == newLimit) return; // Assumes Money VO equality

            CreditLimit = newLimit;
            AddDomainEvent(new CustomerCreditLimitSetEvent(this.Id, newLimit));
        }

        public void AssignSalesRep(string? salesRepId)
        {
            // Add validation if needed (e.g., check if salesRepId is valid user - Application layer?)
            if (AssignedSalesRepId == salesRepId) return;
            AssignedSalesRepId = salesRepId;
            AddDomainEvent(new CustomerSalesRepAssignedEvent(this.Id, salesRepId));
        }

        public void Activate()
        {
            if (IsActive) return;
            IsActive = true;
            AddDomainEvent(new CustomerActivatedEvent(this.Id));
        }

        public void Deactivate()
        {
            if (!IsActive) return;
            IsActive = false;
            AddDomainEvent(new CustomerDeactivatedEvent(this.Id));
        }

        public void MarkAsDeleted()
        {
            if (IsDeleted) return;
            IsDeleted = true;
            IsActive = false; // Deleting should also deactivate
            AddDomainEvent(new CustomerDeletedEvent(this.Id));
        }

        public void Restore()
        {
            if (!IsDeleted) return;
            IsDeleted = false;
            // Should it become active automatically on restore? Business rule.
            // IsActive = true;
            AddDomainEvent(new CustomerRestoredEvent(this.Id));
        }
    }

    // --- Placeholder Domain Event Definitions (Place in Domain/Events) ---
    /*
    namespace ProcureToPay.Domain.Events
    {
        public record CustomerCreatedEvent(Guid CustomerId, Guid TenantId);
        public record CustomerDetailsUpdatedEvent(Guid CustomerId);
        public record CustomerAddressUpdatedEvent(Guid CustomerId);
        public record CustomerContactUpdatedEvent(Guid CustomerId);
        public record CustomerDefaultsUpdatedEvent(Guid CustomerId);
        public record CustomerCreditLimitSetEvent(Guid CustomerId, Money? NewLimit);
        public record CustomerSalesRepAssignedEvent(Guid CustomerId, string? SalesRepId);
        public record CustomerActivatedEvent(Guid CustomerId);
        public record CustomerDeactivatedEvent(Guid CustomerId);
        public record CustomerDeletedEvent(Guid CustomerId);
        public record CustomerRestoredEvent(Guid CustomerId);
    }
    */
}
