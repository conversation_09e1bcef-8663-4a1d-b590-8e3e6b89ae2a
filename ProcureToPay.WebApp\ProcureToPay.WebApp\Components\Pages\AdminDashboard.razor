@page "/admin-dashboard"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using System.Security.Claims
@using ProcureToPay.Domain.Interfaces
@using ProcureToPay.Infrastructure.Identity
@using Microsoft.AspNetCore.Identity

@attribute [Authorize(Roles = "SystemAdmin,TenantAdmin")]

@inject AuthenticationStateProvider AuthenticationStateProvider
@inject ITenantProvider TenantProvider
@inject UserManager<ApplicationUser> UserManager

<PageTitle>Admin Dashboard</PageTitle>

<h1>Admin Dashboard</h1>

<div class="row">
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <h3>User Information</h3>
            </div>
            <div class="card-body">
                @if (isLoading)
                {
                    <p>Loading user information...</p>
                }
                else
                {
                    <dl class="row">
                        <dt class="col-sm-4">Username:</dt>
                        <dd class="col-sm-8">@username</dd>

                        <dt class="col-sm-4">User ID:</dt>
                        <dd class="col-sm-8">@userId</dd>

                        <dt class="col-sm-4">Email:</dt>
                        <dd class="col-sm-8">@email</dd>

                        <dt class="col-sm-4">Roles:</dt>
                        <dd class="col-sm-8">
                            @if (roles.Any())
                            {
                                <ul class="list-unstyled">
                                    @foreach (var role in roles)
                                    {
                                        <li><span class="badge bg-primary">@role</span></li>
                                    }
                                </ul>
                            }
                            else
                            {
                                <span>No roles assigned</span>
                            }
                        </dd>
                    </dl>
                }
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <h3>Tenant Information</h3>
            </div>
            <div class="card-body">
                @if (isLoading)
                {
                    <p>Loading tenant information...</p>
                }
                else
                {
                    <dl class="row">
                        <dt class="col-sm-4">Current Tenant ID:</dt>
                        <dd class="col-sm-8">@currentTenantId</dd>

                        <dt class="col-sm-4">Tenant Claims:</dt>
                        <dd class="col-sm-8">
                            @if (tenantClaims.Any())
                            {
                                <ul class="list-unstyled">
                                    @foreach (var claim in tenantClaims)
                                    {
                                        <li><strong>@claim.Type:</strong> @claim.Value</li>
                                    }
                                </ul>
                            }
                            else
                            {
                                <span>No tenant claims found</span>
                            }
                        </dd>
                    </dl>
                }
            </div>
        </div>
    </div>
</div>

@code {
    private bool isLoading = true;
    private string username = string.Empty;
    private string userId = string.Empty;
    private string email = string.Empty;
    private List<string> roles = new List<string>();
    private Guid? currentTenantId;
    private List<Claim> tenantClaims = new List<Claim>();

    protected override async Task OnInitializedAsync()
    {
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;

        if (user.Identity?.IsAuthenticated == true)
        {
            username = user.Identity.Name ?? "Unknown";
            userId = user.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "Unknown";
            email = user.FindFirst(ClaimTypes.Email)?.Value ?? "Unknown";
            
            // Get roles from claims
            roles = user.Claims
                .Where(c => c.Type == ClaimTypes.Role)
                .Select(c => c.Value)
                .ToList();

            // Get tenant information
            currentTenantId = TenantProvider.GetCurrentTenantId();
            
            // Get tenant-related claims
            tenantClaims = user.Claims
                .Where(c => c.Type.Contains("tenant", StringComparison.OrdinalIgnoreCase))
                .ToList();
        }

        isLoading = false;
    }
}
