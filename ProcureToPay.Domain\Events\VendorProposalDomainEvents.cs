﻿using ProcureToPay.Domain.Enums;
using ProcureToPay.Domain.ValueObjects;

namespace ProcureToPay.Domain.Events
{
    public record VendorProposalSubmittedEvent(Guid ProposalId, Guid VendorId, Guid RfpId); // Or RFQ/RFI Id
    public record VendorProposalStatusChangedEvent(Guid ProposalId, VendorProposalStatus OldStatus, VendorProposalStatus NewStatus);
    public record VendorProposalValueUpdatedEvent(Guid ProposalId, Money NewValue);
    public record VendorProposalDetailsUpdatedEvent(Guid ProposalId);
    public record VendorProposalDeletedEvent(Guid ProposalId);
    public record VendorProposalRestoredEvent(Guid ProposalId);
    // public record VendorProposalVersionIncrementedEvent(Guid ProposalId, int NewVersion);
}