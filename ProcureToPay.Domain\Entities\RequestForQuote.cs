using System;
using System.Collections.Generic;
using System.Linq; // Added for LINQ operations like Any()
using ProcureToPay.Domain.Enums;       // Required for RFQStatus
using ProcureToPay.Domain.ValueObjects; // Assuming Address VO exists
using ProcureToPay.Domain.Exceptions;   // Assuming DomainStateException exists
using ProcureToPay.Domain.Events;       // Assuming RFQ domain events namespace exists

// Assuming BaseEntity<Guid>, RequestForQuoteLine, PurchaseRequisition, Vendor, Contract entities exist
namespace ProcureToPay.Domain.Entities
{
    /// <summary>
    /// Represents a Request for Quote (RFQ) sourcing event used to solicit pricing from vendors.
    /// </summary>
    public class RequestForQuote : BaseEntity<Guid> // Assuming BaseEntity<Guid>
    {
        /// <summary>
        /// Foreign Key for multi-tenancy.
        /// </summary>
        public Guid TenantId { get; private set; }

        /// <summary>
        /// Unique, human-readable identifier for the RFQ (e.g., RFQ-2025-101).
        /// </summary>
        public string RFQNumber { get; private set; } = null!;

        /// <summary>
        /// Title or subject of the RFQ.
        /// </summary>
        public string Title { get; private set; } = null!;

        /// <summary>
        /// Current status of the RFQ lifecycle (Draft, Open, UnderReview, Awarded, Cancelled, Closed).
        /// </summary>
        public RFQStatus Status { get; private set; }

        /// <summary>
        /// The deadline by which vendors must submit their quotes. Stored in UTC.
        /// </summary>
        public DateTime SubmissionDeadline { get; private set; }

        /// <summary>
        /// Optional date by which the goods/services are required. Stored in UTC if applicable.
        /// </summary>
        public DateTime? RequiredDeliveryDate { get; private set; }

        /// <summary>
        /// General description or introduction for the RFQ.
        /// </summary>
        public string? Description { get; private set; }

        /// <summary>
        /// Detailed scope of work or specifications for the requested goods/services.
        /// </summary>
        public string? ScopeOfWork { get; private set; }

        /// <summary>
        /// Standard terms and conditions applicable to this RFQ.
        /// </summary>
        public string? TermsAndConditions { get; private set; }

        /// <summary>
        /// Specific instructions for vendors regarding references they should provide.
        /// </summary>
        public string? VendorReferenceInstructions { get; private set; }

        /// <summary>
        /// The currency code expected for quotes (e.g., "USD", "SAR").
        /// </summary>
        public string CurrencyCode { get; private set; } = null!;

        /// <summary>
        /// The address where goods should be delivered or services performed. Uses Address VO.
        /// </summary>
        public Address DeliverToAddress { get; private set; } = null!; // Assuming required

        /// <summary>
        /// Foreign Key (string representation) of the user who created the RFQ. Links to ApplicationUser Id.
        /// </summary>
        public string CreatedByUserId { get; private set; } = null!;

        // --- Optional Foreign Keys for Relationships ---
        /// <summary>
        /// Optional FK to the originating Purchase Requisition.
        /// </summary>
        public Guid? OriginatingRequisitionId { get; private set; }
        /// <summary>
        /// Optional FK to the Vendor awarded the RFQ.
        /// </summary>
        public Guid? AwardedVendorId { get; private set; }
        /// <summary>
        /// Optional FK to a related Purchase Agreement or Contract.
        /// </summary>
        public Guid? RelatedAgreementId { get; private set; } // Link to Blanket Order/Contract

        // --- Optional Communication Fields ---
        /// <summary>
        /// Primary contact email for vendor questions regarding this RFQ.
        /// </summary>
        public string? ContactPersonEmail { get; private set; }
        /// <summary>
        /// Preferred method for vendor communication (e.g., "Portal", "Email").
        /// </summary>
        public string? CommunicationMethod { get; private set; }

        // --- Lifecycle ---
        /// <summary>
        /// Flag indicating if the RFQ has been soft-deleted.
        /// </summary>
        public bool IsDeleted { get; private set; }


        // --- Navigation Properties ---
        private readonly List<RequestForQuoteLine> _lines = new();
        /// <summary>
        /// Line items detailing the specific goods or services being quoted.
        /// </summary>
        public virtual IReadOnlyCollection<RequestForQuoteLine> Lines => _lines.AsReadOnly();

        // Optional Navigation Properties
        /// <summary>
        /// Navigation property to the originating Purchase Requisition.
        /// </summary>
        public virtual PurchaseRequisition? OriginatingRequisition { get; private set; }
        /// <summary>
        /// Navigation property to the Vendor awarded the RFQ.
        /// </summary>
        public virtual Vendor? AwardedVendor { get; private set; }
        /// <summary>
        /// Navigation property to the related Purchase Agreement or Contract.
        /// </summary>
        public virtual Contract? RelatedAgreement { get; private set; }
        // No direct navigation to CreatedByUser (ApplicationUser)


        /// <summary>
        /// Private constructor for EF Core.
        /// </summary>
        private RequestForQuote() : base(Guid.NewGuid()) { }

        /// <summary>
        /// Creates a new Request for Quote in Draft status.
        /// </summary>
        public RequestForQuote(
            Guid id,
            Guid tenantId,
            string rfqNumber,
            string title,
            DateTime submissionDeadline,
            string currencyCode,
            Address deliverToAddress,
            string createdByUserId,
            DateTime? requiredDeliveryDate = null,
            string? description = null,
            string? scopeOfWork = null,
            string? termsAndConditions = null,
            string? vendorReferenceInstructions = null,
            Guid? originatingRequisitionId = null,
            Guid? relatedAgreementId = null,
            string? contactPersonEmail = null,
            string? communicationMethod = null
            ) : base(id)
        {
            // Validation
            if (tenantId == Guid.Empty) throw new ArgumentException("TenantId cannot be empty.", nameof(tenantId));
            ArgumentException.ThrowIfNullOrWhiteSpace(rfqNumber);
            ArgumentException.ThrowIfNullOrWhiteSpace(title);
            if (submissionDeadline <= DateTime.UtcNow) throw new ArgumentException("Submission deadline must be in the future.", nameof(submissionDeadline));
            ArgumentException.ThrowIfNullOrWhiteSpace(currencyCode);
            if (currencyCode.Length != 3) throw new ArgumentException("Currency code must be 3 characters.", nameof(currencyCode));
            ArgumentNullException.ThrowIfNull(deliverToAddress);
            ArgumentException.ThrowIfNullOrWhiteSpace(createdByUserId);

            TenantId = tenantId;
            RFQNumber = rfqNumber;
            Title = title;
            SubmissionDeadline = submissionDeadline.ToUniversalTime(); // Store as UTC
            CurrencyCode = currencyCode.ToUpperInvariant();
            DeliverToAddress = deliverToAddress;
            CreatedByUserId = createdByUserId;
            RequiredDeliveryDate = requiredDeliveryDate?.ToUniversalTime(); // Store as UTC
            Description = description;
            ScopeOfWork = scopeOfWork;
            TermsAndConditions = termsAndConditions;
            VendorReferenceInstructions = vendorReferenceInstructions;
            OriginatingRequisitionId = originatingRequisitionId;
            RelatedAgreementId = relatedAgreementId;
            ContactPersonEmail = contactPersonEmail;
            CommunicationMethod = communicationMethod;

            Status = RFQStatus.Draft; // Initial status
            IsDeleted = false;

            AddDomainEvent(new RfqCreatedEvent(this.Id, this.TenantId));
        }

        // --- Domain Methods ---

        // Example: Add Line (assuming line created externally and passed in)
        public void AddLine(RequestForQuoteLine line)
        {
            if (Status != RFQStatus.Draft) throw new DomainStateException($"Cannot add lines when RFQ status is '{Status}'.");
            ArgumentNullException.ThrowIfNull(line);
            if (line.RequestForQuoteId != this.Id) throw new ArgumentException("Line does not belong to this RFQ.", nameof(line));
            // Add duplicate check if needed

            _lines.Add(line);
            // AddDomainEvent(new RfqLineAddedEvent(this.Id, line.Id));
        }

        public void RemoveLine(Guid lineId)
        {
            if (Status != RFQStatus.Draft) throw new DomainStateException($"Cannot remove lines when RFQ status is '{Status}'.");
            var line = _lines.FirstOrDefault(l => l.Id == lineId);
            if (line != null)
            {
                _lines.Remove(line);
                // AddDomainEvent(new RfqLineRemovedEvent(this.Id, lineId));
            }
        }

        public void UpdateDetails(
            string title,
            DateTime submissionDeadline,
            Address deliverToAddress,
            DateTime? requiredDeliveryDate = null,
            string? description = null,
            string? scopeOfWork = null,
            string? termsAndConditions = null,
            string? vendorReferenceInstructions = null,
            string? contactPersonEmail = null,
            string? communicationMethod = null
            )
        {
            if (Status != RFQStatus.Draft) throw new DomainStateException($"Cannot update details when RFQ status is '{Status}'.");
            ArgumentException.ThrowIfNullOrWhiteSpace(title);
            if (submissionDeadline <= DateTime.UtcNow) throw new ArgumentException("Submission deadline must be in the future.", nameof(submissionDeadline));
            ArgumentNullException.ThrowIfNull(deliverToAddress);

            // Update properties...
            bool changed = false;
            if (Title != title) { Title = title; changed = true; }
            if (SubmissionDeadline != submissionDeadline.ToUniversalTime()) { SubmissionDeadline = submissionDeadline.ToUniversalTime(); changed = true; }
            if (DeliverToAddress != deliverToAddress) { DeliverToAddress = deliverToAddress; changed = true; } // Assumes Address VO equality
            if (RequiredDeliveryDate != requiredDeliveryDate?.ToUniversalTime()) { RequiredDeliveryDate = requiredDeliveryDate?.ToUniversalTime(); changed = true; }
            if (Description != description) { Description = description; changed = true; }
            if (ScopeOfWork != scopeOfWork) { ScopeOfWork = scopeOfWork; changed = true; }
            if (TermsAndConditions != termsAndConditions) { TermsAndConditions = termsAndConditions; changed = true; }
            if (VendorReferenceInstructions != vendorReferenceInstructions) { VendorReferenceInstructions = vendorReferenceInstructions; changed = true; }
            if (ContactPersonEmail != contactPersonEmail) { ContactPersonEmail = contactPersonEmail; changed = true; }
            if (CommunicationMethod != communicationMethod) { CommunicationMethod = communicationMethod; changed = true; }

            if (changed) AddDomainEvent(new RfqDetailsUpdatedEvent(this.Id));
        }

        public void LinkOriginatingDocument(Guid? requisitionId)
        {
            // Add validation? Check status?
            if (OriginatingRequisitionId == requisitionId) return;
            OriginatingRequisitionId = requisitionId;
            // AddDomainEvent(...)
        }

        public void LinkAgreement(Guid? agreementId)
        {
            // Add validation? Check status?
            if (RelatedAgreementId == agreementId) return;
            RelatedAgreementId = agreementId;
            // AddDomainEvent(...)
        }


        // --- Status Transitions ---
        public void SendToVendors()
        {
            if (Status != RFQStatus.Draft) throw new DomainStateException($"Cannot send RFQ with status '{Status}'. Must be '{RFQStatus.Draft}'.");
            if (!_lines.Any()) throw new DomainStateException("Cannot send RFQ with no line items.");
            // Add other checks?

            SetStatus(RFQStatus.Open); // Use Open status from enum
            // AddDomainEvent(new RfqSentEvent(this.Id)); // More specific event
        }

        public void StartEvaluation()
        {
            // Can only start evaluation after deadline passed and status is Open?
            if (Status != RFQStatus.Open) throw new DomainStateException($"Cannot start evaluation for RFQ with status '{Status}'. Must be '{RFQStatus.Open}'.");
            if (DateTime.UtcNow <= SubmissionDeadline) throw new DomainStateException($"Cannot start evaluation before submission deadline ({SubmissionDeadline}).");

            SetStatus(RFQStatus.UnderReview); // Use UnderReview status from enum
        }

        public void Award(Guid awardedVendorId)
        {
            if (Status != RFQStatus.UnderReview) throw new DomainStateException($"Cannot award RFQ with status '{Status}'. Must be '{RFQStatus.UnderReview}'.");
            if (awardedVendorId == Guid.Empty) throw new ArgumentException("Awarded VendorId cannot be empty.", nameof(awardedVendorId));

            AwardedVendorId = awardedVendorId; // Set the awarded vendor
            SetStatus(RFQStatus.Awarded);
            AddDomainEvent(new RfqAwardedEvent(this.Id, awardedVendorId)); // Specific event
        }

        public void Cancel(string reason)
        {
            if (Status == RFQStatus.Awarded || Status == RFQStatus.Cancelled || Status == RFQStatus.Closed) throw new DomainStateException($"Cannot cancel RFQ with status '{Status}'.");
            ArgumentException.ThrowIfNullOrWhiteSpace(reason);

            SetStatus(RFQStatus.Cancelled);
            // Store reason? Maybe add Notes property or include in event?
            AddDomainEvent(new RfqCancelledEvent(this.Id, reason)); // Specific event
        }

        public void Close()
        {
            // Typically closed after Awarded or Cancelled
            if (Status != RFQStatus.Awarded && Status != RFQStatus.Cancelled) throw new DomainStateException($"Cannot close RFQ with status '{Status}'. Must be '{RFQStatus.Awarded}' or '{RFQStatus.Cancelled}'.");

            SetStatus(RFQStatus.Closed);
        }


        // --- Soft Delete ---
        public void MarkAsDeleted()
        {
            if (IsDeleted) return;
            IsDeleted = true;
            AddDomainEvent(new RfqDeletedEvent(this.Id));
        }

        public void Restore()
        {
            if (!IsDeleted) return;
            IsDeleted = false;
            AddDomainEvent(new RfqRestoredEvent(this.Id));
        }

        // --- Helper ---
        private void SetStatus(RFQStatus newStatus)
        {
            if (Status == newStatus) return;
            var oldStatus = Status;
            Status = newStatus;
            AddDomainEvent(new RfqStatusChangedEvent(this.Id, oldStatus, newStatus));
        }
    }

    // --- Placeholder Domain Event Definitions (Place in Domain/Events) ---
    /*
    namespace ProcureToPay.Domain.Events
    {
        public record RfqCreatedEvent(Guid RfqId, Guid TenantId);
        public record RfqDetailsUpdatedEvent(Guid RfqId);
        // public record RfqSentEvent(Guid RfqId); // Or use StatusChanged
        public record RfqStatusChangedEvent(Guid RfqId, RFQStatus OldStatus, RFQStatus NewStatus);
        public record RfqAwardedEvent(Guid RfqId, Guid AwardedVendorId);
        public record RfqCancelledEvent(Guid RfqId, string Reason);
        public record RfqDeletedEvent(Guid RfqId);
        public record RfqRestoredEvent(Guid RfqId);
        // public record RfqLineAddedEvent(Guid RfqId, Guid LineId);
        // public record RfqLineRemovedEvent(Guid RfqId, Guid LineId);
    }
    */
}
