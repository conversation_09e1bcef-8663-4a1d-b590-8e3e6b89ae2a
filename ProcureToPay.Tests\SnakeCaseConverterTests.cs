using Xunit;
using ProcureToPay.Infrastructure.Persistence;

namespace ProcureToPay.Tests
{
    /// <summary>
    /// Unit tests for the enhanced snake case converter functionality.
    /// Tests various edge cases including acronyms, special characters, and boundary conditions.
    /// </summary>
    public class SnakeCaseConverterTests
    {
        [Theory]
        [InlineData("TestEntity", "test_entity")]
        [InlineData("XMLHttpRequest", "xml_http_request")]
        [InlineData("IOStream", "io_stream")]
        [InlineData("UserId", "user_id")]
        [InlineData("ID", "id")]
        [InlineData("snake_case", "snake_case")]
        [InlineData("PascalCase", "pascal_case")]
        [InlineData("camelCase", "camel_case")]
        [InlineData("HTTPSConnection", "https_connection")]
        [InlineData("URLPath", "url_path")]
        [InlineData("APIKey", "api_key")]
        [InlineData("JSONData", "json_data")]
        [InlineData("XMLParser", "xml_parser")]
        [InlineData("HTMLElement", "html_element")]
        [InlineData("CSSStyle", "css_style")]
        [InlineData("SQLQuery", "sql_query")]
        [InlineData("UUIDGenerator", "uuid_generator")]
        [InlineData("GUIDValue", "guid_value")]
        [InlineData("Base64Encoder", "base64encoder")]
        [InlineData("UTF8String", "utf8string")]
        [InlineData("ASCIICharacter", "ascii_character")]
        [InlineData("", "")]
        [InlineData("A", "a")]
        [InlineData("AB", "ab")]
        [InlineData("ABC", "abc")]
        [InlineData("ABc", "a_bc")]
        [InlineData("AbC", "ab_c")]
        [InlineData("AbCd", "ab_cd")]
        [InlineData("AbCdE", "ab_cd_e")]
        [InlineData("AbCdEf", "ab_cd_ef")]
        [InlineData("Category", "category")]
        [InlineData("CategoryId", "category_id")]
        [InlineData("ParentCategoryId", "parent_category_id")]
        [InlineData("CreatedAt", "created_at")]
        [InlineData("ModifiedAt", "modified_at")]
        [InlineData("TenantId", "tenant_id")]
        [InlineData("PurchaseOrder", "purchase_order")]
        [InlineData("PurchaseOrderLine", "purchase_order_line")]
        [InlineData("VendorId", "vendor_id")]
        [InlineData("SupplierId", "supplier_id")]
        [InlineData("ContractId", "contract_id")]
        [InlineData("InvoiceId", "invoice_id")]
        [InlineData("PaymentTransactionId", "payment_transaction_id")]
        [InlineData("DeliveryNoteId", "delivery_note_id")]
        [InlineData("GoodsReceiptNoteId", "goods_receipt_note_id")]
        [InlineData("RequestForQuoteId", "request_for_quote_id")]
        [InlineData("RequestForProposalId", "request_for_proposal_id")]
        [InlineData("RequestForInformationId", "request_for_information_id")]
        [InlineData("PurchaseRequisitionId", "purchase_requisition_id")]
        [InlineData("BudgetAllocationId", "budget_allocation_id")]
        [InlineData("DepartmentId", "department_id")]
        [InlineData("ProjectId", "project_id")]
        [InlineData("ProductDefinitionId", "product_definition_id")]
        [InlineData("ProductId", "product_id")]
        [InlineData("TenantProductId", "tenant_product_id")]
        [InlineData("VendorProductId", "vendor_product_id")]
        [InlineData("VendorProposalId", "vendor_proposal_id")]
        [InlineData("TechnicalSubmittalId", "technical_submittal_id")]
        [InlineData("SubmittalReviewId", "submittal_review_id")]
        [InlineData("ProcurementWorkflowId", "procurement_workflow_id")]
        [InlineData("ProcurementWorkflowStepId", "procurement_workflow_step_id")]
        [InlineData("SalesOrderId", "sales_order_id")]
        [InlineData("SalesOrderLineId", "sales_order_line_id")]
        [InlineData("SalesTerritoryId", "sales_territory_id")]
        [InlineData("CustomerId", "customer_id")]
        [InlineData("ReturnAuthorizationId", "return_authorization_id")]
        [InlineData("ReturnAuthorizationLineId", "return_authorization_line_id")]
        [InlineData("TestEntityId", "test_entity_id")]
        public void ConvertToSnakeCase_ShouldConvertCorrectly(string input, string expected)
        {
            // Act
            var result = ApplicationDbContext.ConvertToSnakeCase(input);

            // Assert
            Assert.Equal(expected, result);
        }

        [Fact]
        public void ConvertToSnakeCase_WithNullInput_ShouldReturnNull()
        {
            // Act
            var result = ApplicationDbContext.ConvertToSnakeCase(null!);

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public void ConvertToSnakeCase_WithEmptyString_ShouldReturnEmptyString()
        {
            // Act
            var result = ApplicationDbContext.ConvertToSnakeCase("");

            // Assert
            Assert.Equal("", result);
        }

        [Theory]
        [InlineData("already_snake_case", "already_snake_case")]
        [InlineData("mixed_CaseString", "mixed_case_string")]
        [InlineData("_leadingUnderscore", "_leading_underscore")]
        [InlineData("trailingUnderscore_", "trailing_underscore_")]
        [InlineData("multiple__underscores", "multiple__underscores")]
        public void ConvertToSnakeCase_WithSpecialCases_ShouldHandleCorrectly(string input, string expected)
        {
            // Act
            var result = ApplicationDbContext.ConvertToSnakeCase(input);

            // Assert
            Assert.Equal(expected, result);
        }
    }
}
