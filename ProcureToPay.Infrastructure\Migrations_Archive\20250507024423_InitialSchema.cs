﻿using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using ProcureToPay.Domain.ValueObjects;

#nullable disable

namespace ProcureToPay.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class InitialSchema : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.EnsureSchema(
                name: "public");

            migrationBuilder.CreateTable(
                name: "AspNetRoles",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    Name = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    NormalizedName = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ConcurrencyStamp = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AspNetRoles", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "AspNetUsers",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    UserName = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    NormalizedUserName = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    Email = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    NormalizedEmail = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    EmailConfirmed = table.Column<bool>(type: "boolean", nullable: false),
                    PasswordHash = table.Column<string>(type: "text", nullable: true),
                    SecurityStamp = table.Column<string>(type: "text", nullable: true),
                    ConcurrencyStamp = table.Column<string>(type: "text", nullable: true),
                    PhoneNumber = table.Column<string>(type: "text", nullable: true),
                    PhoneNumberConfirmed = table.Column<bool>(type: "boolean", nullable: false),
                    TwoFactorEnabled = table.Column<bool>(type: "boolean", nullable: false),
                    LockoutEnd = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    LockoutEnabled = table.Column<bool>(type: "boolean", nullable: false),
                    AccessFailedCount = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AspNetUsers", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "budgets",
                schema: "public",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "text", nullable: true),
                    FiscalYear = table.Column<int>(type: "integer", nullable: false),
                    StartDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    EndDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    baseline_amount = table.Column<decimal>(type: "numeric(18,4)", nullable: false),
                    forecast_amount = table.Column<decimal>(type: "numeric(18,4)", nullable: true),
                    CurrencyCode = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Version = table.Column<int>(type: "integer", nullable: false, defaultValue: 1),
                    IsRollingForecast = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    ForecastPeriodsJson = table.Column<string>(type: "jsonb", nullable: true),
                    AllocationRulesJson = table.Column<string>(type: "jsonb", nullable: true),
                    WorkflowInstanceId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedById = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: false),
                    ApprovedById = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_budgets", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Categories",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    Code = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    UnspscCode = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    ParentCategoryId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Categories", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Categories_Categories_ParentCategoryId",
                        column: x => x.ParentCategoryId,
                        principalTable: "Categories",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "customers",
                schema: "public",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    customer_code = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    name = table.Column<string>(type: "text", nullable: false),
                    legal_name = table.Column<string>(type: "text", nullable: true),
                    tax_identifier = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    is_active = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    credit_limit_amount = table.Column<decimal>(type: "numeric(18,2)", nullable: true),
                    credit_limit_currency_code = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: true),
                    default_payment_terms = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    default_currency_code = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: true),
                    website = table.Column<string>(type: "text", nullable: true),
                    customer_type = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    billing_street = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    billing_city = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    billing_state = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    billing_country = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    billing_postal_code = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    shipping_street = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    shipping_city = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    shipping_state = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    shipping_country = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    shipping_postal_code = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    primary_contact_name = table.Column<string>(type: "character varying(150)", maxLength: 150, nullable: false),
                    primary_contact_role = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    primary_contact_email = table.Column<string>(type: "character varying(254)", maxLength: 254, nullable: true),
                    primary_contact_phone = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    AssignedSalesRepId = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_customers", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "departments",
                schema: "public",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Code = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    Description = table.Column<string>(type: "text", nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_departments", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "DocumentLink",
                columns: table => new
                {
                    Name = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    Url = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: false),
                    DocumentType = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true)
                },
                constraints: table =>
                {
                });

            migrationBuilder.CreateTable(
                name: "procurement_workflows",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    WorkflowName = table.Column<string>(type: "character varying(150)", maxLength: 150, nullable: false),
                    Name = table.Column<string>(type: "character varying(150)", maxLength: 150, nullable: false),
                    Description = table.Column<string>(type: "text", maxLength: 500, nullable: true),
                    SubjectDocumentId = table.Column<Guid>(type: "uuid", nullable: false),
                    SubjectDocumentType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    WorkflowType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    Version = table.Column<int>(type: "integer", maxLength: 20, nullable: true),
                    CurrentStatus = table.Column<int>(type: "integer", nullable: false),
                    StartedDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CompletedDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    InitiatedByUserId = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: true),
                    InitiatedByName = table.Column<string>(type: "character varying(150)", maxLength: 150, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_procurement_workflows", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "projects",
                schema: "public",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    ProjectCode = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    StartDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    EndDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    BudgetAmount = table.Column<decimal>(type: "numeric(18,2)", precision: 18, scale: 2, nullable: true),
                    BudgetCurrencyCode = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_projects", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "sales_territories",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(150)", maxLength: 150, nullable: false),
                    Code = table.Column<string>(type: "text", nullable: true),
                    TerritoryCode = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    ParentTerritoryId = table.Column<Guid>(type: "uuid", nullable: true),
                    Description = table.Column<string>(type: "text", nullable: true),
                    PrimarySalespersonId = table.Column<string>(type: "text", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_sales_territories", x => x.Id);
                    table.ForeignKey(
                        name: "FK_sales_territories_sales_territories_ParentTerritoryId",
                        column: x => x.ParentTerritoryId,
                        principalTable: "sales_territories",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "tenant_products",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    SKU = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    Description = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    Price = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_tenant_products", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Tenants",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Identifier = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    SubscriptionPlan = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    ContactEmail = table.Column<string>(type: "character varying(254)", maxLength: 254, nullable: true),
                    Settings = table.Column<string>(type: "text", nullable: true),
                    AddressLine1 = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    City = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    PostalCode = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    Country = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Tenants", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "test_entities",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_test_entities", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Vendors",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(250)", maxLength: 250, nullable: false),
                    VendorCode = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false, defaultValue: "Pending"),
                    VatNumber = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    CommercialRegistrationNumber = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    TaxId = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    ContactName = table.Column<string>(type: "character varying(150)", maxLength: 150, nullable: true),
                    ContactEmail = table.Column<string>(type: "character varying(254)", maxLength: 254, nullable: true),
                    PhoneNumber = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    Website = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    PrimaryAddress_Street = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    PrimaryAddress_City = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    PrimaryAddress_State = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    PrimaryAddress_Country = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    PrimaryAddress_PostalCode = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Vendors", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "AspNetRoleClaims",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    RoleId = table.Column<string>(type: "text", nullable: false),
                    ClaimType = table.Column<string>(type: "text", nullable: true),
                    ClaimValue = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AspNetRoleClaims", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AspNetRoleClaims_AspNetRoles_RoleId",
                        column: x => x.RoleId,
                        principalTable: "AspNetRoles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AspNetUserClaims",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    UserId = table.Column<string>(type: "text", nullable: false),
                    ClaimType = table.Column<string>(type: "text", nullable: true),
                    ClaimValue = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AspNetUserClaims", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AspNetUserClaims_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AspNetUserLogins",
                columns: table => new
                {
                    LoginProvider = table.Column<string>(type: "text", nullable: false),
                    ProviderKey = table.Column<string>(type: "text", nullable: false),
                    ProviderDisplayName = table.Column<string>(type: "text", nullable: true),
                    UserId = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AspNetUserLogins", x => new { x.LoginProvider, x.ProviderKey });
                    table.ForeignKey(
                        name: "FK_AspNetUserLogins_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AspNetUserRoles",
                columns: table => new
                {
                    UserId = table.Column<string>(type: "text", nullable: false),
                    RoleId = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AspNetUserRoles", x => new { x.UserId, x.RoleId });
                    table.ForeignKey(
                        name: "FK_AspNetUserRoles_AspNetRoles_RoleId",
                        column: x => x.RoleId,
                        principalTable: "AspNetRoles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_AspNetUserRoles_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AspNetUserTokens",
                columns: table => new
                {
                    UserId = table.Column<string>(type: "text", nullable: false),
                    LoginProvider = table.Column<string>(type: "text", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: false),
                    Value = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AspNetUserTokens", x => new { x.UserId, x.LoginProvider, x.Name });
                    table.ForeignKey(
                        name: "FK_AspNetUserTokens_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ProductDefinitions",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(250)", maxLength: 250, nullable: false),
                    Description = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    Sku = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Gtin = table.Column<string>(type: "character varying(14)", maxLength: 14, nullable: true),
                    Upc = table.Column<string>(type: "character varying(12)", maxLength: 12, nullable: true),
                    Ean = table.Column<string>(type: "character varying(13)", maxLength: 13, nullable: true),
                    LifecycleState = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false, defaultValue: "Active"),
                    CategoryId = table.Column<Guid>(type: "uuid", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    Version = table.Column<int>(type: "integer", nullable: false, defaultValue: 1),
                    AttributesJson = table.Column<string>(type: "jsonb", nullable: true),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ProductDefinitions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ProductDefinitions_Categories_CategoryId",
                        column: x => x.CategoryId,
                        principalTable: "Categories",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "products",
                schema: "public",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    ProductCode = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Description = table.Column<string>(type: "text", nullable: true),
                    UnitOfMeasure = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    CategoryId = table.Column<Guid>(type: "uuid", nullable: true),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_products", x => x.Id);
                    table.ForeignKey(
                        name: "FK_products_Categories_CategoryId",
                        column: x => x.CategoryId,
                        principalTable: "Categories",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "budget_allocations",
                schema: "public",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    BudgetId = table.Column<Guid>(type: "uuid", nullable: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    DepartmentId = table.Column<Guid>(type: "uuid", nullable: false),
                    allocated_amount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    allocated_currency_code = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    consumed_amount = table.Column<decimal>(type: "numeric(18,2)", nullable: false, defaultValue: 0m),
                    consumed_currency_code = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    AllocationDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    FiscalPeriodIdentifier = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    Description = table.Column<string>(type: "text", nullable: true),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_budget_allocations", x => x.Id);
                    table.ForeignKey(
                        name: "FK_budget_allocations_budgets_BudgetId",
                        column: x => x.BudgetId,
                        principalSchema: "public",
                        principalTable: "budgets",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_budget_allocations_departments_DepartmentId",
                        column: x => x.DepartmentId,
                        principalSchema: "public",
                        principalTable: "departments",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "procurement_workflow_steps",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    WorkflowId = table.Column<Guid>(type: "uuid", nullable: false),
                    ProcurementWorkflowId = table.Column<Guid>(type: "uuid", nullable: false),
                    StepName = table.Column<string>(type: "character varying(150)", maxLength: 150, nullable: false),
                    SequenceOrder = table.Column<int>(type: "integer", nullable: false),
                    StepOrder = table.Column<int>(type: "integer", nullable: false),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    AssigneeUserId = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: true),
                    ApproverRoleId = table.Column<Guid>(type: "uuid", nullable: true),
                    ApproverUserId = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: true),
                    AssigneeName = table.Column<string>(type: "character varying(150)", maxLength: 150, nullable: true),
                    AssignedDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ActionDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    Comments = table.Column<string>(type: "text", nullable: true),
                    ConditionExpression = table.Column<string>(type: "text", nullable: true),
                    SlaDuration = table.Column<TimeSpan>(type: "interval", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_procurement_workflow_steps", x => x.Id);
                    table.ForeignKey(
                        name: "FK_procurement_workflow_steps_AspNetUsers_ApproverUserId",
                        column: x => x.ApproverUserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_procurement_workflow_steps_procurement_workflows_Procuremen~",
                        column: x => x.ProcurementWorkflowId,
                        principalTable: "procurement_workflows",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_procurement_workflow_steps_procurement_workflows_WorkflowId",
                        column: x => x.WorkflowId,
                        principalTable: "procurement_workflows",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "requests_for_information",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    RfiNumber = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Title = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    Description = table.Column<string>(type: "text", nullable: true),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    IssuedDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IssueDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    ResponseDueDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ResponseDeadline = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    ProjectId = table.Column<Guid>(type: "uuid", nullable: true),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    IssuedByUserId = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: true),
                    IssuedByName = table.Column<string>(type: "character varying(150)", maxLength: 150, nullable: true),
                    TargetAudienceDescription = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    Notes = table.Column<string>(type: "text", nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_requests_for_information", x => x.Id);
                    table.ForeignKey(
                        name: "FK_requests_for_information_projects_ProjectId",
                        column: x => x.ProjectId,
                        principalSchema: "public",
                        principalTable: "projects",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "sales_territory_representatives",
                columns: table => new
                {
                    SalesTerritoryId = table.Column<Guid>(type: "uuid", nullable: false),
                    RepresentativeId = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_sales_territory_representatives", x => new { x.SalesTerritoryId, x.RepresentativeId });
                    table.ForeignKey(
                        name: "FK_sales_territory_representatives_AspNetUsers_RepresentativeId",
                        column: x => x.RepresentativeId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_sales_territory_representatives_sales_territories_SalesTerr~",
                        column: x => x.SalesTerritoryId,
                        principalTable: "sales_territories",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Contracts",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ContractNumber = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Title = table.Column<string>(type: "character varying(250)", maxLength: 250, nullable: false),
                    ContractType = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    VendorId = table.Column<Guid>(type: "uuid", nullable: false),
                    StartDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    EndDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    TotalContractValueAmount = table.Column<decimal>(type: "numeric(18,4)", precision: 18, scale: 4, nullable: true),
                    TotalContractValueCurrencyCode = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: true),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    PaymentTerms = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    RenewalTerms = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    IsAutoRenew = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    TerminationPenaltyTerms = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    TermsAndConditions = table.Column<string>(type: "text", nullable: true),
                    MilestonesJson = table.Column<string>(type: "jsonb", nullable: true),
                    SlaDetailsJson = table.Column<string>(type: "jsonb", nullable: true),
                    ComplianceDocumentLinksJson = table.Column<string>(type: "jsonb", nullable: true),
                    VendorPerformanceScoreSnapshot = table.Column<decimal>(type: "numeric(5,2)", precision: 5, scale: 2, nullable: true),
                    Version = table.Column<int>(type: "integer", nullable: false, defaultValue: 1),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Contracts", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Contracts_Vendors_VendorId",
                        column: x => x.VendorId,
                        principalTable: "Vendors",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "Suppliers",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    SupplierName = table.Column<string>(type: "character varying(250)", maxLength: 250, nullable: false),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false, defaultValue: "Active"),
                    RiskRating = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    SustainabilityScore = table.Column<decimal>(type: "numeric(5,2)", precision: 5, scale: 2, nullable: true),
                    IsContractManufacturer = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    AverageLeadTimeDays = table.Column<int>(type: "integer", nullable: true),
                    IsCsrCompliant = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    CapacityUtilizationPercent = table.Column<decimal>(type: "numeric(5,2)", precision: 5, scale: 2, nullable: true),
                    VendorId = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    EmergencyContacts = table.Column<string>(type: "jsonb", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Suppliers", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Suppliers_Vendors_VendorId",
                        column: x => x.VendorId,
                        principalTable: "Vendors",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "VendorProducts",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ProductDefinitionId = table.Column<Guid>(type: "uuid", nullable: false),
                    VendorId = table.Column<Guid>(type: "uuid", nullable: false),
                    VendorSku = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    UnitPriceAmount = table.Column<decimal>(type: "numeric(18,4)", precision: 18, scale: 4, nullable: false),
                    UnitPriceCurrencyCode = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    UnitOfMeasure = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    PackSize = table.Column<decimal>(type: "numeric(18,4)", precision: 18, scale: 4, nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    LeadTimeDays = table.Column<int>(type: "integer", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VendorProducts", x => x.Id);
                    table.ForeignKey(
                        name: "FK_VendorProducts_ProductDefinitions_ProductDefinitionId",
                        column: x => x.ProductDefinitionId,
                        principalTable: "ProductDefinitions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_VendorProducts_Vendors_VendorId",
                        column: x => x.VendorId,
                        principalTable: "Vendors",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "requests_for_proposal",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    RfpNumber = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Title = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    ScopeOfWork = table.Column<string>(type: "text", nullable: true),
                    EvaluationCriteria = table.Column<string>(type: "jsonb", nullable: true),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    IssuedDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IssueDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    DecisionDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    ProjectId = table.Column<Guid>(type: "uuid", nullable: true),
                    QuestionDeadline = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    SubmissionDeadline = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    IssuedByUserId = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: true),
                    IssuedByName = table.Column<string>(type: "character varying(150)", maxLength: 150, nullable: true),
                    Department = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    ExpectedContractStartDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ExpectedContractDuration = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    AwardedVendorId = table.Column<Guid>(type: "uuid", nullable: true),
                    AwardedContractId = table.Column<Guid>(type: "uuid", nullable: true),
                    CompletedDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_requests_for_proposal", x => x.Id);
                    table.ForeignKey(
                        name: "FK_requests_for_proposal_Contracts_AwardedContractId",
                        column: x => x.AwardedContractId,
                        principalTable: "Contracts",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_requests_for_proposal_Vendors_AwardedVendorId",
                        column: x => x.AwardedVendorId,
                        principalTable: "Vendors",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_requests_for_proposal_projects_ProjectId",
                        column: x => x.ProjectId,
                        principalSchema: "public",
                        principalTable: "projects",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "vendor_proposals",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    VendorId = table.Column<Guid>(type: "uuid", nullable: false),
                    RequestForProposalId = table.Column<Guid>(type: "uuid", nullable: false),
                    SubmissionDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    TotalProposedValue = table.Column<decimal>(type: "numeric(18,2)", nullable: true),
                    ValidityPeriodDays = table.Column<int>(type: "integer", nullable: false),
                    ValidityEndDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    AlternatePaymentTerms = table.Column<string>(type: "text", nullable: true),
                    ValueAddedServices = table.Column<string>(type: "text", nullable: true),
                    SustainabilityCommitments = table.Column<string>(type: "text", nullable: true),
                    RiskSharingClauses = table.Column<string>(type: "text", nullable: true),
                    PerformanceGuarantees = table.Column<string>(type: "text", nullable: true),
                    SubcontractorDisclosures = table.Column<string>(type: "text", nullable: true),
                    ComplianceCertificationsJson = table.Column<string>(type: "text", nullable: true),
                    Comments = table.Column<string>(type: "text", nullable: true),
                    Version = table.Column<int>(type: "integer", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_vendor_proposals", x => x.Id);
                    table.ForeignKey(
                        name: "FK_vendor_proposals_Vendors_VendorId",
                        column: x => x.VendorId,
                        principalTable: "Vendors",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_vendor_proposals_requests_for_proposal_RequestForProposalId",
                        column: x => x.RequestForProposalId,
                        principalTable: "requests_for_proposal",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "delivery_note_lines",
                columns: table => new
                {
                    DeliveryNoteId = table.Column<Guid>(type: "uuid", nullable: false),
                    LineNumber = table.Column<int>(type: "integer", nullable: false),
                    PurchaseOrderLineId = table.Column<Guid>(type: "uuid", nullable: true),
                    ProductId = table.Column<Guid>(type: "uuid", nullable: false),
                    SalesOrderId = table.Column<Guid>(type: "uuid", nullable: true),
                    SalesOrderLineNumber = table.Column<int>(type: "integer", nullable: true),
                    ProductSkuSnapshot = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    QuantityShipped = table.Column<decimal>(type: "numeric(18,4)", nullable: false),
                    UnitOfMeasure = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    BatchNumber = table.Column<string>(type: "text", maxLength: 100, nullable: true),
                    SerialNumber = table.Column<string>(type: "text", maxLength: 100, nullable: true),
                    Notes = table.Column<string>(type: "text", nullable: true),
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_delivery_note_lines", x => new { x.DeliveryNoteId, x.LineNumber });
                    table.ForeignKey(
                        name: "FK_delivery_note_lines_ProductDefinitions_ProductId",
                        column: x => x.ProductId,
                        principalTable: "ProductDefinitions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "delivery_notes",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    DeliveryNoteNumber = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    PurchaseOrderId = table.Column<Guid>(type: "uuid", nullable: true),
                    VendorId = table.Column<Guid>(type: "uuid", nullable: false),
                    SalesOrderId = table.Column<Guid>(type: "uuid", nullable: true),
                    ShipmentDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    DeliveryDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    ReceivedBy = table.Column<string>(type: "character varying(150)", maxLength: 150, nullable: true),
                    carrier_name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    tracking_number = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    shipping_street = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    shipping_city = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    shipping_state = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    shipping_country = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    shipping_postal_code = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    billing_street = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    billing_city = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    billing_state = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    billing_country = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    billing_postal_code = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    Notes = table.Column<string>(type: "text", nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_delivery_notes", x => x.Id);
                    table.ForeignKey(
                        name: "FK_delivery_notes_Vendors_VendorId",
                        column: x => x.VendorId,
                        principalTable: "Vendors",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "goods_receipt_note_lines",
                columns: table => new
                {
                    LineNumber = table.Column<int>(type: "integer", nullable: false),
                    GoodsReceiptNoteId = table.Column<Guid>(type: "uuid", nullable: false),
                    PurchaseOrderLineId = table.Column<Guid>(type: "uuid", nullable: false),
                    DeliveryNoteLineId = table.Column<Guid>(type: "uuid", nullable: true),
                    DeliveryNoteLineDeliveryNoteId = table.Column<Guid>(type: "uuid", nullable: true),
                    DeliveryNoteLineLineNumber = table.Column<int>(type: "integer", nullable: true),
                    ProductId = table.Column<Guid>(type: "uuid", nullable: false),
                    ProductSkuSnapshot = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    ProductDescriptionSnapshot = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    QuantityReceived = table.Column<decimal>(type: "numeric(18,4)", nullable: false, defaultValue: 0m),
                    UnitOfMeasure = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    QualityControlStatus = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    PutAwayLocation = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    BatchNumber = table.Column<string>(type: "text", nullable: true),
                    LotNumber = table.Column<string>(type: "text", nullable: true),
                    ExpiryDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    InspectionCompleted = table.Column<bool>(type: "boolean", nullable: false),
                    QuantityAccepted = table.Column<decimal>(type: "numeric(18,4)", nullable: false, defaultValue: 0m),
                    QuantityRejected = table.Column<decimal>(type: "numeric(18,4)", nullable: false, defaultValue: 0m),
                    RejectionReason = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    Notes = table.Column<string>(type: "text", nullable: true),
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_goods_receipt_note_lines", x => new { x.GoodsReceiptNoteId, x.LineNumber });
                    table.ForeignKey(
                        name: "FK_goods_receipt_note_lines_ProductDefinitions_ProductId",
                        column: x => x.ProductId,
                        principalTable: "ProductDefinitions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_goods_receipt_note_lines_delivery_note_lines_DeliveryNoteLi~",
                        columns: x => new { x.DeliveryNoteLineDeliveryNoteId, x.DeliveryNoteLineLineNumber },
                        principalTable: "delivery_note_lines",
                        principalColumns: new[] { "DeliveryNoteId", "LineNumber" });
                });

            migrationBuilder.CreateTable(
                name: "goods_receipt_notes",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    GoodsReceiptNoteNumber = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    GrnNumber = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    PurchaseOrderId = table.Column<Guid>(type: "uuid", nullable: false),
                    DeliveryNoteId = table.Column<Guid>(type: "uuid", nullable: true),
                    VendorId = table.Column<Guid>(type: "uuid", nullable: false),
                    ReceiptDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    InspectionDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    ReceivingLocation = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    ReceivedByUserId = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: false),
                    ReceivedByName = table.Column<string>(type: "character varying(150)", maxLength: 150, nullable: false),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Notes = table.Column<string>(type: "text", nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_goods_receipt_notes", x => x.Id);
                    table.ForeignKey(
                        name: "FK_goods_receipt_notes_AspNetUsers_ReceivedByUserId",
                        column: x => x.ReceivedByUserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_goods_receipt_notes_Vendors_VendorId",
                        column: x => x.VendorId,
                        principalTable: "Vendors",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_goods_receipt_notes_delivery_notes_DeliveryNoteId",
                        column: x => x.DeliveryNoteId,
                        principalTable: "delivery_notes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "invoice_lines",
                columns: table => new
                {
                    LineNumber = table.Column<int>(type: "integer", nullable: false),
                    InvoiceId = table.Column<Guid>(type: "uuid", nullable: false),
                    PurchaseOrderLineId = table.Column<Guid>(type: "uuid", nullable: true),
                    ProductId = table.Column<Guid>(type: "uuid", nullable: true),
                    Description = table.Column<string>(type: "text", maxLength: 1000, nullable: false),
                    Quantity = table.Column<decimal>(type: "numeric(18,4)", nullable: false),
                    UnitPrice = table.Column<decimal>(type: "numeric(18,4)", nullable: false),
                    LineTotal = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    TaxAmount = table.Column<decimal>(type: "numeric(18,2)", nullable: true, defaultValue: 0m),
                    TaxRate = table.Column<decimal>(type: "numeric(5,4)", nullable: true),
                    UnitOfMeasure = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    Notes = table.Column<string>(type: "text", nullable: true),
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_invoice_lines", x => new { x.InvoiceId, x.LineNumber });
                    table.ForeignKey(
                        name: "FK_invoice_lines_ProductDefinitions_ProductId",
                        column: x => x.ProductId,
                        principalTable: "ProductDefinitions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "invoices",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    InvoiceNumber = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    VendorId = table.Column<Guid>(type: "uuid", nullable: false),
                    CustomerId = table.Column<Guid>(type: "uuid", nullable: true),
                    PurchaseOrderId = table.Column<Guid>(type: "uuid", nullable: true),
                    InvoiceDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    DueDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    CurrencyCode = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    SubTotal = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    Subtotal = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    TaxAmount = table.Column<decimal>(type: "numeric(18,2)", nullable: false, defaultValue: 0m),
                    TotalAmount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    AmountPaid = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    PaymentDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    Notes = table.Column<string>(type: "text", nullable: true),
                    PaymentTerms = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_invoices", x => x.Id);
                    table.ForeignKey(
                        name: "FK_invoices_Vendors_VendorId",
                        column: x => x.VendorId,
                        principalTable: "Vendors",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_invoices_customers_CustomerId",
                        column: x => x.CustomerId,
                        principalSchema: "public",
                        principalTable: "customers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "payment_transactions",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    TransactionReference = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    PaymentDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    Amount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    CurrencyCode = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    PaymentMethod = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    VendorId = table.Column<Guid>(type: "uuid", nullable: false),
                    InvoiceId = table.Column<Guid>(type: "uuid", nullable: true),
                    Notes = table.Column<string>(type: "text", nullable: true),
                    BankReference = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_payment_transactions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_payment_transactions_Vendors_VendorId",
                        column: x => x.VendorId,
                        principalTable: "Vendors",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_payment_transactions_invoices_InvoiceId",
                        column: x => x.InvoiceId,
                        principalTable: "invoices",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "PurchaseOrderLines",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    PurchaseOrderId = table.Column<Guid>(type: "uuid", nullable: false),
                    VendorProductId = table.Column<Guid>(type: "uuid", nullable: false),
                    SkuSnapshot = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    DescriptionSnapshot = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: false),
                    UnitPriceAmount = table.Column<decimal>(type: "numeric(18,4)", precision: 18, scale: 4, nullable: false),
                    UnitPriceCurrencyCode = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    UnitOfMeasureSnapshot = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Quantity = table.Column<decimal>(type: "numeric(18,4)", precision: 18, scale: 4, nullable: false),
                    LineTotalAmount = table.Column<decimal>(type: "numeric(18,4)", precision: 18, scale: 4, nullable: false),
                    LineTotalCurrencyCode = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    Notes = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PurchaseOrderLines", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PurchaseOrderLines_VendorProducts_VendorProductId",
                        column: x => x.VendorProductId,
                        principalTable: "VendorProducts",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "technical_submittals",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    SubmittalNumber = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Revision = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    Title = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    Description = table.Column<string>(type: "text", nullable: true),
                    SubmittalType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    CurrentReviewCycle = table.Column<int>(type: "integer", nullable: false),
                    ReviewDueDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    RequiredDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    ReviewStartDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ReviewCompletionDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CurrentOverallDisposition = table.Column<int>(type: "integer", nullable: false),
                    PurchaseOrderLineId = table.Column<Guid>(type: "uuid", nullable: true),
                    ContractId = table.Column<Guid>(type: "uuid", nullable: true),
                    ProjectId = table.Column<Guid>(type: "uuid", nullable: false),
                    SpecificationSection = table.Column<string>(type: "text", nullable: true),
                    SpecificationId = table.Column<Guid>(type: "uuid", nullable: true),
                    RelatedITPReference = table.Column<string>(type: "text", nullable: true),
                    TestPlanId = table.Column<Guid>(type: "uuid", nullable: true),
                    RelatedNCRReference = table.Column<string>(type: "text", nullable: true),
                    NonConformanceReportId = table.Column<Guid>(type: "uuid", nullable: true),
                    VendorId = table.Column<Guid>(type: "uuid", nullable: true),
                    SubmittedById = table.Column<string>(type: "text", nullable: false),
                    SubmittedByUserId = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: false),
                    CurrentReviewerId = table.Column<Guid>(type: "uuid", nullable: true),
                    CycleCount = table.Column<int>(type: "integer", nullable: false, defaultValue: 1),
                    IsAsBuilt = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    IsFinalDocumentation = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    SubmittedDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    FinalSignOffById = table.Column<string>(type: "text", nullable: true),
                    SignedOffById = table.Column<Guid>(type: "uuid", nullable: true),
                    FinalSignOffDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    SignedOffDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    ResubmissionCount = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    MaxResubmissions = table.Column<int>(type: "integer", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    SubmittedDocuments = table.Column<List<DocumentLink>>(type: "jsonb", nullable: false),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_technical_submittals", x => x.Id);
                    table.ForeignKey(
                        name: "FK_technical_submittals_AspNetUsers_SubmittedByUserId",
                        column: x => x.SubmittedByUserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_technical_submittals_Contracts_ContractId",
                        column: x => x.ContractId,
                        principalTable: "Contracts",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_technical_submittals_PurchaseOrderLines_PurchaseOrderLineId",
                        column: x => x.PurchaseOrderLineId,
                        principalTable: "PurchaseOrderLines",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_technical_submittals_Vendors_VendorId",
                        column: x => x.VendorId,
                        principalTable: "Vendors",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_technical_submittals_projects_ProjectId",
                        column: x => x.ProjectId,
                        principalSchema: "public",
                        principalTable: "projects",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "submittal_reviews",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TechnicalSubmittalId = table.Column<Guid>(type: "uuid", nullable: false),
                    ReviewCycle = table.Column<int>(type: "integer", nullable: false),
                    ReviewerId = table.Column<string>(type: "text", nullable: false),
                    ReviewerGuid = table.Column<Guid>(type: "uuid", nullable: false),
                    ReviewerName = table.Column<string>(type: "text", nullable: false),
                    ReviewDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    Disposition = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Comments = table.Column<string>(type: "text", nullable: true),
                    MarkupDocument = table.Column<DocumentLink>(type: "jsonb", nullable: true),
                    TechnicalSubmittalId1 = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_submittal_reviews", x => x.Id);
                    table.ForeignKey(
                        name: "FK_submittal_reviews_AspNetUsers_ReviewerId",
                        column: x => x.ReviewerId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_submittal_reviews_technical_submittals_TechnicalSubmittalId",
                        column: x => x.TechnicalSubmittalId,
                        principalTable: "technical_submittals",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_submittal_reviews_technical_submittals_TechnicalSubmittalId1",
                        column: x => x.TechnicalSubmittalId1,
                        principalTable: "technical_submittals",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "PurchaseOrders",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    OrderNumber = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    OrderDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    DeliveryDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    PaymentTerms = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    CurrencyCode = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    TotalAmount = table.Column<decimal>(type: "numeric(18,4)", precision: 18, scale: 4, nullable: false),
                    VendorId = table.Column<Guid>(type: "uuid", nullable: false),
                    ContractId = table.Column<Guid>(type: "uuid", nullable: true),
                    RequisitionId = table.Column<Guid>(type: "uuid", nullable: true),
                    Shipment_Street = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Shipment_City = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Shipment_State = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Shipment_Country = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Shipment_PostalCode = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PurchaseOrders", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PurchaseOrders_Contracts_ContractId",
                        column: x => x.ContractId,
                        principalTable: "Contracts",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_PurchaseOrders_Vendors_VendorId",
                        column: x => x.VendorId,
                        principalTable: "Vendors",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "PurchaseRequisitions",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    RequisitionNumber = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    RequestorName = table.Column<string>(type: "character varying(150)", maxLength: 150, nullable: false),
                    RequestorEmail = table.Column<string>(type: "character varying(254)", maxLength: 254, nullable: false),
                    RequestorUserId = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: true),
                    Department = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    RequestDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    DateNeeded = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    Justification = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: false),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    CurrencyCode = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    TotalEstimatedCostAmount = table.Column<decimal>(type: "numeric(18,4)", precision: 18, scale: 4, nullable: false),
                    Shipping_Street = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    Shipping_City = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    Shipping_State = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    Shipping_Country = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    Shipping_PostalCode = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    Notes = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    AssociatedPurchaseOrderId = table.Column<Guid>(type: "uuid", nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PurchaseRequisitions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PurchaseRequisitions_PurchaseOrders_AssociatedPurchaseOrder~",
                        column: x => x.AssociatedPurchaseOrderId,
                        principalTable: "PurchaseOrders",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "PurchaseRequisitionLines",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    PurchaseRequisitionId = table.Column<Guid>(type: "uuid", nullable: false),
                    LineNumber = table.Column<int>(type: "integer", nullable: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    VendorProductId = table.Column<Guid>(type: "uuid", nullable: true),
                    ProductDefinitionId = table.Column<Guid>(type: "uuid", nullable: true),
                    Description = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: false),
                    Quantity = table.Column<decimal>(type: "numeric(18,4)", precision: 18, scale: 4, nullable: false),
                    UnitOfMeasure = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    EstUnitPriceAmount = table.Column<decimal>(type: "numeric(18,4)", precision: 18, scale: 4, nullable: true),
                    EstUnitPriceCurrencyCode = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: true),
                    EstLineCostAmount = table.Column<decimal>(type: "numeric(18,4)", precision: 18, scale: 4, nullable: true),
                    EstLineCostCurrencyCode = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: true),
                    GLAccountCode = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    DateNeeded = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    SuggestedVendorId = table.Column<Guid>(type: "uuid", nullable: true),
                    Notes = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PurchaseRequisitionLines", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PurchaseRequisitionLines_ProductDefinitions_ProductDefiniti~",
                        column: x => x.ProductDefinitionId,
                        principalTable: "ProductDefinitions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_PurchaseRequisitionLines_PurchaseRequisitions_PurchaseRequi~",
                        column: x => x.PurchaseRequisitionId,
                        principalTable: "PurchaseRequisitions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_PurchaseRequisitionLines_VendorProducts_VendorProductId",
                        column: x => x.VendorProductId,
                        principalTable: "VendorProducts",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_PurchaseRequisitionLines_Vendors_SuggestedVendorId",
                        column: x => x.SuggestedVendorId,
                        principalTable: "Vendors",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "request_for_quotes",
                schema: "public",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    RFQNumber = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Title = table.Column<string>(type: "character varying(250)", maxLength: 250, nullable: false),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    SubmissionDeadline = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    RequiredDeliveryDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    Description = table.Column<string>(type: "text", nullable: true),
                    ScopeOfWork = table.Column<string>(type: "text", nullable: true),
                    TermsAndConditions = table.Column<string>(type: "text", nullable: true),
                    VendorReferenceInstructions = table.Column<string>(type: "text", nullable: true),
                    CurrencyCode = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    deliver_to_street = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    deliver_to_city = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    deliver_to_state = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    deliver_to_country = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    deliver_to_postal_code = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    CreatedByUserId = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: false),
                    OriginatingRequisitionId = table.Column<Guid>(type: "uuid", nullable: true),
                    AwardedVendorId = table.Column<Guid>(type: "uuid", nullable: true),
                    RelatedAgreementId = table.Column<Guid>(type: "uuid", nullable: true),
                    ContactPersonEmail = table.Column<string>(type: "character varying(254)", maxLength: 254, nullable: true),
                    CommunicationMethod = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_request_for_quotes", x => x.Id);
                    table.ForeignKey(
                        name: "FK_request_for_quotes_Contracts_RelatedAgreementId",
                        column: x => x.RelatedAgreementId,
                        principalTable: "Contracts",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_request_for_quotes_PurchaseRequisitions_OriginatingRequisit~",
                        column: x => x.OriginatingRequisitionId,
                        principalTable: "PurchaseRequisitions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_request_for_quotes_Vendors_AwardedVendorId",
                        column: x => x.AwardedVendorId,
                        principalTable: "Vendors",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "request_for_quote_lines",
                schema: "public",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    RequestForQuoteId = table.Column<Guid>(type: "uuid", nullable: false),
                    LineNumber = table.Column<int>(type: "integer", nullable: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    ProductDefinitionId = table.Column<Guid>(type: "uuid", nullable: true),
                    VendorProductId = table.Column<Guid>(type: "uuid", nullable: true),
                    Description = table.Column<string>(type: "text", nullable: false),
                    Quantity = table.Column<decimal>(type: "numeric(18,4)", precision: 18, scale: 4, nullable: false),
                    UnitOfMeasure = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    target_unit_price_amount = table.Column<decimal>(type: "numeric(18,4)", precision: 18, scale: 4, nullable: true),
                    target_unit_price_currency_code = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: true),
                    AlternateItemProposal = table.Column<string>(type: "text", nullable: true),
                    est_tco_value_amount = table.Column<decimal>(type: "numeric(18,4)", precision: 18, scale: 4, nullable: true),
                    est_tco_value_currency_code = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: true),
                    TechnicalSpecifications = table.Column<string>(type: "text", nullable: true),
                    SampleRequired = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    MinimumOrderQuantity = table.Column<decimal>(type: "numeric(18,4)", precision: 18, scale: 4, nullable: true),
                    PreferredIncoterm = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    IsSubstituteAllowed = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    Notes = table.Column<string>(type: "text", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_request_for_quote_lines", x => x.Id);
                    table.ForeignKey(
                        name: "FK_request_for_quote_lines_ProductDefinitions_ProductDefinitio~",
                        column: x => x.ProductDefinitionId,
                        principalTable: "ProductDefinitions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_request_for_quote_lines_VendorProducts_VendorProductId",
                        column: x => x.VendorProductId,
                        principalTable: "VendorProducts",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_request_for_quote_lines_request_for_quotes_RequestForQuoteId",
                        column: x => x.RequestForQuoteId,
                        principalSchema: "public",
                        principalTable: "request_for_quotes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "return_authorization_lines",
                columns: table => new
                {
                    LineNumber = table.Column<int>(type: "integer", nullable: false),
                    ReturnAuthorizationId = table.Column<Guid>(type: "uuid", nullable: false),
                    OriginalSalesOrderLineId = table.Column<Guid>(type: "uuid", nullable: true),
                    SalesOrderId = table.Column<Guid>(type: "uuid", nullable: true),
                    SalesOrderLineNumber = table.Column<int>(type: "integer", nullable: true),
                    InvoiceId = table.Column<Guid>(type: "uuid", nullable: true),
                    InvoiceLineNumber = table.Column<int>(type: "integer", nullable: true),
                    ItemCondition = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    ProductDefinitionId = table.Column<Guid>(type: "uuid", nullable: true),
                    VendorProductId = table.Column<Guid>(type: "uuid", nullable: true),
                    ProductId = table.Column<Guid>(type: "uuid", nullable: false),
                    SkuSnapshot = table.Column<string>(type: "text", nullable: false),
                    DescriptionSnapshot = table.Column<string>(type: "text", nullable: false),
                    QuantityAuthorized = table.Column<decimal>(type: "numeric(18,4)", nullable: false),
                    UnitOfMeasure = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    QuantityReceived = table.Column<decimal>(type: "numeric", nullable: false),
                    ReasonForReturn = table.Column<string>(type: "text", nullable: false),
                    RequestedAction = table.Column<int>(type: "integer", nullable: false),
                    OriginalSalesOrderLineSalesOrderId = table.Column<Guid>(type: "uuid", nullable: true),
                    OriginalSalesOrderLineLineNumber = table.Column<int>(type: "integer", nullable: true),
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_return_authorization_lines", x => new { x.ReturnAuthorizationId, x.LineNumber });
                    table.ForeignKey(
                        name: "FK_return_authorization_lines_ProductDefinitions_ProductDefini~",
                        column: x => x.ProductDefinitionId,
                        principalTable: "ProductDefinitions",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_return_authorization_lines_VendorProducts_VendorProductId",
                        column: x => x.VendorProductId,
                        principalTable: "VendorProducts",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_return_authorization_lines_invoice_lines_InvoiceId_InvoiceL~",
                        columns: x => new { x.InvoiceId, x.InvoiceLineNumber },
                        principalTable: "invoice_lines",
                        principalColumns: new[] { "InvoiceId", "LineNumber" },
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_return_authorization_lines_products_ProductId",
                        column: x => x.ProductId,
                        principalSchema: "public",
                        principalTable: "products",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "return_authorizations",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    RmaNumber = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    RequestDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    CustomerId = table.Column<Guid>(type: "uuid", nullable: false),
                    OriginalSalesOrderId = table.Column<Guid>(type: "uuid", nullable: false),
                    SalesOrderId = table.Column<Guid>(type: "uuid", nullable: true),
                    InvoiceId = table.Column<Guid>(type: "uuid", nullable: true),
                    AuthorizationDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    ExpiryDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    ReasonForReturn = table.Column<string>(type: "text", nullable: true),
                    RequestedAction = table.Column<int>(type: "integer", nullable: false),
                    ShippingInstructions = table.Column<string>(type: "text", nullable: true),
                    Notes = table.Column<string>(type: "text", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_return_authorizations", x => x.Id);
                    table.ForeignKey(
                        name: "FK_return_authorizations_customers_CustomerId",
                        column: x => x.CustomerId,
                        principalSchema: "public",
                        principalTable: "customers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_return_authorizations_invoices_InvoiceId",
                        column: x => x.InvoiceId,
                        principalTable: "invoices",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "sales_orders",
                schema: "public",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    OrderNumber = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    OrderDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    CustomerId = table.Column<Guid>(type: "uuid", nullable: true),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    billing_street = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    billing_city = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    billing_state = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    billing_country = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    billing_postal_code = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    shipping_street = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    shipping_city = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    shipping_state = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    shipping_country = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    shipping_postal_code = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    total_amount = table.Column<decimal>(type: "numeric(18,4)", precision: 18, scale: 4, nullable: false),
                    CurrencyCode = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    IsCreditApproved = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    AtpCheckDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    IsAtpConfirmed = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    IsDropShipment = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    SalespersonId = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: true),
                    CommissionRate = table.Column<decimal>(type: "numeric(5,4)", precision: 5, scale: 4, nullable: true),
                    RelatedReturnAuthorizationId = table.Column<Guid>(type: "uuid", nullable: true),
                    SalesTerritoryId = table.Column<Guid>(type: "uuid", nullable: true),
                    EdiTransactionReference = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    SalesTerritoryId1 = table.Column<Guid>(type: "uuid", nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_sales_orders", x => x.Id);
                    table.ForeignKey(
                        name: "FK_sales_orders_customers_CustomerId",
                        column: x => x.CustomerId,
                        principalSchema: "public",
                        principalTable: "customers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_sales_orders_return_authorizations_RelatedReturnAuthorizati~",
                        column: x => x.RelatedReturnAuthorizationId,
                        principalTable: "return_authorizations",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_sales_orders_sales_territories_SalesTerritoryId",
                        column: x => x.SalesTerritoryId,
                        principalTable: "sales_territories",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_sales_orders_sales_territories_SalesTerritoryId1",
                        column: x => x.SalesTerritoryId1,
                        principalTable: "sales_territories",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "sales_order_lines",
                columns: table => new
                {
                    SalesOrderId = table.Column<Guid>(type: "uuid", nullable: false),
                    LineNumber = table.Column<int>(type: "integer", nullable: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    VendorProductId = table.Column<Guid>(type: "uuid", nullable: false),
                    SkuSnapshot = table.Column<string>(type: "text", nullable: false),
                    DescriptionSnapshot = table.Column<string>(type: "text", nullable: false),
                    Description = table.Column<string>(type: "text", nullable: false),
                    RequestedDeliveryDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    ProductId = table.Column<Guid>(type: "uuid", nullable: false),
                    Quantity = table.Column<decimal>(type: "numeric(18,4)", nullable: false),
                    UnitOfMeasure = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    UnitPriceAmount = table.Column<decimal>(type: "numeric(18,4)", nullable: false),
                    UnitPriceCurrency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    AppliedDiscountDescription = table.Column<string>(type: "text", nullable: true),
                    DiscountAmountValue = table.Column<decimal>(type: "numeric(18,2)", nullable: true),
                    DiscountAmountCurrency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: true),
                    LineTotalAmount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    LineTotalCurrency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    QuantityBackordered = table.Column<decimal>(type: "numeric", nullable: false),
                    ReservedSerialNumbersJson = table.Column<string>(type: "text", nullable: true),
                    WarrantyEndDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsKitComponent = table.Column<bool>(type: "boolean", nullable: false),
                    ParentSalesOrderLineId = table.Column<Guid>(type: "uuid", nullable: true),
                    ProjectId = table.Column<Guid>(type: "uuid", nullable: true),
                    CostCode = table.Column<string>(type: "text", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    ParentSalesOrderLineSalesOrderId = table.Column<Guid>(type: "uuid", nullable: true),
                    ParentSalesOrderLineLineNumber = table.Column<int>(type: "integer", nullable: true),
                    ProjectId1 = table.Column<Guid>(type: "uuid", nullable: true),
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_sales_order_lines", x => new { x.SalesOrderId, x.LineNumber });
                    table.ForeignKey(
                        name: "FK_sales_order_lines_VendorProducts_VendorProductId",
                        column: x => x.VendorProductId,
                        principalTable: "VendorProducts",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_sales_order_lines_products_ProductId",
                        column: x => x.ProductId,
                        principalSchema: "public",
                        principalTable: "products",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_sales_order_lines_projects_ProjectId",
                        column: x => x.ProjectId,
                        principalSchema: "public",
                        principalTable: "projects",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_sales_order_lines_projects_ProjectId1",
                        column: x => x.ProjectId1,
                        principalSchema: "public",
                        principalTable: "projects",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_sales_order_lines_sales_order_lines_ParentSalesOrderLineSal~",
                        columns: x => new { x.ParentSalesOrderLineSalesOrderId, x.ParentSalesOrderLineLineNumber },
                        principalTable: "sales_order_lines",
                        principalColumns: new[] { "SalesOrderId", "LineNumber" });
                    table.ForeignKey(
                        name: "FK_sales_order_lines_sales_orders_SalesOrderId",
                        column: x => x.SalesOrderId,
                        principalSchema: "public",
                        principalTable: "sales_orders",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_AspNetRoleClaims_RoleId",
                table: "AspNetRoleClaims",
                column: "RoleId");

            migrationBuilder.CreateIndex(
                name: "RoleNameIndex",
                table: "AspNetRoles",
                column: "NormalizedName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_AspNetUserClaims_UserId",
                table: "AspNetUserClaims",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_AspNetUserLogins_UserId",
                table: "AspNetUserLogins",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_AspNetUserRoles_RoleId",
                table: "AspNetUserRoles",
                column: "RoleId");

            migrationBuilder.CreateIndex(
                name: "EmailIndex",
                table: "AspNetUsers",
                column: "NormalizedEmail");

            migrationBuilder.CreateIndex(
                name: "UserNameIndex",
                table: "AspNetUsers",
                column: "NormalizedUserName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_budget_allocations_BudgetId",
                schema: "public",
                table: "budget_allocations",
                column: "BudgetId");

            migrationBuilder.CreateIndex(
                name: "IX_budget_allocations_BudgetId_FiscalPeriodIdentifier",
                schema: "public",
                table: "budget_allocations",
                columns: new[] { "BudgetId", "FiscalPeriodIdentifier" });

            migrationBuilder.CreateIndex(
                name: "IX_budget_allocations_DepartmentId",
                schema: "public",
                table: "budget_allocations",
                column: "DepartmentId");

            migrationBuilder.CreateIndex(
                name: "IX_budget_allocations_FiscalPeriodIdentifier",
                schema: "public",
                table: "budget_allocations",
                column: "FiscalPeriodIdentifier");

            migrationBuilder.CreateIndex(
                name: "IX_budget_allocations_Status",
                schema: "public",
                table: "budget_allocations",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_budget_allocations_TenantId",
                schema: "public",
                table: "budget_allocations",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_budgets_ApprovedById",
                schema: "public",
                table: "budgets",
                column: "ApprovedById");

            migrationBuilder.CreateIndex(
                name: "IX_budgets_CreatedById",
                schema: "public",
                table: "budgets",
                column: "CreatedById");

            migrationBuilder.CreateIndex(
                name: "IX_budgets_EndDate",
                schema: "public",
                table: "budgets",
                column: "EndDate");

            migrationBuilder.CreateIndex(
                name: "IX_budgets_FiscalYear",
                schema: "public",
                table: "budgets",
                column: "FiscalYear");

            migrationBuilder.CreateIndex(
                name: "IX_budgets_IsDeleted",
                schema: "public",
                table: "budgets",
                column: "IsDeleted");

            migrationBuilder.CreateIndex(
                name: "IX_budgets_Name",
                schema: "public",
                table: "budgets",
                column: "Name");

            migrationBuilder.CreateIndex(
                name: "IX_budgets_StartDate",
                schema: "public",
                table: "budgets",
                column: "StartDate");

            migrationBuilder.CreateIndex(
                name: "IX_budgets_Status",
                schema: "public",
                table: "budgets",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_budgets_TenantId",
                schema: "public",
                table: "budgets",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_budgets_WorkflowInstanceId",
                schema: "public",
                table: "budgets",
                column: "WorkflowInstanceId");

            migrationBuilder.CreateIndex(
                name: "IX_Categories_Code",
                table: "Categories",
                column: "Code",
                unique: true,
                filter: "\"Code\" IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_Categories_ParentCategoryId",
                table: "Categories",
                column: "ParentCategoryId");

            migrationBuilder.CreateIndex(
                name: "IX_Categories_UnspscCode",
                table: "Categories",
                column: "UnspscCode");

            migrationBuilder.CreateIndex(
                name: "IX_Contracts_ContractNumber",
                table: "Contracts",
                column: "ContractNumber",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Contracts_EndDate",
                table: "Contracts",
                column: "EndDate");

            migrationBuilder.CreateIndex(
                name: "IX_Contracts_IsDeleted",
                table: "Contracts",
                column: "IsDeleted");

            migrationBuilder.CreateIndex(
                name: "IX_Contracts_StartDate",
                table: "Contracts",
                column: "StartDate");

            migrationBuilder.CreateIndex(
                name: "IX_Contracts_Status",
                table: "Contracts",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_Contracts_TenantId",
                table: "Contracts",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_Contracts_VendorId",
                table: "Contracts",
                column: "VendorId");

            migrationBuilder.CreateIndex(
                name: "IX_customers_AssignedSalesRepId",
                schema: "public",
                table: "customers",
                column: "AssignedSalesRepId");

            migrationBuilder.CreateIndex(
                name: "IX_customers_customer_type",
                schema: "public",
                table: "customers",
                column: "customer_type");

            migrationBuilder.CreateIndex(
                name: "IX_customers_is_active",
                schema: "public",
                table: "customers",
                column: "is_active");

            migrationBuilder.CreateIndex(
                name: "IX_customers_IsDeleted",
                schema: "public",
                table: "customers",
                column: "IsDeleted");

            migrationBuilder.CreateIndex(
                name: "IX_customers_name",
                schema: "public",
                table: "customers",
                column: "name");

            migrationBuilder.CreateIndex(
                name: "IX_customers_tax_identifier",
                schema: "public",
                table: "customers",
                column: "tax_identifier");

            migrationBuilder.CreateIndex(
                name: "IX_customers_TenantId",
                schema: "public",
                table: "customers",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_customers_TenantId_customer_code",
                schema: "public",
                table: "customers",
                columns: new[] { "TenantId", "customer_code" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_delivery_note_lines_ProductId",
                table: "delivery_note_lines",
                column: "ProductId");

            migrationBuilder.CreateIndex(
                name: "IX_delivery_note_lines_PurchaseOrderLineId",
                table: "delivery_note_lines",
                column: "PurchaseOrderLineId");

            migrationBuilder.CreateIndex(
                name: "IX_delivery_note_lines_SalesOrderId_SalesOrderLineNumber",
                table: "delivery_note_lines",
                columns: new[] { "SalesOrderId", "SalesOrderLineNumber" });

            migrationBuilder.CreateIndex(
                name: "IX_delivery_notes_DeliveryDate",
                table: "delivery_notes",
                column: "DeliveryDate");

            migrationBuilder.CreateIndex(
                name: "IX_delivery_notes_DeliveryNoteNumber",
                table: "delivery_notes",
                column: "DeliveryNoteNumber",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_delivery_notes_PurchaseOrderId",
                table: "delivery_notes",
                column: "PurchaseOrderId");

            migrationBuilder.CreateIndex(
                name: "IX_delivery_notes_SalesOrderId",
                table: "delivery_notes",
                column: "SalesOrderId");

            migrationBuilder.CreateIndex(
                name: "IX_delivery_notes_Status",
                table: "delivery_notes",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_delivery_notes_TenantId",
                table: "delivery_notes",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_delivery_notes_VendorId",
                table: "delivery_notes",
                column: "VendorId");

            migrationBuilder.CreateIndex(
                name: "IX_departments_Name",
                schema: "public",
                table: "departments",
                column: "Name");

            migrationBuilder.CreateIndex(
                name: "IX_departments_TenantId",
                schema: "public",
                table: "departments",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_departments_TenantId_Code",
                schema: "public",
                table: "departments",
                columns: new[] { "TenantId", "Code" },
                unique: true,
                filter: "\"Code\" IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_goods_receipt_note_lines_DeliveryNoteLineDeliveryNoteId_Del~",
                table: "goods_receipt_note_lines",
                columns: new[] { "DeliveryNoteLineDeliveryNoteId", "DeliveryNoteLineLineNumber" });

            migrationBuilder.CreateIndex(
                name: "IX_goods_receipt_note_lines_ExpiryDate",
                table: "goods_receipt_note_lines",
                column: "ExpiryDate");

            migrationBuilder.CreateIndex(
                name: "IX_goods_receipt_note_lines_ProductId",
                table: "goods_receipt_note_lines",
                column: "ProductId");

            migrationBuilder.CreateIndex(
                name: "IX_goods_receipt_note_lines_PurchaseOrderLineId",
                table: "goods_receipt_note_lines",
                column: "PurchaseOrderLineId");

            migrationBuilder.CreateIndex(
                name: "IX_goods_receipt_note_lines_QualityControlStatus",
                table: "goods_receipt_note_lines",
                column: "QualityControlStatus");

            migrationBuilder.CreateIndex(
                name: "IX_goods_receipt_notes_DeliveryNoteId",
                table: "goods_receipt_notes",
                column: "DeliveryNoteId");

            migrationBuilder.CreateIndex(
                name: "IX_goods_receipt_notes_GoodsReceiptNoteNumber",
                table: "goods_receipt_notes",
                column: "GoodsReceiptNoteNumber",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_goods_receipt_notes_PurchaseOrderId",
                table: "goods_receipt_notes",
                column: "PurchaseOrderId");

            migrationBuilder.CreateIndex(
                name: "IX_goods_receipt_notes_ReceiptDate",
                table: "goods_receipt_notes",
                column: "ReceiptDate");

            migrationBuilder.CreateIndex(
                name: "IX_goods_receipt_notes_ReceivedByUserId",
                table: "goods_receipt_notes",
                column: "ReceivedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_goods_receipt_notes_Status",
                table: "goods_receipt_notes",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_goods_receipt_notes_TenantId",
                table: "goods_receipt_notes",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_goods_receipt_notes_VendorId",
                table: "goods_receipt_notes",
                column: "VendorId");

            migrationBuilder.CreateIndex(
                name: "IX_invoice_lines_ProductId",
                table: "invoice_lines",
                column: "ProductId");

            migrationBuilder.CreateIndex(
                name: "IX_invoice_lines_PurchaseOrderLineId",
                table: "invoice_lines",
                column: "PurchaseOrderLineId");

            migrationBuilder.CreateIndex(
                name: "IX_invoices_CustomerId",
                table: "invoices",
                column: "CustomerId");

            migrationBuilder.CreateIndex(
                name: "IX_invoices_DueDate",
                table: "invoices",
                column: "DueDate");

            migrationBuilder.CreateIndex(
                name: "IX_invoices_InvoiceDate",
                table: "invoices",
                column: "InvoiceDate");

            migrationBuilder.CreateIndex(
                name: "IX_invoices_PurchaseOrderId",
                table: "invoices",
                column: "PurchaseOrderId");

            migrationBuilder.CreateIndex(
                name: "IX_invoices_Status",
                table: "invoices",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_invoices_TenantId",
                table: "invoices",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_invoices_VendorId",
                table: "invoices",
                column: "VendorId");

            migrationBuilder.CreateIndex(
                name: "IX_invoices_VendorId_InvoiceNumber",
                table: "invoices",
                columns: new[] { "VendorId", "InvoiceNumber" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_payment_transactions_InvoiceId",
                table: "payment_transactions",
                column: "InvoiceId");

            migrationBuilder.CreateIndex(
                name: "IX_payment_transactions_PaymentDate",
                table: "payment_transactions",
                column: "PaymentDate");

            migrationBuilder.CreateIndex(
                name: "IX_payment_transactions_Status",
                table: "payment_transactions",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_payment_transactions_TenantId",
                table: "payment_transactions",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_payment_transactions_TransactionReference",
                table: "payment_transactions",
                column: "TransactionReference",
                unique: true,
                filter: "transaction_reference IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_payment_transactions_VendorId",
                table: "payment_transactions",
                column: "VendorId");

            migrationBuilder.CreateIndex(
                name: "IX_procurement_workflow_steps_ApproverRoleId",
                table: "procurement_workflow_steps",
                column: "ApproverRoleId");

            migrationBuilder.CreateIndex(
                name: "IX_procurement_workflow_steps_ApproverUserId",
                table: "procurement_workflow_steps",
                column: "ApproverUserId");

            migrationBuilder.CreateIndex(
                name: "IX_procurement_workflow_steps_ProcurementWorkflowId",
                table: "procurement_workflow_steps",
                column: "ProcurementWorkflowId");

            migrationBuilder.CreateIndex(
                name: "IX_procurement_workflow_steps_ProcurementWorkflowId_StepOrder",
                table: "procurement_workflow_steps",
                columns: new[] { "ProcurementWorkflowId", "StepOrder" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_procurement_workflow_steps_WorkflowId",
                table: "procurement_workflow_steps",
                column: "WorkflowId");

            migrationBuilder.CreateIndex(
                name: "IX_procurement_workflows_IsActive",
                table: "procurement_workflows",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_procurement_workflows_Name",
                table: "procurement_workflows",
                column: "Name");

            migrationBuilder.CreateIndex(
                name: "IX_procurement_workflows_TenantId",
                table: "procurement_workflows",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_procurement_workflows_TenantId_WorkflowType_Name",
                table: "procurement_workflows",
                columns: new[] { "TenantId", "WorkflowType", "Name" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_procurement_workflows_WorkflowType",
                table: "procurement_workflows",
                column: "WorkflowType");

            migrationBuilder.CreateIndex(
                name: "IX_ProductDefinitions_CategoryId",
                table: "ProductDefinitions",
                column: "CategoryId");

            migrationBuilder.CreateIndex(
                name: "IX_ProductDefinitions_Ean",
                table: "ProductDefinitions",
                column: "Ean",
                unique: true,
                filter: "\"Ean\" IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_ProductDefinitions_Gtin",
                table: "ProductDefinitions",
                column: "Gtin",
                unique: true,
                filter: "\"Gtin\" IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_ProductDefinitions_IsDeleted",
                table: "ProductDefinitions",
                column: "IsDeleted");

            migrationBuilder.CreateIndex(
                name: "IX_ProductDefinitions_Sku",
                table: "ProductDefinitions",
                column: "Sku",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ProductDefinitions_Upc",
                table: "ProductDefinitions",
                column: "Upc",
                unique: true,
                filter: "\"Upc\" IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_products_CategoryId",
                schema: "public",
                table: "products",
                column: "CategoryId");

            migrationBuilder.CreateIndex(
                name: "IX_products_IsActive",
                schema: "public",
                table: "products",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_products_IsDeleted",
                schema: "public",
                table: "products",
                column: "IsDeleted");

            migrationBuilder.CreateIndex(
                name: "IX_products_Name",
                schema: "public",
                table: "products",
                column: "Name");

            migrationBuilder.CreateIndex(
                name: "IX_products_TenantId",
                schema: "public",
                table: "products",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_products_TenantId_ProductCode",
                schema: "public",
                table: "products",
                columns: new[] { "TenantId", "ProductCode" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_projects_EndDate",
                schema: "public",
                table: "projects",
                column: "EndDate");

            migrationBuilder.CreateIndex(
                name: "IX_projects_IsDeleted",
                schema: "public",
                table: "projects",
                column: "IsDeleted");

            migrationBuilder.CreateIndex(
                name: "IX_projects_Name",
                schema: "public",
                table: "projects",
                column: "Name");

            migrationBuilder.CreateIndex(
                name: "IX_projects_StartDate",
                schema: "public",
                table: "projects",
                column: "StartDate");

            migrationBuilder.CreateIndex(
                name: "IX_projects_Status",
                schema: "public",
                table: "projects",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_projects_TenantId",
                schema: "public",
                table: "projects",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_projects_TenantId_ProjectCode",
                schema: "public",
                table: "projects",
                columns: new[] { "TenantId", "ProjectCode" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_PurchaseOrderLines_PurchaseOrderId",
                table: "PurchaseOrderLines",
                column: "PurchaseOrderId");

            migrationBuilder.CreateIndex(
                name: "IX_PurchaseOrderLines_VendorProductId",
                table: "PurchaseOrderLines",
                column: "VendorProductId");

            migrationBuilder.CreateIndex(
                name: "IX_PurchaseOrder_DeliveryDate",
                table: "PurchaseOrders",
                column: "DeliveryDate");

            migrationBuilder.CreateIndex(
                name: "IX_PurchaseOrders_ContractId",
                table: "PurchaseOrders",
                column: "ContractId");

            migrationBuilder.CreateIndex(
                name: "IX_PurchaseOrders_OrderDate",
                table: "PurchaseOrders",
                column: "OrderDate");

            migrationBuilder.CreateIndex(
                name: "IX_PurchaseOrders_OrderNumber",
                table: "PurchaseOrders",
                column: "OrderNumber",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_PurchaseOrders_RequisitionId",
                table: "PurchaseOrders",
                column: "RequisitionId");

            migrationBuilder.CreateIndex(
                name: "IX_PurchaseOrders_Status",
                table: "PurchaseOrders",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_PurchaseOrders_VendorId",
                table: "PurchaseOrders",
                column: "VendorId");

            migrationBuilder.CreateIndex(
                name: "IX_PurchaseRequisitionLines_GLAccountCode",
                table: "PurchaseRequisitionLines",
                column: "GLAccountCode");

            migrationBuilder.CreateIndex(
                name: "IX_PurchaseRequisitionLines_ProductDefinitionId",
                table: "PurchaseRequisitionLines",
                column: "ProductDefinitionId");

            migrationBuilder.CreateIndex(
                name: "IX_PurchaseRequisitionLines_PurchaseRequisitionId_LineNumber",
                table: "PurchaseRequisitionLines",
                columns: new[] { "PurchaseRequisitionId", "LineNumber" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_PurchaseRequisitionLines_SuggestedVendorId",
                table: "PurchaseRequisitionLines",
                column: "SuggestedVendorId");

            migrationBuilder.CreateIndex(
                name: "IX_PurchaseRequisitionLines_TenantId",
                table: "PurchaseRequisitionLines",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_PurchaseRequisitionLines_VendorProductId",
                table: "PurchaseRequisitionLines",
                column: "VendorProductId");

            migrationBuilder.CreateIndex(
                name: "IX_PurchaseRequisitions_AssociatedPurchaseOrderId",
                table: "PurchaseRequisitions",
                column: "AssociatedPurchaseOrderId");

            migrationBuilder.CreateIndex(
                name: "IX_PurchaseRequisitions_RequestDate",
                table: "PurchaseRequisitions",
                column: "RequestDate");

            migrationBuilder.CreateIndex(
                name: "IX_PurchaseRequisitions_RequestorUserId",
                table: "PurchaseRequisitions",
                column: "RequestorUserId");

            migrationBuilder.CreateIndex(
                name: "IX_PurchaseRequisitions_Status",
                table: "PurchaseRequisitions",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_PurchaseRequisitions_TenantId",
                table: "PurchaseRequisitions",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_PurchaseRequisitions_TenantId_RequisitionNumber",
                table: "PurchaseRequisitions",
                columns: new[] { "TenantId", "RequisitionNumber" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_request_for_quote_lines_IsDeleted",
                schema: "public",
                table: "request_for_quote_lines",
                column: "IsDeleted");

            migrationBuilder.CreateIndex(
                name: "IX_request_for_quote_lines_ProductDefinitionId",
                schema: "public",
                table: "request_for_quote_lines",
                column: "ProductDefinitionId");

            migrationBuilder.CreateIndex(
                name: "IX_request_for_quote_lines_RequestForQuoteId_LineNumber",
                schema: "public",
                table: "request_for_quote_lines",
                columns: new[] { "RequestForQuoteId", "LineNumber" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_request_for_quote_lines_TenantId",
                schema: "public",
                table: "request_for_quote_lines",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_request_for_quote_lines_VendorProductId",
                schema: "public",
                table: "request_for_quote_lines",
                column: "VendorProductId");

            migrationBuilder.CreateIndex(
                name: "IX_request_for_quotes_AwardedVendorId",
                schema: "public",
                table: "request_for_quotes",
                column: "AwardedVendorId");

            migrationBuilder.CreateIndex(
                name: "IX_request_for_quotes_CreatedByUserId",
                schema: "public",
                table: "request_for_quotes",
                column: "CreatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_request_for_quotes_IsDeleted",
                schema: "public",
                table: "request_for_quotes",
                column: "IsDeleted");

            migrationBuilder.CreateIndex(
                name: "IX_request_for_quotes_OriginatingRequisitionId",
                schema: "public",
                table: "request_for_quotes",
                column: "OriginatingRequisitionId");

            migrationBuilder.CreateIndex(
                name: "IX_request_for_quotes_RelatedAgreementId",
                schema: "public",
                table: "request_for_quotes",
                column: "RelatedAgreementId");

            migrationBuilder.CreateIndex(
                name: "IX_request_for_quotes_Status",
                schema: "public",
                table: "request_for_quotes",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_request_for_quotes_SubmissionDeadline",
                schema: "public",
                table: "request_for_quotes",
                column: "SubmissionDeadline");

            migrationBuilder.CreateIndex(
                name: "IX_request_for_quotes_TenantId",
                schema: "public",
                table: "request_for_quotes",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_request_for_quotes_TenantId_RFQNumber",
                schema: "public",
                table: "request_for_quotes",
                columns: new[] { "TenantId", "RFQNumber" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_requests_for_information_ProjectId",
                table: "requests_for_information",
                column: "ProjectId");

            migrationBuilder.CreateIndex(
                name: "IX_requests_for_information_ResponseDeadline",
                table: "requests_for_information",
                column: "ResponseDeadline");

            migrationBuilder.CreateIndex(
                name: "IX_requests_for_information_RfiNumber",
                table: "requests_for_information",
                column: "RfiNumber",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_requests_for_information_Status",
                table: "requests_for_information",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_requests_for_information_TenantId",
                table: "requests_for_information",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_requests_for_proposal_AwardedContractId",
                table: "requests_for_proposal",
                column: "AwardedContractId");

            migrationBuilder.CreateIndex(
                name: "IX_requests_for_proposal_AwardedVendorId",
                table: "requests_for_proposal",
                column: "AwardedVendorId");

            migrationBuilder.CreateIndex(
                name: "IX_requests_for_proposal_ProjectId",
                table: "requests_for_proposal",
                column: "ProjectId");

            migrationBuilder.CreateIndex(
                name: "IX_requests_for_proposal_RfpNumber",
                table: "requests_for_proposal",
                column: "RfpNumber",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_requests_for_proposal_Status",
                table: "requests_for_proposal",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_requests_for_proposal_SubmissionDeadline",
                table: "requests_for_proposal",
                column: "SubmissionDeadline");

            migrationBuilder.CreateIndex(
                name: "IX_requests_for_proposal_TenantId",
                table: "requests_for_proposal",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_return_authorization_lines_InvoiceId_InvoiceLineNumber",
                table: "return_authorization_lines",
                columns: new[] { "InvoiceId", "InvoiceLineNumber" });

            migrationBuilder.CreateIndex(
                name: "IX_return_authorization_lines_OriginalSalesOrderLineSalesOrder~",
                table: "return_authorization_lines",
                columns: new[] { "OriginalSalesOrderLineSalesOrderId", "OriginalSalesOrderLineLineNumber" });

            migrationBuilder.CreateIndex(
                name: "IX_return_authorization_lines_ProductDefinitionId",
                table: "return_authorization_lines",
                column: "ProductDefinitionId");

            migrationBuilder.CreateIndex(
                name: "IX_return_authorization_lines_ProductId",
                table: "return_authorization_lines",
                column: "ProductId");

            migrationBuilder.CreateIndex(
                name: "IX_return_authorization_lines_SalesOrderId_SalesOrderLineNumber",
                table: "return_authorization_lines",
                columns: new[] { "SalesOrderId", "SalesOrderLineNumber" });

            migrationBuilder.CreateIndex(
                name: "IX_return_authorization_lines_VendorProductId",
                table: "return_authorization_lines",
                column: "VendorProductId");

            migrationBuilder.CreateIndex(
                name: "IX_return_authorizations_CustomerId",
                table: "return_authorizations",
                column: "CustomerId");

            migrationBuilder.CreateIndex(
                name: "IX_return_authorizations_InvoiceId",
                table: "return_authorizations",
                column: "InvoiceId");

            migrationBuilder.CreateIndex(
                name: "IX_return_authorizations_RequestDate",
                table: "return_authorizations",
                column: "RequestDate");

            migrationBuilder.CreateIndex(
                name: "IX_return_authorizations_RmaNumber",
                table: "return_authorizations",
                column: "RmaNumber",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_return_authorizations_SalesOrderId",
                table: "return_authorizations",
                column: "SalesOrderId");

            migrationBuilder.CreateIndex(
                name: "IX_return_authorizations_Status",
                table: "return_authorizations",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_return_authorizations_TenantId",
                table: "return_authorizations",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_sales_order_lines_ParentSalesOrderLineSalesOrderId_ParentSa~",
                table: "sales_order_lines",
                columns: new[] { "ParentSalesOrderLineSalesOrderId", "ParentSalesOrderLineLineNumber" });

            migrationBuilder.CreateIndex(
                name: "IX_sales_order_lines_ProductId",
                table: "sales_order_lines",
                column: "ProductId");

            migrationBuilder.CreateIndex(
                name: "IX_sales_order_lines_ProjectId",
                table: "sales_order_lines",
                column: "ProjectId");

            migrationBuilder.CreateIndex(
                name: "IX_sales_order_lines_ProjectId1",
                table: "sales_order_lines",
                column: "ProjectId1");

            migrationBuilder.CreateIndex(
                name: "IX_sales_order_lines_VendorProductId",
                table: "sales_order_lines",
                column: "VendorProductId");

            migrationBuilder.CreateIndex(
                name: "IX_sales_orders_CustomerId",
                schema: "public",
                table: "sales_orders",
                column: "CustomerId");

            migrationBuilder.CreateIndex(
                name: "IX_sales_orders_EdiTransactionReference",
                schema: "public",
                table: "sales_orders",
                column: "EdiTransactionReference");

            migrationBuilder.CreateIndex(
                name: "IX_sales_orders_IsDeleted",
                schema: "public",
                table: "sales_orders",
                column: "IsDeleted");

            migrationBuilder.CreateIndex(
                name: "IX_sales_orders_OrderDate",
                schema: "public",
                table: "sales_orders",
                column: "OrderDate");

            migrationBuilder.CreateIndex(
                name: "IX_sales_orders_RelatedReturnAuthorizationId",
                schema: "public",
                table: "sales_orders",
                column: "RelatedReturnAuthorizationId");

            migrationBuilder.CreateIndex(
                name: "IX_sales_orders_SalespersonId",
                schema: "public",
                table: "sales_orders",
                column: "SalespersonId");

            migrationBuilder.CreateIndex(
                name: "IX_sales_orders_SalesTerritoryId",
                schema: "public",
                table: "sales_orders",
                column: "SalesTerritoryId");

            migrationBuilder.CreateIndex(
                name: "IX_sales_orders_SalesTerritoryId1",
                schema: "public",
                table: "sales_orders",
                column: "SalesTerritoryId1");

            migrationBuilder.CreateIndex(
                name: "IX_sales_orders_Status",
                schema: "public",
                table: "sales_orders",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_sales_orders_TenantId",
                schema: "public",
                table: "sales_orders",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_sales_orders_TenantId_OrderNumber",
                schema: "public",
                table: "sales_orders",
                columns: new[] { "TenantId", "OrderNumber" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_sales_territories_Name",
                table: "sales_territories",
                column: "Name");

            migrationBuilder.CreateIndex(
                name: "IX_sales_territories_ParentTerritoryId",
                table: "sales_territories",
                column: "ParentTerritoryId");

            migrationBuilder.CreateIndex(
                name: "IX_sales_territories_TenantId",
                table: "sales_territories",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_sales_territories_TerritoryCode",
                table: "sales_territories",
                column: "TerritoryCode",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_sales_territory_representatives_RepresentativeId",
                table: "sales_territory_representatives",
                column: "RepresentativeId");

            migrationBuilder.CreateIndex(
                name: "IX_submittal_reviews_Disposition",
                table: "submittal_reviews",
                column: "Disposition");

            migrationBuilder.CreateIndex(
                name: "IX_submittal_reviews_ReviewDate",
                table: "submittal_reviews",
                column: "ReviewDate");

            migrationBuilder.CreateIndex(
                name: "IX_submittal_reviews_ReviewerId",
                table: "submittal_reviews",
                column: "ReviewerId");

            migrationBuilder.CreateIndex(
                name: "IX_submittal_reviews_TechnicalSubmittalId",
                table: "submittal_reviews",
                column: "TechnicalSubmittalId");

            migrationBuilder.CreateIndex(
                name: "IX_submittal_reviews_TechnicalSubmittalId1",
                table: "submittal_reviews",
                column: "TechnicalSubmittalId1");

            migrationBuilder.CreateIndex(
                name: "IX_Suppliers_IsContractManufacturer",
                table: "Suppliers",
                column: "IsContractManufacturer");

            migrationBuilder.CreateIndex(
                name: "IX_Suppliers_RiskRating",
                table: "Suppliers",
                column: "RiskRating");

            migrationBuilder.CreateIndex(
                name: "IX_Suppliers_VendorId",
                table: "Suppliers",
                column: "VendorId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_technical_submittals_ContractId",
                table: "technical_submittals",
                column: "ContractId");

            migrationBuilder.CreateIndex(
                name: "IX_technical_submittals_ProjectId",
                table: "technical_submittals",
                column: "ProjectId");

            migrationBuilder.CreateIndex(
                name: "IX_technical_submittals_ProjectId_SubmittalNumber_Revision",
                table: "technical_submittals",
                columns: new[] { "ProjectId", "SubmittalNumber", "Revision" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_technical_submittals_PurchaseOrderLineId",
                table: "technical_submittals",
                column: "PurchaseOrderLineId");

            migrationBuilder.CreateIndex(
                name: "IX_technical_submittals_RequiredDate",
                table: "technical_submittals",
                column: "RequiredDate");

            migrationBuilder.CreateIndex(
                name: "IX_technical_submittals_SpecificationId",
                table: "technical_submittals",
                column: "SpecificationId");

            migrationBuilder.CreateIndex(
                name: "IX_technical_submittals_Status",
                table: "technical_submittals",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_technical_submittals_SubmittalNumber",
                table: "technical_submittals",
                column: "SubmittalNumber",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_technical_submittals_SubmittedByUserId",
                table: "technical_submittals",
                column: "SubmittedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_technical_submittals_SubmittedDate",
                table: "technical_submittals",
                column: "SubmittedDate");

            migrationBuilder.CreateIndex(
                name: "IX_technical_submittals_TenantId",
                table: "technical_submittals",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_technical_submittals_VendorId",
                table: "technical_submittals",
                column: "VendorId");

            migrationBuilder.CreateIndex(
                name: "IX_tenant_products_TenantId",
                table: "tenant_products",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_Tenants_Identifier",
                table: "Tenants",
                column: "Identifier",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_vendor_proposals_RequestForProposalId",
                table: "vendor_proposals",
                column: "RequestForProposalId");

            migrationBuilder.CreateIndex(
                name: "IX_vendor_proposals_RequestForProposalId_VendorId",
                table: "vendor_proposals",
                columns: new[] { "RequestForProposalId", "VendorId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_vendor_proposals_Status",
                table: "vendor_proposals",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_vendor_proposals_SubmissionDate",
                table: "vendor_proposals",
                column: "SubmissionDate");

            migrationBuilder.CreateIndex(
                name: "IX_vendor_proposals_TenantId",
                table: "vendor_proposals",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_vendor_proposals_VendorId",
                table: "vendor_proposals",
                column: "VendorId");

            migrationBuilder.CreateIndex(
                name: "IX_VendorProducts_ProductDefinitionId",
                table: "VendorProducts",
                column: "ProductDefinitionId");

            migrationBuilder.CreateIndex(
                name: "IX_VendorProducts_VendorId_ProductDefinitionId_UnitOfMeasure_P~",
                table: "VendorProducts",
                columns: new[] { "VendorId", "ProductDefinitionId", "UnitOfMeasure", "PackSize" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_VendorProducts_VendorId_VendorSku",
                table: "VendorProducts",
                columns: new[] { "VendorId", "VendorSku" },
                filter: "\"VendorSku\" IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_Vendors_CommercialRegistrationNumber",
                table: "Vendors",
                column: "CommercialRegistrationNumber");

            migrationBuilder.CreateIndex(
                name: "IX_Vendors_Name",
                table: "Vendors",
                column: "Name");

            migrationBuilder.CreateIndex(
                name: "IX_Vendors_Status",
                table: "Vendors",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_Vendors_TaxId",
                table: "Vendors",
                column: "TaxId");

            migrationBuilder.CreateIndex(
                name: "IX_Vendors_VatNumber",
                table: "Vendors",
                column: "VatNumber");

            migrationBuilder.CreateIndex(
                name: "IX_Vendors_VendorCode",
                table: "Vendors",
                column: "VendorCode",
                unique: true,
                filter: "\"VendorCode\" IS NOT NULL");

            migrationBuilder.AddForeignKey(
                name: "FK_delivery_note_lines_PurchaseOrderLines_PurchaseOrderLineId",
                table: "delivery_note_lines",
                column: "PurchaseOrderLineId",
                principalTable: "PurchaseOrderLines",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_delivery_note_lines_delivery_notes_DeliveryNoteId",
                table: "delivery_note_lines",
                column: "DeliveryNoteId",
                principalTable: "delivery_notes",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_delivery_note_lines_sales_order_lines_SalesOrderId_SalesOrd~",
                table: "delivery_note_lines",
                columns: new[] { "SalesOrderId", "SalesOrderLineNumber" },
                principalTable: "sales_order_lines",
                principalColumns: new[] { "SalesOrderId", "LineNumber" },
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_delivery_notes_PurchaseOrders_PurchaseOrderId",
                table: "delivery_notes",
                column: "PurchaseOrderId",
                principalTable: "PurchaseOrders",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_delivery_notes_sales_orders_SalesOrderId",
                table: "delivery_notes",
                column: "SalesOrderId",
                principalSchema: "public",
                principalTable: "sales_orders",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_goods_receipt_note_lines_PurchaseOrderLines_PurchaseOrderLi~",
                table: "goods_receipt_note_lines",
                column: "PurchaseOrderLineId",
                principalTable: "PurchaseOrderLines",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_goods_receipt_note_lines_goods_receipt_notes_GoodsReceiptNo~",
                table: "goods_receipt_note_lines",
                column: "GoodsReceiptNoteId",
                principalTable: "goods_receipt_notes",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_goods_receipt_notes_PurchaseOrders_PurchaseOrderId",
                table: "goods_receipt_notes",
                column: "PurchaseOrderId",
                principalTable: "PurchaseOrders",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_invoice_lines_PurchaseOrderLines_PurchaseOrderLineId",
                table: "invoice_lines",
                column: "PurchaseOrderLineId",
                principalTable: "PurchaseOrderLines",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_invoice_lines_invoices_InvoiceId",
                table: "invoice_lines",
                column: "InvoiceId",
                principalTable: "invoices",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_invoices_PurchaseOrders_PurchaseOrderId",
                table: "invoices",
                column: "PurchaseOrderId",
                principalTable: "PurchaseOrders",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_PurchaseOrderLines_PurchaseOrders_PurchaseOrderId",
                table: "PurchaseOrderLines",
                column: "PurchaseOrderId",
                principalTable: "PurchaseOrders",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_PurchaseOrders_PurchaseRequisitions_RequisitionId",
                table: "PurchaseOrders",
                column: "RequisitionId",
                principalTable: "PurchaseRequisitions",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_return_authorization_lines_return_authorizations_ReturnAuth~",
                table: "return_authorization_lines",
                column: "ReturnAuthorizationId",
                principalTable: "return_authorizations",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_return_authorization_lines_sales_order_lines_OriginalSalesO~",
                table: "return_authorization_lines",
                columns: new[] { "OriginalSalesOrderLineSalesOrderId", "OriginalSalesOrderLineLineNumber" },
                principalTable: "sales_order_lines",
                principalColumns: new[] { "SalesOrderId", "LineNumber" });

            migrationBuilder.AddForeignKey(
                name: "FK_return_authorization_lines_sales_order_lines_SalesOrderId_S~",
                table: "return_authorization_lines",
                columns: new[] { "SalesOrderId", "SalesOrderLineNumber" },
                principalTable: "sales_order_lines",
                principalColumns: new[] { "SalesOrderId", "LineNumber" },
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_return_authorizations_sales_orders_SalesOrderId",
                table: "return_authorizations",
                column: "SalesOrderId",
                principalSchema: "public",
                principalTable: "sales_orders",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Contracts_Vendors_VendorId",
                table: "Contracts");

            migrationBuilder.DropForeignKey(
                name: "FK_invoices_Vendors_VendorId",
                table: "invoices");

            migrationBuilder.DropForeignKey(
                name: "FK_PurchaseOrders_Vendors_VendorId",
                table: "PurchaseOrders");

            migrationBuilder.DropForeignKey(
                name: "FK_invoices_PurchaseOrders_PurchaseOrderId",
                table: "invoices");

            migrationBuilder.DropForeignKey(
                name: "FK_PurchaseRequisitions_PurchaseOrders_AssociatedPurchaseOrder~",
                table: "PurchaseRequisitions");

            migrationBuilder.DropForeignKey(
                name: "FK_return_authorizations_sales_orders_SalesOrderId",
                table: "return_authorizations");

            migrationBuilder.DropTable(
                name: "AspNetRoleClaims");

            migrationBuilder.DropTable(
                name: "AspNetUserClaims");

            migrationBuilder.DropTable(
                name: "AspNetUserLogins");

            migrationBuilder.DropTable(
                name: "AspNetUserRoles");

            migrationBuilder.DropTable(
                name: "AspNetUserTokens");

            migrationBuilder.DropTable(
                name: "budget_allocations",
                schema: "public");

            migrationBuilder.DropTable(
                name: "DocumentLink");

            migrationBuilder.DropTable(
                name: "goods_receipt_note_lines");

            migrationBuilder.DropTable(
                name: "payment_transactions");

            migrationBuilder.DropTable(
                name: "procurement_workflow_steps");

            migrationBuilder.DropTable(
                name: "PurchaseRequisitionLines");

            migrationBuilder.DropTable(
                name: "request_for_quote_lines",
                schema: "public");

            migrationBuilder.DropTable(
                name: "requests_for_information");

            migrationBuilder.DropTable(
                name: "return_authorization_lines");

            migrationBuilder.DropTable(
                name: "sales_territory_representatives");

            migrationBuilder.DropTable(
                name: "submittal_reviews");

            migrationBuilder.DropTable(
                name: "Suppliers");

            migrationBuilder.DropTable(
                name: "tenant_products");

            migrationBuilder.DropTable(
                name: "Tenants");

            migrationBuilder.DropTable(
                name: "test_entities");

            migrationBuilder.DropTable(
                name: "vendor_proposals");

            migrationBuilder.DropTable(
                name: "AspNetRoles");

            migrationBuilder.DropTable(
                name: "budgets",
                schema: "public");

            migrationBuilder.DropTable(
                name: "departments",
                schema: "public");

            migrationBuilder.DropTable(
                name: "delivery_note_lines");

            migrationBuilder.DropTable(
                name: "goods_receipt_notes");

            migrationBuilder.DropTable(
                name: "procurement_workflows");

            migrationBuilder.DropTable(
                name: "request_for_quotes",
                schema: "public");

            migrationBuilder.DropTable(
                name: "invoice_lines");

            migrationBuilder.DropTable(
                name: "technical_submittals");

            migrationBuilder.DropTable(
                name: "requests_for_proposal");

            migrationBuilder.DropTable(
                name: "sales_order_lines");

            migrationBuilder.DropTable(
                name: "delivery_notes");

            migrationBuilder.DropTable(
                name: "AspNetUsers");

            migrationBuilder.DropTable(
                name: "PurchaseOrderLines");

            migrationBuilder.DropTable(
                name: "products",
                schema: "public");

            migrationBuilder.DropTable(
                name: "projects",
                schema: "public");

            migrationBuilder.DropTable(
                name: "VendorProducts");

            migrationBuilder.DropTable(
                name: "ProductDefinitions");

            migrationBuilder.DropTable(
                name: "Categories");

            migrationBuilder.DropTable(
                name: "Vendors");

            migrationBuilder.DropTable(
                name: "PurchaseOrders");

            migrationBuilder.DropTable(
                name: "Contracts");

            migrationBuilder.DropTable(
                name: "PurchaseRequisitions");

            migrationBuilder.DropTable(
                name: "sales_orders",
                schema: "public");

            migrationBuilder.DropTable(
                name: "return_authorizations");

            migrationBuilder.DropTable(
                name: "sales_territories");

            migrationBuilder.DropTable(
                name: "invoices");

            migrationBuilder.DropTable(
                name: "customers",
                schema: "public");
        }
    }
}
