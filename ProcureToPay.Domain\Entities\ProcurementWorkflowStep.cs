using System;
using System.ComponentModel.DataAnnotations;
using ProcureToPay.Domain.Enums; // Required for ProcurementWorkflowStepStatus
using ProcureToPay.Domain.Entities; // Required for BaseEntity and ProcurementWorkflow

namespace ProcureToPay.Domain.Entities;

/// <summary>
/// Represents a single step within a Procurement Workflow instance.
/// </summary>
public class ProcurementWorkflowStep : BaseEntity<Guid>
{
    /// <summary>
    /// Foreign Key to the parent Procurement Workflow.
    /// </summary>
    [Required]
    public Guid WorkflowId { get; private set; }

    /// <summary>
    /// Foreign Key to the parent Procurement Workflow.
    /// </summary>
    [Required]
    public Guid ProcurementWorkflowId { get; private set; }
    /// <summary>
    /// Navigation property to the parent Procurement Workflow. Virtual for lazy loading.
    /// </summary>
    public virtual ProcurementWorkflow Workflow { get; private set; } = null!;

    /// <summary>
    /// Navigation property to the parent Procurement Workflow. Virtual for lazy loading.
    /// </summary>
    public virtual ProcurementWorkflow ProcurementWorkflow { get; private set; } = null!;

    /// <summary>
    /// Name or description of this workflow step (e.g., "Manager Approval", "Budget Check").
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string StepName { get; private set; } = string.Empty;

    /// <summary>
    /// Order in which this step occurs within the workflow sequence.
    /// </summary>
    [Required]
    public int SequenceOrder { get; private set; }

    /// <summary>
    /// Order in which this step occurs within the workflow sequence.
    /// </summary>
    [Required]
    public int StepOrder { get; private set; }

    /// <summary>
    /// Current status of this individual step.
    /// </summary>
    [Required]
    public ProcurementWorkflowStepStatus Status { get; private set; }

    /// <summary>
    /// Optional ID linking the assigned user/role to the ApplicationUser identity or a Role entity.
    /// </summary>
    [MaxLength(450)]
    public string? AssigneeUserId { get; private set; } // Could also be RoleId depending on design

    /// <summary>
    /// Optional Foreign Key to the approver role.
    /// </summary>
    public Guid? ApproverRoleId { get; private set; }

    /// <summary>
    /// Optional Foreign Key to the approver user.
    /// </summary>
    [MaxLength(450)]
    public string? ApproverUserId { get; private set; }

    /// <summary>
    /// Name of the assignee (user or role).
    /// </summary>
    [MaxLength(150)]
    public string? AssigneeName { get; private set; } // Denormalized for display?

    /// <summary>
    /// Date and time when this step was assigned or became active.
    /// </summary>
    public DateTime? AssignedDate { get; private set; }

    /// <summary>
    /// Date and time when this step was completed or actioned upon.
    /// </summary>
    public DateTime? ActionDate { get; private set; }

    /// <summary>
    /// Optional comments provided by the assignee when actioning the step.
    /// </summary>
    public string? Comments { get; private set; }

    /// <summary>
    /// Optional condition expression for the step.
    /// </summary>
    public string? ConditionExpression { get; private set; }

    /// <summary>
    /// Optional SLA duration for the step.
    /// </summary>
    public TimeSpan? SlaDuration { get; private set; }

    /// <summary>
    /// Private constructor for EF Core.
    /// </summary>
    private ProcurementWorkflowStep() : base(Guid.NewGuid()) { }

    /// <summary>
    /// Internal constructor, typically called from ProcurementWorkflow.
    /// </summary>
    internal ProcurementWorkflowStep(
        Guid workflowId,
        string stepName,
        int sequenceOrder,
        Guid? approverRoleId = null,
        string? approverUserId = null,
        string? assigneeUserId = null,
        string? assigneeName = null,
        string? conditionExpression = null,
        TimeSpan? slaDuration = null) : base(Guid.NewGuid())
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(stepName);
        if (workflowId == Guid.Empty) throw new ArgumentException("WorkflowId cannot be empty.", nameof(workflowId));
        if (sequenceOrder <= 0) throw new ArgumentOutOfRangeException(nameof(sequenceOrder), "Sequence order must be positive.");

        WorkflowId = workflowId;
        ProcurementWorkflowId = workflowId;
        StepName = stepName;
        SequenceOrder = sequenceOrder;
        StepOrder = sequenceOrder;
        ApproverRoleId = approverRoleId;
        ApproverUserId = approverUserId;
        AssigneeUserId = assigneeUserId;
        AssigneeName = assigneeName;
        ConditionExpression = conditionExpression;
        SlaDuration = slaDuration;
        Status = ProcurementWorkflowStepStatus.Pending; // Initial status
    }

    // --- Internal Methods (Called by Workflow Aggregate Root) ---

    internal void MarkAsInProgress(DateTime assignedDate)
    {
        if (Status != ProcurementWorkflowStepStatus.Pending)
            throw new InvalidOperationException($"Cannot start step with status '{Status}'.");

        Status = ProcurementWorkflowStepStatus.InProgress;
        AssignedDate = assignedDate;
    }

    internal void Complete(string? comments, DateTime actionDate)
    {
        if (Status != ProcurementWorkflowStepStatus.InProgress)
            throw new InvalidOperationException($"Cannot complete step with status '{Status}'.");

        Status = ProcurementWorkflowStepStatus.Completed;
        Comments = comments;
        ActionDate = actionDate;
    }

    internal void Reject(string comments, DateTime actionDate)
    {
        if (Status != ProcurementWorkflowStepStatus.InProgress)
            throw new InvalidOperationException($"Cannot reject step with status '{Status}'.");

        ArgumentException.ThrowIfNullOrWhiteSpace(comments); // Require comments for rejection

        Status = ProcurementWorkflowStepStatus.Rejected;
        Comments = comments;
        ActionDate = actionDate;
    }

    internal void Skip(string comments, DateTime actionDate)
    {
        if (Status != ProcurementWorkflowStepStatus.Pending && Status != ProcurementWorkflowStepStatus.InProgress)
            throw new InvalidOperationException($"Cannot skip step with status '{Status}'.");

        Status = ProcurementWorkflowStepStatus.Skipped;
        Comments = comments;
        ActionDate = actionDate;
    }

    internal void Cancel()
    {
        // Allow cancellation from Pending or InProgress
        if (Status == ProcurementWorkflowStepStatus.Pending || Status == ProcurementWorkflowStepStatus.InProgress)
        {
            Status = ProcurementWorkflowStepStatus.Cancelled;
            ActionDate = DateTime.UtcNow;
        }
        // Ignore if already completed/rejected/cancelled
    }
}
