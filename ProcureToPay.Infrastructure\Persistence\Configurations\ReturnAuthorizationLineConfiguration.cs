using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProcureToPay.Domain.Entities;
using ProcureToPay.Domain.Enums; // Assuming UnitOfMeasure, ItemCondition enums exist

namespace ProcureToPay.Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// Configuration for the ReturnAuthorizationLine entity for EF Core and PostgreSQL.
    /// </summary>
    public class ReturnAuthorizationLineConfiguration : IEntityTypeConfiguration<ReturnAuthorizationLine>
    {
        public void Configure(EntityTypeBuilder<ReturnAuthorizationLine> builder)
        {
            // Table Mapping
            builder.ToTable("return_authorization_lines");

            // Primary Key (Composite Key)
            // Assuming ReturnAuthorizationId is long (from RA) and LineNumber is int
            builder.HasKey(l => new { l.ReturnAuthorizationId, l.LineNumber });

            // Properties
            builder.Property(l => l.LineNumber)
                .ValueGeneratedNever(); // Part of composite key

            builder.Property(l => l.QuantityAuthorized)
                .HasColumnType("numeric(18, 4)")
                .IsRequired();

            builder.Property(l => l.UnitOfMeasure)
                .IsRequired()
                .HasConversion<string>()
                .HasMaxLength(20);

            builder.Property(l => l.ItemCondition)
                .HasConversion<string>() // Or int
                .HasMaxLength(50) // Adjust size if string
                .IsRequired(false); // Condition might be TBD

            // Foreign Keys (Assuming Guid or appropriate types)
            builder.Property(l => l.ProductId).IsRequired();
            builder.Property(l => l.SalesOrderLineId).IsRequired(false); // Link to original SO Line or Invoice Line
            builder.Property(l => l.InvoiceLineId).IsRequired(false); // Link to original SO Line or Invoice Line


            // --- Relationships ---
            // Line-Header (Many-to-One)
            builder.HasOne(l => l.ReturnAuthorization)
                   .WithMany(ra => ra.Lines)
                   .HasForeignKey(l => l.ReturnAuthorizationId)
                   .IsRequired() // Part of composite key
                   .OnDelete(DeleteBehavior.Cascade); // Matches header config

            // Link to Product (Many-to-One)
            builder.HasOne(l => l.Product)
                   .WithMany() // Assuming ProductDefinition doesn't have direct nav back
                   .HasForeignKey(l => l.ProductId)
                   .IsRequired()
                   .OnDelete(DeleteBehavior.Restrict); // Prevent deleting product if used in RMA lines

            // Link to Origin Line (Many-to-One, Optional)
            builder.HasOne(l => l.SalesOrderLine)
                   .WithMany() // Assuming SOLine doesn't have direct nav back
                   .HasForeignKey(l => new { l.SalesOrderId, l.SalesOrderLineNumber })
                   .IsRequired(false)
                   .OnDelete(DeleteBehavior.Restrict); // Or SetNull?

            // Ignore the old property
            builder.Ignore(l => l.SalesOrderLineId);

            // Link to Origin Invoice Line (Many-to-One, Optional)
            builder.HasOne(l => l.InvoiceLine)
                  .WithMany() // Assuming InvoiceLine doesn't have direct nav back
                  .HasForeignKey(l => new { l.InvoiceId, l.InvoiceLineNumber })
                  .IsRequired(false)
                  .OnDelete(DeleteBehavior.Restrict); // Or SetNull?

            // Ignore the old property
            builder.Ignore(l => l.InvoiceLineId);


            // --- Indexes ---
            builder.HasIndex(l => l.ProductId);
            builder.HasIndex(l => new { l.SalesOrderId, l.SalesOrderLineNumber });
            builder.HasIndex(l => new { l.InvoiceId, l.InvoiceLineNumber });
            // Composite PK serves as an index for ReturnAuthorizationId


            // --- TODO ---
            // TODO: Verify FK types (ProductId, SalesOrderLineId, InvoiceLineId) match related entities.
            // TODO: Define ItemCondition enum if needed.
            // TODO: Decide if link is to SalesOrderLine OR InvoiceLine, or both allowed.
        }
    }
}

