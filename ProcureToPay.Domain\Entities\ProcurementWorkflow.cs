using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using ProcureToPay.Domain.Enums; // Required for ProcurementWorkflowStatus
using ProcureToPay.Domain.Entities; // Required for BaseEntity and related entities

namespace ProcureToPay.Domain.Entities;

/// <summary>
/// Represents an instance of a defined procurement workflow (e.g., approval process)
/// associated with a specific document like a Purchase Requisition or Invoice.
/// Acts as the Aggregate Root for the workflow process.
/// </summary>
public class ProcurementWorkflow : BaseEntity<Guid>
{
    /// <summary>
    /// Tenant identifier for multi-tenancy.
    /// </summary>
    [Required]
    public Guid TenantId { get; private set; }

    /// <summary>
    /// Name of the workflow definition being executed (e.g., "Standard Requisition Approval > $1000").
    /// </summary>
    [Required]
    [MaxLength(150)]
    public string WorkflowName { get; private set; } = string.Empty;

    /// <summary>
    /// Name of the workflow.
    /// </summary>
    [Required]
    [MaxLength(150)]
    public string Name { get; private set; } = string.Empty;

    /// <summary>
    /// Optional description of this specific workflow instance.
    /// </summary>
    [MaxLength(500)]
    public string? Description { get; private set; }

    /// <summary>
    /// Foreign Key to the primary document this workflow instance applies to
    /// (e.g., PurchaseRequisitionId, InvoiceId, ContractId).
    /// Using a generic name here; consider specific FKs if workflow only applies to one type.
    /// </summary>
    [Required]
    public Guid SubjectDocumentId { get; private set; }

    /// <summary>
    /// Type of the document the workflow applies to (e.g., "PurchaseRequisition", "Invoice").
    /// Useful if SubjectDocumentId can refer to different entity types.
    /// </summary>
    [Required]
    [MaxLength(50)]
    public string SubjectDocumentType { get; private set; } = string.Empty;

    /// <summary>
    /// Type of the workflow.
    /// </summary>
    [Required]
    [MaxLength(50)]
    public string WorkflowType { get; private set; } = string.Empty;

    /// <summary>
    /// Flag indicating if the workflow is active.
    /// </summary>
    public bool IsActive { get; private set; }

    /// <summary>
    /// Version of the workflow.
    /// </summary>
    public int? Version { get; private set; }

    /// <summary>
    /// The overall status of the workflow instance.
    /// </summary>
    [Required]
    public ProcurementWorkflowStatus CurrentStatus { get; private set; }

    /// <summary>
    /// Date and time the workflow instance was initiated.
    /// </summary>
    [Required]
    public DateTime StartedDate { get; private set; }

    /// <summary>
    /// Date and time the workflow instance reached a final state (Completed, Cancelled, Rejected).
    /// </summary>
    public DateTime? CompletedDate { get; private set; }

    /// <summary>
    /// Optional ID linking the initiator to the ApplicationUser identity.
    /// </summary>
    [MaxLength(450)]
    public string? InitiatedByUserId { get; private set; }

    /// <summary>
    /// Name of the initiator.
    /// </summary>
    [MaxLength(150)]
    public string? InitiatedByName { get; private set; }

    // Collection of steps defined for this workflow instance. Encapsulated list.
    private readonly List<ProcurementWorkflowStep> _steps = new();
    public virtual IReadOnlyCollection<ProcurementWorkflowStep> Steps => _steps.OrderBy(s => s.SequenceOrder).ToList().AsReadOnly();

    /// <summary>
    /// Private constructor for EF Core.
    /// </summary>
    private ProcurementWorkflow() : base(Guid.NewGuid()) { }

    /// <summary>
    /// Creates a new Procurement Workflow instance. Steps should be added subsequently.
    /// </summary>
    public ProcurementWorkflow(
        Guid tenantId,
        string name,
        string workflowName,
        string workflowType,
        Guid subjectDocumentId,
        string subjectDocumentType,
        int? version = 1,
        bool isActive = true,
        string? initiatedByUserId = null,
        string? initiatedByName = null,
        string? description = null
        ) : base(Guid.NewGuid())
    {
        if (tenantId == Guid.Empty) throw new ArgumentException("TenantId cannot be empty.", nameof(tenantId));
        ArgumentException.ThrowIfNullOrWhiteSpace(name);
        ArgumentException.ThrowIfNullOrWhiteSpace(workflowName);
        ArgumentException.ThrowIfNullOrWhiteSpace(workflowType);
        ArgumentException.ThrowIfNullOrWhiteSpace(subjectDocumentType);
        if (subjectDocumentId == Guid.Empty) throw new ArgumentException("SubjectDocumentId cannot be empty.", nameof(subjectDocumentId));
        if (version.HasValue && version.Value <= 0) throw new ArgumentOutOfRangeException(nameof(version), "Version must be positive if provided.");

        TenantId = tenantId;
        Name = name;
        WorkflowName = workflowName;
        WorkflowType = workflowType;
        SubjectDocumentId = subjectDocumentId;
        SubjectDocumentType = subjectDocumentType;
        Version = version;
        IsActive = isActive;
        InitiatedByUserId = initiatedByUserId;
        InitiatedByName = initiatedByName;
        Description = description;

        CurrentStatus = ProcurementWorkflowStatus.NotStarted; // Initial status
        // StartedDate will be set when Start() is called.
    }

    // --- Domain Methods ---

    /// <summary>
    /// Adds a step definition to this workflow instance. Should only be done before starting.
    /// </summary>
    public void AddStep(string stepName, int sequenceOrder, string? assigneeUserId = null, string? assigneeName = null)
    {
        if (CurrentStatus != ProcurementWorkflowStatus.NotStarted)
            throw new InvalidOperationException("Cannot add steps after the workflow has started.");
        if (_steps.Any(s => s.SequenceOrder == sequenceOrder))
            throw new InvalidOperationException($"A step with sequence order {sequenceOrder} already exists.");
        if (_steps.Any(s => s.StepName.Equals(stepName, StringComparison.OrdinalIgnoreCase)))
            throw new InvalidOperationException($"A step with name '{stepName}' already exists.");

        var newStep = new ProcurementWorkflowStep(
            workflowId: this.Id,
            stepName: stepName,
            sequenceOrder: sequenceOrder,
            approverRoleId: null,
            approverUserId: null,
            assigneeUserId: assigneeUserId,
            assigneeName: assigneeName);

        _steps.Add(newStep);
    }

    /// <summary>
    /// Starts the workflow, marking it and the first step as InProgress.
    /// </summary>
    public void Start()
    {
        if (CurrentStatus != ProcurementWorkflowStatus.NotStarted)
            throw new InvalidOperationException($"Workflow with status '{CurrentStatus}' cannot be started.");
        if (!_steps.Any())
            throw new InvalidOperationException("Cannot start a workflow with no steps defined.");

        CurrentStatus = ProcurementWorkflowStatus.InProgress;
        StartedDate = DateTime.UtcNow;

        // Mark the first step as InProgress
        var firstStep = Steps.OrderBy(s => s.SequenceOrder).FirstOrDefault();
        firstStep?.MarkAsInProgress(StartedDate);

        // TODO: Raise Domain Event? WorkflowStartedEvent(this.Id)
    }

    /// <summary>
    /// Records the completion of a specific step and advances the workflow.
    /// </summary>
    /// <param name="stepId">The ID of the step being completed.</param>
    /// <param name="actioningUserId">The ID of the user performing the action.</param>
    /// <param name="comments">Optional comments.</param>
    public void CompleteStep(Guid stepId, string actioningUserId, string? comments = null)
    {
        if (CurrentStatus != ProcurementWorkflowStatus.InProgress)
            throw new InvalidOperationException($"Workflow with status '{CurrentStatus}' cannot process step completion.");

        var currentStep = FindStepInProgress(stepId);

        // TODO: Add authorization check - is actioningUserId the AssigneeUserId?

        currentStep.Complete(comments, DateTime.UtcNow);

        AdvanceToNextStep();
        // TODO: Raise Domain Event? WorkflowStepCompletedEvent(this.Id, stepId)
    }

    /// <summary>
    /// Records the rejection of a specific step and sets the workflow status to Rejected.
    /// </summary>
    /// <param name="stepId">The ID of the step being rejected.</param>
    /// <param name="actioningUserId">The ID of the user performing the action.</param>
    /// <param name="comments">Required comments explaining the rejection.</param>
    public void RejectStep(Guid stepId, string actioningUserId, string comments)
    {
        if (CurrentStatus != ProcurementWorkflowStatus.InProgress)
            throw new InvalidOperationException($"Workflow with status '{CurrentStatus}' cannot process step rejection.");

        ArgumentException.ThrowIfNullOrWhiteSpace(comments);

        var currentStep = FindStepInProgress(stepId);

        // TODO: Add authorization check - is actioningUserId the AssigneeUserId?

        currentStep.Reject(comments, DateTime.UtcNow);
        CurrentStatus = ProcurementWorkflowStatus.Rejected; // Workflow ends on rejection
        CompletedDate = DateTime.UtcNow;
        // TODO: Raise Domain Event? WorkflowStepRejectedEvent(this.Id, stepId, comments)
        // TODO: Raise Domain Event? WorkflowRejectedEvent(this.Id)
    }

    /// <summary>
    /// Cancels the entire workflow instance.
    /// </summary>
    /// <param name="reason">Reason for cancellation.</param>
    public void Cancel(string reason)
    {
        if (CurrentStatus == ProcurementWorkflowStatus.Completed ||
            CurrentStatus == ProcurementWorkflowStatus.Cancelled ||
            CurrentStatus == ProcurementWorkflowStatus.Rejected)
            throw new InvalidOperationException($"Workflow with status '{CurrentStatus}' cannot be cancelled.");

        ArgumentException.ThrowIfNullOrWhiteSpace(reason);

        foreach (var step in _steps.Where(s => s.Status == ProcurementWorkflowStepStatus.Pending || s.Status == ProcurementWorkflowStepStatus.InProgress))
        {
            step.Cancel();
        }

        CurrentStatus = ProcurementWorkflowStatus.Cancelled;
        CompletedDate = DateTime.UtcNow;
        Description = $"Cancelled: {reason}\n---\n{Description}"; // Prepend reason
                                                                  // TODO: Raise Domain Event? WorkflowCancelledEvent(this.Id, reason)
    }


    // --- Private Helper Methods ---

    private ProcurementWorkflowStep FindStepInProgress(Guid stepId)
    {
        var step = _steps.FirstOrDefault(s => s.Id == stepId);
        if (step == null)
            throw new KeyNotFoundException($"Workflow Step with ID {stepId} not found in this workflow.");
        if (step.Status != ProcurementWorkflowStepStatus.InProgress)
            throw new InvalidOperationException($"Step '{step.StepName}' is not currently In Progress (Status: {step.Status}). Cannot action.");
        return step;
    }

    private void AdvanceToNextStep()
    {
        var nextStep = Steps
            .Where(s => s.Status == ProcurementWorkflowStepStatus.Pending)
            .OrderBy(s => s.SequenceOrder)
            .FirstOrDefault();

        if (nextStep != null)
        {
            // Mark next step as In Progress
            nextStep.MarkAsInProgress(DateTime.UtcNow);
            // Optionally update CurrentStepAssigneeUserId on the main workflow?
        }
        else
        {
            // No more pending steps, workflow is complete
            CurrentStatus = ProcurementWorkflowStatus.Completed;
            CompletedDate = DateTime.UtcNow;
            // TODO: Raise Domain Event? WorkflowCompletedEvent(this.Id)
        }
    }
}
