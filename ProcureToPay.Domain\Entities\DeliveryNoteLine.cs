﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using ProcureToPay.Domain.Entities; // Required for BaseEntity and related entities
using ProcureToPay.Domain.Enums; // Required for UnitOfMeasure

namespace ProcureToPay.Domain.Entities;

/// <summary>
/// Represents a single line item on a Delivery Note, detailing the quantity of a specific product delivered.
/// </summary>
public class DeliveryNoteLine : BaseEntity<Guid>
{
    /// <summary>
    /// Foreign Key to the parent Delivery Note.
    /// </summary>
    [Required]
    public Guid DeliveryNoteId { get; private set; }

    /// <summary>
    /// Navigation property to the parent Delivery Note. Virtual for lazy loading.
    /// </summary>
    public virtual DeliveryNote DeliveryNote { get; private set; } = null!;

    /// <summary>
    /// Line number for ordering within the delivery note.
    /// </summary>
    [Required]
    public int LineNumber { get; private set; }

    /// <summary>
    /// Foreign Key to the original Purchase Order Line this delivery corresponds to.
    /// </summary>
    public Guid? PurchaseOrderLineId { get; private set; }

    /// <summary>
    /// Navigation property to the original Purchase Order Line. Virtual for lazy loading.
    /// </summary>
    public virtual PurchaseOrderLine PurchaseOrderLine { get; private set; } = null!;

    /// <summary>
    /// Foreign Key to the Product delivered.
    /// </summary>
    [Required]
    public Guid ProductId { get; private set; }

    /// <summary>
    /// Navigation property to the Product delivered. Virtual for lazy loading.
    /// </summary>
    public virtual ProductDefinition Product { get; private set; } = null!;

    /// <summary>
    /// Optional Foreign Key to a related Sales Order Line.
    /// </summary>
    [System.ComponentModel.DataAnnotations.Schema.NotMapped]
    public Guid? SalesOrderLineId { get; private set; }

    /// <summary>
    /// Optional Foreign Key to the Sales Order this line relates to.
    /// </summary>
    public Guid? SalesOrderId { get; private set; }

    /// <summary>
    /// Optional Foreign Key to the Sales Order Line Number this line relates to.
    /// </summary>
    public int? SalesOrderLineNumber { get; private set; }

    /// <summary>
    /// Optional Navigation property to a related Sales Order Line.
    /// </summary>
    public virtual SalesOrderLine? SalesOrderLine { get; private set; }

    // Store snapshots in case product details change
    [Required]
    [MaxLength(100)]
    public string ProductSkuSnapshot { get; private set; } = string.Empty;

    /// <summary>
    /// Description of the product being delivered.
    /// </summary>
    [Required]
    [MaxLength(500)]
    public string Description { get; private set; } = string.Empty;

    /// <summary>
    /// The quantity of the product shipped in this specific delivery note line.
    /// Using decimal for flexibility.
    /// </summary>
    [Required]
    [Column(TypeName = "decimal(18, 4)")]
    public decimal QuantityShipped { get; private set; }

    /// <summary>
    /// The unit of measure for the quantity.
    /// </summary>
    [Required]
    [MaxLength(20)]
    public string UnitOfMeasure { get; private set; } = string.Empty;

    /// <summary>
    /// Optional batch number for the delivered product.
    /// </summary>
    [MaxLength(100)]
    public string? BatchNumber { get; private set; }

    /// <summary>
    /// Optional serial number for the delivered product.
    /// </summary>
    [MaxLength(100)]
    public string? SerialNumber { get; private set; }

    /// <summary>
    /// Optional notes specific to this delivered line item.
    /// </summary>
    public string? Notes { get; private set; }

    /// <summary>
    /// Private constructor for EF Core hydration.
    /// </summary>
    private DeliveryNoteLine() : base(Guid.Empty) { }

    /// <summary>
    /// Internal constructor, called from DeliveryNote.AddLine.
    /// </summary>
    internal DeliveryNoteLine(
        Guid deliveryNoteId,
        Guid? purchaseOrderLineId,
        Guid productId,
        string productSkuSnapshot,
        string description,
        decimal quantityShipped,
        int lineNumber,
        string unitOfMeasure,
        string? batchNumber = null,
        string? serialNumber = null,
        Guid? salesOrderLineId = null,
        string? notes = null) : base(Guid.NewGuid())
    {
        // Validation
        if (deliveryNoteId == Guid.Empty) throw new ArgumentException("DeliveryNoteId cannot be empty.", nameof(deliveryNoteId));
        if (purchaseOrderLineId.HasValue && purchaseOrderLineId.Value == Guid.Empty) throw new ArgumentException("PurchaseOrderLineId cannot be empty if provided.", nameof(purchaseOrderLineId));
        if (productId == Guid.Empty) throw new ArgumentException("ProductId cannot be empty.", nameof(productId));
        ArgumentException.ThrowIfNullOrWhiteSpace(productSkuSnapshot);
        ArgumentException.ThrowIfNullOrWhiteSpace(description);
        ArgumentException.ThrowIfNullOrWhiteSpace(unitOfMeasure);
        if (lineNumber <= 0) throw new ArgumentOutOfRangeException(nameof(lineNumber), "Line number must be positive.");
        if (quantityShipped <= 0) throw new ArgumentOutOfRangeException(nameof(quantityShipped), "Quantity shipped must be positive.");

        DeliveryNoteId = deliveryNoteId;
        PurchaseOrderLineId = purchaseOrderLineId;
        ProductId = productId;
        ProductSkuSnapshot = productSkuSnapshot;
        Description = description;
        QuantityShipped = quantityShipped;
        LineNumber = lineNumber;
        UnitOfMeasure = unitOfMeasure;
        BatchNumber = batchNumber;
        SerialNumber = serialNumber;
        SalesOrderLineId = salesOrderLineId;
        // If SalesOrderLineId is provided, we need to parse it to get SalesOrderId and SalesOrderLineNumber
        if (salesOrderLineId.HasValue)
        {
            // This is a placeholder. In a real implementation, you would need to look up the SalesOrderId and LineNumber
            // based on the SalesOrderLineId, or change the API to accept SalesOrderId and SalesOrderLineNumber directly.
            SalesOrderId = null;
            SalesOrderLineNumber = null;
        }
        Notes = notes;
    }

    // Generally, Delivery Note Lines are immutable once created/received.
    // If updates are needed, they might involve creating adjustment notes or handling in GoodsReceiptNote.
}
