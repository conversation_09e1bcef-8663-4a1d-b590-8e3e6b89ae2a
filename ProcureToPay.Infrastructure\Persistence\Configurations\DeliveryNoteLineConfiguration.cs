using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProcureToPay.Domain.Entities;
using ProcureToPay.Domain.Enums; // Assuming UnitOfMeasure enum exists
using ProcureToPay.Infrastructure.Persistence.Extensions;

namespace ProcureToPay.Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// Configuration for the DeliveryNoteLine entity for EF Core and PostgreSQL.
    /// </summary>
    public class DeliveryNoteLineConfiguration : IEntityTypeConfiguration<DeliveryNoteLine>
    {
        public void Configure(EntityTypeBuilder<DeliveryNoteLine> builder)
        {
            // Table Mapping
            builder.ToTable("delivery_note_lines");

            // Primary Key (Composite Key)
            // Assuming DeliveryNoteId is long (from DeliveryNote) and LineNumber is int
            builder.HasKey(l => new { l.DeliveryNoteId, l.LineNumber });

            // Properties
            builder.Property(l => l.LineNumber)
                .ValueGeneratedNever(); // Part of composite key

            builder.Property(l => l.QuantityShipped)
                .HasColumnType("numeric(18, 4)") // Explicit numeric type with precision/scale
                .IsRequired();

            builder.Property(l => l.UnitOfMeasure)
                .IsRequired()
                .HasConversion<string>() // Assuming enum stored as string
                .HasMaxLength(20); // Adjust size as needed

            builder.Property(l => l.BatchNumber)
                .HasColumnType("text") // Use text for potentially long/variable batch numbers
                .IsRequired(false);

            builder.Property(l => l.SerialNumber)
                .HasColumnType("text")
                .IsRequired(false);

            // Foreign Keys (Assuming Guid or appropriate types)
            builder.Property(l => l.ProductId).IsRequired();
            builder.Property(l => l.SalesOrderLineId).IsRequired(false); // Link to SO Line or PO Line
            builder.Property(l => l.PurchaseOrderLineId).IsRequired(false); // Link to SO Line or PO Line

            // Description property from previous version (keep if needed)
            builder.Property(l => l.Description)
                .IsRequired()
                .HasMaxLength(500);


            // --- Relationships ---
            // Line-Header (Many-to-One)
            builder.HasOne(l => l.DeliveryNote)
                   .WithMany(dn => dn.Lines)
                   .HasForeignKey(l => l.DeliveryNoteId)
                   .IsRequired() // Part of composite key
                   .OnDelete(DeleteBehavior.Cascade); // Matches header config

            // Link to Product/Service (Many-to-One)
            builder.HasOne(l => l.Product)
                   .WithMany() // Assuming ProductDefinition doesn't have direct nav back
                   .HasForeignKey(l => l.ProductId)
                   .IsRequired()
                   .OnDelete(DeleteBehavior.Restrict); // Prevent deleting product if used in DN lines

            // Link to Origin Line (Many-to-One) - Choose SO Line or PO Line
            builder.HasOne(l => l.SalesOrderLine)
                   .WithMany() // Assuming SalesOrderLine doesn't have direct nav back
                   .HasForeignKey(l => new { l.SalesOrderId, l.SalesOrderLineNumber })
                   .IsRequired(false) // Not required if linked to PO Line instead
                   .OnDelete(DeleteBehavior.Restrict); // Prevent deleting SO Line if linked DN line exists

            // Ignore the old property
            builder.Ignore(l => l.SalesOrderLineId);

            builder.HasOne(l => l.PurchaseOrderLine)
                   .WithMany(pol => pol.DeliveryNoteLines) // Assuming POLine has collection
                   .HasForeignKey(l => l.PurchaseOrderLineId)
                   .IsRequired(false) // Not required if linked to SO Line instead
                   .OnDelete(DeleteBehavior.Restrict); // Prevent deleting PO Line if linked DN line exists


            // --- Indexes ---
            builder.HasIndex(l => l.ProductId);
            builder.HasIndex(l => new { l.SalesOrderId, l.SalesOrderLineNumber });
            builder.HasIndex(l => l.PurchaseOrderLineId);
            // Composite PK serves as an index for DeliveryNoteId


            // --- TODO ---
            // TODO: Confirm if link is to SalesOrderLine OR PurchaseOrderLine, adjust IsRequired accordingly.
            // TODO: Verify FK types match related entities (ProductId, SalesOrderLineId, PurchaseOrderLineId).
        }
    }
}
