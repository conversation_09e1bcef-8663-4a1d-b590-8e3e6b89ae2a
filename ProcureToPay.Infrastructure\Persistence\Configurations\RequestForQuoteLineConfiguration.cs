using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProcureToPay.Domain.Entities; // Assuming RFQLine, RFQ, ProductDefinition, VendorProduct entities exist
using ProcureToPay.Domain.Enums;   // Assuming UnitOfMeasure enum exists
using ProcureToPay.Domain.ValueObjects; // Assuming Money VO exists
using System;

namespace ProcureToPay.Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// EF Core Configuration for the RequestForQuoteLine entity targeting PostgreSQL.
    /// </summary>
    public class RequestForQuoteLineConfiguration : IEntityTypeConfiguration<RequestForQuoteLine>
    {
        private const string DefaultSchema = "public"; // Example schema

        public void Configure(EntityTypeBuilder<RequestForQuoteLine> builder)
        {
            // --- Table Mapping ---
            builder.ToTable("request_for_quote_lines", DefaultSchema); // Use snake_case and schema

            // --- Primary Key ---
            // Assuming RequestForQuoteLine inherits from BaseEntity<Guid>
            builder.HasKey(l => l.Id);

            // --- Unique Constraint ---
            // Enforce that LineNumber is unique within a single RequestForQuote
            builder.HasIndex(l => new { l.RequestForQuoteId, l.LineNumber })
                   .IsUnique();

            // --- Soft Delete Configuration ---
            // Assumes RequestForQuoteLine has: public bool IsDeleted { get; private set; }
            builder.Property(l => l.IsDeleted)
                   .HasDefaultValue(false)
                   .IsRequired();
            builder.HasIndex(l => l.IsDeleted);


            // --- Concurrency Control (PostgreSQL xmin - EF Core 7+) ---
            builder.Property<uint>("xmin")
                   .HasColumnType("xid")
                   .ValueGeneratedOnAddOrUpdate()
                   .IsConcurrencyToken();


            // --- Property Mappings & Constraints ---
            builder.Property(l => l.TenantId)
                   .IsRequired();
            builder.HasIndex(l => l.TenantId); // Index for tenant filtering

            builder.Property(l => l.LineNumber)
                   .IsRequired();

            // Configure FKs (relationships configured below)
            builder.Property(l => l.ProductDefinitionId); // Nullable Guid
            builder.Property(l => l.VendorProductId);     // Nullable Guid

            builder.Property(l => l.Description)
                   .IsRequired()
                   .HasColumnType("text"); // Use 'text' for potentially long descriptions

            builder.Property(l => l.Quantity)
                   .IsRequired()
                   .HasPrecision(18, 4); // Precision for decimal quantity

            // Configure UnitOfMeasure Enum conversion
            builder.Property(l => l.UnitOfMeasure)
                   .IsRequired()
                   .HasConversion<string>()
                   .HasMaxLength(50);

            // Sourcing Detail Properties
            builder.Property(l => l.AlternateItemProposal).HasColumnType("text"); // Optional text field
            builder.Property(l => l.TechnicalSpecifications).HasColumnType("text"); // Optional text field
            builder.Property(l => l.SampleRequired).IsRequired().HasDefaultValue(false);
            builder.Property(l => l.MinimumOrderQuantity).HasPrecision(18, 4); // Nullable decimal
            builder.Property(l => l.PreferredIncoterm).HasMaxLength(10); // e.g., "FOB", "CIF"
            builder.Property(l => l.IsSubstituteAllowed).IsRequired().HasDefaultValue(false);

            // Standard Properties
            builder.Property(l => l.Notes).HasColumnType("text");


            // --- Value Object Mapping (Money?) ---

            // Configure TargetUnitPrice (nullable Money VO)
            builder.OwnsOne(l => l.TargetUnitPrice, price =>
            {
                price.Property(m => m.Amount)
                    .HasColumnName("target_unit_price_amount") // snake_case
                    .HasPrecision(18, 4)
                    .IsRequired(); // Required if VO exists
                price.Property(m => m.CurrencyCode)
                    .HasColumnName("target_unit_price_currency_code") // snake_case
                    .HasMaxLength(3)
                    .IsRequired(); // Required if VO exists
            });
            // Navigation is implicitly optional as TargetUnitPrice property is nullable

            // Configure EstimatedTcoValue (nullable Money VO)
            builder.OwnsOne(l => l.EstimatedTcoValue, tco =>
            {
                tco.Property(m => m.Amount)
                    .HasColumnName("est_tco_value_amount") // snake_case
                    .HasPrecision(18, 4)
                    .IsRequired(); // Required if VO exists
                tco.Property(m => m.CurrencyCode)
                    .HasColumnName("est_tco_value_currency_code") // snake_case
                    .HasMaxLength(3)
                    .IsRequired(); // Required if VO exists
            });
            // Navigation is implicitly optional as EstimatedTcoValue property is nullable


            // --- Relationships ---

            // Relationship to RequestForQuote header (Many Lines to One Header)
            builder.HasOne(l => l.RequestForQuote)
                   .WithMany(r => r.Lines) // Assumes RequestForQuote has ICollection<RequestForQuoteLine> Lines
                   .HasForeignKey(l => l.RequestForQuoteId)
                   .IsRequired()
                   .OnDelete(DeleteBehavior.Cascade); // Deleting header deletes lines

            // Relationship to ProductDefinition (Optional)
            builder.HasOne(l => l.ProductDefinition)
                   .WithMany() // Assuming ProductDefinition doesn't need direct collection of RFQ Lines
                   .HasForeignKey(l => l.ProductDefinitionId)
                   .IsRequired(false) // Link is optional
                   .OnDelete(DeleteBehavior.SetNull); // If ProductDefinition deleted, nullify FK

            // Relationship to VendorProduct (Optional)
            builder.HasOne(l => l.VendorProduct)
                   .WithMany() // Assuming VendorProduct doesn't need direct collection of RFQ Lines
                   .HasForeignKey(l => l.VendorProductId)
                   .IsRequired(false) // Link is optional
                   .OnDelete(DeleteBehavior.SetNull); // If VendorProduct deleted, nullify FK


            // --- Indexes ---
            // Included unique index on RFQId+LineNumber and index on TenantId above.
            builder.HasIndex(l => l.ProductDefinitionId);
            builder.HasIndex(l => l.VendorProductId);


            // --- Tenant Isolation & Soft Delete Query Filter ---
            // Note: Tenant filtering should be implemented at the DbContext level
            // Example:
            // builder.HasQueryFilter(l => !l.IsDeleted && l.TenantId == currentTenantId);
            // Where currentTenantId is obtained from the DbContext or a tenant provider service

            // For now, just implement the soft delete filter
            builder.HasQueryFilter(l => !l.IsDeleted);
        }
    }
}

