using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProcureToPay.Domain.Entities;
using ProcureToPay.Domain.Enums;
using ProcureToPay.Domain.ValueObjects;
using System;

namespace ProcureToPay.Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// EF Core Configuration for the Project entity targeting PostgreSQL.
    /// </summary>
    public class ProjectConfiguration : IEntityTypeConfiguration<Project>
    {
        private const string DefaultSchema = "public"; // Example schema

        public void Configure(EntityTypeBuilder<Project> builder)
        {
            // --- Table Mapping ---
            builder.ToTable("projects", DefaultSchema); // Use snake_case and schema

            // --- Primary Key ---
            // Project inherits from BaseEntity<Guid>
            builder.HasKey(p => p.Id);

            // --- Soft Delete Configuration ---
            builder.Property(p => p.IsDeleted)
                   .HasDefaultValue(false)
                   .IsRequired();
            builder.HasIndex(p => p.IsDeleted);

            // --- Concurrency Control (PostgreSQL xmin - EF Core 7+) ---
            builder.Property<uint>("xmin")
                   .HasColumnType("xid")
                   .ValueGeneratedOnAddOrUpdate()
                   .IsConcurrencyToken();

            // --- Property Mappings & Constraints ---
            builder.Property(p => p.TenantId)
                   .IsRequired();
            builder.HasIndex(p => p.TenantId);

            builder.Property(p => p.ProjectCode)
                   .IsRequired()
                   .HasMaxLength(50);
            // Create a unique index on TenantId + ProjectCode
            builder.HasIndex(p => new { p.TenantId, p.ProjectCode })
                   .IsUnique();

            builder.Property(p => p.Name)
                   .IsRequired()
                   .HasMaxLength(200);
            builder.HasIndex(p => p.Name);

            builder.Property(p => p.Description)
                   .HasMaxLength(2000);

            builder.Property(p => p.Status)
                   .IsRequired()
                   .HasConversion<string>()
                   .HasMaxLength(50);
            builder.HasIndex(p => p.Status);

            builder.Property(p => p.StartDate);
            builder.Property(p => p.EndDate);
            builder.HasIndex(p => p.StartDate);
            builder.HasIndex(p => p.EndDate);

            // --- Value Object Mappings ---
            // Configure Money value object for BudgetAmount
            builder.OwnsOne(p => p.BudgetAmount, moneyBuilder =>
            {
                moneyBuilder.Property(m => m.Amount)
                            .HasColumnName("BudgetAmount")
                            .HasPrecision(18, 2);

                moneyBuilder.Property(m => m.CurrencyCode)
                            .HasColumnName("BudgetCurrencyCode")
                            .HasMaxLength(3);
            });

            // --- Relationships ---
            // To SalesOrderLines (One Project to Many SalesOrderLines)
            builder.HasMany(p => p.SalesOrderLines)
                   .WithOne() // Assuming SalesOrderLine has a Project navigation property
                   .HasForeignKey("ProjectId") // Assuming SalesOrderLine has a ProjectId FK
                   .IsRequired(false) // SalesOrderLine might not require a project
                   .OnDelete(DeleteBehavior.SetNull); // Set ProjectId to null if Project is deleted

            // --- Tenant Isolation & Soft Delete Query Filter ---
            // Note: Tenant filtering should be implemented at the DbContext level
            // Example:
            // builder.HasQueryFilter(p => !p.IsDeleted && p.TenantId == currentTenantId);
            // Where currentTenantId is obtained from the DbContext or a tenant provider service

            // For now, just implement the soft delete filter
            builder.HasQueryFilter(p => !p.IsDeleted);
        }
    }
}

