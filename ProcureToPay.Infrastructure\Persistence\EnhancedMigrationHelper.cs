using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Polly;
using Polly.Retry;
using System;
using System.Threading;
using System.Threading.Tasks;
using ProcureToPay.Domain.Interfaces;

namespace ProcureToPay.Infrastructure.Persistence
{
    /// <summary>
    /// Enhanced helper class for applying database migrations programmatically at application startup.
    /// Uses Polly for robust retry policies and provides detailed logging of the migration process.
    /// </summary>
    public static class EnhancedMigrationHelper
    {
        /// <summary>
        /// Applies pending migrations to the database with enhanced retry logic and tenant context validation.
        /// </summary>
        /// <param name="serviceProvider">The service provider to resolve services from.</param>
        /// <param name="maxRetries">Maximum number of retry attempts.</param>
        /// <returns>True if migrations were applied successfully, false otherwise.</returns>
        public static bool ApplyMigrationsWithRetry(this IServiceProvider serviceProvider, int maxRetries = 10)
        {
            using (var scope = serviceProvider.CreateScope())
            {
                var services = scope.ServiceProvider;
                var logger = services.GetRequiredService<ILogger<ApplicationDbContext>>();

                try
                {
                    // Verify tenant context before applying migrations
                    // Ensure MigrationTenantProvider is used for logging tenant information
                    var migrationTenantProviderLogging = new MigrationTenantProvider();
                    var tenantId = migrationTenantProviderLogging.GetCurrentTenantId(); // Should be null

                    logger.LogInformation(
                        "[MIGRATION] TenantProvider type: {TenantProviderType}, Resolved TenantId: {TenantId}",
                        migrationTenantProviderLogging.GetType().Name, tenantId?.ToString() ?? "NULL");

                    if (tenantId.HasValue)
                    {
                        // This path should ideally not be taken if MigrationTenantProvider works as expected (returns null TenantId)
                        logger.LogWarning(
                            "[MIGRATION] WARNING: TenantId '{TenantId}' was resolved during migration. " +
                            "Migrations should run without specific tenant context. This may cause issues with query filters.",
                            tenantId.Value);
                    }
                    else
                    {
                        logger.LogInformation("[MIGRATION] TenantId is NULL, as expected for migrations. Query filters should be disabled.");
                    }

                    logger.LogInformation("[MIGRATION] Attempting to apply database migrations with retry policy (Max Retries: {MaxRetries})...", maxRetries);

                    // Create a retry policy with exponential backoff
                    var retryPolicy = CreateRetryPolicy(maxRetries, logger);

                    // Execute the migration with retry policy
                    return retryPolicy.Execute(() => ExecuteMigration(services, logger));
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "[MIGRATION] Critical failure applying migrations after {MaxRetries} attempts.", maxRetries);
                    return false;
                }
            }
        }

        /// <summary>
        /// Applies pending migrations to the database asynchronously with enhanced retry logic.
        /// </summary>
        /// <param name="serviceProvider">The service provider to resolve services from.</param>
        /// <param name="maxRetries">Maximum number of retry attempts.</param>
        /// <param name="cancellationToken">Cancellation token.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains true if migrations were applied successfully, false otherwise.</returns>
        public static async Task<bool> ApplyMigrationsWithRetryAsync(
            this IServiceProvider serviceProvider,
            int maxRetries = 10,
            CancellationToken cancellationToken = default)
        {
            using (var scope = serviceProvider.CreateScope())
            {
                var services = scope.ServiceProvider;
                var logger = services.GetRequiredService<ILogger<ApplicationDbContext>>();

                try
                {
                    // Verify tenant context before applying migrations
                    // Ensure MigrationTenantProvider is used for logging tenant information
                    var migrationTenantProviderLogging = new MigrationTenantProvider();
                    var tenantId = migrationTenantProviderLogging.GetCurrentTenantId(); // Should be null

                    logger.LogInformation(
                        "[MIGRATION] TenantProvider type: {TenantProviderType}, Resolved TenantId: {TenantId}",
                        migrationTenantProviderLogging.GetType().Name, tenantId?.ToString() ?? "NULL");

                    if (tenantId.HasValue)
                    {
                        // This path should ideally not be taken if MigrationTenantProvider works as expected (returns null TenantId)
                        logger.LogWarning(
                            "[MIGRATION] WARNING: TenantId '{TenantId}' was resolved during migration. " +
                            "Migrations should run without specific tenant context. This may cause issues with query filters.",
                            tenantId.Value);
                    }
                    else
                    {
                        logger.LogInformation("[MIGRATION] TenantId is NULL, as expected for migrations. Query filters should be disabled.");
                    }

                    logger.LogInformation("[MIGRATION] Attempting to apply database migrations asynchronously with retry policy (Max Retries: {MaxRetries})...", maxRetries);

                    // Create an async retry policy with exponential backoff
                    var retryPolicy = CreateAsyncRetryPolicy(maxRetries, logger);

                    // Execute the migration with retry policy
                    return await retryPolicy.ExecuteAsync(async (ct) => await ExecuteMigrationAsync(services, logger, ct), cancellationToken);
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "[MIGRATION] Critical failure applying migrations after {MaxRetries} attempts.", maxRetries);
                    return false;
                }
            }
        }

        /// <summary>
        /// Creates a retry policy with exponential backoff.
        /// </summary>
        private static RetryPolicy<bool> CreateRetryPolicy(int maxRetries, ILogger logger)
        {
            return Policy<bool>
                .Handle<Exception>() // Handle any exception
                .WaitAndRetry(
                    maxRetries,
                    retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)), // Exponential backoff: 2, 4, 8, 16, etc. seconds
                    (outcome, timeSpan, retryCount, context) =>
                    {
                        logger.LogWarning(outcome.Exception,
                            "[MIGRATION] Error applying migrations (attempt {RetryCount}/{MaxRetries}). " +
                            "Retrying in {TimeSpanS}s. Error: {ErrorMessage}",
                            retryCount, maxRetries, timeSpan.TotalSeconds, outcome.Exception.Message);
                    });
        }

        /// <summary>
        /// Creates an async retry policy with exponential backoff.
        /// </summary>
        private static AsyncRetryPolicy<bool> CreateAsyncRetryPolicy(int maxRetries, ILogger logger)
        {
            return Policy<bool>
                .Handle<Exception>() // Handle any exception
                .WaitAndRetryAsync(
                    maxRetries,
                    retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)), // Exponential backoff: 2, 4, 8, 16, etc. seconds
                    (outcome, timeSpan, retryCount, context) =>
                    {
                        logger.LogWarning(outcome.Exception,
                            "[MIGRATION] Error applying migrations (attempt {RetryCount}/{MaxRetries}). " +
                            "Retrying in {TimeSpanS}s. Error: {ErrorMessage}",
                            retryCount, maxRetries, timeSpan.TotalSeconds, outcome.Exception.Message);
                    });
        }

        /// <summary>
        /// Executes the migration operation.
        /// </summary>
        private static bool ExecuteMigration(IServiceProvider services, ILogger logger)
        {
            // Resolve DbContextOptions and instantiate ApplicationDbContext with MigrationTenantProvider
            var options = services.GetRequiredService<DbContextOptions<ApplicationDbContext>>();
            var context = new ApplicationDbContext(options, new MigrationTenantProvider());

            // Extract database name from connection string for logging
            var connectionString = context.Database.GetConnectionString();
            string dbName = "unknown";
            if (!string.IsNullOrEmpty(connectionString))
            {
                var connStringParts = connectionString.Split(';');
                var dbNamePart = connStringParts.FirstOrDefault(p => p.StartsWith("Database=", StringComparison.OrdinalIgnoreCase));
                dbName = dbNamePart?.Substring("Database=".Length) ?? "unknown";
            }

            logger.LogInformation("[MIGRATION] Checking database connectivity to database '{DbName}'...", dbName);

            try
            {
                if (!context.Database.CanConnect())
                {
                    logger.LogWarning("[MIGRATION] Cannot connect to the database '{DbName}'. Will retry if attempts remain.", dbName);
                    throw new InvalidOperationException($"Failed to connect to the database '{dbName}' for migrations.");
                }
            }
            catch (Npgsql.NpgsqlException npgEx)
            {
                logger.LogWarning(npgEx, "[MIGRATION] PostgreSQL error connecting to database '{DbName}': {ErrorMessage}", dbName, npgEx.Message);
                throw new InvalidOperationException($"PostgreSQL error connecting to database '{dbName}': {npgEx.Message}", npgEx);
            }

            logger.LogInformation("[MIGRATION] Database connection successful. Applying migrations...");
            context.Database.Migrate();
            logger.LogInformation("[MIGRATION] Database migrations applied successfully.");

            return true;
        }

        /// <summary>
        /// Executes the migration operation asynchronously.
        /// </summary>
        private static async Task<bool> ExecuteMigrationAsync(
            IServiceProvider services,
            ILogger logger,
            CancellationToken cancellationToken)
        {
            // Resolve DbContextOptions and instantiate ApplicationDbContext with MigrationTenantProvider
            var options = services.GetRequiredService<DbContextOptions<ApplicationDbContext>>();
            var context = new ApplicationDbContext(options, new MigrationTenantProvider());

            // Extract database name from connection string for logging
            var connectionString = context.Database.GetConnectionString();
            string dbName = "unknown";
            if (!string.IsNullOrEmpty(connectionString))
            {
                var connStringParts = connectionString.Split(';');
                var dbNamePart = connStringParts.FirstOrDefault(p => p.StartsWith("Database=", StringComparison.OrdinalIgnoreCase));
                dbName = dbNamePart?.Substring("Database=".Length) ?? "unknown";
            }

            logger.LogInformation("[MIGRATION] Checking database connectivity to database '{DbName}'...", dbName);

            try
            {
                if (!await context.Database.CanConnectAsync(cancellationToken))
                {
                    logger.LogWarning("[MIGRATION] Cannot connect to the database '{DbName}'. Will retry if attempts remain.", dbName);
                    throw new InvalidOperationException($"Failed to connect to the database '{dbName}' for migrations.");
                }
            }
            catch (Npgsql.NpgsqlException npgEx)
            {
                logger.LogWarning(npgEx, "[MIGRATION] PostgreSQL error connecting to database '{DbName}': {ErrorMessage}", dbName, npgEx.Message);
                throw new InvalidOperationException($"PostgreSQL error connecting to database '{dbName}': {npgEx.Message}", npgEx);
            }

            logger.LogInformation("[MIGRATION] Database connection successful. Applying migrations...");
            await context.Database.MigrateAsync(cancellationToken);
            logger.LogInformation("[MIGRATION] Database migrations applied successfully.");

            return true;
        }
    }
}
