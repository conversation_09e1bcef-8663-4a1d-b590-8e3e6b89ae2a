﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ProcureToPay.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class CleanInitialSchema : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_payment_transactions_TransactionReference",
                table: "payment_transactions");

            migrationBuilder.CreateIndex(
                name: "IX_payment_transactions_TransactionReference",
                table: "payment_transactions",
                column: "TransactionReference",
                unique: true,
                filter: "\"TransactionReference\" IS NOT NULL");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_payment_transactions_TransactionReference",
                table: "payment_transactions");

            migrationBuilder.CreateIndex(
                name: "IX_payment_transactions_TransactionReference",
                table: "payment_transactions",
                column: "TransactionReference",
                unique: true,
                filter: "transaction_reference IS NOT NULL");
        }
    }
}
