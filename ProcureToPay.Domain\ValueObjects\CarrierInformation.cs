using System;
using System.Collections.Generic;

namespace ProcureToPay.Domain.ValueObjects
{
    /// <summary>
    /// Represents shipping carrier information as a Value Object.
    /// Value Objects are compared by their values, not their reference.
    /// </summary>
    public class CarrierInformation : ValueObject
    {
        /// <summary>
        /// Name of the shipping carrier (e.g., FedEx, DHL, UPS).
        /// </summary>
        public string Name { get; private set; } = string.Empty;

        /// <summary>
        /// Tracking number provided by the carrier (if applicable).
        /// </summary>
        public string? TrackingNumber { get; private set; }

        /// <summary>
        /// Private constructor for EF Core or deserialization.
        /// </summary>
        private CarrierInformation() { }

        /// <summary>
        /// Creates a new CarrierInformation instance.
        /// </summary>
        /// <param name="name">Name of the shipping carrier.</param>
        /// <param name="trackingNumber">Optional tracking number.</param>
        public CarrierInformation(string name, string? trackingNumber = null)
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new ArgumentNullException(nameof(name), "Carrier name cannot be null or empty.");

            Name = name;
            TrackingNumber = trackingNumber;
        }

        /// <summary>
        /// Required for Value Object semantics.
        /// </summary>
        protected override IEnumerable<object> GetEqualityComponents()
        {
            yield return Name;
            if (TrackingNumber != null)
                yield return TrackingNumber;
        }

        // Equality is handled by the ValueObject base class

        public override bool Equals(object? obj)
        {
            if (obj == null || obj.GetType() != GetType())
            {
                return false;
            }

            var other = (CarrierInformation)obj;
            return GetEqualityComponents().SequenceEqual(other.GetEqualityComponents());
        }

        public override int GetHashCode()
        {
            return GetEqualityComponents()
                .Select(x => x != null ? x.GetHashCode() : 0)
                .Aggregate((x, y) => x ^ y);
        }
    }
}
