namespace ProcureToPay.Domain.Enums;

/// <summary>
/// Represents the status of a Request for Information (RFI).
/// </summary>
public enum RfiStatus
{
    /// <summary>
    /// RFI is being prepared.
    /// </summary>
    Draft = 0,

    /// <summary>
    /// RFI has been sent out to potential respondents.
    /// </summary>
    Issued = 1,

    /// <summary>
    /// The response period has ended, or the RFI has been manually closed.
    /// </summary>
    Closed = 2,

    /// <summary>
    /// The RFI process was cancelled before completion.
    /// </summary>
    Cancelled = 3
}
