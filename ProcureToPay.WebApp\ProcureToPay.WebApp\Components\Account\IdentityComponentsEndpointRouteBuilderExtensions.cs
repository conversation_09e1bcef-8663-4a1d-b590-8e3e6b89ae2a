using System.Security.Claims;
using System.Text.Json;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Primitives;
using ProcureToPay.WebApp.Components.Account.Pages; // Assuming this using might be needed for ExternalLogin/ExternalLogins constants if they are defined there
using ProcureToPay.WebApp.Components.Account.Pages.Manage; // Assuming this using might be needed for ExternalLogin/ExternalLogins constants if they are defined there
using ProcureToPay.Infrastructure.Identity; // Needed for ApplicationUser

// Ensure necessary using directives are present
using Microsoft.Extensions.Logging;
using System.Linq;
using System;
using System.Collections.Generic;
using Microsoft.AspNetCore.Http; // Needed for Results, HttpContext, etc.


namespace Microsoft.AspNetCore.Routing // Standard namespace for this type of extension
{
    internal static class IdentityComponentsEndpointRouteBuilderExtensions
    {
        // These endpoints are required by the Identity Razor components defined in the /Components/Account/Pages directory of this project.
        public static IEndpointConventionBuilder MapAdditionalIdentityEndpoints(this IEndpointRouteBuilder endpoints)
        {
            ArgumentNullException.ThrowIfNull(endpoints);

            var accountGroup = endpoints.MapGroup("/Account");

            accountGroup.MapPost("/PerformExternalLogin", (
                HttpContext context,
                [FromServices] SignInManager<ApplicationUser> signInManager,
                [FromForm] string provider,
                [FromForm] string returnUrl) =>
            {
                // Constants for action names might be better defined elsewhere, e.g., within the component pages themselves
                const string ExternalLoginCallbackAction = "LoginCallback";

                IEnumerable<KeyValuePair<string, StringValues>> query =
                [
                    new("ReturnUrl", returnUrl),
                    new("Action", ExternalLoginCallbackAction) // Using constant or string literal
                ];

                var redirectUrl = UriHelper.BuildRelative(
                    context.Request.PathBase,
                    "/Account/ExternalLogin", // Path to the ExternalLogin component/page
                    QueryString.Create(query));

                var properties = signInManager.ConfigureExternalAuthenticationProperties(provider, redirectUrl);
                return TypedResults.Challenge(properties, [provider]);
            });

            accountGroup.MapPost("/Logout", async (
                ClaimsPrincipal user, // user might not be strictly needed here unless logging specific user out
                [FromServices] SignInManager<ApplicationUser> signInManager,
                [FromForm] string returnUrl) =>
            {
                await signInManager.SignOutAsync();
                // Ensure the return URL is safe and local
                if (string.IsNullOrEmpty(returnUrl) || !Uri.IsWellFormedUriString(returnUrl, UriKind.Relative))
                {
                    returnUrl = "~/"; // Default redirect location
                }
                return TypedResults.LocalRedirect(returnUrl);
            });

            var manageGroup = accountGroup.MapGroup("/Manage").RequireAuthorization();

            manageGroup.MapPost("/LinkExternalLogin", async (
                HttpContext context,
                [FromServices] SignInManager<ApplicationUser> signInManager,
                [FromForm] string provider) =>
            {
                // Clear the existing external cookie to ensure a clean login process
                await context.SignOutAsync(IdentityConstants.ExternalScheme);

                // Constants for action names might be better defined elsewhere
                const string LinkLoginCallbackAction = "LinkLoginCallback";

                var redirectUrl = UriHelper.BuildRelative(
                    context.Request.PathBase,
                    "/Account/Manage/ExternalLogins", // Path to the ExternalLogins manage page
                    QueryString.Create("Action", LinkLoginCallbackAction)); // Using constant or string literal

                var userId = signInManager.UserManager.GetUserId(context.User);
                var properties = signInManager.ConfigureExternalAuthenticationProperties(provider, redirectUrl, userId);
                return TypedResults.Challenge(properties, [provider]);
            });

            var loggerFactory = endpoints.ServiceProvider.GetRequiredService<ILoggerFactory>();
            var downloadLogger = loggerFactory.CreateLogger("DownloadPersonalData");

            manageGroup.MapPost("/DownloadPersonalData", async (
                HttpContext context,
                [FromServices] UserManager<ApplicationUser> userManager,
                [FromServices] AuthenticationStateProvider authenticationStateProvider) => // authenticationStateProvider may not be needed here
            {
                var user = await userManager.GetUserAsync(context.User);
                if (user is null)
                {
                    return Results.NotFound($"Unable to load user with ID '{userManager.GetUserId(context.User)}'.");
                }

                var userId = await userManager.GetUserIdAsync(user);
                downloadLogger.LogInformation("User with ID '{UserId}' asked for their personal data.", userId);

                // Only include personal data for download
                var personalData = new Dictionary<string, string>();
                var personalDataProps = typeof(ApplicationUser).GetProperties().Where(
                    prop => Attribute.IsDefined(prop, typeof(PersonalDataAttribute)));

                foreach (var p in personalDataProps)
                {
                    // Use null-conditional operator ?. and null-coalescing operator ?? for safer access
                    personalData.Add(p.Name, p.GetValue(user)?.ToString() ?? "null");
                }

                var logins = await userManager.GetLoginsAsync(user);
                foreach (var l in logins)
                {
                    personalData.Add($"{l.LoginProvider} external login provider key", l.ProviderKey);
                }

                // --- CORRECTED CODE ---
                // Safely get and add the authenticator key only if it exists
                var authenticatorKey = await userManager.GetAuthenticatorKeyAsync(user);
                if (authenticatorKey is not null)
                {
                    personalData.Add("Authenticator Key", authenticatorKey);
                }
                // --- END CORRECTED CODE ---

                byte[] fileBytes;
                try
                {
                    fileBytes = JsonSerializer.SerializeToUtf8Bytes(personalData, new JsonSerializerOptions { WriteIndented = true }); // Added indentation for readability
                }
                catch (JsonException ex)
                {
                    downloadLogger.LogError(ex, "Error serializing personal data for user ID '{UserId}'.", userId);
                    // Return a server error response instead of letting it crash
                    return Results.Problem("Error generating personal data file.", statusCode: 500);
                }


                context.Response.Headers.TryAdd("Content-Disposition", "attachment; filename=PersonalData.json");
                return TypedResults.File(fileBytes, contentType: "application/json", fileDownloadName: "PersonalData.json");
            });

            return accountGroup; // Return the group convention builder
        }
    }
}