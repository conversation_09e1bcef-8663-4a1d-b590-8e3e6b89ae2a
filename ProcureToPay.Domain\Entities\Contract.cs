using System;
using System.Collections.Generic;
using ProcureToPay.Domain.Enums;       // Required for ContractStatus
using ProcureToPay.Domain.ValueObjects; // Required for Money, potentially others
using ProcureToPay.Domain.Events;       // Assuming Contract domain events namespace exists
using ProcureToPay.Domain.Exceptions;   // Assuming DomainStateException exists

namespace ProcureToPay.Domain.Entities
{
    /// <summary>
    /// Represents a formal agreement with a Vendor for goods or services.
    /// Includes terms, milestones, SLAs, versioning, and compliance tracking.
    /// </summary>
    public class Contract : BaseEntity<Guid> // Assuming BaseEntity<Guid> provides Id and event handling
    {
        // --- Core Details ---
        public string ContractNumber { get; private set; } = null!;
        public string Title { get; private set; } = null!;
        public string? ContractType { get; private set; }
        public Guid VendorId { get; private set; }
        public DateTime StartDate { get; private set; }
        public DateTime EndDate { get; private set; } // Changed from Required to allow flexibility if needed, validate in constructor/methods
        public Money? TotalContractValue { get; private set; } // Changed to Money VO, nullable
        public ContractStatus Status { get; private set; }

        // --- Terms ---
        public string? PaymentTerms { get; private set; }
        public string? RenewalTerms { get; private set; } // Textual description
        public bool IsAutoRenew { get; private set; }      // Added: Flag for automatic renewal
        public string? TerminationPenaltyTerms { get; private set; } // Added: Specific penalty terms
        public string? TermsAndConditions { get; private set; } // General T&Cs

        // --- Scheduling & Tracking ---
        /// <summary>
        /// Stores milestone payment schedule as JSON. Requires external logic for parsing/management.
        /// Alternative: List<ContractMilestone> with separate entity/VO and OwnsMany/relationship config.
        /// </summary>
        public string? MilestonesJson { get; private set; } // Added: For Milestone Scheduling

        /// <summary>
        /// Stores Service Level Agreement details as JSON. Requires external logic.
        /// Alternative: Dedicated SLA properties or List<SlaMetric> with OwnsMany/relationship config.
        /// </summary>
        public string? SlaDetailsJson { get; private set; } // Added: For SLA Tracking

        // --- Compliance & Performance ---
        /// <summary>
        /// Stores links/references to compliance documents as JSON. Requires external logic.
        /// Alternative: List<DocumentLink> with OwnsMany/relationship config.
        /// </summary>
        public string? ComplianceDocumentLinksJson { get; private set; } // Added: For Compliance Docs

        /// <summary>
        /// Snapshot of relevant vendor performance score at time of contract agreement or review period.
        /// </summary>
        public decimal? VendorPerformanceScoreSnapshot { get; private set; } // Added: For Perf Scoring

        // --- Versioning & Lifecycle ---
        public int Version { get; private set; }       // Added: For Versioning
        public bool IsDeleted { get; private set; }   // Added: For Soft Delete
        public Guid? TenantId { get; private set; }  // Added: For Tenant Isolation/Sharing

        // --- Navigation Properties ---
        public virtual Vendor Vendor { get; private set; } = null!;
        public virtual ICollection<PurchaseOrder> PurchaseOrders { get; private set; } = new List<PurchaseOrder>(); // Renamed for consistency


        // --- Constructor ---
        private Contract() : base(Guid.NewGuid()) { } // For EF Core

        /// <summary>
        /// Creates a new Contract in Draft status.
        /// </summary>
        public Contract(
            Guid id,
            string contractNumber,
            string title,
            Guid vendorId,
            DateTime startDate,
            DateTime endDate,
            string currencyCode, // Required for initializing Money VO
            Guid? tenantId = null,
            string? contractType = null,
            decimal? totalContractValueAmount = null, // Accept decimal amount
            string? paymentTerms = null,
            string? renewalTerms = null,
            bool isAutoRenew = false,
            string? terminationPenaltyTerms = null,
            string? termsAndConditions = null,
            string? milestonesJson = null, // Optional initial values
            string? slaDetailsJson = null,
            string? complianceDocumentLinksJson = null,
            decimal? vendorPerformanceScoreSnapshot = null
            ) : base(id)
        {
            // Validation
            ArgumentException.ThrowIfNullOrWhiteSpace(contractNumber);
            ArgumentException.ThrowIfNullOrWhiteSpace(title);
            if (vendorId == Guid.Empty) throw new ArgumentException("VendorId cannot be empty.", nameof(vendorId));
            if (endDate < startDate) throw new ArgumentException("End date cannot be before start date.", nameof(endDate));
            if (totalContractValueAmount.HasValue && totalContractValueAmount < 0) throw new ArgumentOutOfRangeException(nameof(totalContractValueAmount), "Total contract value cannot be negative.");
            ArgumentException.ThrowIfNullOrWhiteSpace(currencyCode); // Need currency for Money VO

            ContractNumber = contractNumber;
            Title = title;
            VendorId = vendorId;
            StartDate = startDate.Date;
            EndDate = endDate.Date;
            TenantId = tenantId;
            ContractType = contractType;
            TotalContractValue = totalContractValueAmount.HasValue ? new Money(totalContractValueAmount.Value, currencyCode) : null;
            PaymentTerms = paymentTerms;
            RenewalTerms = renewalTerms;
            IsAutoRenew = isAutoRenew;
            TerminationPenaltyTerms = terminationPenaltyTerms;
            TermsAndConditions = termsAndConditions;
            Status = ContractStatus.Draft; // Start as Draft
            MilestonesJson = milestonesJson;
            SlaDetailsJson = slaDetailsJson;
            ComplianceDocumentLinksJson = complianceDocumentLinksJson;
            VendorPerformanceScoreSnapshot = vendorPerformanceScoreSnapshot;
            Version = 1; // Initial version
            IsDeleted = false;

            AddDomainEvent(new ContractCreatedEvent(this.Id, this.TenantId));
        }

        // --- Domain Logic Methods ---

        public void UpdateDetails(
            string title,
            DateTime startDate,
            DateTime endDate,
            string? contractType,
            string? paymentTerms,
            string? renewalTerms,
            bool isAutoRenew,
            string? terminationPenaltyTerms,
            string? termsAndConditions
            )
        {
            // Add validation...
            ArgumentException.ThrowIfNullOrWhiteSpace(title);
            if (endDate < startDate) throw new ArgumentException("End date cannot be before start date.", nameof(endDate));

            // Check if updatable based on Status?
            // if (Status != ContractStatus.Draft) throw new DomainStateException("Cannot update details unless contract is in Draft status.");

            bool changed = (Title != title || StartDate != startDate.Date || EndDate != endDate.Date || ContractType != contractType ||
                           PaymentTerms != paymentTerms || RenewalTerms != renewalTerms || IsAutoRenew != isAutoRenew ||
                           TerminationPenaltyTerms != terminationPenaltyTerms || TermsAndConditions != termsAndConditions);

            Title = title;
            StartDate = startDate.Date;
            EndDate = endDate.Date;
            ContractType = contractType;
            PaymentTerms = paymentTerms;
            RenewalTerms = renewalTerms;
            IsAutoRenew = isAutoRenew;
            TerminationPenaltyTerms = terminationPenaltyTerms;
            TermsAndConditions = termsAndConditions;

            if (changed)
            {
                IncrementVersion();
                AddDomainEvent(new ContractDetailsUpdatedEvent(this.Id));
            }
        }

        public void UpdateValue(Money? newValue) // Use Money VO
        {
            // Add validation, check status?
            if (newValue?.Amount < 0) throw new ArgumentOutOfRangeException(nameof(newValue), "Total contract value cannot be negative.");
            // Potentially check currency matches original if applicable?

            if (TotalContractValue == newValue) return;

            TotalContractValue = newValue;
            IncrementVersion();
            AddDomainEvent(new ContractValueUpdatedEvent(this.Id, newValue));
        }

        public void UpdateMilestones(string? milestonesJson)
        {
            // Add JSON validation if possible/needed
            if (MilestonesJson == milestonesJson) return;
            MilestonesJson = milestonesJson;
            IncrementVersion();
            AddDomainEvent(new ContractMilestonesUpdatedEvent(this.Id));
        }

        public void UpdateSlaDetails(string? slaDetailsJson)
        {
            if (SlaDetailsJson == slaDetailsJson) return;
            SlaDetailsJson = slaDetailsJson;
            IncrementVersion();
            AddDomainEvent(new ContractSlaUpdatedEvent(this.Id));
        }

        public void UpdateComplianceLinks(string? linksJson)
        {
            if (ComplianceDocumentLinksJson == linksJson) return;
            ComplianceDocumentLinksJson = linksJson;
            IncrementVersion();
            AddDomainEvent(new ContractComplianceLinksUpdatedEvent(this.Id));
        }

        public void RecordPerformanceScore(decimal? score)
        {
            // Add validation (e.g., range)
            if (VendorPerformanceScoreSnapshot == score) return;
            VendorPerformanceScoreSnapshot = score;
            IncrementVersion();
            AddDomainEvent(new ContractPerformanceScoreRecordedEvent(this.Id, score));
        }


        public void Activate()
        {
            if (Status == ContractStatus.Draft || Status == ContractStatus.PendingApproval)
            {
                Status = ContractStatus.Active;
                IncrementVersion();
                AddDomainEvent(new ContractStatusChangedEvent(this.Id, Status));
            }
            else
            {
                throw new DomainStateException($"Cannot activate a contract with status '{Status}'.");
            }
        }

        public void Expire()
        {
            if (Status == ContractStatus.Active)
            {
                // Optional: Add check for EndDate
                // if (DateTime.UtcNow.Date <= EndDate) throw new DomainStateException($"Contract cannot be expired before its end date ({EndDate.ToShortDateString()}).");

                Status = ContractStatus.Expired;
                IncrementVersion();
                AddDomainEvent(new ContractStatusChangedEvent(this.Id, Status));
            }
            else
            {
                throw new DomainStateException($"Cannot expire a contract with status '{Status}'.");
            }
        }

        public void Terminate(string reason)
        {
            if (Status == ContractStatus.Active || Status == ContractStatus.PendingApproval)
            {
                ArgumentException.ThrowIfNullOrWhiteSpace(reason);
                Status = ContractStatus.Terminated;
                IncrementVersion();
                AddDomainEvent(new ContractTerminatedEvent(this.Id, reason));
            }
            else
            {
                throw new DomainStateException($"Cannot terminate a contract with status '{Status}'.");
            }
        }

        public void MarkAsDeleted()
        {
            if (IsDeleted) return;
            IsDeleted = true;
            // Status = ContractStatus.Terminated; // Or another appropriate status?
            IncrementVersion();
            AddDomainEvent(new ContractDeletedEvent(this.Id));
        }

        public void Restore()
        {
            if (!IsDeleted) return;
            IsDeleted = false;
            // Consider resetting Status if appropriate
            IncrementVersion();
            AddDomainEvent(new ContractRestoredEvent(this.Id));
        }

        private void IncrementVersion()
        {
            Version++;
            // AddDomainEvent(new ContractVersionIncrementedEvent(this.Id, this.Version)); // Optional specific event
        }
    }

    // --- Placeholder Domain Event Definitions (Place in Domain/Events) ---
    /*
    namespace ProcureToPay.Domain.Events
    {
        public record ContractCreatedEvent(Guid ContractId, Guid? TenantId);
        public record ContractDetailsUpdatedEvent(Guid ContractId);
        public record ContractValueUpdatedEvent(Guid ContractId, Money? NewValue);
        public record ContractStatusChangedEvent(Guid ContractId, ContractStatus NewStatus);
        public record ContractMilestonesUpdatedEvent(Guid ContractId);
        public record ContractSlaUpdatedEvent(Guid ContractId);
        public record ContractComplianceLinksUpdatedEvent(Guid ContractId);
        public record ContractPerformanceScoreRecordedEvent(Guid ContractId, decimal? Score);
        public record ContractTerminatedEvent(Guid ContractId, string Reason);
        public record ContractDeletedEvent(Guid ContractId);
        public record ContractRestoredEvent(Guid ContractId);
        // public record ContractVersionIncrementedEvent(Guid ContractId, int NewVersion); // Optional
    }
    */

    // --- Placeholder Value Object/Record Definitions (Place in Domain/ValueObjects) ---
    /*
    public record ContractMilestone(string Name, DateTime DueDate, Money Amount, string? Description);
    public record SlaMetric(string MetricName, string Target, string MeasurementPeriod);
    public record DocumentLink(string Name, string Url, string? Description);
    */
}
