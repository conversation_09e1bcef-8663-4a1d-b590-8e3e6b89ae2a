﻿namespace ProcureToPay.Domain.Enums
{
    /// <summary>
    /// Represents the lifecycle status of a Budget.
    /// </summary>
    public enum BudgetStatus
    {
        /// <summary>
        /// Budget is being planned or created, not yet ready for approval or use.
        /// </summary>
        Draft = 0,

        /// <summary>
        /// Budget has been submitted for review and approval.
        /// (Note: The Budget entity code currently uses 'PendingApproval' - recommend standardizing)
        /// </summary>
        Submitted = 1, // Corresponds to 'PendingApproval' used in Budget entity logic

        /// <summary>
        /// Budget has been approved and is active for allocations and consumption tracking.
        /// </summary>
        Approved = 2,

        /// <summary>
        /// Budget was rejected during the approval process.
        /// </summary>
        Rejected = 3,

        /// <summary>
        /// Budget period has ended, or the budget has been manually closed. No further activity allowed.
        /// </summary>
        Closed = 4,

        /// <summary>
        /// Budget period has passed (relevant if not manually closed).
        /// </summary>
        Expired = 5 // Note: Not explicitly used in current Budget entity methods, but defined in enum.
    }
}
