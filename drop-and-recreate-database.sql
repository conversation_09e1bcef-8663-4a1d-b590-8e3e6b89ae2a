-- <PERSON><PERSON><PERSON> to drop all tables and recreate the database from scratch

-- First, drop all tables in all schemas
DO $$ 
DECLARE
    r RECORD;
BEGIN
    -- Disable all triggers
    EXECUTE 'SET session_replication_role = replica';
    
    -- Drop all tables in all schemas
    FOR r IN (
        SELECT 
            table_schema, 
            table_name 
        FROM 
            information_schema.tables 
        WHERE 
            table_type = 'BASE TABLE' AND 
            table_schema NOT IN ('pg_catalog', 'information_schema')
    ) 
    LOOP
        EXECUTE format('DROP TABLE IF EXISTS %I.%I CASCADE', r.table_schema, r.table_name);
    END LOOP;
    
    -- Drop the EF Core migrations history table specifically
    EXECUTE 'DROP TABLE IF EXISTS "__EFMigrationsHistory" CASCADE';
    
    -- Re-enable triggers
    EXECUTE 'SET session_replication_role = DEFAULT';
END $$;

-- Confirm all tables are dropped
SELECT 
    table_schema, 
    table_name 
FROM 
    information_schema.tables 
WHERE 
    table_type = 'BASE TABLE' AND 
    table_schema NOT IN ('pg_catalog', 'information_schema');
