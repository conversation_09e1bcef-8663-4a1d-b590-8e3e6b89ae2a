﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ProcureToPay.Domain.Events
{
    public record TechnicalSubmittalCreatedEvent(Guid SubmittalId, Guid TenantId, string SubmittalNumber);
    public record TechnicalSubmittalSubmittedEvent(Guid SubmittalId, int ReviewCycle, string SubmittedById);
    public record TechnicalSubmittalReviewRecordedEvent(Guid SubmittalId, Guid ReviewId, string ReviewerId, SubmittalDisposition Disposition);
    public record TechnicalSubmittalRequiresRevisionEvent(Guid SubmittalId, int CompletedCycle);
    public record TechnicalSubmittalClosedEvent(Guid SubmittalId, SubmittalDisposition FinalDisposition, string? FinalSignOffById);
    public record TechnicalSubmittalStatusChangedEvent(Guid SubmittalId, TechnicalSubmittalStatus OldStatus, TechnicalSubmittalStatus NewStatus);
    public record TechnicalSubmittalSupersededEvent(Guid SubmittalId, Guid? SupersedingSubmittalId);
    public record TechnicalSubmittalCancelledEvent(Guid SubmittalId);
    public record TechnicalSubmittalDeletedEvent(Guid SubmittalId);
    public record TechnicalSubmittalRestoredEvent(Guid SubmittalId);
}