@using ProcureToPay.Application.Features.Categories.DTOs
@using ProcureToPay.Application.Features.Categories.Services
@using System.ComponentModel.DataAnnotations

@inject ICategoryService CategoryService
@inject NavigationManager NavigationManager

<div class="card">
    <div class="card-body">
        @if (_isLoading)
        {
            <div class="d-flex justify-content-center">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
        }
        else
        {
            <EditForm Model="_model" OnValidSubmit="HandleValidSubmit">
                <DataAnnotationsValidator />

                @if (!string.IsNullOrEmpty(_errorMessage))
                {
                    <div class="alert alert-danger">
                        @_errorMessage
                    </div>
                }

                <div class="mb-3">
                    <label for="name" class="form-label">Name</label>
                    <InputText id="name" @bind-Value="_model.Name" class="form-control" />
                    <ValidationMessage For="() => _model.Name" class="text-danger" />
                </div>

                <div class="mb-3">
                    <label for="code" class="form-label">Code</label>
                    <InputText id="code" @bind-Value="_model.Code" class="form-control" />
                    <ValidationMessage For="() => _model.Code" class="text-danger" />
                    <div class="form-text">Optional. A unique code for this category.</div>
                </div>

                <div class="mb-3">
                    <label for="unspscCode" class="form-label">UNSPSC Code</label>
                    <InputText id="unspscCode" @bind-Value="_model.UnspscCode" class="form-control" />
                    <ValidationMessage For="() => _model.UnspscCode" class="text-danger" />
                    <div class="form-text">Optional. United Nations Standard Products and Services Code.</div>
                </div>

                <div class="mb-3">
                    <label for="parentCategoryId" class="form-label">Parent Category</label>
                    <InputSelect id="parentCategoryId" @bind-Value="_model.ParentCategoryId" class="form-select">
                        <option value="">-- None --</option>
                        @foreach (var category in _parentCategoryCandidates)
                        {
                            <option value="@category.Id">@category.Name</option>
                        }
                    </InputSelect>
                    <div class="form-text">Optional. Select a parent category if this is a subcategory.</div>
                </div>

                <div class="mb-3">
                    <label for="description" class="form-label">Description</label>
                    <InputTextArea id="description" @bind-Value="_model.Description" class="form-control" rows="3" />
                    <ValidationMessage For="() => _model.Description" class="text-danger" />
                </div>

                <div class="d-flex justify-content-between">
                    <button type="button" class="btn btn-secondary" @onclick="@(() => NavigationManager.NavigateTo("/categories"))">
                        Cancel
                    </button>
                    <button type="submit" class="btn btn-primary">
                        @(_isEditMode ? "Update" : "Create") Category
                    </button>
                </div>
            </EditForm>
        }
    </div>
</div>

@code {
    [Parameter]
    public Guid? CategoryIdToEdit { get; set; }

    [Parameter]
    public EventCallback OnValidSubmitCallback { get; set; }

    private CategoryFormModel _model = new();
    private IEnumerable<CategoryDto> _parentCategoryCandidates = new List<CategoryDto>();
    private bool _isLoading = true;
    private string? _errorMessage;

    private bool _isEditMode => CategoryIdToEdit.HasValue;

    protected override async Task OnInitializedAsync()
    {
        try
        {
            _isLoading = true;

            // Load parent category candidates
            _parentCategoryCandidates = await CategoryService.GetParentCategoryCandidatesAsync(CategoryIdToEdit);

            // If in edit mode, load the category to edit
            if (_isEditMode)
            {
                var category = await CategoryService.GetCategoryByIdAsync(CategoryIdToEdit!.Value);
                if (category != null)
                {
                    _model = new CategoryFormModel
                    {
                        Id = category.Id,
                        Name = category.Name,
                        Description = category.Description,
                        Code = category.Code,
                        UnspscCode = category.UnspscCode,
                        ParentCategoryId = category.ParentCategoryId
                    };
                }
                else
                {
                    _errorMessage = "Category not found.";
                    NavigationManager.NavigateTo("/categories");
                }
            }
        }
        catch (Exception ex)
        {
            _errorMessage = $"Error loading data: {ex.Message}";
        }
        finally
        {
            _isLoading = false;
        }
    }

    private async Task HandleValidSubmit()
    {
        _errorMessage = null;

        try
        {
            if (_isEditMode)
            {
                var updateRequest = new UpdateCategoryRequest
                {
                    Id = _model.Id!.Value,
                    Name = _model.Name,
                    Description = _model.Description,
                    Code = _model.Code,
                    UnspscCode = _model.UnspscCode,
                    ParentCategoryId = _model.ParentCategoryId
                };

                var (updatedCategory, errors) = await CategoryService.UpdateCategoryAsync(updateRequest);

                if (errors.Any())
                {
                    _errorMessage = string.Join(", ", errors);
                    return;
                }

                if (updatedCategory != null)
                {
                    await OnValidSubmitCallback.InvokeAsync();
                }
            }
            else
            {
                var createRequest = new CreateCategoryRequest
                {
                    Name = _model.Name,
                    Description = _model.Description,
                    Code = _model.Code,
                    UnspscCode = _model.UnspscCode,
                    ParentCategoryId = _model.ParentCategoryId
                };

                var (createdCategory, errors) = await CategoryService.CreateCategoryAsync(createRequest);

                if (errors.Any())
                {
                    _errorMessage = string.Join(", ", errors);
                    return;
                }

                if (createdCategory != null)
                {
                    await OnValidSubmitCallback.InvokeAsync();
                }
            }
        }
        catch (Exception ex)
        {
            _errorMessage = $"Error saving category: {ex.Message}";
        }
    }

    private class CategoryFormModel
    {
        public Guid? Id { get; set; }

        [Required(ErrorMessage = "Name is required")]
        [StringLength(200, ErrorMessage = "Name cannot be longer than 200 characters")]
        public string Name { get; set; } = string.Empty;

        public string? Description { get; set; }

        [StringLength(50, ErrorMessage = "Code cannot be longer than 50 characters")]
        public string? Code { get; set; }

        [StringLength(20, ErrorMessage = "UNSPSC code cannot be longer than 20 characters")]
        public string? UnspscCode { get; set; }

        public Guid? ParentCategoryId { get; set; }
    }
}
