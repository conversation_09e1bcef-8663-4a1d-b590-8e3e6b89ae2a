using Microsoft.EntityFrameworkCore;
using ProcureToPay.Domain.Entities;
using ProcureToPay.Domain.Interfaces;
using System.Linq.Expressions;

namespace ProcureToPay.Tests
{
    /// <summary>
    /// A simplified version of ApplicationDbContext for testing purposes.
    /// Only includes the TestEntity to avoid issues with complex entity types.
    /// </summary>
    public class TestApplicationDbContext : DbContext
    {
        private readonly ITenantProvider _tenantProvider;
        private readonly Guid? _currentTenantId;

        public DbSet<TestEntity> TestEntities { get; set; } = null!;

        public TestApplicationDbContext(
            DbContextOptions<TestApplicationDbContext> options,
            ITenantProvider tenantProvider)
            : base(options)
        {
            _tenantProvider = tenantProvider ?? throw new ArgumentNullException(nameof(tenantProvider));
            _currentTenantId = _tenantProvider.GetCurrentTenantId();
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure global query filter for TestEntity
            ConfigureTenantQueryFilters(modelBuilder);
        }

        private void ConfigureTenantQueryFilters(ModelBuilder modelBuilder)
        {
            // Check if the entity implements the ITenantEntity interface
            var entityType = modelBuilder.Model.FindEntityType(typeof(TestEntity));
            if (entityType != null)
            {
                // Build the lambda expression for the query filter dynamically
                var parameter = Expression.Parameter(typeof(TestEntity), "e");

                // Check if we're in a migration or design-time context
                bool isMigrationOrDesignTime = _currentTenantId == null;

                if (isMigrationOrDesignTime)
                {
                    // During migrations or design-time operations, don't apply tenant filtering
                    var trueExpression = Expression.Constant(true);
                    var lambda = Expression.Lambda<Func<TestEntity, bool>>(trueExpression, parameter);
                    modelBuilder.Entity<TestEntity>().HasQueryFilter(lambda);
                }
                else
                {
                    // In normal operation with a valid tenant, apply tenant filtering
                    var property = Expression.Property(parameter, nameof(ITenantEntity.TenantId));
                    // We've already checked that _currentTenantId has a value in the if statement
                    var tenantIdValue = Expression.Constant(_currentTenantId!.Value);
                    var equal = Expression.Equal(property, tenantIdValue);
                    var lambda = Expression.Lambda<Func<TestEntity, bool>>(equal, parameter);
                    modelBuilder.Entity<TestEntity>().HasQueryFilter(lambda);
                }
            }
        }

        public override int SaveChanges()
        {
            SetTenantIdBeforeSaving();
            return base.SaveChanges();
        }

        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            SetTenantIdBeforeSaving();
            return await base.SaveChangesAsync(cancellationToken);
        }

        private void SetTenantIdBeforeSaving()
        {
            // Get all Added or Modified entries that implement ITenantEntity
            var entries = ChangeTracker.Entries<ITenantEntity>()
                .Where(e => e.State == EntityState.Added || e.State == EntityState.Modified);

            // If there are no tenant entities being modified, we don't need to do anything
            if (!entries.Any())
            {
                return;
            }

            // Check if we're in a migration or design-time context
            bool isMigrationOrDesignTime = _currentTenantId == null;

            if (isMigrationOrDesignTime)
            {
                // During migrations or design-time operations, use a default tenant ID
                Guid defaultTenantId = Guid.Parse("11111111-1111-1111-1111-111111111111");

                foreach (var entry in entries)
                {
                    // Only set the TenantId if it's not already set
                    if (entry.Entity.TenantId == Guid.Empty)
                    {
                        entry.Entity.TenantId = defaultTenantId;
                    }
                }
            }
            else
            {
                // In normal operation with a valid tenant, set the tenant ID
                // Make sure _currentTenantId is not null before accessing .Value
                if (_currentTenantId.HasValue)
                {
                    foreach (var entry in entries)
                    {
                        entry.Entity.TenantId = _currentTenantId.Value;
                    }
                }
                else
                {
                    // This should not happen in normal operation, but handle it gracefully
                    throw new InvalidOperationException("No tenant ID is available, but tenant entities are being saved. This indicates a configuration issue with the tenant provider.");
                }
            }
        }
    }
}
