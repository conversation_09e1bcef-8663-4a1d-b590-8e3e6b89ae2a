using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProcureToPay.Domain.Entities;

namespace ProcureToPay.Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// EF Core Configuration for the TenantProduct entity.
    /// </summary>
    public class TenantProductConfiguration : IEntityTypeConfiguration<TenantProduct>
    {
        public void Configure(EntityTypeBuilder<TenantProduct> builder)
        {
            builder.ToTable("tenant_products");

            // Key configuration
            builder.HasKey(tp => tp.Id);

            // Properties configuration
            builder.Property(tp => tp.Name)
                .IsRequired()
                .HasMaxLength(200);

            builder.Property(tp => tp.SKU)
                .HasMaxLength(50);

            builder.Property(tp => tp.Description)
                .HasMaxLength(1000);

            builder.Property(tp => tp.Price)
                .HasColumnType("decimal(18,2)")
                .IsRequired();

            // Tenant isolation
            builder.Property(tp => tp.TenantId)
                .IsRequired();

            builder.HasIndex(tp => tp.TenantId);
        }
    }
}
