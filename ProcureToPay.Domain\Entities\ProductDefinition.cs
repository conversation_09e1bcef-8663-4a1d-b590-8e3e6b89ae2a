using System;
using System.Collections.Generic;
using ProcureToPay.Domain.Enums;
using ProcureToPay.Domain.Events;
using ProcureToPay.Domain.Exceptions; // Assuming DomainStateException exists

namespace ProcureToPay.Domain.Entities
{
    /// <summary>
    /// Represents the master definition of a product or service, independent of any specific vendor.
    /// Includes support for versioning, soft delete, dynamic attributes, and multi-tenancy.
    /// </summary>
    public class ProductDefinition : BaseEntity<Guid>
    {
        /// <summary>
        /// The primary display name of the product definition.
        /// </summary>
        public string Name { get; private set; } = null!;

        /// <summary>
        /// A detailed description of the product definition.
        /// </summary>
        public string? Description { get; private set; }

        /// <summary>
        /// The Master Stock Keeping Unit (SKU) - a unique internal identifier for the definition.
        /// </summary>
        public string Sku { get; private set; } = null!;

        /// <summary>
        /// Global Trade Item Number (GTIN). Optional.
        /// </summary>
        public string? Gtin { get; private set; }

        /// <summary>
        /// Universal Product Code (UPC). Optional.
        /// </summary>
        public string? Upc { get; private set; }

        /// <summary>
        /// European Article Number (EAN). Optional.
        /// </summary>
        public string? Ean { get; private set; }

        /// <summary>
        /// The current stage in the product definition's lifecycle.
        /// </summary>
        public ProductLifecycleState LifecycleState { get; private set; }

        /// <summary>
        /// Foreign key for the product definition's category. Optional.
        /// </summary>
        public Guid? CategoryId { get; private set; }

        /// <summary>
        /// Flag indicating if the definition has been soft-deleted.
        /// </summary>
        public bool IsDeleted { get; private set; } // Added for Soft Delete

        /// <summary>
        /// Version number to track significant changes to the definition.
        /// </summary>
        public int Version { get; private set; } // Added for Versioning

        /// <summary>
        /// JSON string storing dynamic attributes associated with this definition.
        /// Structure and validation are handled by application/domain logic.
        /// </summary>
        public string? AttributesJson { get; private set; } // Added for Dynamic Attributes

        /// <summary>
        /// Optional TenantId for multi-tenant isolation. Null indicates a shared definition.
        /// </summary>
        public Guid? TenantId { get; private set; } // Added for Tenant Isolation/Sharing


        // --- Navigation Properties ---

        /// <summary>
        /// Navigation property to the product definition's category. Virtual for lazy loading.
        /// </summary>
        public virtual Category? Category { get; private set; }

        /// <summary>
        /// Navigation property to the vendor-specific offerings of this product definition. Virtual for lazy loading.
        /// </summary>
        public virtual ICollection<VendorProduct> VendorProducts { get; private set; } = new List<VendorProduct>();


        /// <summary>
        /// Private constructor for EF Core.
        /// </summary>
        private ProductDefinition() : base(Guid.NewGuid()) { }

        /// <summary>
        /// Creates a new ProductDefinition instance.
        /// </summary>
        /// <param name="id">Identifier.</param>
        /// <param name="name">Name.</param>
        /// <param name="sku">Master SKU.</param>
        /// <param name="tenantId">Optional Tenant ID for isolation.</param> // Added
        /// <param name="description">Optional description.</param>
        /// <param name="categoryId">Optional category ID.</param>
        /// <param name="gtin">Optional GTIN.</param>
        /// <param name="upc">Optional UPC.</param>
        /// <param name="ean">Optional EAN.</param>
        /// <param name="initialState">Initial lifecycle state (defaults to Active).</param>
        /// <param name="initialAttributesJson">Optional initial JSON attributes.</param> // Added
        public ProductDefinition(
            Guid id,
            string name,
            string sku,
            Guid? tenantId = null, // Added
            string? description = null,
            Guid? categoryId = null,
            string? gtin = null,
            string? upc = null,
            string? ean = null,
            ProductLifecycleState initialState = ProductLifecycleState.Active,
            string? initialAttributesJson = null // Added
            ) : base(id)
        {
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
            if (string.IsNullOrWhiteSpace(sku)) throw new ArgumentNullException(nameof(sku));

            Name = name;
            Sku = sku;
            TenantId = tenantId; // Assign TenantId
            Description = description;
            CategoryId = categoryId;
            Gtin = gtin;
            Upc = upc;
            Ean = ean;
            LifecycleState = initialState;
            AttributesJson = initialAttributesJson; // Assign initial attributes

            IsDeleted = false; // Initialize soft delete flag
            Version = 1; // Initial version

            AddDomainEvent(new ProductDefinitionCreatedEvent(this.Id, this.TenantId));
        }

        // --- Domain Methods ---

        /// <summary>
        /// Updates the descriptive details, increments version.
        /// </summary>
        public void UpdateDescription(string name, string? description)
        {
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
            bool changed = (Name != name || Description != description);

            Name = name;
            Description = description;

            if (changed) IncrementVersion();
            AddDomainEvent(new ProductDefinitionDescriptionUpdatedEvent(this.Id));
        }

        /// <summary>
        /// Updates the identifying codes, increments version.
        /// </summary>
        public void UpdateIdentifiers(string sku, string? gtin, string? upc, string? ean)
        {
            if (string.IsNullOrWhiteSpace(sku)) throw new ArgumentNullException(nameof(sku));
            // Add validation for formats if needed before assigning

            bool changed = (Sku != sku || Gtin != gtin || Upc != upc || Ean != ean);

            Sku = sku;
            Gtin = gtin;
            Upc = upc;
            Ean = ean;

            if (changed) IncrementVersion();
            AddDomainEvent(new ProductDefinitionIdentifiersUpdatedEvent(this.Id));
        }

        /// <summary>
        /// Updates the lifecycle state, increments version.
        /// </summary>
        public void UpdateLifecycleState(ProductLifecycleState newState)
        {
            // Add validation logic here if needed (e.g., allowed transitions)
            if (LifecycleState == newState) return;

            LifecycleState = newState;
            IncrementVersion();
            AddDomainEvent(new ProductDefinitionLifecycleStateChangedEvent(this.Id, newState));
        }

        /// <summary>
        /// Assigns or updates the category, increments version.
        /// </summary>
        public void AssignCategory(Guid? categoryId)
        {
            if (CategoryId == categoryId) return;
            // Add validation if needed (e.g., ensure category exists - Application layer?)

            CategoryId = categoryId;
            IncrementVersion();
            AddDomainEvent(new ProductDefinitionCategoryAssignedEvent(this.Id, categoryId));
        }

        /// <summary>
        /// Updates the dynamic attributes JSON, increments version.
        /// </summary>
        /// <param name="attributesJson">The new JSON string representing attributes.</param>
        public void UpdateAttributes(string? attributesJson)
        {
            // Add JSON validation logic here or in Application layer if needed
            if (AttributesJson == attributesJson) return;

            AttributesJson = attributesJson;
            IncrementVersion();
            AddDomainEvent(new ProductDefinitionAttributesUpdatedEvent(this.Id));
        }


        /// <summary>
        /// Marks the product definition as deleted (soft delete).
        /// </summary>
        public void MarkAsDeleted()
        {
            if (IsDeleted) return;
            IsDeleted = true;
            LifecycleState = ProductLifecycleState.Obsolete; // Optionally set lifecycle state too
            IncrementVersion(); // Deletion is a version change
            AddDomainEvent(new ProductDefinitionDeletedEvent(this.Id));
        }

        /// <summary>
        /// Restores a soft-deleted product definition.
        /// </summary>
        public void Restore()
        {
            if (!IsDeleted) return;
            IsDeleted = false;
            // Consider resetting LifecycleState if appropriate (e.g., back to Active or Draft?)
            // LifecycleState = ProductLifecycleState.Active;
            IncrementVersion(); // Restoration is a version change
            AddDomainEvent(new ProductDefinitionRestoredEvent(this.Id));
        }


        /// <summary>
        /// Increments the version number. Called internally when significant changes occur.
        /// </summary>
        private void IncrementVersion()
        {
            // Consider concurrency checks if needed before incrementing
            Version++;
            AddDomainEvent(new ProductDefinitionVersionIncrementedEvent(this.Id, this.Version));
        }

        /// <summary>
        /// Helper method to check if the definition is in a state where it can be ordered/used.
        /// </summary>
        public bool IsOrderable()
        {
            return !IsDeleted && (LifecycleState == ProductLifecycleState.Active || LifecycleState == ProductLifecycleState.PhasedOut); // Example logic
        }
    }

    // --- Placeholder Domain Event Definitions (Place in Domain/Events) ---
    /*
    namespace ProcureToPay.Domain.Events
    {
        public record ProductDefinitionCreatedEvent(Guid ProductDefinitionId, Guid? TenantId);
        public record ProductDefinitionDescriptionUpdatedEvent(Guid ProductDefinitionId);
        public record ProductDefinitionIdentifiersUpdatedEvent(Guid ProductDefinitionId);
        public record ProductDefinitionLifecycleStateChangedEvent(Guid ProductDefinitionId, ProductLifecycleState NewState);
        public record ProductDefinitionCategoryAssignedEvent(Guid ProductDefinitionId, Guid? CategoryId);
        public record ProductDefinitionAttributesUpdatedEvent(Guid ProductDefinitionId);
        public record ProductDefinitionDeletedEvent(Guid ProductDefinitionId);
        public record ProductDefinitionRestoredEvent(Guid ProductDefinitionId);
        public record ProductDefinitionVersionIncrementedEvent(Guid ProductDefinitionId, int NewVersion);
    }
    */
}
