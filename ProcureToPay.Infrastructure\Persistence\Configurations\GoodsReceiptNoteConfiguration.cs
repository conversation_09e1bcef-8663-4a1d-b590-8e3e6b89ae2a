using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProcureToPay.Domain.Entities;
using ProcureToPay.Domain.Enums; // Assuming GoodsReceiptNoteStatus enum exists
using ProcureToPay.Infrastructure.Identity; // Assuming ApplicationUser exists for ReceivedByUserId
using ProcureToPay.Infrastructure.Persistence.Extensions;

namespace ProcureToPay.Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// Configuration for the GoodsReceiptNote entity for EF Core and PostgreSQL.
    /// </summary>
    public class GoodsReceiptNoteConfiguration : IEntityTypeConfiguration<GoodsReceiptNote>
    {
        public void Configure(EntityTypeBuilder<GoodsReceiptNote> builder)
        {
            // Table Mapping
            builder.ToTable("goods_receipt_notes");

            // Primary Key
            builder.HasKey(grn => grn.Id);
            builder.Property(grn => grn.Id).ValueGeneratedOnAdd();

            // Properties
            builder.Property(grn => grn.GoodsReceiptNoteNumber)
                .IsRequired()
                .HasMaxLength(100); // Adjust length as needed

            builder.Property(grn => grn.ReceiptDate)
                .IsRequired()
                .HasColumnType("timestamp without time zone");

            builder.Property(grn => grn.InspectionDate)
                .HasColumnType("timestamp without time zone")
                .IsRequired(false); // Inspection might happen later

            builder.Property(grn => grn.Status)
                .IsRequired()
                .HasConversion<string>() // Or HasConversion<int>()
                .HasMaxLength(50); // Adjust size if string

            builder.Property(grn => grn.ReceivedByUserId) // Assuming FK to ApplicationUser (Guid or string)
                .IsRequired();
                // If ApplicationUser Id is string: .HasMaxLength(450);

            builder.Property(grn => grn.ReceivingLocation)
                .HasMaxLength(255)
                .IsRequired(false); // Location might be optional or derived

            // Foreign Keys (Assuming Guid or appropriate types)
            builder.Property(grn => grn.PurchaseOrderId).IsRequired(); // GRN must link to a PO
            builder.Property(grn => grn.DeliveryNoteId).IsRequired(false); // Optional link to DN

            builder.Property(grn => grn.TenantId).IsRequired();


            // --- Concurrency Token ---
            builder.UseXminAsConcurrencyToken();


            // --- Relationships ---
            // Header-Lines (One-to-Many)
            builder.HasMany(grn => grn.Lines)
                   .WithOne(l => l.GoodsReceiptNote) // Assuming GRNLine has GoodsReceiptNote nav prop
                   .HasForeignKey(l => l.GoodsReceiptNoteId)
                   .IsRequired()
                   .OnDelete(DeleteBehavior.Cascade); // Deleting header deletes lines

            // Link to Origin (Many-to-One)
            builder.HasOne(grn => grn.PurchaseOrder)
                   .WithMany(po => po.GoodsReceiptNotes) // Assuming PO has collection
                   .HasForeignKey(grn => grn.PurchaseOrderId)
                   .IsRequired()
                   .OnDelete(DeleteBehavior.Restrict); // Prevent deleting PO if linked GRN exists

            // Link to Delivery (Many-to-One, Optional)
            builder.HasOne(grn => grn.DeliveryNote)
                   .WithMany() // Assuming DN doesn't have direct nav back
                   .HasForeignKey(grn => grn.DeliveryNoteId)
                   .IsRequired(false)
                   .OnDelete(DeleteBehavior.SetNull); // Nullify link if DN deleted

            // Link to User (Many-to-One)
            // Assuming ApplicationUser entity exists (adjust type if not Guid)
            builder.HasOne<ProcureToPay.Infrastructure.Identity.ApplicationUser>() // Or your specific user entity type
                   .WithMany() // Assuming User doesn't have direct nav back
                   .HasForeignKey(grn => grn.ReceivedByUserId)
                   .IsRequired()
                   .OnDelete(DeleteBehavior.Restrict); // Prevent deleting user if they received goods


            // --- Indexes ---
            builder.HasIndex(grn => grn.GoodsReceiptNoteNumber).IsUnique();
            builder.HasIndex(grn => grn.Status);
            builder.HasIndex(grn => grn.ReceiptDate);
            builder.HasIndex(grn => grn.PurchaseOrderId);
            builder.HasIndex(grn => grn.DeliveryNoteId);
            builder.HasIndex(grn => grn.ReceivedByUserId);
            builder.HasIndex(grn => grn.TenantId);


            // --- TODO ---
            // TODO: Verify FK types (PurchaseOrderId, DeliveryNoteId, ReceivedByUserId) match related entities.
            // TODO: Confirm relationship multiplicity and delete behaviors.
        }
    }
}
