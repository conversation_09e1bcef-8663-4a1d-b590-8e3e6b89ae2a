using System;
using System.Collections.Generic;
using ProcureToPay.Domain.Enums;       // For AllocationStatus
using ProcureToPay.Domain.ValueObjects; // For Money
using ProcureToPay.Domain.Exceptions;   // Assuming DomainStateException exists
using ProcureToPay.Domain.Events;       // Assuming Budget domain events namespace exists

// Assuming BaseEntity<Guid>, Budget, Department (or Project/CostCenter) entities exist
namespace ProcureToPay.Domain.Entities
{
    /// <summary>
    /// Represents an allocation of funds from a specific Budget to a consuming entity (e.g., Department).
    /// Tracks allocated vs consumed amounts for a given period.
    /// </summary>
    public class BudgetAllocation : BaseEntity<Guid> // Assuming BaseEntity<Guid>
    {
        /// <summary>
        /// Foreign Key to the parent Budget.
        /// </summary>
        public Guid BudgetId { get; private set; }

        /// <summary>
        /// Foreign Key to the Tenant, likely inherited via Budget.
        /// </summary>
        public Guid TenantId { get; private set; }

        /// <summary>
        /// Foreign Key to the Department receiving the allocation.
        /// Could be CostCenterId or ProjectId depending on allocation model.
        /// </summary>
        public Guid DepartmentId { get; private set; } // Example: Allocating to Department

        /// <summary>
        /// The total amount allocated for the period. Uses Money VO.
        /// </summary>
        public Money AllocatedAmount { get; private set; } = null!;

        /// <summary>
        /// The amount consumed or committed against this allocation. Uses Money VO.
        /// </summary>
        public Money ConsumedAmount { get; private set; } = null!;

        /// <summary>
        /// Date the allocation was made or becomes effective.
        /// </summary>
        public DateTime AllocationDate { get; private set; }

        /// <summary>
        /// Optional identifier for the specific fiscal period this allocation applies to (e.g., "2025-Q3").
        /// </summary>
        public string? FiscalPeriodIdentifier { get; private set; }

        /// <summary>
        /// Optional description for the purpose or details of this allocation.
        /// </summary>
        public string? Description { get; private set; }

        /// <summary>
        /// Current status of the allocation (Active, Inactive, Closed).
        /// </summary>
        public AllocationStatus Status { get; private set; }


        // --- Navigation Properties ---

        /// <summary>
        /// Navigation property back to the parent Budget. Virtual for lazy loading.
        /// </summary>
        public virtual Budget Budget { get; private set; } = null!;

        /// <summary>
        /// Navigation property to the Department receiving the allocation. Virtual for lazy loading.
        /// Replace/add Project/CostCenter if needed.
        /// </summary>
        public virtual Department Department { get; private set; } = null!; // Assuming Department entity exists

        // Optional: Direct link to consuming transactions (less common)
        // public virtual ICollection<PurchaseRequisitionLine> ConsumingRequisitionLines { get; private set; } = new List<PurchaseRequisitionLine>();


        /// <summary>
        /// Private constructor for EF Core.
        /// </summary>
        private BudgetAllocation() : base(Guid.NewGuid())
        {
            // Initialize Money VOs to prevent null reference issues
            AllocatedAmount = new Money(0, "XXX"); // Placeholder
            ConsumedAmount = new Money(0, "XXX");  // Placeholder
        }

        /// <summary>
        /// Creates a new Budget Allocation.
        /// </summary>
        /// <param name="id">Allocation identifier.</param>
        /// <param name="tenantId">Tenant identifier.</param>
        /// <param name="budgetId">ID of the parent Budget.</param>
        /// <param name="departmentId">ID of the receiving Department (or Project/CostCenter).</param>
        /// <param name="allocatedAmount">The amount being allocated (Money VO).</param>
        /// <param name="allocationDate">The date the allocation is effective.</param>
        /// <param name="fiscalPeriodIdentifier">Optional identifier for the fiscal period.</param>
        /// <param name="description">Optional description.</param>
        /// <param name="initialStatus">Initial status (defaults to Active).</param>
        public BudgetAllocation(
            Guid id,
            Guid tenantId,
            Guid budgetId,
            Guid departmentId, // Or ProjectId/CostCenterId
            Money allocatedAmount,
            DateTime allocationDate,
            string? fiscalPeriodIdentifier = null,
            string? description = null,
            AllocationStatus initialStatus = AllocationStatus.Active
            ) : base(id)
        {
            // Validation
            if (tenantId == Guid.Empty) throw new ArgumentException("TenantId cannot be empty.", nameof(tenantId));
            if (budgetId == Guid.Empty) throw new ArgumentException("BudgetId cannot be empty.", nameof(budgetId));
            if (departmentId == Guid.Empty) throw new ArgumentException("DepartmentId cannot be empty.", nameof(departmentId)); // Or other target ID
            ArgumentNullException.ThrowIfNull(allocatedAmount);
            if (allocatedAmount.Amount < 0) throw new ArgumentOutOfRangeException(nameof(allocatedAmount), "Allocated amount cannot be negative.");
            if (allocationDate == default) throw new ArgumentException("Allocation date must be specified.", nameof(allocationDate));

            TenantId = tenantId;
            BudgetId = budgetId;
            DepartmentId = departmentId;
            AllocatedAmount = allocatedAmount;
            AllocationDate = allocationDate.Date;
            FiscalPeriodIdentifier = fiscalPeriodIdentifier;
            Description = description;
            Status = initialStatus;

            // Initialize consumed amount to zero in the correct currency
            ConsumedAmount = new Money(0m, allocatedAmount.CurrencyCode);

            AddDomainEvent(new BudgetAllocationCreatedEvent(this.Id, this.BudgetId, this.DepartmentId, this.AllocatedAmount));
        }

        // --- Domain Logic Methods ---

        /// <summary>
        /// Checks if a specific amount can be consumed from the available allocation.
        /// </summary>
        /// <param name="amountToConsume">The amount to check (Money VO).</param>
        /// <returns>True if the amount can be consumed, false otherwise.</returns>
        /// <exception cref="ArgumentNullException">Thrown if amountToConsume is null.</exception>
        /// <exception cref="ArgumentException">Thrown if currency codes do not match.</exception>
        public bool CanConsume(Money amountToConsume)
        {
            ArgumentNullException.ThrowIfNull(amountToConsume);
            if (amountToConsume.CurrencyCode != this.AllocatedAmount.CurrencyCode)
            {
                throw new ArgumentException("Currency mismatch: Cannot consume amount with different currency.", nameof(amountToConsume));
            }

            return Status == AllocationStatus.Active && (ConsumedAmount.Amount + amountToConsume.Amount) <= AllocatedAmount.Amount;
        }

        /// <summary>
        /// Increases the consumed amount for this allocation.
        /// </summary>
        /// <param name="amountToConsume">The amount being consumed (Money VO).</param>
        /// <exception cref="ArgumentNullException">Thrown if amountToConsume is null.</exception>
        /// <exception cref="ArgumentException">Thrown if currency codes do not match.</exception>
        /// <exception cref="DomainStateException">Thrown if allocation is not active or consumption exceeds allocation.</exception>
        public void Consume(Money amountToConsume)
        {
            if (!CanConsume(amountToConsume)) // Also handles null check and currency check
            {
                throw new DomainStateException($"Cannot consume {amountToConsume}: Allocation status is '{Status}' or available amount ({AvailableAmount}) is insufficient.");
            }
            if (amountToConsume.Amount <= 0) throw new ArgumentOutOfRangeException(nameof(amountToConsume), "Amount to consume must be positive.");


            ConsumedAmount += amountToConsume; // Use Money operator overload
            AddDomainEvent(new BudgetAllocationConsumedEvent(this.Id, amountToConsume));
        }

        /// <summary>
        /// Decreases the consumed amount (e.g., when a consuming transaction is cancelled or reduced).
        /// </summary>
        /// <param name="amountToRelease">The amount being released back to the allocation (Money VO).</param>
        /// <exception cref="ArgumentNullException">Thrown if amountToRelease is null.</exception>
        /// <exception cref="ArgumentException">Thrown if currency codes do not match.</exception>
        /// <exception cref="DomainStateException">Thrown if trying to release more than was consumed.</exception>
        public void Release(Money amountToRelease)
        {
            ArgumentNullException.ThrowIfNull(amountToRelease);
            if (amountToRelease.CurrencyCode != this.ConsumedAmount.CurrencyCode)
            {
                throw new ArgumentException("Currency mismatch: Cannot release amount with different currency.", nameof(amountToRelease));
            }
            if (amountToRelease.Amount <= 0) throw new ArgumentOutOfRangeException(nameof(amountToRelease), "Amount to release must be positive.");
            if (amountToRelease.Amount > ConsumedAmount.Amount)
            {
                throw new DomainStateException($"Cannot release {amountToRelease}: Amount exceeds consumed amount ({ConsumedAmount}).");
            }

            ConsumedAmount -= amountToRelease; // Use Money operator overload
            AddDomainEvent(new BudgetAllocationReleasedEvent(this.Id, amountToRelease));
        }

        /// <summary>
        /// Updates the status of the allocation (e.g., to Inactive or Closed).
        /// </summary>
        /// <param name="newStatus">The new status.</param>
        public void UpdateStatus(AllocationStatus newStatus)
        {
            if (Status == newStatus) return;
            // Add any validation rules for status transitions if needed
            // e.g., if (Status == AllocationStatus.Closed) throw new DomainStateException("Cannot change status of a closed allocation.");

            Status = newStatus;
            AddDomainEvent(new BudgetAllocationStatusChangedEvent(this.Id, newStatus));
        }

        // --- Calculated Property ---

        /// <summary>
        /// Calculates the remaining available amount in this allocation.
        /// </summary>
        public Money AvailableAmount => AllocatedAmount - ConsumedAmount; // Use Money operator overload
    }

    // --- Placeholder Domain Event Definitions (Place in Domain/Events) ---
    /*
    namespace ProcureToPay.Domain.Events
    {
        public record BudgetAllocationCreatedEvent(Guid AllocationId, Guid BudgetId, Guid DepartmentId, Money AllocatedAmount);
        public record BudgetAllocationConsumedEvent(Guid AllocationId, Money ConsumedAmount);
        public record BudgetAllocationReleasedEvent(Guid AllocationId, Money ReleasedAmount);
        public record BudgetAllocationStatusChangedEvent(Guid AllocationId, AllocationStatus NewStatus);
    }
    */

    // --- Placeholder Department Entity (Place in Domain/Entities) ---
    /*
    namespace ProcureToPay.Domain.Entities
    {
        public class Department : BaseEntity<Guid>
        {
            public string Name { get; set; } = null!;
            // Other properties...

            public virtual ICollection<BudgetAllocation> BudgetAllocations { get; set; } = new List<BudgetAllocation>();
        }
    }
    */
}
