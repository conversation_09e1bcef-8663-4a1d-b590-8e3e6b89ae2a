﻿namespace ProcureToPay.Domain.Enums
{
    /// <summary>
    /// Defines the possible lifecycle states for a Product.
    /// </summary>
    public enum ProductLifecycleState
    {
        /// <summary>
        /// Product definition is in progress, not yet ready for use.
        /// </summary>
        Draft = 0,

        /// <summary>
        /// Product is active and available for procurement/use.
        /// </summary>
        Active = 1,

        /// <summary>
        /// Product is being phased out, potentially replaced by another. Limited procurement.
        /// </summary>
        PhasedOut = 2,

        /// <summary>
        /// Product is obsolete, no longer available or supported. Cannot be procured.
        /// </summary>
        Obsolete = 3
    }
}

