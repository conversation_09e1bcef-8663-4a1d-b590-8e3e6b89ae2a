﻿using System;
using ProcureToPay.Domain.Enums;       // For BudgetStatus
using ProcureToPay.Domain.ValueObjects; // For Money

namespace ProcureToPay.Domain.Events
{
    // --- Placeholder: Define a base marker interface or class if desired ---
    // public interface IDomainEvent { }

    // --- Budget Specific Events ---

    public record BudgetCreatedEvent(Guid BudgetId, Guid TenantId);

    public record BudgetDetailsUpdatedEvent(Guid BudgetId);

    public record BudgetBaselineUpdatedEvent(Guid BudgetId, Money NewBaseline);

    public record BudgetForecastUpdatedEvent(Guid BudgetId, Money? NewForecast); // Nullable if forecast can be removed

    public record BudgetRulesUpdatedEvent(Guid BudgetId);

    public record BudgetForecastPeriodsUpdatedEvent(Guid BudgetId);

    public record BudgetSubmittedForApprovalEvent(Guid BudgetId); // Corresponds to Submitted status

    public record BudgetApprovedEvent(Guid BudgetId, string ApprovedById);

    public record BudgetRejectedEvent(Guid BudgetId, string RejectedById, string Reason);

    public record BudgetClosedEvent(Guid BudgetId);

    public record BudgetDeletedEvent(Guid BudgetId);

    public record BudgetRestoredEvent(Guid BudgetId);

    public record BudgetVersionIncrementedEvent(Guid BudgetId, int NewVersion);

    public record BudgetStatusChangedEvent(Guid BudgetId, BudgetStatus OldStatus, BudgetStatus NewStatus);

    // Note: Event for utilization alerts would likely be generated by a
    // background service or application logic monitoring consumption vs budget,
    // not directly by the Budget entity itself.

}
