namespace ProcureToPay.Domain.Enums;

/// <summary>
/// Represents the status of a Vendor's Proposal submitted in response to an RFP.
/// </summary>
public enum VendorProposalStatus
{
    /// <summary>
    /// Proposal has been received/submitted by the vendor.
    /// </summary>
    Submitted = 0,
    /// <summary>
    /// Proposal is currently being reviewed and evaluated.
    /// </summary>
    UnderReview = 1,
    /// <summary>
    /// Proposal has passed initial review and is on the shortlist.
    /// </summary>
    Shortlisted = 2,
    /// <summary>
    /// Proposal has been accepted and selected for award.
    /// </summary>
    Accepted = 3,
    /// <summary>
    /// Proposal has been rejected.
    /// </summary>
    Rejected = 4,
    /// <summary>
    /// Proposal was withdrawn by the vendor before a decision was made.
    /// </summary>
    Withdrawn = 5
}
