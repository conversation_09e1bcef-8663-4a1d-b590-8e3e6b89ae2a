using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using ProcureToPay.Domain.Enums; // Required for RfiStatus
using ProcureToPay.Domain.Entities; // Required for BaseEntity

namespace ProcureToPay.Domain.Entities;

/// <summary>
/// Represents a Request for Information (RFI) process used to gather
/// information from potential vendors or the market.
/// </summary>
public class RequestForInformation : BaseEntity<Guid>
{
    /// <summary>
    /// Unique, human-readable identifier for the RFI.
    /// </summary>
    [Required]
    [MaxLength(50)]
    public string RfiNumber { get; private set; } = string.Empty;

    /// <summary>
    /// The main subject or title of the RFI.
    /// </summary>
    [Required]
    [MaxLength(250)]
    public string Title { get; private set; } = string.Empty;

    /// <summary>
    /// Detailed description of the information being sought and the context.
    /// </summary>
    [Required]
    public string Description { get; private set; } = string.Empty;

    /// <summary>
    /// Tenant identifier for multi-tenancy.
    /// </summary>
    [Required]
    public Guid TenantId { get; private set; }

    /// <summary>
    /// The date the RFI was officially issued or published. Null if in Draft.
    /// </summary>
    public DateTime? IssuedDate { get; private set; }

    /// <summary>
    /// The issue date of the RFI.
    /// </summary>
    [Required]
    public DateTime IssueDate { get; private set; }

    /// <summary>
    /// The deadline for vendors/suppliers to submit their responses.
    /// </summary>
    [Required]
    public DateTime ResponseDueDate { get; private set; }

    /// <summary>
    /// The deadline for responses.
    /// </summary>
    [Required]
    public DateTime ResponseDeadline { get; private set; }

    /// <summary>
    /// Foreign Key to the Project this RFI is associated with.
    /// </summary>
    public Guid? ProjectId { get; private set; }

    /// <summary>
    /// Current status of the RFI process.
    /// </summary>
    [Required]
    public RfiStatus Status { get; private set; }

    /// <summary>
    /// Optional ID linking the issuer to the ApplicationUser identity.
    /// </summary>
    [MaxLength(450)]
    public string? IssuedByUserId { get; private set; }
    // Optional Navigation Property:
    // public virtual ApplicationUser? IssuedByUser { get; private set; }

    /// <summary>
    /// Name of the person or department issuing the RFI.
    /// </summary>
    [MaxLength(150)]
    public string? IssuedByName { get; private set; }

    /// <summary>
    /// Optional description of the intended recipients or target audience for the RFI.
    /// </summary>
    [MaxLength(500)]
    public string? TargetAudienceDescription { get; private set; }

    /// <summary>
    /// Optional notes or internal comments about the RFI.
    /// </summary>
    public string? Notes { get; private set; }

    /// <summary>
    /// Navigation property to the Project this RFI is associated with.
    /// </summary>
    public virtual Project? Project { get; private set; }

    // TODO: Consider adding a collection for RFI Responses if they need to be tracked as structured data.
    // This might involve creating an RfiResponse entity.
    // public virtual ICollection<RfiResponse> Responses { get; private set; } = new List<RfiResponse>();

    /// <summary>
    /// Private constructor for EF Core.
    /// </summary>
    private RequestForInformation() : base(Guid.NewGuid()) { }

    /// <summary>
    /// Creates a new Request for Information in Draft status.
    /// </summary>
    public RequestForInformation(
        string rfiNumber,
        string title,
        string description,
        DateTime responseDueDate,
        string? issuedByUserId = null,
        string? issuedByName = null,
        string? targetAudience = null,
        string? notes = null
        ) : base(Guid.NewGuid())
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(rfiNumber);
        ArgumentException.ThrowIfNullOrWhiteSpace(title);
        ArgumentException.ThrowIfNullOrWhiteSpace(description);
        if (responseDueDate.Date < DateTime.UtcNow.Date) // Basic check: due date shouldn't be in the past
            throw new ArgumentOutOfRangeException(nameof(responseDueDate), "Response due date must be in the future.");

        RfiNumber = rfiNumber;
        Title = title;
        Description = description;
        ResponseDueDate = responseDueDate.Date; // Store date part only
        IssuedByUserId = issuedByUserId;
        IssuedByName = issuedByName;
        TargetAudienceDescription = targetAudience;
        Notes = notes;
        Status = RfiStatus.Draft; // Initial status
        IssuedDate = null;
    }

    // --- Domain Methods ---

    /// <summary>
    /// Issues the RFI, changing its status and setting the issued date.
    /// </summary>
    /// <exception cref="InvalidOperationException">Thrown if the RFI is not in Draft status.</exception>
    public void Issue()
    {
        if (Status != RfiStatus.Draft)
        {
            throw new InvalidOperationException($"Cannot issue an RFI with status '{Status}'. RFI must be in Draft status.");
        }
        // Add any other pre-issue checks if needed

        Status = RfiStatus.Issued;
        IssuedDate = DateTime.UtcNow;
        // TODO: Raise Domain Event? RfiIssuedEvent(this.Id)
    }

    /// <summary>
    /// Closes the RFI, typically after the response due date or when information gathering is complete.
    /// </summary>
    /// <exception cref="InvalidOperationException">Thrown if the RFI is not in Issued status.</exception>
    public void Close()
    {
        if (Status != RfiStatus.Issued)
        {
            throw new InvalidOperationException($"Cannot close an RFI with status '{Status}'. RFI must be Issued.");
        }
        // Optional: Check if ResponseDueDate has passed?
        // if (DateTime.UtcNow.Date <= ResponseDueDate) { ... }

        Status = RfiStatus.Closed;
        // TODO: Raise Domain Event? RfiClosedEvent(this.Id)
    }

    /// <summary>
    /// Cancels the RFI process.
    /// </summary>
    /// <param name="reason">Reason for cancellation (required).</param>
    /// <exception cref="InvalidOperationException">Thrown if the RFI is already Closed or Cancelled.</exception>
    public void Cancel(string reason)
    {
        if (Status == RfiStatus.Closed || Status == RfiStatus.Cancelled)
            throw new InvalidOperationException($"Cannot cancel an RFI with status '{Status}'.");

        ArgumentException.ThrowIfNullOrWhiteSpace(reason);
        Status = RfiStatus.Cancelled;
        Notes = $"Cancelled: {reason}\n---\n{Notes}"; // Prepend reason
                                                      // TODO: Raise Domain Event? RfiCancelledEvent(this.Id, reason)
    }

    /// <summary>
    /// Updates the details of the RFI. Only allowed when in Draft status.
    /// </summary>
    public void UpdateDetails(string title, string description, DateTime responseDueDate, string? targetAudience, string? notes)
    {
        if (Status != RfiStatus.Draft)
            throw new InvalidOperationException("Cannot update details unless RFI is in Draft status.");

        ArgumentException.ThrowIfNullOrWhiteSpace(title);
        ArgumentException.ThrowIfNullOrWhiteSpace(description);
        if (responseDueDate.Date < DateTime.UtcNow.Date)
            throw new ArgumentOutOfRangeException(nameof(responseDueDate), "Response due date must be in the future.");

        Title = title;
        Description = description;
        ResponseDueDate = responseDueDate.Date;
        TargetAudienceDescription = targetAudience;
        Notes = notes;
    }
}
