using Microsoft.AspNetCore.Identity;
using System.Collections.Generic;
using ProcureToPay.Domain.Entities;

namespace ProcureToPay.Infrastructure.Identity;

// Add profile data for application users by adding properties to the ApplicationUser class
public class ApplicationUser : IdentityUser
{
    /// <summary>
    /// Navigation property to the sales territories this user is assigned to.
    /// </summary>
    public virtual ICollection<SalesTerritory> SalesTerritories { get; set; } = new HashSet<SalesTerritory>();
}

