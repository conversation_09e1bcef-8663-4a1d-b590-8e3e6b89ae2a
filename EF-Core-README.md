# Entity Framework Core Setup for ProcureToPay

This document provides guidance on the Entity Framework Core setup for the ProcureToPay application, including entity configurations, data seeding, and migration strategies.

## Table of Contents

1. [Overview](#overview)
2. [Entity Configuration Best Practices](#entity-configuration-best-practices)
3. [Data Seeding](#data-seeding)
4. [Migration Management](#migration-management)
5. [Multi-Tenancy Support](#multi-tenancy-support)
6. [PostgreSQL-Specific Features](#postgresql-specific-features)
7. [Helper Scripts](#helper-scripts)

## Overview

The ProcureToPay application uses Entity Framework Core with PostgreSQL as the database provider. The setup follows Clean Architecture principles, with domain entities in the `ProcureToPay.Domain` project and database configurations in the `ProcureToPay.Infrastructure` project.

Key components:
- **Domain Entities**: Located in `ProcureToPay.Domain/Entities`
- **Entity Configurations**: Located in `ProcureToPay.Infrastructure/Persistence/Configurations`
- **DbContext**: `ApplicationDbContext` in `ProcureToPay.Infrastructure/Persistence`
- **Migrations**: Located in `ProcureToPay.Infrastructure/Persistence/Migrations`

## Entity Configuration Best Practices

### IEntityTypeConfiguration<T>

All entity configurations should implement `IEntityTypeConfiguration<T>` and be placed in the `Configurations` folder:

```csharp
public class ProductConfiguration : IEntityTypeConfiguration<Product>
{
    public void Configure(EntityTypeBuilder<Product> builder)
    {
        // Configuration code here
    }
}
```

### Table and Schema Naming

Use explicit table and schema names with PostgreSQL conventions:

```csharp
builder.ToTable("products", "public");
```

### Property Configuration

Configure properties with appropriate constraints:

```csharp
builder.Property(p => p.Name)
    .IsRequired()
    .HasMaxLength(255);

builder.Property(p => p.Description)
    .HasColumnType("text")
    .IsRequired(false);
```

### Indexes and Constraints

Use PostgreSQL-specific syntax for indexes and constraints:

```csharp
// Simple index
builder.HasIndex(p => p.Name)
    .HasDatabaseName("IX_products_name");

// Unique index with filter
builder.HasIndex(c => c.Code)
    .IsUnique()
    .HasFilter("\"Code\" IS NOT NULL")
    .HasDatabaseName("IX_categories_code_unique");

// Composite unique index
builder.HasIndex(p => new { p.TenantId, p.ProductCode })
    .IsUnique()
    .HasDatabaseName("IX_products_tenant_id_product_code_unique");
```

### Relationships

Configure relationships with appropriate delete behaviors:

```csharp
// One-to-many relationship
builder.HasOne(p => p.Category)
    .WithMany(c => c.Products)
    .HasForeignKey(p => p.CategoryId)
    .OnDelete(DeleteBehavior.SetNull);
```

## Data Seeding

### Static GUIDs

Define static GUIDs for seeded entities to ensure consistent references:

```csharp
public static readonly Guid DefaultTenantId = Guid.Parse("11111111-1111-1111-1111-111111111111");
```

### Anonymous Types for HasData

Use anonymous types with HasData for seeding:

```csharp
builder.HasData(
    new
    {
        Id = DefaultTenantId,
        Name = "Default System Tenant",
        Identifier = "system",
        IsActive = true,
        CreatedAt = SeedDate,
        ModifiedAt = (DateTime?)null
    }
);
```

### Fixed Dates

Use a fixed date for seeding to ensure consistency:

```csharp
private static readonly DateTime SeedDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc);
```

## Migration Management

### Creating Migrations

Use the provided script to create migrations:

```powershell
.\create-ef-migration.ps1 -MigrationName "InitialSchema"
```

Or manually:

```powershell
dotnet ef migrations add InitialSchema --project ProcureToPay.Infrastructure --startup-project ProcureToPay.WebApp\ProcureToPay.WebApp --context ApplicationDbContext --output-dir Persistence\Migrations
```

### Applying Migrations

Use the provided script to apply migrations:

```powershell
.\apply-ef-migrations.ps1 -FromDashboard
```

Or manually:

```powershell
dotnet ef database update --project ProcureToPay.Infrastructure --startup-project ProcureToPay.WebApp\ProcureToPay.WebApp
```

### Generating SQL Scripts

Generate SQL scripts for manual application:

```powershell
dotnet ef migrations script --project ProcureToPay.Infrastructure --startup-project ProcureToPay.WebApp\ProcureToPay.WebApp --output migration_script.sql --idempotent
```

## Multi-Tenancy Support

The application uses a multi-tenant architecture with row-level security in a shared database:

1. **ITenantEntity Interface**: Entities that belong to a tenant implement this interface
2. **Global Query Filters**: Automatically filter data by tenant
3. **Tenant ID Assignment**: Automatically set tenant ID on entity creation

Example configuration:

```csharp
builder.Property(p => p.TenantId)
    .IsRequired();

builder.HasIndex(p => p.TenantId)
    .HasDatabaseName("IX_products_tenant_id");
```

## PostgreSQL-Specific Features

### Case Sensitivity

PostgreSQL is case-sensitive, so use double quotes for identifiers in filters:

```csharp
builder.HasIndex(c => c.Code)
    .IsUnique()
    .HasFilter("\"Code\" IS NOT NULL");
```

### JSON Support

Use PostgreSQL's JSONB type for structured data:

```csharp
builder.Property(t => t.Settings)
    .HasColumnType("jsonb")
    .IsRequired(false);
```

### Concurrency Tokens

Use PostgreSQL's xmin system column for optimistic concurrency:

```csharp
builder.Property<uint>("xmin")
    .HasColumnType("xid")
    .ValueGeneratedOnAddOrUpdate()
    .IsConcurrencyToken();
```

## Helper Scripts

### create-ef-migration.ps1

Creates a new migration with proper naming and configuration:

```powershell
.\create-ef-migration.ps1 -MigrationName "AddNewEntity"
```

Parameters:
- `-MigrationName`: Name of the migration (required)
- `-NoCompile`: Skip building projects
- `-OutputDir`: Custom output directory for migrations

### apply-ef-migrations.ps1

Applies migrations to the database with retry logic:

```powershell
.\apply-ef-migrations.ps1 -FromDashboard
```

Parameters:
- `-FromDashboard`: Prompt for connection string from Aspire Dashboard
- `-ConnectionString`: Explicit connection string
- `-MaxRetries`: Maximum number of retry attempts (default: 10)
- `-RetryDelaySeconds`: Delay between retry attempts (default: 5)
