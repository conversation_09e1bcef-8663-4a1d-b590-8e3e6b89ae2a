using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProcureToPay.Domain.Entities;
using ProcureToPay.Domain.Enums; // Assuming UnitOfMeasure enum exists

namespace ProcureToPay.Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// Configuration for the SalesOrderLine entity for EF Core and PostgreSQL.
    /// </summary>
    public class SalesOrderLineConfiguration : IEntityTypeConfiguration<SalesOrderLine>
    {
        public void Configure(EntityTypeBuilder<SalesOrderLine> builder)
        {
            // Table Mapping
            builder.ToTable("sales_order_lines");

            // Primary Key (Composite Key)
            // Assuming SalesOrderId is Guid (from SalesOrder) and LineNumber is int
            builder.HasKey(l => new { l.SalesOrderId, l.LineNumber });

            // Properties
            builder.Property(l => l.LineNumber)
                .ValueGeneratedNever(); // Part of composite key

            builder.Property(l => l.Quantity)
                .HasColumnType("numeric(18, 4)") // Define precision/scale
                .IsRequired();

            builder.Property(l => l.UnitPriceAmount)
                .HasColumnType("numeric(18, 4)") // Define precision/scale
                .IsRequired();

            builder.Property(l => l.UnitPriceCurrency)
                .HasMaxLength(3)
                .IsRequired();

            builder.Property(l => l.LineTotalAmount)
                .HasColumnType("numeric(18, 2)") // Define precision/scale
                .IsRequired();

            builder.Property(l => l.LineTotalCurrency)
                .HasMaxLength(3)
                .IsRequired();

            builder.Property(l => l.DiscountAmountValue)
                .HasColumnType("numeric(18, 2)")
                .IsRequired(false);

            builder.Property(l => l.DiscountAmountCurrency)
                .HasMaxLength(3)
                .IsRequired(false);

            // Ignore Money properties
            builder.Ignore(l => l.UnitPrice);
            builder.Ignore(l => l.LineTotal);
            builder.Ignore(l => l.DiscountAmount);
                // Note: LineTotal calculation logic belongs in Domain/Application layer.

            builder.Property(l => l.UnitOfMeasure)
                .IsRequired()
                .HasConversion<string>()
                .HasMaxLength(20);

            builder.Property(l => l.Description)
                .HasColumnType("text")
                .IsRequired(); // Description usually required

            builder.Property(l => l.RequestedDeliveryDate)
                .HasColumnType("timestamp without time zone") // Or "date"
                .IsRequired(false);

            // Foreign Keys (Assuming Guid or appropriate types)
            builder.Property(l => l.ProductId).IsRequired(); // Product is usually required for SO Line


            // --- Relationships ---
            // Line-Header (Many-to-One)
            builder.HasOne(l => l.SalesOrder)
                   .WithMany(so => so.Lines)
                   .HasForeignKey(l => l.SalesOrderId)
                   .IsRequired() // Part of composite key
                   .OnDelete(DeleteBehavior.Cascade); // Matches header config

            // Link to Product (Many-to-One)
            builder.HasOne(l => l.Product)
                   .WithMany() // Assuming ProductDefinition doesn't have direct nav back
                   .HasForeignKey(l => l.ProductId)
                   .IsRequired()
                   .OnDelete(DeleteBehavior.Restrict); // Prevent deleting product if used in SO lines


            // --- Indexes ---
            builder.HasIndex(l => l.ProductId);
            // Composite PK serves as an index for SalesOrderId


            // --- TODO ---
            // TODO: Verify FK type for ProductId matches ProductDefinition entity.
            // TODO: Add other properties from previous version if still needed (Backorder, Pricing, Warranty, Project links etc.) and configure them.
            // TODO: Address deferred TODOs from previous version (Kit decomposition, Pricing logic, etc.) if implementing now.
        }
    }
}
