using System;

namespace ProcureToPay.Application.Features.Categories.DTOs
{
    /// <summary>
    /// Data Transfer Object for displaying category information.
    /// </summary>
    public class CategoryDto
    {
        /// <summary>
        /// The unique identifier of the category.
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// The name of the category.
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// The optional description of the category.
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// The optional code of the category.
        /// </summary>
        public string? Code { get; set; }

        /// <summary>
        /// The optional UNSPSC code of the category.
        /// </summary>
        public string? UnspscCode { get; set; }

        /// <summary>
        /// The optional parent category ID.
        /// </summary>
        public Guid? ParentCategoryId { get; set; }

        /// <summary>
        /// The name of the parent category, if any.
        /// </summary>
        public string? ParentCategoryName { get; set; }

        /// <summary>
        /// The number of products in this category.
        /// </summary>
        public int ProductCount { get; set; }
    }
}
