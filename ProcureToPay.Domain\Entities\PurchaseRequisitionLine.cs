using System;
using ProcureToPay.Domain.Enums;       // Required for UnitOfMeasure
using ProcureToPay.Domain.ValueObjects; // Required for Money
using ProcureToPay.Domain.Exceptions;   // Assuming DomainStateException exists
using ProcureToPay.Domain.Events;       // Assuming Requisition domain events namespace exists

namespace ProcureToPay.Domain.Entities
{
    /// <summary>
    /// Represents a single line item on a Purchase Requisition, detailing a requested good or service.
    /// Uses a single Guid Id as PK and includes LineNumber for ordering.
    /// </summary>
    public class PurchaseRequisitionLine : BaseEntity<Guid> // Using single Guid PK
    {
        /// <summary>
        /// Foreign Key to the parent Purchase Requisition.
        /// </summary>
        public Guid PurchaseRequisitionId { get; private set; }

        /// <summary>
        /// Line number for ordering within the requisition (e.g., 1, 2, 3...).
        /// Typically assigned sequentially when adding lines. Unique within a Requisition.
        /// </summary>
        public int LineNumber { get; private set; }

        /// <summary>
        /// Foreign Key to the Tenant, inherited from the header.
        /// </summary>
        public Guid TenantId { get; private set; } // Added for tenant isolation configuration

        /// <summary>
        /// Optional Foreign Key to a specific VendorProduct offering, if known.
        /// Allows indexing.
        /// </summary>
        public Guid? VendorProductId { get; private set; }

        /// <summary>
        /// Optional Foreign Key to a master ProductDefinition, if VendorProduct is not specified or applicable.
        /// Allows indexing (if needed separately from VendorProductId).
        /// </summary>
        public Guid? ProductDefinitionId { get; private set; }

        /// <summary>
        /// Description of the requested item or service. Required.
        /// </summary>
        public string Description { get; private set; } = null!;

        /// <summary>
        /// The quantity requested. Can be fractional. Required.
        /// </summary>
        public decimal Quantity { get; private set; }

        /// <summary>
        /// The unit of measure for the quantity. Required. (Configured for enum conversion).
        /// </summary>
        public UnitOfMeasure UnitOfMeasure { get; private set; }

        /// <summary>
        /// The estimated price per unit (Money VO). Optional. (Allows precision configuration).
        /// </summary>
        public Money? EstimatedUnitPrice { get; private set; }

        /// <summary>
        /// The calculated estimated total cost for this line (Quantity * EstimatedUnitPrice) as Money VO.
        /// Null if UnitPrice is not known. Represents 'NetAmount'. (Allows precision configuration).
        /// </summary>
        public Money? EstimatedLineCost { get; private set; } // Used by PR header calculation

        /// <summary>
        /// Optional General Ledger account code for this line item. (Allows indexing).
        /// </summary>
        public string? GLAccountCode { get; private set; }

        /// <summary>
        /// Optional date by which this specific line item is needed.
        /// </summary>
        public DateTime? DateNeeded { get; private set; }

        /// <summary>
        /// Optional Foreign Key to a suggested Vendor for this line item.
        /// </summary>
        public Guid? SuggestedVendorId { get; private set; }

        /// <summary>
        /// Optional notes specific to this line item.
        /// </summary>
        public string? Notes { get; private set; }


        // --- Navigation Properties ---

        /// <summary>
        /// Navigation property to the parent Purchase Requisition. Virtual for lazy loading. (Relationship configured).
        /// </summary>
        public virtual PurchaseRequisition PurchaseRequisition { get; private set; } = null!;

        /// <summary>
        /// Optional navigation property to the specific VendorProduct. Virtual for lazy loading.
        /// </summary>
        public virtual VendorProduct? VendorProduct { get; private set; }

        /// <summary>
        /// Optional navigation property to the master ProductDefinition. Virtual for lazy loading.
        /// </summary>
        public virtual ProductDefinition? ProductDefinition { get; private set; }

        /// <summary>
        /// Optional navigation property to the suggested Vendor. Virtual for lazy loading.
        /// </summary>
        public virtual Vendor? SuggestedVendor { get; private set; }


        /// <summary>
        /// Private constructor for EF Core hydration.
        /// </summary>
        private PurchaseRequisitionLine() : base(Guid.NewGuid()) { }

        /// <summary>
        /// Internal constructor for creating a new Purchase Requisition Line.
        /// Should only be called from within the PurchaseRequisition aggregate root.
        /// </summary>
        /// <param name="id">Line item identifier.</param>
        /// <param name="purchaseRequisitionId">ID of the parent requisition.</param>
        /// <param name="lineNumber">Sequential line number.</param>
        /// <param name="tenantId">Tenant ID.</param>
        /// <param name="description">Item description.</param>
        /// <param name="quantity">Quantity requested.</param>
        /// <param name="unitOfMeasure">Unit of measure.</param>
        /// <param name="vendorProductId">Optional ID of a specific vendor product.</param>
        /// <param name="productDefinitionId">Optional ID of a master product definition.</param>
        /// <param name="estimatedUnitPrice">Optional estimated unit price (Money VO).</param>
        /// <param name="glAccountCode">Optional GL account code.</param>
        /// <param name="dateNeeded">Optional specific date needed.</param>
        /// <param name="suggestedVendorId">Optional suggested vendor ID.</param>
        /// <param name="notes">Optional line item notes.</param>
        internal PurchaseRequisitionLine(
            Guid id, // Allow passing ID if generated by AR or factory
            Guid purchaseRequisitionId,
            int lineNumber,
            Guid tenantId,
            string description,
            decimal quantity,
            UnitOfMeasure unitOfMeasure,
            Guid? vendorProductId = null,
            Guid? productDefinitionId = null,
            Money? estimatedUnitPrice = null,
            string? glAccountCode = null,
            DateTime? dateNeeded = null,
            Guid? suggestedVendorId = null,
            string? notes = null
            ) : base(id)
        {
            // Validation
            if (purchaseRequisitionId == Guid.Empty) throw new ArgumentException("PurchaseRequisitionId cannot be empty.", nameof(purchaseRequisitionId));
            if (tenantId == Guid.Empty) throw new ArgumentException("TenantId cannot be empty.", nameof(tenantId));
            if (lineNumber <= 0) throw new ArgumentOutOfRangeException(nameof(lineNumber), "Line number must be positive.");
            ArgumentException.ThrowIfNullOrWhiteSpace(description);
            if (quantity <= 0) throw new ArgumentOutOfRangeException(nameof(quantity), "Quantity must be positive.");
            if (vendorProductId.HasValue && productDefinitionId.HasValue) throw new ArgumentException("Cannot specify both VendorProductId and ProductDefinitionId.");
            // Enum validation is implicit

            PurchaseRequisitionId = purchaseRequisitionId;
            LineNumber = lineNumber;
            TenantId = tenantId;
            VendorProductId = vendorProductId;
            ProductDefinitionId = productDefinitionId;
            Description = description;
            Quantity = quantity;
            UnitOfMeasure = unitOfMeasure;
            EstimatedUnitPrice = estimatedUnitPrice; // Assign Money VO (can be null)
            GLAccountCode = glAccountCode;
            DateNeeded = dateNeeded;
            SuggestedVendorId = suggestedVendorId;
            Notes = notes;

            RecalculateEstimatedLineCost(); // Calculate initial cost
        }

        // --- Domain Methods ---

        /// <summary>
        /// Updates the estimated unit price and recalculates the line cost.
        /// Should only be called via the PurchaseRequisition aggregate root.
        /// </summary>
        /// <param name="newPrice">The new estimated unit price (Money VO, can be null).</param>
        /// <param name="requisitionCurrencyCode">The currency code of the parent requisition for validation.</param>
        internal void UpdateEstimatedPrice(Money? newPrice, string requisitionCurrencyCode)
        {
            if (newPrice != null && newPrice.CurrencyCode != requisitionCurrencyCode)
            {
                throw new ArgumentException($"Line item price currency '{newPrice.CurrencyCode}' does not match requisition currency '{requisitionCurrencyCode}'.", nameof(newPrice));
            }
            if (newPrice?.Amount < 0) throw new ArgumentOutOfRangeException(nameof(newPrice), "Estimated unit price amount cannot be negative.");

            if (EstimatedUnitPrice == newPrice) return; // Assumes Money VO equality

            EstimatedUnitPrice = newPrice;
            RecalculateEstimatedLineCost();
            // AddDomainEvent(new RequisitionLinePriceUpdatedEvent(this.Id, newPrice));
        }

        /// <summary>
        /// Updates the quantity and recalculates the line cost.
        /// Should only be called via the PurchaseRequisition aggregate root.
        /// </summary>
        /// <param name="newQuantity">The new quantity (must be positive).</param>
        internal void UpdateQuantity(decimal newQuantity)
        {
            if (newQuantity <= 0) throw new ArgumentOutOfRangeException(nameof(newQuantity), "Quantity must be positive.");
            if (Quantity == newQuantity) return;

            Quantity = newQuantity;
            RecalculateEstimatedLineCost();
            // AddDomainEvent(new RequisitionLineQuantityUpdatedEvent(this.Id, newQuantity));
        }

        /// <summary>
        /// Updates other details of the line item.
        /// Should only be called via the PurchaseRequisition aggregate root.
        /// </summary>
        internal void UpdateDetails(string description, UnitOfMeasure unitOfMeasure, string? glAccountCode, DateTime? dateNeeded, string? notes, Guid? suggestedVendorId, Guid? vendorProductId, Guid? productDefinitionId)
        {
            ArgumentException.ThrowIfNullOrWhiteSpace(description);
            if (vendorProductId.HasValue && productDefinitionId.HasValue) throw new ArgumentException("Cannot specify both VendorProductId and ProductDefinitionId.");
            // Add other validation as needed

            bool changed = false;
            if (Description != description) { Description = description; changed = true; }
            if (UnitOfMeasure != unitOfMeasure) { UnitOfMeasure = unitOfMeasure; changed = true; }
            if (GLAccountCode != glAccountCode) { GLAccountCode = glAccountCode; changed = true; }
            if (DateNeeded != dateNeeded) { DateNeeded = dateNeeded; changed = true; }
            if (Notes != notes) { Notes = notes; changed = true; }
            if (SuggestedVendorId != suggestedVendorId) { SuggestedVendorId = suggestedVendorId; changed = true; }
            if (VendorProductId != vendorProductId) { VendorProductId = vendorProductId; changed = true; }
            if (ProductDefinitionId != productDefinitionId) { ProductDefinitionId = productDefinitionId; changed = true; }

            // Note: RecalculateEstimatedLineCost() is not called here as only descriptive fields are changed.
            // If price/qty changed, separate methods handle recalculation.

            if (changed) AddDomainEvent(new RequisitionLineDetailsUpdatedEvent(this.Id));
        }


        /// <summary>
        /// Recalculates the estimated line cost based on Quantity and EstimatedUnitPrice.
        /// Sets the EstimatedLineCost property.
        /// </summary>
        private void RecalculateEstimatedLineCost()
        {
            if (EstimatedUnitPrice == null || EstimatedUnitPrice.Amount == 0) // If no price, cost is null or zero
            {
                // Set cost to null if price is null/zero. Assumes parent Req has correct currency for later.
                EstimatedLineCost = null;
            }
            else
            {
                // Use the Money operator overload for multiplication
                EstimatedLineCost = EstimatedUnitPrice * Quantity;
            }
        }
    }

    // --- Placeholder Domain Event Definitions (Place in Domain/Events) ---
    /*
    namespace ProcureToPay.Domain.Events
    {
        public record RequisitionLinePriceUpdatedEvent(Guid LineId, Money? NewPrice);
        public record RequisitionLineQuantityUpdatedEvent(Guid LineId, decimal NewQuantity);
        public record RequisitionLineDetailsUpdatedEvent(Guid LineId);
    }
    */
}
