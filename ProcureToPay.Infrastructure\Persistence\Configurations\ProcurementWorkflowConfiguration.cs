using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProcureToPay.Domain.Entities;
using ProcureToPay.Domain.Enums; // Assuming WorkflowType enum exists

namespace ProcureToPay.Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// Configuration for the ProcurementWorkflow entity for EF Core and PostgreSQL.
    /// </summary>
    public class ProcurementWorkflowConfiguration : IEntityTypeConfiguration<ProcurementWorkflow>
    {
        public void Configure(EntityTypeBuilder<ProcurementWorkflow> builder)
        {
            // Table Mapping
            builder.ToTable("procurement_workflows");

            // Primary Key
            builder.HasKey(w => w.Id);
            builder.Property(w => w.Id).ValueGeneratedOnAdd();

            // Properties
            builder.Property(w => w.Name)
                .IsRequired()
                .HasMaxLength(150);

            builder.Property(w => w.Description)
                .HasColumnType("text")
                .IsRequired(false);

            builder.Property(w => w.WorkflowType)
                .IsRequired()
                .HasConversion<string>() // Or int
                .HasMaxLength(50); // e.g., 'PurchaseRequisition', 'Invoice', 'TechnicalSubmittal'

            builder.Property(w => w.IsActive)
                .IsRequired()
                .HasDefaultValue(true);

            builder.Property(w => w.Version)
                .HasMaxLength(20) // Or use integer type if preferred
                .IsRequired(false); // Version might be optional or managed differently

            builder.Property(w => w.TenantId).IsRequired();


            // --- Relationships ---
            // Workflow-Steps (One-to-Many)
            builder.HasMany(w => w.Steps)
                   .WithOne(s => s.ProcurementWorkflow) // Assuming Step has Workflow nav prop
                   .HasForeignKey(s => s.ProcurementWorkflowId)
                   .IsRequired()
                   .OnDelete(DeleteBehavior.Cascade); // Deleting workflow deletes its steps


            // --- Indexes ---
            builder.HasIndex(w => w.Name);
            builder.HasIndex(w => w.WorkflowType);
            builder.HasIndex(w => w.IsActive);
            builder.HasIndex(w => w.TenantId);
            // Consider unique index on Name + TenantId or Name + WorkflowType + TenantId if needed
            builder.HasIndex(w => new { w.TenantId, w.WorkflowType, w.Name }).IsUnique();


            // --- TODO ---
            // TODO: Define WorkflowType enum.
            // TODO: Decide on Version property type and necessity.
        }
    }
}
