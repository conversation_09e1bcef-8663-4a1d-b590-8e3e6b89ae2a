using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProcureToPay.Domain.Entities;
using ProcureToPay.Domain.Enums; // Assuming UnitOfMeasure enum exists

namespace ProcureToPay.Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// Configuration for the InvoiceLine entity for EF Core and PostgreSQL.
    /// </summary>
    public class InvoiceLineConfiguration : IEntityTypeConfiguration<InvoiceLine>
    {
        public void Configure(EntityTypeBuilder<InvoiceLine> builder)
        {
            // Table Mapping
            builder.ToTable("invoice_lines");

            // Primary Key (Composite Key)
            // Assuming InvoiceId is long (from Invoice) and LineNumber is int
            builder.HasKey(l => new { l.InvoiceId, l.LineNumber });

            // Properties
            builder.Property(l => l.LineNumber)
                .ValueGeneratedNever(); // Part of composite key

            builder.Property(l => l.Description)
                .HasColumnType("text")
                .IsRequired(); // Description usually required for invoice lines

            builder.Property(l => l.Quantity)
                .HasColumnType("numeric(18, 4)") // Allow fractional quantities
                .IsRequired();

            builder.Property(l => l.UnitPrice)
                .HasColumnType("numeric(18, 4)") // Higher precision for unit price
                .IsRequired();

            builder.Property(l => l.LineTotal)
                .HasColumnType("numeric(18, 2)") // Standard precision for line total
                .IsRequired();

            builder.Property(l => l.TaxRate)
                .HasColumnType("numeric(5, 4)") // e.g., 0.1500 for 15%
                .IsRequired(false); // Tax might be applied at header level

            builder.Property(l => l.TaxAmount)
                .HasColumnType("numeric(18, 2)")
                .IsRequired(false)
                .HasDefaultValue(0m);

            builder.Property(l => l.UnitOfMeasure)
                .IsRequired()
                .HasConversion<string>()
                .HasMaxLength(20);

            // Foreign Keys (Assuming Guid or appropriate types)
            builder.Property(l => l.ProductId).IsRequired(false); // May be a service/description-only line
            builder.Property(l => l.PurchaseOrderLineId).IsRequired(false); // May not link directly to PO line


            // --- Relationships ---
            // Line-Header (Many-to-One)
            builder.HasOne(l => l.Invoice)
                   .WithMany(i => i.Lines)
                   .HasForeignKey(l => l.InvoiceId)
                   .IsRequired() // Part of composite key
                   .OnDelete(DeleteBehavior.Cascade); // Matches header config

            // Link to Product/Service (Many-to-One, Optional)
            builder.HasOne(l => l.Product)
                   .WithMany() // Assuming ProductDefinition doesn't have direct nav back
                   .HasForeignKey(l => l.ProductId)
                   .IsRequired(false) // Line might be for a service or non-catalog item
                   .OnDelete(DeleteBehavior.Restrict); // Prevent deleting product if used in invoice lines

            // Link to Origin Line (Many-to-One, Optional)
            builder.HasOne(l => l.PurchaseOrderLine)
                   .WithMany(pol => pol.InvoiceLines) // Assuming POLine has collection
                   .HasForeignKey(l => l.PurchaseOrderLineId)
                   .IsRequired(false)
                   .OnDelete(DeleteBehavior.Restrict); // Or SetNull? Review rule.


            // --- Indexes ---
            builder.HasIndex(l => l.ProductId);
            builder.HasIndex(l => l.PurchaseOrderLineId);
            // Composite PK serves as an index for InvoiceId


            // --- TODO ---
            // TODO: Verify FK types (ProductId, PurchaseOrderLineId) match related entities.
            // TODO: Consider adding GLAccountCode property if needed for accounting integration.
            // TODO: Confirm precision/scale requirements for monetary/quantity fields.
        }
    }
}
