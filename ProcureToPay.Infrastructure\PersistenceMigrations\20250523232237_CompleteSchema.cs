﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ProcureToPay.Infrastructure.PersistenceMigrations
{
    /// <inheritdoc />
    public partial class CompleteSchema : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_asp_net_role_claims_asp_net_roles_role_id",
                table: "AspNetRoleClaims");

            migrationBuilder.DropForeignKey(
                name: "fk_asp_net_user_claims_asp_net_users_user_id",
                table: "AspNetUserClaims");

            migrationBuilder.DropForeignKey(
                name: "fk_asp_net_user_logins_asp_net_users_user_id",
                table: "AspNetUserLogins");

            migrationBuilder.DropForeignKey(
                name: "fk_asp_net_user_roles_asp_net_roles_role_id",
                table: "AspNetUserRoles");

            migrationBuilder.DropForeignKey(
                name: "fk_asp_net_user_roles_asp_net_users_user_id",
                table: "AspNetUserRoles");

            migrationBuilder.DropForeignKey(
                name: "fk_asp_net_user_tokens_asp_net_users_user_id",
                table: "AspNetUserTokens");

            migrationBuilder.DropForeignKey(
                name: "fk_budget_allocations_budgets_budget_id",
                schema: "public",
                table: "budget_allocations");

            migrationBuilder.DropForeignKey(
                name: "fk_budget_allocations_departments_department_id",
                schema: "public",
                table: "budget_allocations");

            migrationBuilder.DropForeignKey(
                name: "fk_categories_categories_parent_category_id",
                schema: "public",
                table: "categories");

            migrationBuilder.DropForeignKey(
                name: "fk_contracts_vendors_vendor_id",
                table: "contracts");

            migrationBuilder.DropForeignKey(
                name: "fk_delivery_note_lines_delivery_notes_delivery_note_id",
                table: "delivery_note_lines");

            migrationBuilder.DropForeignKey(
                name: "fk_delivery_note_lines_product_definitions_product_id",
                table: "delivery_note_lines");

            migrationBuilder.DropForeignKey(
                name: "fk_delivery_note_lines_purchase_order_lines_purchase_order_lin",
                table: "delivery_note_lines");

            migrationBuilder.DropForeignKey(
                name: "fk_delivery_note_lines_sales_order_lines_sales_order_id_sales_",
                table: "delivery_note_lines");

            migrationBuilder.DropForeignKey(
                name: "fk_delivery_notes_purchase_orders_purchase_order_id",
                table: "delivery_notes");

            migrationBuilder.DropForeignKey(
                name: "fk_delivery_notes_sales_orders_sales_order_id",
                table: "delivery_notes");

            migrationBuilder.DropForeignKey(
                name: "fk_delivery_notes_vendors_vendor_id",
                table: "delivery_notes");

            migrationBuilder.DropForeignKey(
                name: "fk_goods_receipt_note_lines_delivery_note_lines_delivery_note_",
                table: "goods_receipt_note_lines");

            migrationBuilder.DropForeignKey(
                name: "fk_goods_receipt_note_lines_goods_receipt_notes_goods_receipt_",
                table: "goods_receipt_note_lines");

            migrationBuilder.DropForeignKey(
                name: "fk_goods_receipt_note_lines_product_definitions_product_id",
                table: "goods_receipt_note_lines");

            migrationBuilder.DropForeignKey(
                name: "fk_goods_receipt_note_lines_purchase_order_lines_purchase_orde",
                table: "goods_receipt_note_lines");

            migrationBuilder.DropForeignKey(
                name: "fk_goods_receipt_notes_asp_net_users_received_by_user_id",
                table: "goods_receipt_notes");

            migrationBuilder.DropForeignKey(
                name: "fk_goods_receipt_notes_delivery_notes_delivery_note_id",
                table: "goods_receipt_notes");

            migrationBuilder.DropForeignKey(
                name: "fk_goods_receipt_notes_purchase_orders_purchase_order_id",
                table: "goods_receipt_notes");

            migrationBuilder.DropForeignKey(
                name: "fk_goods_receipt_notes_vendors_vendor_id",
                table: "goods_receipt_notes");

            migrationBuilder.DropForeignKey(
                name: "fk_invoice_lines_invoices_invoice_id",
                table: "invoice_lines");

            migrationBuilder.DropForeignKey(
                name: "fk_invoice_lines_product_definitions_product_id",
                table: "invoice_lines");

            migrationBuilder.DropForeignKey(
                name: "fk_invoice_lines_purchase_order_lines_purchase_order_line_id",
                table: "invoice_lines");

            migrationBuilder.DropForeignKey(
                name: "fk_invoices_customers_customer_id",
                table: "invoices");

            migrationBuilder.DropForeignKey(
                name: "fk_invoices_purchase_orders_purchase_order_id",
                table: "invoices");

            migrationBuilder.DropForeignKey(
                name: "fk_invoices_vendors_vendor_id",
                table: "invoices");

            migrationBuilder.DropForeignKey(
                name: "fk_payment_transactions_invoices_invoice_id",
                table: "payment_transactions");

            migrationBuilder.DropForeignKey(
                name: "fk_payment_transactions_vendors_vendor_id",
                table: "payment_transactions");

            migrationBuilder.DropForeignKey(
                name: "fk_procurement_workflow_steps_asp_net_users_approver_user_id",
                table: "procurement_workflow_steps");

            migrationBuilder.DropForeignKey(
                name: "fk_procurement_workflow_steps_procurement_workflows_procuremen",
                table: "procurement_workflow_steps");

            migrationBuilder.DropForeignKey(
                name: "fk_procurement_workflow_steps_procurement_workflows_workflow_id",
                table: "procurement_workflow_steps");

            migrationBuilder.DropForeignKey(
                name: "fk_product_definitions_categories_category_id",
                table: "product_definitions");

            migrationBuilder.DropForeignKey(
                name: "fk_products_categories_category_id",
                schema: "public",
                table: "products");

            migrationBuilder.DropForeignKey(
                name: "fk_purchase_orders_contracts_contract_id",
                table: "purchase_orders");

            migrationBuilder.DropForeignKey(
                name: "fk_purchase_orders_purchase_requisitions_requisition_id",
                table: "purchase_orders");

            migrationBuilder.DropForeignKey(
                name: "fk_purchase_orders_vendors_vendor_id",
                table: "purchase_orders");

            migrationBuilder.DropForeignKey(
                name: "fk_purchase_order_lines_purchase_orders_purchase_order_id",
                table: "PurchaseOrderLines");

            migrationBuilder.DropForeignKey(
                name: "fk_purchase_order_lines_vendor_products_vendor_product_id",
                table: "PurchaseOrderLines");

            migrationBuilder.DropForeignKey(
                name: "fk_purchase_requisition_lines_product_definitions_product_defini",
                table: "PurchaseRequisitionLines");

            migrationBuilder.DropForeignKey(
                name: "fk_purchase_requisition_lines_purchase_requisitions_purchase_requ",
                table: "PurchaseRequisitionLines");

            migrationBuilder.DropForeignKey(
                name: "fk_purchase_requisition_lines_vendor_products_vendor_product_id",
                table: "PurchaseRequisitionLines");

            migrationBuilder.DropForeignKey(
                name: "fk_purchase_requisition_lines_vendors_suggested_vendor_id",
                table: "PurchaseRequisitionLines");

            migrationBuilder.DropForeignKey(
                name: "fk_purchase_requisitions_purchase_orders_associated_purchase_or",
                table: "PurchaseRequisitions");

            migrationBuilder.DropForeignKey(
                name: "fk_request_for_quote_lines_product_definitions_product_definit",
                schema: "public",
                table: "request_for_quote_lines");

            migrationBuilder.DropForeignKey(
                name: "fk_request_for_quote_lines_requests_for_quote_request_for_quot",
                schema: "public",
                table: "request_for_quote_lines");

            migrationBuilder.DropForeignKey(
                name: "fk_request_for_quote_lines_vendor_products_vendor_product_id",
                schema: "public",
                table: "request_for_quote_lines");

            migrationBuilder.DropForeignKey(
                name: "fk_request_for_quotes_contracts_related_agreement_id",
                schema: "public",
                table: "request_for_quotes");

            migrationBuilder.DropForeignKey(
                name: "fk_request_for_quotes_purchase_requisitions_originating_requisi",
                schema: "public",
                table: "request_for_quotes");

            migrationBuilder.DropForeignKey(
                name: "fk_request_for_quotes_vendors_awarded_vendor_id",
                schema: "public",
                table: "request_for_quotes");

            migrationBuilder.DropForeignKey(
                name: "fk_requests_for_information_projects_project_id",
                table: "requests_for_information");

            migrationBuilder.DropForeignKey(
                name: "fk_requests_for_proposal_contracts_awarded_contract_id",
                table: "requests_for_proposal");

            migrationBuilder.DropForeignKey(
                name: "fk_requests_for_proposal_projects_project_id",
                table: "requests_for_proposal");

            migrationBuilder.DropForeignKey(
                name: "fk_requests_for_proposal_vendors_awarded_vendor_id",
                table: "requests_for_proposal");

            migrationBuilder.DropForeignKey(
                name: "fk_return_authorization_lines_invoice_lines_invoice_id_invoice",
                table: "return_authorization_lines");

            migrationBuilder.DropForeignKey(
                name: "fk_return_authorization_lines_product_definitions_product_defi",
                table: "return_authorization_lines");

            migrationBuilder.DropForeignKey(
                name: "fk_return_authorization_lines_products_product_id",
                table: "return_authorization_lines");

            migrationBuilder.DropForeignKey(
                name: "fk_return_authorization_lines_return_authorizations_return_aut",
                table: "return_authorization_lines");

            migrationBuilder.DropForeignKey(
                name: "fk_return_authorization_lines_sales_order_lines_original_sales",
                table: "return_authorization_lines");

            migrationBuilder.DropForeignKey(
                name: "fk_return_authorization_lines_sales_order_lines_sales_order_id",
                table: "return_authorization_lines");

            migrationBuilder.DropForeignKey(
                name: "fk_return_authorization_lines_vendor_products_vendor_product_id",
                table: "return_authorization_lines");

            migrationBuilder.DropForeignKey(
                name: "fk_return_authorizations_customers_customer_id",
                table: "return_authorizations");

            migrationBuilder.DropForeignKey(
                name: "fk_return_authorizations_invoices_invoice_id",
                table: "return_authorizations");

            migrationBuilder.DropForeignKey(
                name: "fk_return_authorizations_sales_orders_sales_order_id",
                table: "return_authorizations");

            migrationBuilder.DropForeignKey(
                name: "fk_sales_order_lines_products_product_id",
                table: "sales_order_lines");

            migrationBuilder.DropForeignKey(
                name: "fk_sales_order_lines_projects_project_id",
                table: "sales_order_lines");

            migrationBuilder.DropForeignKey(
                name: "fk_sales_order_lines_projects_project_id1",
                table: "sales_order_lines");

            migrationBuilder.DropForeignKey(
                name: "fk_sales_order_lines_sales_order_lines_parent_sales_order_line",
                table: "sales_order_lines");

            migrationBuilder.DropForeignKey(
                name: "fk_sales_order_lines_sales_orders_sales_order_id",
                table: "sales_order_lines");

            migrationBuilder.DropForeignKey(
                name: "fk_sales_order_lines_vendor_products_vendor_product_id",
                table: "sales_order_lines");

            migrationBuilder.DropForeignKey(
                name: "fk_sales_orders_customers_customer_id",
                schema: "public",
                table: "sales_orders");

            migrationBuilder.DropForeignKey(
                name: "fk_sales_orders_return_authorizations_related_return_authoriza",
                schema: "public",
                table: "sales_orders");

            migrationBuilder.DropForeignKey(
                name: "fk_sales_orders_sales_territories_sales_territory_id",
                schema: "public",
                table: "sales_orders");

            migrationBuilder.DropForeignKey(
                name: "fk_sales_orders_sales_territories_sales_territory_id1",
                schema: "public",
                table: "sales_orders");

            migrationBuilder.DropForeignKey(
                name: "fk_sales_territories_sales_territories_parent_territory_id",
                table: "sales_territories");

            migrationBuilder.DropForeignKey(
                name: "fk_sales_territory_representatives_asp_net_users_representative_",
                table: "sales_territory_representatives");

            migrationBuilder.DropForeignKey(
                name: "fk_sales_territory_representatives_sales_territories_sales_ter",
                table: "sales_territory_representatives");

            migrationBuilder.DropForeignKey(
                name: "fk_submittal_reviews_asp_net_users_reviewer_id",
                table: "submittal_reviews");

            migrationBuilder.DropForeignKey(
                name: "fk_submittal_reviews_technical_submittals_technical_submittal_",
                table: "submittal_reviews");

            migrationBuilder.DropForeignKey(
                name: "fk_submittal_reviews_technical_submittals_technical_submittal_1",
                table: "submittal_reviews");

            migrationBuilder.DropForeignKey(
                name: "fk_suppliers_vendors_vendor_id",
                table: "suppliers");

            migrationBuilder.DropForeignKey(
                name: "fk_technical_submittals_asp_net_users_submitted_by_user_id",
                table: "technical_submittals");

            migrationBuilder.DropForeignKey(
                name: "fk_technical_submittals_contracts_contract_id",
                table: "technical_submittals");

            migrationBuilder.DropForeignKey(
                name: "fk_technical_submittals_projects_project_id",
                table: "technical_submittals");

            migrationBuilder.DropForeignKey(
                name: "fk_technical_submittals_purchase_order_lines_purchase_order_li",
                table: "technical_submittals");

            migrationBuilder.DropForeignKey(
                name: "fk_technical_submittals_vendors_vendor_id",
                table: "technical_submittals");

            migrationBuilder.DropForeignKey(
                name: "fk_vendor_products_product_definitions_product_definition_id",
                table: "vendor_products");

            migrationBuilder.DropForeignKey(
                name: "fk_vendor_products_vendors_vendor_id",
                table: "vendor_products");

            migrationBuilder.DropForeignKey(
                name: "fk_vendor_proposals_requests_for_proposal_request_for_proposal",
                table: "vendor_proposals");

            migrationBuilder.DropForeignKey(
                name: "fk_vendor_proposals_vendors_vendor_id",
                table: "vendor_proposals");

            migrationBuilder.DropPrimaryKey(
                name: "pk_vendors",
                table: "vendors");

            migrationBuilder.DropPrimaryKey(
                name: "pk_vendor_proposals",
                table: "vendor_proposals");

            migrationBuilder.DropPrimaryKey(
                name: "pk_vendor_products",
                table: "vendor_products");

            migrationBuilder.DropPrimaryKey(
                name: "pk_test_entities",
                table: "test_entities");

            migrationBuilder.DropPrimaryKey(
                name: "pk_tenants",
                schema: "public",
                table: "tenants");

            migrationBuilder.DropPrimaryKey(
                name: "pk_tenant_products",
                table: "tenant_products");

            migrationBuilder.DropPrimaryKey(
                name: "pk_technical_submittals",
                table: "technical_submittals");

            migrationBuilder.DropPrimaryKey(
                name: "pk_suppliers",
                table: "suppliers");

            migrationBuilder.DropPrimaryKey(
                name: "pk_submittal_reviews",
                table: "submittal_reviews");

            migrationBuilder.DropPrimaryKey(
                name: "pk_sales_territory_representatives",
                table: "sales_territory_representatives");

            migrationBuilder.DropPrimaryKey(
                name: "pk_sales_territories",
                table: "sales_territories");

            migrationBuilder.DropPrimaryKey(
                name: "pk_sales_orders",
                schema: "public",
                table: "sales_orders");

            migrationBuilder.DropPrimaryKey(
                name: "pk_sales_order_lines",
                table: "sales_order_lines");

            migrationBuilder.DropPrimaryKey(
                name: "pk_return_authorizations",
                table: "return_authorizations");

            migrationBuilder.DropPrimaryKey(
                name: "pk_return_authorization_lines",
                table: "return_authorization_lines");

            migrationBuilder.DropPrimaryKey(
                name: "pk_requests_for_proposal",
                table: "requests_for_proposal");

            migrationBuilder.DropPrimaryKey(
                name: "pk_requests_for_information",
                table: "requests_for_information");

            migrationBuilder.DropPrimaryKey(
                name: "pk_request_for_quotes",
                schema: "public",
                table: "request_for_quotes");

            migrationBuilder.DropPrimaryKey(
                name: "pk_request_for_quote_lines",
                schema: "public",
                table: "request_for_quote_lines");

            migrationBuilder.DropPrimaryKey(
                name: "pk_purchase_requisitions",
                table: "PurchaseRequisitions");

            migrationBuilder.DropPrimaryKey(
                name: "pk_purchase_requisition_lines",
                table: "PurchaseRequisitionLines");

            migrationBuilder.DropPrimaryKey(
                name: "pk_purchase_order_lines",
                table: "PurchaseOrderLines");

            migrationBuilder.DropPrimaryKey(
                name: "pk_purchase_orders",
                table: "purchase_orders");

            migrationBuilder.DropPrimaryKey(
                name: "pk_projects",
                schema: "public",
                table: "projects");

            migrationBuilder.DropPrimaryKey(
                name: "pk_products",
                schema: "public",
                table: "products");

            migrationBuilder.DropPrimaryKey(
                name: "pk_product_definitions",
                table: "product_definitions");

            migrationBuilder.DropPrimaryKey(
                name: "pk_procurement_workflows",
                table: "procurement_workflows");

            migrationBuilder.DropPrimaryKey(
                name: "pk_procurement_workflow_steps",
                table: "procurement_workflow_steps");

            migrationBuilder.DropPrimaryKey(
                name: "pk_payment_transactions",
                table: "payment_transactions");

            migrationBuilder.DropPrimaryKey(
                name: "pk_invoices",
                table: "invoices");

            migrationBuilder.DropPrimaryKey(
                name: "pk_invoice_lines",
                table: "invoice_lines");

            migrationBuilder.DropPrimaryKey(
                name: "pk_goods_receipt_notes",
                table: "goods_receipt_notes");

            migrationBuilder.DropPrimaryKey(
                name: "pk_goods_receipt_note_lines",
                table: "goods_receipt_note_lines");

            migrationBuilder.DropPrimaryKey(
                name: "pk_departments",
                schema: "public",
                table: "departments");

            migrationBuilder.DropPrimaryKey(
                name: "pk_delivery_notes",
                table: "delivery_notes");

            migrationBuilder.DropPrimaryKey(
                name: "pk_delivery_note_lines",
                table: "delivery_note_lines");

            migrationBuilder.DropPrimaryKey(
                name: "pk_customers",
                schema: "public",
                table: "customers");

            migrationBuilder.DropPrimaryKey(
                name: "pk_contracts",
                table: "contracts");

            migrationBuilder.DropPrimaryKey(
                name: "pk_categories",
                schema: "public",
                table: "categories");

            migrationBuilder.DropPrimaryKey(
                name: "pk_budgets",
                schema: "public",
                table: "budgets");

            migrationBuilder.DropPrimaryKey(
                name: "pk_budget_allocations",
                schema: "public",
                table: "budget_allocations");

            migrationBuilder.DropPrimaryKey(
                name: "pk_asp_net_user_tokens",
                table: "AspNetUserTokens");

            migrationBuilder.DropPrimaryKey(
                name: "pk_asp_net_users",
                table: "AspNetUsers");

            migrationBuilder.DropPrimaryKey(
                name: "pk_asp_net_user_roles",
                table: "AspNetUserRoles");

            migrationBuilder.DropPrimaryKey(
                name: "pk_asp_net_user_logins",
                table: "AspNetUserLogins");

            migrationBuilder.DropPrimaryKey(
                name: "pk_asp_net_user_claims",
                table: "AspNetUserClaims");

            migrationBuilder.DropPrimaryKey(
                name: "pk_asp_net_roles",
                table: "AspNetRoles");

            migrationBuilder.DropPrimaryKey(
                name: "pk_asp_net_role_claims",
                table: "AspNetRoleClaims");

            migrationBuilder.RenameTable(
                name: "document_link",
                newName: "DocumentLink");

            migrationBuilder.RenameColumn(
                name: "website",
                table: "vendors",
                newName: "Website");

            migrationBuilder.RenameColumn(
                name: "status",
                table: "vendors",
                newName: "Status");

            migrationBuilder.RenameColumn(
                name: "name",
                table: "vendors",
                newName: "Name");

            migrationBuilder.RenameColumn(
                name: "id",
                table: "vendors",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "vendor_code",
                table: "vendors",
                newName: "VendorCode");

            migrationBuilder.RenameColumn(
                name: "vat_number",
                table: "vendors",
                newName: "VatNumber");

            migrationBuilder.RenameColumn(
                name: "tax_id",
                table: "vendors",
                newName: "TaxId");

            migrationBuilder.RenameColumn(
                name: "phone_number",
                table: "vendors",
                newName: "PhoneNumber");

            migrationBuilder.RenameColumn(
                name: "modified_at",
                table: "vendors",
                newName: "ModifiedAt");

            migrationBuilder.RenameColumn(
                name: "created_at",
                table: "vendors",
                newName: "CreatedAt");

            migrationBuilder.RenameColumn(
                name: "contact_name",
                table: "vendors",
                newName: "ContactName");

            migrationBuilder.RenameColumn(
                name: "contact_email",
                table: "vendors",
                newName: "ContactEmail");

            migrationBuilder.RenameColumn(
                name: "commercial_registration_number",
                table: "vendors",
                newName: "CommercialRegistrationNumber");

            migrationBuilder.RenameIndex(
                name: "ix_vendors_status",
                table: "vendors",
                newName: "IX_vendors_Status");

            migrationBuilder.RenameIndex(
                name: "ix_vendors_name",
                table: "vendors",
                newName: "IX_vendors_Name");

            migrationBuilder.RenameIndex(
                name: "ix_vendors_vendor_code",
                table: "vendors",
                newName: "IX_vendors_VendorCode");

            migrationBuilder.RenameIndex(
                name: "ix_vendors_vat_number",
                table: "vendors",
                newName: "IX_vendors_VatNumber");

            migrationBuilder.RenameIndex(
                name: "ix_vendors_tax_id",
                table: "vendors",
                newName: "IX_vendors_TaxId");

            migrationBuilder.RenameIndex(
                name: "ix_vendors_commercial_registration_number",
                table: "vendors",
                newName: "IX_vendors_CommercialRegistrationNumber");

            migrationBuilder.RenameColumn(
                name: "version",
                table: "vendor_proposals",
                newName: "Version");

            migrationBuilder.RenameColumn(
                name: "status",
                table: "vendor_proposals",
                newName: "Status");

            migrationBuilder.RenameColumn(
                name: "comments",
                table: "vendor_proposals",
                newName: "Comments");

            migrationBuilder.RenameColumn(
                name: "id",
                table: "vendor_proposals",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "vendor_id",
                table: "vendor_proposals",
                newName: "VendorId");

            migrationBuilder.RenameColumn(
                name: "value_added_services",
                table: "vendor_proposals",
                newName: "ValueAddedServices");

            migrationBuilder.RenameColumn(
                name: "validity_period_days",
                table: "vendor_proposals",
                newName: "ValidityPeriodDays");

            migrationBuilder.RenameColumn(
                name: "validity_end_date",
                table: "vendor_proposals",
                newName: "ValidityEndDate");

            migrationBuilder.RenameColumn(
                name: "total_proposed_value",
                table: "vendor_proposals",
                newName: "TotalProposedValue");

            migrationBuilder.RenameColumn(
                name: "tenant_id",
                table: "vendor_proposals",
                newName: "TenantId");

            migrationBuilder.RenameColumn(
                name: "sustainability_commitments",
                table: "vendor_proposals",
                newName: "SustainabilityCommitments");

            migrationBuilder.RenameColumn(
                name: "submission_date",
                table: "vendor_proposals",
                newName: "SubmissionDate");

            migrationBuilder.RenameColumn(
                name: "subcontractor_disclosures",
                table: "vendor_proposals",
                newName: "SubcontractorDisclosures");

            migrationBuilder.RenameColumn(
                name: "risk_sharing_clauses",
                table: "vendor_proposals",
                newName: "RiskSharingClauses");

            migrationBuilder.RenameColumn(
                name: "request_for_proposal_id",
                table: "vendor_proposals",
                newName: "RequestForProposalId");

            migrationBuilder.RenameColumn(
                name: "performance_guarantees",
                table: "vendor_proposals",
                newName: "PerformanceGuarantees");

            migrationBuilder.RenameColumn(
                name: "modified_at",
                table: "vendor_proposals",
                newName: "ModifiedAt");

            migrationBuilder.RenameColumn(
                name: "is_deleted",
                table: "vendor_proposals",
                newName: "IsDeleted");

            migrationBuilder.RenameColumn(
                name: "created_at",
                table: "vendor_proposals",
                newName: "CreatedAt");

            migrationBuilder.RenameColumn(
                name: "compliance_certifications_json",
                table: "vendor_proposals",
                newName: "ComplianceCertificationsJson");

            migrationBuilder.RenameColumn(
                name: "alternate_payment_terms",
                table: "vendor_proposals",
                newName: "AlternatePaymentTerms");

            migrationBuilder.RenameIndex(
                name: "ix_vendor_proposals_status",
                table: "vendor_proposals",
                newName: "IX_vendor_proposals_Status");

            migrationBuilder.RenameIndex(
                name: "ix_vendor_proposals_vendor_id",
                table: "vendor_proposals",
                newName: "IX_vendor_proposals_VendorId");

            migrationBuilder.RenameIndex(
                name: "ix_vendor_proposals_tenant_id",
                table: "vendor_proposals",
                newName: "IX_vendor_proposals_TenantId");

            migrationBuilder.RenameIndex(
                name: "ix_vendor_proposals_submission_date",
                table: "vendor_proposals",
                newName: "IX_vendor_proposals_SubmissionDate");

            migrationBuilder.RenameIndex(
                name: "ix_vendor_proposals_request_for_proposal_id_vendor_id",
                table: "vendor_proposals",
                newName: "IX_vendor_proposals_RequestForProposalId_VendorId");

            migrationBuilder.RenameIndex(
                name: "ix_vendor_proposals_request_for_proposal_id",
                table: "vendor_proposals",
                newName: "IX_vendor_proposals_RequestForProposalId");

            migrationBuilder.RenameColumn(
                name: "id",
                table: "vendor_products",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "vendor_sku",
                table: "vendor_products",
                newName: "VendorSku");

            migrationBuilder.RenameColumn(
                name: "vendor_id",
                table: "vendor_products",
                newName: "VendorId");

            migrationBuilder.RenameColumn(
                name: "unit_of_measure",
                table: "vendor_products",
                newName: "UnitOfMeasure");

            migrationBuilder.RenameColumn(
                name: "product_definition_id",
                table: "vendor_products",
                newName: "ProductDefinitionId");

            migrationBuilder.RenameColumn(
                name: "pack_size",
                table: "vendor_products",
                newName: "PackSize");

            migrationBuilder.RenameColumn(
                name: "modified_at",
                table: "vendor_products",
                newName: "ModifiedAt");

            migrationBuilder.RenameColumn(
                name: "lead_time_days",
                table: "vendor_products",
                newName: "LeadTimeDays");

            migrationBuilder.RenameColumn(
                name: "is_active",
                table: "vendor_products",
                newName: "IsActive");

            migrationBuilder.RenameColumn(
                name: "created_at",
                table: "vendor_products",
                newName: "CreatedAt");

            migrationBuilder.RenameIndex(
                name: "ix_vendor_products_vendor_id_vendor_sku",
                table: "vendor_products",
                newName: "IX_vendor_products_VendorId_VendorSku");

            migrationBuilder.RenameIndex(
                name: "ix_vendor_products_vendor_id_product_definition_id_unit_of_mea",
                table: "vendor_products",
                newName: "IX_vendor_products_VendorId_ProductDefinitionId_UnitOfMeasure_~");

            migrationBuilder.RenameIndex(
                name: "ix_vendor_products_product_definition_id",
                table: "vendor_products",
                newName: "IX_vendor_products_ProductDefinitionId");

            migrationBuilder.RenameColumn(
                name: "name",
                table: "test_entities",
                newName: "Name");

            migrationBuilder.RenameColumn(
                name: "id",
                table: "test_entities",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "tenant_id",
                table: "test_entities",
                newName: "TenantId");

            migrationBuilder.RenameColumn(
                name: "settings",
                schema: "public",
                table: "tenants",
                newName: "Settings");

            migrationBuilder.RenameColumn(
                name: "name",
                schema: "public",
                table: "tenants",
                newName: "Name");

            migrationBuilder.RenameColumn(
                name: "identifier",
                schema: "public",
                table: "tenants",
                newName: "Identifier");

            migrationBuilder.RenameColumn(
                name: "country",
                schema: "public",
                table: "tenants",
                newName: "Country");

            migrationBuilder.RenameColumn(
                name: "city",
                schema: "public",
                table: "tenants",
                newName: "City");

            migrationBuilder.RenameColumn(
                name: "id",
                schema: "public",
                table: "tenants",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "subscription_plan",
                schema: "public",
                table: "tenants",
                newName: "SubscriptionPlan");

            migrationBuilder.RenameColumn(
                name: "postal_code",
                schema: "public",
                table: "tenants",
                newName: "PostalCode");

            migrationBuilder.RenameColumn(
                name: "modified_at",
                schema: "public",
                table: "tenants",
                newName: "ModifiedAt");

            migrationBuilder.RenameColumn(
                name: "is_active",
                schema: "public",
                table: "tenants",
                newName: "IsActive");

            migrationBuilder.RenameColumn(
                name: "created_at",
                schema: "public",
                table: "tenants",
                newName: "CreatedAt");

            migrationBuilder.RenameColumn(
                name: "contact_email",
                schema: "public",
                table: "tenants",
                newName: "ContactEmail");

            migrationBuilder.RenameColumn(
                name: "address_line1",
                schema: "public",
                table: "tenants",
                newName: "AddressLine1");

            migrationBuilder.RenameColumn(
                name: "sku",
                table: "tenant_products",
                newName: "SKU");

            migrationBuilder.RenameColumn(
                name: "price",
                table: "tenant_products",
                newName: "Price");

            migrationBuilder.RenameColumn(
                name: "name",
                table: "tenant_products",
                newName: "Name");

            migrationBuilder.RenameColumn(
                name: "description",
                table: "tenant_products",
                newName: "Description");

            migrationBuilder.RenameColumn(
                name: "id",
                table: "tenant_products",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "tenant_id",
                table: "tenant_products",
                newName: "TenantId");

            migrationBuilder.RenameColumn(
                name: "modified_at",
                table: "tenant_products",
                newName: "ModifiedAt");

            migrationBuilder.RenameColumn(
                name: "created_at",
                table: "tenant_products",
                newName: "CreatedAt");

            migrationBuilder.RenameIndex(
                name: "ix_tenant_products_tenant_id",
                table: "tenant_products",
                newName: "IX_tenant_products_TenantId");

            migrationBuilder.RenameColumn(
                name: "title",
                table: "technical_submittals",
                newName: "Title");

            migrationBuilder.RenameColumn(
                name: "status",
                table: "technical_submittals",
                newName: "Status");

            migrationBuilder.RenameColumn(
                name: "revision",
                table: "technical_submittals",
                newName: "Revision");

            migrationBuilder.RenameColumn(
                name: "description",
                table: "technical_submittals",
                newName: "Description");

            migrationBuilder.RenameColumn(
                name: "id",
                table: "technical_submittals",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "vendor_id",
                table: "technical_submittals",
                newName: "VendorId");

            migrationBuilder.RenameColumn(
                name: "test_plan_id",
                table: "technical_submittals",
                newName: "TestPlanId");

            migrationBuilder.RenameColumn(
                name: "tenant_id",
                table: "technical_submittals",
                newName: "TenantId");

            migrationBuilder.RenameColumn(
                name: "submitted_documents",
                table: "technical_submittals",
                newName: "SubmittedDocuments");

            migrationBuilder.RenameColumn(
                name: "submitted_date",
                table: "technical_submittals",
                newName: "SubmittedDate");

            migrationBuilder.RenameColumn(
                name: "submitted_by_user_id",
                table: "technical_submittals",
                newName: "SubmittedByUserId");

            migrationBuilder.RenameColumn(
                name: "submitted_by_id",
                table: "technical_submittals",
                newName: "SubmittedById");

            migrationBuilder.RenameColumn(
                name: "submittal_type",
                table: "technical_submittals",
                newName: "SubmittalType");

            migrationBuilder.RenameColumn(
                name: "submittal_number",
                table: "technical_submittals",
                newName: "SubmittalNumber");

            migrationBuilder.RenameColumn(
                name: "specification_section",
                table: "technical_submittals",
                newName: "SpecificationSection");

            migrationBuilder.RenameColumn(
                name: "specification_id",
                table: "technical_submittals",
                newName: "SpecificationId");

            migrationBuilder.RenameColumn(
                name: "signed_off_date",
                table: "technical_submittals",
                newName: "SignedOffDate");

            migrationBuilder.RenameColumn(
                name: "signed_off_by_id",
                table: "technical_submittals",
                newName: "SignedOffById");

            migrationBuilder.RenameColumn(
                name: "review_start_date",
                table: "technical_submittals",
                newName: "ReviewStartDate");

            migrationBuilder.RenameColumn(
                name: "review_due_date",
                table: "technical_submittals",
                newName: "ReviewDueDate");

            migrationBuilder.RenameColumn(
                name: "review_completion_date",
                table: "technical_submittals",
                newName: "ReviewCompletionDate");

            migrationBuilder.RenameColumn(
                name: "resubmission_count",
                table: "technical_submittals",
                newName: "ResubmissionCount");

            migrationBuilder.RenameColumn(
                name: "required_date",
                table: "technical_submittals",
                newName: "RequiredDate");

            migrationBuilder.RenameColumn(
                name: "related_ncr_reference",
                table: "technical_submittals",
                newName: "RelatedNCRReference");

            migrationBuilder.RenameColumn(
                name: "related_itp_reference",
                table: "technical_submittals",
                newName: "RelatedITPReference");

            migrationBuilder.RenameColumn(
                name: "purchase_order_line_id",
                table: "technical_submittals",
                newName: "PurchaseOrderLineId");

            migrationBuilder.RenameColumn(
                name: "project_id",
                table: "technical_submittals",
                newName: "ProjectId");

            migrationBuilder.RenameColumn(
                name: "non_conformance_report_id",
                table: "technical_submittals",
                newName: "NonConformanceReportId");

            migrationBuilder.RenameColumn(
                name: "modified_at",
                table: "technical_submittals",
                newName: "ModifiedAt");

            migrationBuilder.RenameColumn(
                name: "max_resubmissions",
                table: "technical_submittals",
                newName: "MaxResubmissions");

            migrationBuilder.RenameColumn(
                name: "is_final_documentation",
                table: "technical_submittals",
                newName: "IsFinalDocumentation");

            migrationBuilder.RenameColumn(
                name: "is_deleted",
                table: "technical_submittals",
                newName: "IsDeleted");

            migrationBuilder.RenameColumn(
                name: "is_as_built",
                table: "technical_submittals",
                newName: "IsAsBuilt");

            migrationBuilder.RenameColumn(
                name: "final_sign_off_date",
                table: "technical_submittals",
                newName: "FinalSignOffDate");

            migrationBuilder.RenameColumn(
                name: "final_sign_off_by_id",
                table: "technical_submittals",
                newName: "FinalSignOffById");

            migrationBuilder.RenameColumn(
                name: "cycle_count",
                table: "technical_submittals",
                newName: "CycleCount");

            migrationBuilder.RenameColumn(
                name: "current_reviewer_id",
                table: "technical_submittals",
                newName: "CurrentReviewerId");

            migrationBuilder.RenameColumn(
                name: "current_review_cycle",
                table: "technical_submittals",
                newName: "CurrentReviewCycle");

            migrationBuilder.RenameColumn(
                name: "current_overall_disposition",
                table: "technical_submittals",
                newName: "CurrentOverallDisposition");

            migrationBuilder.RenameColumn(
                name: "created_at",
                table: "technical_submittals",
                newName: "CreatedAt");

            migrationBuilder.RenameColumn(
                name: "contract_id",
                table: "technical_submittals",
                newName: "ContractId");

            migrationBuilder.RenameIndex(
                name: "ix_technical_submittals_status",
                table: "technical_submittals",
                newName: "IX_technical_submittals_Status");

            migrationBuilder.RenameIndex(
                name: "ix_technical_submittals_vendor_id",
                table: "technical_submittals",
                newName: "IX_technical_submittals_VendorId");

            migrationBuilder.RenameIndex(
                name: "ix_technical_submittals_tenant_id",
                table: "technical_submittals",
                newName: "IX_technical_submittals_TenantId");

            migrationBuilder.RenameIndex(
                name: "ix_technical_submittals_submitted_date",
                table: "technical_submittals",
                newName: "IX_technical_submittals_SubmittedDate");

            migrationBuilder.RenameIndex(
                name: "ix_technical_submittals_submitted_by_user_id",
                table: "technical_submittals",
                newName: "IX_technical_submittals_SubmittedByUserId");

            migrationBuilder.RenameIndex(
                name: "ix_technical_submittals_submittal_number",
                table: "technical_submittals",
                newName: "IX_technical_submittals_SubmittalNumber");

            migrationBuilder.RenameIndex(
                name: "ix_technical_submittals_specification_id",
                table: "technical_submittals",
                newName: "IX_technical_submittals_SpecificationId");

            migrationBuilder.RenameIndex(
                name: "ix_technical_submittals_required_date",
                table: "technical_submittals",
                newName: "IX_technical_submittals_RequiredDate");

            migrationBuilder.RenameIndex(
                name: "ix_technical_submittals_purchase_order_line_id",
                table: "technical_submittals",
                newName: "IX_technical_submittals_PurchaseOrderLineId");

            migrationBuilder.RenameIndex(
                name: "ix_technical_submittals_project_id_submittal_number_revision",
                table: "technical_submittals",
                newName: "IX_technical_submittals_ProjectId_SubmittalNumber_Revision");

            migrationBuilder.RenameIndex(
                name: "ix_technical_submittals_project_id",
                table: "technical_submittals",
                newName: "IX_technical_submittals_ProjectId");

            migrationBuilder.RenameIndex(
                name: "ix_technical_submittals_contract_id",
                table: "technical_submittals",
                newName: "IX_technical_submittals_ContractId");

            migrationBuilder.RenameColumn(
                name: "status",
                table: "suppliers",
                newName: "Status");

            migrationBuilder.RenameColumn(
                name: "id",
                table: "suppliers",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "vendor_id",
                table: "suppliers",
                newName: "VendorId");

            migrationBuilder.RenameColumn(
                name: "sustainability_score",
                table: "suppliers",
                newName: "SustainabilityScore");

            migrationBuilder.RenameColumn(
                name: "supplier_name",
                table: "suppliers",
                newName: "SupplierName");

            migrationBuilder.RenameColumn(
                name: "risk_rating",
                table: "suppliers",
                newName: "RiskRating");

            migrationBuilder.RenameColumn(
                name: "modified_at",
                table: "suppliers",
                newName: "ModifiedAt");

            migrationBuilder.RenameColumn(
                name: "is_csr_compliant",
                table: "suppliers",
                newName: "IsCsrCompliant");

            migrationBuilder.RenameColumn(
                name: "is_contract_manufacturer",
                table: "suppliers",
                newName: "IsContractManufacturer");

            migrationBuilder.RenameColumn(
                name: "created_at",
                table: "suppliers",
                newName: "CreatedAt");

            migrationBuilder.RenameColumn(
                name: "capacity_utilization_percent",
                table: "suppliers",
                newName: "CapacityUtilizationPercent");

            migrationBuilder.RenameColumn(
                name: "average_lead_time_days",
                table: "suppliers",
                newName: "AverageLeadTimeDays");

            migrationBuilder.RenameColumn(
                name: "emergency_contacts",
                table: "suppliers",
                newName: "EmergencyContacts");

            migrationBuilder.RenameIndex(
                name: "ix_suppliers_vendor_id",
                table: "suppliers",
                newName: "IX_suppliers_VendorId");

            migrationBuilder.RenameIndex(
                name: "ix_suppliers_risk_rating",
                table: "suppliers",
                newName: "IX_suppliers_RiskRating");

            migrationBuilder.RenameIndex(
                name: "ix_suppliers_is_contract_manufacturer",
                table: "suppliers",
                newName: "IX_suppliers_IsContractManufacturer");

            migrationBuilder.RenameColumn(
                name: "disposition",
                table: "submittal_reviews",
                newName: "Disposition");

            migrationBuilder.RenameColumn(
                name: "comments",
                table: "submittal_reviews",
                newName: "Comments");

            migrationBuilder.RenameColumn(
                name: "id",
                table: "submittal_reviews",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "technical_submittal_id1",
                table: "submittal_reviews",
                newName: "TechnicalSubmittalId1");

            migrationBuilder.RenameColumn(
                name: "technical_submittal_id",
                table: "submittal_reviews",
                newName: "TechnicalSubmittalId");

            migrationBuilder.RenameColumn(
                name: "reviewer_name",
                table: "submittal_reviews",
                newName: "ReviewerName");

            migrationBuilder.RenameColumn(
                name: "reviewer_id",
                table: "submittal_reviews",
                newName: "ReviewerId");

            migrationBuilder.RenameColumn(
                name: "reviewer_guid",
                table: "submittal_reviews",
                newName: "ReviewerGuid");

            migrationBuilder.RenameColumn(
                name: "review_date",
                table: "submittal_reviews",
                newName: "ReviewDate");

            migrationBuilder.RenameColumn(
                name: "review_cycle",
                table: "submittal_reviews",
                newName: "ReviewCycle");

            migrationBuilder.RenameColumn(
                name: "modified_at",
                table: "submittal_reviews",
                newName: "ModifiedAt");

            migrationBuilder.RenameColumn(
                name: "markup_document",
                table: "submittal_reviews",
                newName: "MarkupDocument");

            migrationBuilder.RenameColumn(
                name: "created_at",
                table: "submittal_reviews",
                newName: "CreatedAt");

            migrationBuilder.RenameIndex(
                name: "ix_submittal_reviews_disposition",
                table: "submittal_reviews",
                newName: "IX_submittal_reviews_Disposition");

            migrationBuilder.RenameIndex(
                name: "ix_submittal_reviews_technical_submittal_id1",
                table: "submittal_reviews",
                newName: "IX_submittal_reviews_TechnicalSubmittalId1");

            migrationBuilder.RenameIndex(
                name: "ix_submittal_reviews_technical_submittal_id",
                table: "submittal_reviews",
                newName: "IX_submittal_reviews_TechnicalSubmittalId");

            migrationBuilder.RenameIndex(
                name: "ix_submittal_reviews_reviewer_id",
                table: "submittal_reviews",
                newName: "IX_submittal_reviews_ReviewerId");

            migrationBuilder.RenameIndex(
                name: "ix_submittal_reviews_review_date",
                table: "submittal_reviews",
                newName: "IX_submittal_reviews_ReviewDate");

            migrationBuilder.RenameColumn(
                name: "representative_id",
                table: "sales_territory_representatives",
                newName: "RepresentativeId");

            migrationBuilder.RenameColumn(
                name: "sales_territory_id",
                table: "sales_territory_representatives",
                newName: "SalesTerritoryId");

            migrationBuilder.RenameIndex(
                name: "ix_sales_territory_representatives_representative_id",
                table: "sales_territory_representatives",
                newName: "IX_sales_territory_representatives_RepresentativeId");

            migrationBuilder.RenameColumn(
                name: "name",
                table: "sales_territories",
                newName: "Name");

            migrationBuilder.RenameColumn(
                name: "description",
                table: "sales_territories",
                newName: "Description");

            migrationBuilder.RenameColumn(
                name: "code",
                table: "sales_territories",
                newName: "Code");

            migrationBuilder.RenameColumn(
                name: "id",
                table: "sales_territories",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "territory_code",
                table: "sales_territories",
                newName: "TerritoryCode");

            migrationBuilder.RenameColumn(
                name: "tenant_id",
                table: "sales_territories",
                newName: "TenantId");

            migrationBuilder.RenameColumn(
                name: "primary_salesperson_id",
                table: "sales_territories",
                newName: "PrimarySalespersonId");

            migrationBuilder.RenameColumn(
                name: "parent_territory_id",
                table: "sales_territories",
                newName: "ParentTerritoryId");

            migrationBuilder.RenameColumn(
                name: "modified_at",
                table: "sales_territories",
                newName: "ModifiedAt");

            migrationBuilder.RenameColumn(
                name: "is_deleted",
                table: "sales_territories",
                newName: "IsDeleted");

            migrationBuilder.RenameColumn(
                name: "created_at",
                table: "sales_territories",
                newName: "CreatedAt");

            migrationBuilder.RenameIndex(
                name: "ix_sales_territories_name",
                table: "sales_territories",
                newName: "IX_sales_territories_Name");

            migrationBuilder.RenameIndex(
                name: "ix_sales_territories_territory_code",
                table: "sales_territories",
                newName: "IX_sales_territories_TerritoryCode");

            migrationBuilder.RenameIndex(
                name: "ix_sales_territories_tenant_id",
                table: "sales_territories",
                newName: "IX_sales_territories_TenantId");

            migrationBuilder.RenameIndex(
                name: "ix_sales_territories_parent_territory_id",
                table: "sales_territories",
                newName: "IX_sales_territories_ParentTerritoryId");

            migrationBuilder.RenameColumn(
                name: "status",
                schema: "public",
                table: "sales_orders",
                newName: "Status");

            migrationBuilder.RenameColumn(
                name: "id",
                schema: "public",
                table: "sales_orders",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "tenant_id",
                schema: "public",
                table: "sales_orders",
                newName: "TenantId");

            migrationBuilder.RenameColumn(
                name: "salesperson_id",
                schema: "public",
                table: "sales_orders",
                newName: "SalespersonId");

            migrationBuilder.RenameColumn(
                name: "sales_territory_id1",
                schema: "public",
                table: "sales_orders",
                newName: "SalesTerritoryId1");

            migrationBuilder.RenameColumn(
                name: "sales_territory_id",
                schema: "public",
                table: "sales_orders",
                newName: "SalesTerritoryId");

            migrationBuilder.RenameColumn(
                name: "related_return_authorization_id",
                schema: "public",
                table: "sales_orders",
                newName: "RelatedReturnAuthorizationId");

            migrationBuilder.RenameColumn(
                name: "order_number",
                schema: "public",
                table: "sales_orders",
                newName: "OrderNumber");

            migrationBuilder.RenameColumn(
                name: "order_date",
                schema: "public",
                table: "sales_orders",
                newName: "OrderDate");

            migrationBuilder.RenameColumn(
                name: "modified_at",
                schema: "public",
                table: "sales_orders",
                newName: "ModifiedAt");

            migrationBuilder.RenameColumn(
                name: "is_drop_shipment",
                schema: "public",
                table: "sales_orders",
                newName: "IsDropShipment");

            migrationBuilder.RenameColumn(
                name: "is_deleted",
                schema: "public",
                table: "sales_orders",
                newName: "IsDeleted");

            migrationBuilder.RenameColumn(
                name: "is_credit_approved",
                schema: "public",
                table: "sales_orders",
                newName: "IsCreditApproved");

            migrationBuilder.RenameColumn(
                name: "is_atp_confirmed",
                schema: "public",
                table: "sales_orders",
                newName: "IsAtpConfirmed");

            migrationBuilder.RenameColumn(
                name: "edi_transaction_reference",
                schema: "public",
                table: "sales_orders",
                newName: "EdiTransactionReference");

            migrationBuilder.RenameColumn(
                name: "customer_id",
                schema: "public",
                table: "sales_orders",
                newName: "CustomerId");

            migrationBuilder.RenameColumn(
                name: "currency_code",
                schema: "public",
                table: "sales_orders",
                newName: "CurrencyCode");

            migrationBuilder.RenameColumn(
                name: "created_at",
                schema: "public",
                table: "sales_orders",
                newName: "CreatedAt");

            migrationBuilder.RenameColumn(
                name: "commission_rate",
                schema: "public",
                table: "sales_orders",
                newName: "CommissionRate");

            migrationBuilder.RenameColumn(
                name: "atp_check_date",
                schema: "public",
                table: "sales_orders",
                newName: "AtpCheckDate");

            migrationBuilder.RenameIndex(
                name: "ix_sales_orders_status",
                schema: "public",
                table: "sales_orders",
                newName: "IX_sales_orders_Status");

            migrationBuilder.RenameIndex(
                name: "ix_sales_orders_tenant_id_order_number",
                schema: "public",
                table: "sales_orders",
                newName: "IX_sales_orders_TenantId_OrderNumber");

            migrationBuilder.RenameIndex(
                name: "ix_sales_orders_tenant_id",
                schema: "public",
                table: "sales_orders",
                newName: "IX_sales_orders_TenantId");

            migrationBuilder.RenameIndex(
                name: "ix_sales_orders_salesperson_id",
                schema: "public",
                table: "sales_orders",
                newName: "IX_sales_orders_SalespersonId");

            migrationBuilder.RenameIndex(
                name: "ix_sales_orders_sales_territory_id1",
                schema: "public",
                table: "sales_orders",
                newName: "IX_sales_orders_SalesTerritoryId1");

            migrationBuilder.RenameIndex(
                name: "ix_sales_orders_sales_territory_id",
                schema: "public",
                table: "sales_orders",
                newName: "IX_sales_orders_SalesTerritoryId");

            migrationBuilder.RenameIndex(
                name: "ix_sales_orders_related_return_authorization_id",
                schema: "public",
                table: "sales_orders",
                newName: "IX_sales_orders_RelatedReturnAuthorizationId");

            migrationBuilder.RenameIndex(
                name: "ix_sales_orders_order_date",
                schema: "public",
                table: "sales_orders",
                newName: "IX_sales_orders_OrderDate");

            migrationBuilder.RenameIndex(
                name: "ix_sales_orders_is_deleted",
                schema: "public",
                table: "sales_orders",
                newName: "IX_sales_orders_IsDeleted");

            migrationBuilder.RenameIndex(
                name: "ix_sales_orders_edi_transaction_reference",
                schema: "public",
                table: "sales_orders",
                newName: "IX_sales_orders_EdiTransactionReference");

            migrationBuilder.RenameIndex(
                name: "ix_sales_orders_customer_id",
                schema: "public",
                table: "sales_orders",
                newName: "IX_sales_orders_CustomerId");

            migrationBuilder.RenameColumn(
                name: "quantity",
                table: "sales_order_lines",
                newName: "Quantity");

            migrationBuilder.RenameColumn(
                name: "id",
                table: "sales_order_lines",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "description",
                table: "sales_order_lines",
                newName: "Description");

            migrationBuilder.RenameColumn(
                name: "warranty_end_date",
                table: "sales_order_lines",
                newName: "WarrantyEndDate");

            migrationBuilder.RenameColumn(
                name: "vendor_product_id",
                table: "sales_order_lines",
                newName: "VendorProductId");

            migrationBuilder.RenameColumn(
                name: "unit_price_currency",
                table: "sales_order_lines",
                newName: "UnitPriceCurrency");

            migrationBuilder.RenameColumn(
                name: "unit_price_amount",
                table: "sales_order_lines",
                newName: "UnitPriceAmount");

            migrationBuilder.RenameColumn(
                name: "unit_of_measure",
                table: "sales_order_lines",
                newName: "UnitOfMeasure");

            migrationBuilder.RenameColumn(
                name: "tenant_id",
                table: "sales_order_lines",
                newName: "TenantId");

            migrationBuilder.RenameColumn(
                name: "sku_snapshot",
                table: "sales_order_lines",
                newName: "SkuSnapshot");

            migrationBuilder.RenameColumn(
                name: "reserved_serial_numbers_json",
                table: "sales_order_lines",
                newName: "ReservedSerialNumbersJson");

            migrationBuilder.RenameColumn(
                name: "requested_delivery_date",
                table: "sales_order_lines",
                newName: "RequestedDeliveryDate");

            migrationBuilder.RenameColumn(
                name: "quantity_backordered",
                table: "sales_order_lines",
                newName: "QuantityBackordered");

            migrationBuilder.RenameColumn(
                name: "project_id1",
                table: "sales_order_lines",
                newName: "ProjectId1");

            migrationBuilder.RenameColumn(
                name: "project_id",
                table: "sales_order_lines",
                newName: "ProjectId");

            migrationBuilder.RenameColumn(
                name: "product_id",
                table: "sales_order_lines",
                newName: "ProductId");

            migrationBuilder.RenameColumn(
                name: "parent_sales_order_line_sales_order_id",
                table: "sales_order_lines",
                newName: "ParentSalesOrderLineSalesOrderId");

            migrationBuilder.RenameColumn(
                name: "parent_sales_order_line_line_number",
                table: "sales_order_lines",
                newName: "ParentSalesOrderLineLineNumber");

            migrationBuilder.RenameColumn(
                name: "parent_sales_order_line_id",
                table: "sales_order_lines",
                newName: "ParentSalesOrderLineId");

            migrationBuilder.RenameColumn(
                name: "modified_at",
                table: "sales_order_lines",
                newName: "ModifiedAt");

            migrationBuilder.RenameColumn(
                name: "line_total_currency",
                table: "sales_order_lines",
                newName: "LineTotalCurrency");

            migrationBuilder.RenameColumn(
                name: "line_total_amount",
                table: "sales_order_lines",
                newName: "LineTotalAmount");

            migrationBuilder.RenameColumn(
                name: "is_kit_component",
                table: "sales_order_lines",
                newName: "IsKitComponent");

            migrationBuilder.RenameColumn(
                name: "is_deleted",
                table: "sales_order_lines",
                newName: "IsDeleted");

            migrationBuilder.RenameColumn(
                name: "discount_amount_value",
                table: "sales_order_lines",
                newName: "DiscountAmountValue");

            migrationBuilder.RenameColumn(
                name: "discount_amount_currency",
                table: "sales_order_lines",
                newName: "DiscountAmountCurrency");

            migrationBuilder.RenameColumn(
                name: "description_snapshot",
                table: "sales_order_lines",
                newName: "DescriptionSnapshot");

            migrationBuilder.RenameColumn(
                name: "created_at",
                table: "sales_order_lines",
                newName: "CreatedAt");

            migrationBuilder.RenameColumn(
                name: "cost_code",
                table: "sales_order_lines",
                newName: "CostCode");

            migrationBuilder.RenameColumn(
                name: "applied_discount_description",
                table: "sales_order_lines",
                newName: "AppliedDiscountDescription");

            migrationBuilder.RenameColumn(
                name: "line_number",
                table: "sales_order_lines",
                newName: "LineNumber");

            migrationBuilder.RenameColumn(
                name: "sales_order_id",
                table: "sales_order_lines",
                newName: "SalesOrderId");

            migrationBuilder.RenameIndex(
                name: "ix_sales_order_lines_vendor_product_id",
                table: "sales_order_lines",
                newName: "IX_sales_order_lines_VendorProductId");

            migrationBuilder.RenameIndex(
                name: "ix_sales_order_lines_project_id1",
                table: "sales_order_lines",
                newName: "IX_sales_order_lines_ProjectId1");

            migrationBuilder.RenameIndex(
                name: "ix_sales_order_lines_project_id",
                table: "sales_order_lines",
                newName: "IX_sales_order_lines_ProjectId");

            migrationBuilder.RenameIndex(
                name: "ix_sales_order_lines_product_id",
                table: "sales_order_lines",
                newName: "IX_sales_order_lines_ProductId");

            migrationBuilder.RenameIndex(
                name: "ix_sales_order_lines_parent_sales_order_line_sales_order_id_pa",
                table: "sales_order_lines",
                newName: "IX_sales_order_lines_ParentSalesOrderLineSalesOrderId_ParentSa~");

            migrationBuilder.RenameColumn(
                name: "status",
                table: "return_authorizations",
                newName: "Status");

            migrationBuilder.RenameColumn(
                name: "notes",
                table: "return_authorizations",
                newName: "Notes");

            migrationBuilder.RenameColumn(
                name: "id",
                table: "return_authorizations",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "tenant_id",
                table: "return_authorizations",
                newName: "TenantId");

            migrationBuilder.RenameColumn(
                name: "shipping_instructions",
                table: "return_authorizations",
                newName: "ShippingInstructions");

            migrationBuilder.RenameColumn(
                name: "sales_order_id",
                table: "return_authorizations",
                newName: "SalesOrderId");

            migrationBuilder.RenameColumn(
                name: "rma_number",
                table: "return_authorizations",
                newName: "RmaNumber");

            migrationBuilder.RenameColumn(
                name: "requested_action",
                table: "return_authorizations",
                newName: "RequestedAction");

            migrationBuilder.RenameColumn(
                name: "request_date",
                table: "return_authorizations",
                newName: "RequestDate");

            migrationBuilder.RenameColumn(
                name: "reason_for_return",
                table: "return_authorizations",
                newName: "ReasonForReturn");

            migrationBuilder.RenameColumn(
                name: "original_sales_order_id",
                table: "return_authorizations",
                newName: "OriginalSalesOrderId");

            migrationBuilder.RenameColumn(
                name: "modified_at",
                table: "return_authorizations",
                newName: "ModifiedAt");

            migrationBuilder.RenameColumn(
                name: "is_deleted",
                table: "return_authorizations",
                newName: "IsDeleted");

            migrationBuilder.RenameColumn(
                name: "invoice_id",
                table: "return_authorizations",
                newName: "InvoiceId");

            migrationBuilder.RenameColumn(
                name: "expiry_date",
                table: "return_authorizations",
                newName: "ExpiryDate");

            migrationBuilder.RenameColumn(
                name: "customer_id",
                table: "return_authorizations",
                newName: "CustomerId");

            migrationBuilder.RenameColumn(
                name: "created_at",
                table: "return_authorizations",
                newName: "CreatedAt");

            migrationBuilder.RenameColumn(
                name: "authorization_date",
                table: "return_authorizations",
                newName: "AuthorizationDate");

            migrationBuilder.RenameIndex(
                name: "ix_return_authorizations_status",
                table: "return_authorizations",
                newName: "IX_return_authorizations_Status");

            migrationBuilder.RenameIndex(
                name: "ix_return_authorizations_tenant_id",
                table: "return_authorizations",
                newName: "IX_return_authorizations_TenantId");

            migrationBuilder.RenameIndex(
                name: "ix_return_authorizations_sales_order_id",
                table: "return_authorizations",
                newName: "IX_return_authorizations_SalesOrderId");

            migrationBuilder.RenameIndex(
                name: "ix_return_authorizations_rma_number",
                table: "return_authorizations",
                newName: "IX_return_authorizations_RmaNumber");

            migrationBuilder.RenameIndex(
                name: "ix_return_authorizations_request_date",
                table: "return_authorizations",
                newName: "IX_return_authorizations_RequestDate");

            migrationBuilder.RenameIndex(
                name: "ix_return_authorizations_invoice_id",
                table: "return_authorizations",
                newName: "IX_return_authorizations_InvoiceId");

            migrationBuilder.RenameIndex(
                name: "ix_return_authorizations_customer_id",
                table: "return_authorizations",
                newName: "IX_return_authorizations_CustomerId");

            migrationBuilder.RenameColumn(
                name: "id",
                table: "return_authorization_lines",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "vendor_product_id",
                table: "return_authorization_lines",
                newName: "VendorProductId");

            migrationBuilder.RenameColumn(
                name: "unit_of_measure",
                table: "return_authorization_lines",
                newName: "UnitOfMeasure");

            migrationBuilder.RenameColumn(
                name: "sku_snapshot",
                table: "return_authorization_lines",
                newName: "SkuSnapshot");

            migrationBuilder.RenameColumn(
                name: "sales_order_line_number",
                table: "return_authorization_lines",
                newName: "SalesOrderLineNumber");

            migrationBuilder.RenameColumn(
                name: "sales_order_id",
                table: "return_authorization_lines",
                newName: "SalesOrderId");

            migrationBuilder.RenameColumn(
                name: "requested_action",
                table: "return_authorization_lines",
                newName: "RequestedAction");

            migrationBuilder.RenameColumn(
                name: "reason_for_return",
                table: "return_authorization_lines",
                newName: "ReasonForReturn");

            migrationBuilder.RenameColumn(
                name: "quantity_received",
                table: "return_authorization_lines",
                newName: "QuantityReceived");

            migrationBuilder.RenameColumn(
                name: "quantity_authorized",
                table: "return_authorization_lines",
                newName: "QuantityAuthorized");

            migrationBuilder.RenameColumn(
                name: "product_id",
                table: "return_authorization_lines",
                newName: "ProductId");

            migrationBuilder.RenameColumn(
                name: "product_definition_id",
                table: "return_authorization_lines",
                newName: "ProductDefinitionId");

            migrationBuilder.RenameColumn(
                name: "original_sales_order_line_sales_order_id",
                table: "return_authorization_lines",
                newName: "OriginalSalesOrderLineSalesOrderId");

            migrationBuilder.RenameColumn(
                name: "original_sales_order_line_line_number",
                table: "return_authorization_lines",
                newName: "OriginalSalesOrderLineLineNumber");

            migrationBuilder.RenameColumn(
                name: "original_sales_order_line_id",
                table: "return_authorization_lines",
                newName: "OriginalSalesOrderLineId");

            migrationBuilder.RenameColumn(
                name: "modified_at",
                table: "return_authorization_lines",
                newName: "ModifiedAt");

            migrationBuilder.RenameColumn(
                name: "item_condition",
                table: "return_authorization_lines",
                newName: "ItemCondition");

            migrationBuilder.RenameColumn(
                name: "invoice_line_number",
                table: "return_authorization_lines",
                newName: "InvoiceLineNumber");

            migrationBuilder.RenameColumn(
                name: "invoice_id",
                table: "return_authorization_lines",
                newName: "InvoiceId");

            migrationBuilder.RenameColumn(
                name: "description_snapshot",
                table: "return_authorization_lines",
                newName: "DescriptionSnapshot");

            migrationBuilder.RenameColumn(
                name: "created_at",
                table: "return_authorization_lines",
                newName: "CreatedAt");

            migrationBuilder.RenameColumn(
                name: "line_number",
                table: "return_authorization_lines",
                newName: "LineNumber");

            migrationBuilder.RenameColumn(
                name: "return_authorization_id",
                table: "return_authorization_lines",
                newName: "ReturnAuthorizationId");

            migrationBuilder.RenameIndex(
                name: "ix_return_authorization_lines_vendor_product_id",
                table: "return_authorization_lines",
                newName: "IX_return_authorization_lines_VendorProductId");

            migrationBuilder.RenameIndex(
                name: "ix_return_authorization_lines_sales_order_id_sales_order_line_",
                table: "return_authorization_lines",
                newName: "IX_return_authorization_lines_SalesOrderId_SalesOrderLineNumber");

            migrationBuilder.RenameIndex(
                name: "ix_return_authorization_lines_product_id",
                table: "return_authorization_lines",
                newName: "IX_return_authorization_lines_ProductId");

            migrationBuilder.RenameIndex(
                name: "ix_return_authorization_lines_product_definition_id",
                table: "return_authorization_lines",
                newName: "IX_return_authorization_lines_ProductDefinitionId");

            migrationBuilder.RenameIndex(
                name: "ix_return_authorization_lines_original_sales_order_line_sales_",
                table: "return_authorization_lines",
                newName: "IX_return_authorization_lines_OriginalSalesOrderLineSalesOrder~");

            migrationBuilder.RenameIndex(
                name: "ix_return_authorization_lines_invoice_id_invoice_line_number",
                table: "return_authorization_lines",
                newName: "IX_return_authorization_lines_InvoiceId_InvoiceLineNumber");

            migrationBuilder.RenameColumn(
                name: "title",
                table: "requests_for_proposal",
                newName: "Title");

            migrationBuilder.RenameColumn(
                name: "status",
                table: "requests_for_proposal",
                newName: "Status");

            migrationBuilder.RenameColumn(
                name: "department",
                table: "requests_for_proposal",
                newName: "Department");

            migrationBuilder.RenameColumn(
                name: "id",
                table: "requests_for_proposal",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "tenant_id",
                table: "requests_for_proposal",
                newName: "TenantId");

            migrationBuilder.RenameColumn(
                name: "submission_deadline",
                table: "requests_for_proposal",
                newName: "SubmissionDeadline");

            migrationBuilder.RenameColumn(
                name: "scope_of_work",
                table: "requests_for_proposal",
                newName: "ScopeOfWork");

            migrationBuilder.RenameColumn(
                name: "rfp_number",
                table: "requests_for_proposal",
                newName: "RfpNumber");

            migrationBuilder.RenameColumn(
                name: "question_deadline",
                table: "requests_for_proposal",
                newName: "QuestionDeadline");

            migrationBuilder.RenameColumn(
                name: "project_id",
                table: "requests_for_proposal",
                newName: "ProjectId");

            migrationBuilder.RenameColumn(
                name: "modified_at",
                table: "requests_for_proposal",
                newName: "ModifiedAt");

            migrationBuilder.RenameColumn(
                name: "issued_date",
                table: "requests_for_proposal",
                newName: "IssuedDate");

            migrationBuilder.RenameColumn(
                name: "issued_by_user_id",
                table: "requests_for_proposal",
                newName: "IssuedByUserId");

            migrationBuilder.RenameColumn(
                name: "issued_by_name",
                table: "requests_for_proposal",
                newName: "IssuedByName");

            migrationBuilder.RenameColumn(
                name: "issue_date",
                table: "requests_for_proposal",
                newName: "IssueDate");

            migrationBuilder.RenameColumn(
                name: "expected_contract_start_date",
                table: "requests_for_proposal",
                newName: "ExpectedContractStartDate");

            migrationBuilder.RenameColumn(
                name: "expected_contract_duration",
                table: "requests_for_proposal",
                newName: "ExpectedContractDuration");

            migrationBuilder.RenameColumn(
                name: "evaluation_criteria",
                table: "requests_for_proposal",
                newName: "EvaluationCriteria");

            migrationBuilder.RenameColumn(
                name: "decision_date",
                table: "requests_for_proposal",
                newName: "DecisionDate");

            migrationBuilder.RenameColumn(
                name: "created_at",
                table: "requests_for_proposal",
                newName: "CreatedAt");

            migrationBuilder.RenameColumn(
                name: "completed_date",
                table: "requests_for_proposal",
                newName: "CompletedDate");

            migrationBuilder.RenameColumn(
                name: "awarded_vendor_id",
                table: "requests_for_proposal",
                newName: "AwardedVendorId");

            migrationBuilder.RenameColumn(
                name: "awarded_contract_id",
                table: "requests_for_proposal",
                newName: "AwardedContractId");

            migrationBuilder.RenameIndex(
                name: "ix_requests_for_proposal_status",
                table: "requests_for_proposal",
                newName: "IX_requests_for_proposal_Status");

            migrationBuilder.RenameIndex(
                name: "ix_requests_for_proposal_tenant_id",
                table: "requests_for_proposal",
                newName: "IX_requests_for_proposal_TenantId");

            migrationBuilder.RenameIndex(
                name: "ix_requests_for_proposal_submission_deadline",
                table: "requests_for_proposal",
                newName: "IX_requests_for_proposal_SubmissionDeadline");

            migrationBuilder.RenameIndex(
                name: "ix_requests_for_proposal_rfp_number",
                table: "requests_for_proposal",
                newName: "IX_requests_for_proposal_RfpNumber");

            migrationBuilder.RenameIndex(
                name: "ix_requests_for_proposal_project_id",
                table: "requests_for_proposal",
                newName: "IX_requests_for_proposal_ProjectId");

            migrationBuilder.RenameIndex(
                name: "ix_requests_for_proposal_awarded_vendor_id",
                table: "requests_for_proposal",
                newName: "IX_requests_for_proposal_AwardedVendorId");

            migrationBuilder.RenameIndex(
                name: "ix_requests_for_proposal_awarded_contract_id",
                table: "requests_for_proposal",
                newName: "IX_requests_for_proposal_AwardedContractId");

            migrationBuilder.RenameColumn(
                name: "title",
                table: "requests_for_information",
                newName: "Title");

            migrationBuilder.RenameColumn(
                name: "status",
                table: "requests_for_information",
                newName: "Status");

            migrationBuilder.RenameColumn(
                name: "notes",
                table: "requests_for_information",
                newName: "Notes");

            migrationBuilder.RenameColumn(
                name: "description",
                table: "requests_for_information",
                newName: "Description");

            migrationBuilder.RenameColumn(
                name: "id",
                table: "requests_for_information",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "tenant_id",
                table: "requests_for_information",
                newName: "TenantId");

            migrationBuilder.RenameColumn(
                name: "target_audience_description",
                table: "requests_for_information",
                newName: "TargetAudienceDescription");

            migrationBuilder.RenameColumn(
                name: "rfi_number",
                table: "requests_for_information",
                newName: "RfiNumber");

            migrationBuilder.RenameColumn(
                name: "response_due_date",
                table: "requests_for_information",
                newName: "ResponseDueDate");

            migrationBuilder.RenameColumn(
                name: "response_deadline",
                table: "requests_for_information",
                newName: "ResponseDeadline");

            migrationBuilder.RenameColumn(
                name: "project_id",
                table: "requests_for_information",
                newName: "ProjectId");

            migrationBuilder.RenameColumn(
                name: "modified_at",
                table: "requests_for_information",
                newName: "ModifiedAt");

            migrationBuilder.RenameColumn(
                name: "issued_date",
                table: "requests_for_information",
                newName: "IssuedDate");

            migrationBuilder.RenameColumn(
                name: "issued_by_user_id",
                table: "requests_for_information",
                newName: "IssuedByUserId");

            migrationBuilder.RenameColumn(
                name: "issued_by_name",
                table: "requests_for_information",
                newName: "IssuedByName");

            migrationBuilder.RenameColumn(
                name: "issue_date",
                table: "requests_for_information",
                newName: "IssueDate");

            migrationBuilder.RenameColumn(
                name: "created_at",
                table: "requests_for_information",
                newName: "CreatedAt");

            migrationBuilder.RenameIndex(
                name: "ix_requests_for_information_status",
                table: "requests_for_information",
                newName: "IX_requests_for_information_Status");

            migrationBuilder.RenameIndex(
                name: "ix_requests_for_information_tenant_id",
                table: "requests_for_information",
                newName: "IX_requests_for_information_TenantId");

            migrationBuilder.RenameIndex(
                name: "ix_requests_for_information_rfi_number",
                table: "requests_for_information",
                newName: "IX_requests_for_information_RfiNumber");

            migrationBuilder.RenameIndex(
                name: "ix_requests_for_information_response_deadline",
                table: "requests_for_information",
                newName: "IX_requests_for_information_ResponseDeadline");

            migrationBuilder.RenameIndex(
                name: "ix_requests_for_information_project_id",
                table: "requests_for_information",
                newName: "IX_requests_for_information_ProjectId");

            migrationBuilder.RenameColumn(
                name: "title",
                schema: "public",
                table: "request_for_quotes",
                newName: "Title");

            migrationBuilder.RenameColumn(
                name: "status",
                schema: "public",
                table: "request_for_quotes",
                newName: "Status");

            migrationBuilder.RenameColumn(
                name: "description",
                schema: "public",
                table: "request_for_quotes",
                newName: "Description");

            migrationBuilder.RenameColumn(
                name: "id",
                schema: "public",
                table: "request_for_quotes",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "vendor_reference_instructions",
                schema: "public",
                table: "request_for_quotes",
                newName: "VendorReferenceInstructions");

            migrationBuilder.RenameColumn(
                name: "terms_and_conditions",
                schema: "public",
                table: "request_for_quotes",
                newName: "TermsAndConditions");

            migrationBuilder.RenameColumn(
                name: "tenant_id",
                schema: "public",
                table: "request_for_quotes",
                newName: "TenantId");

            migrationBuilder.RenameColumn(
                name: "submission_deadline",
                schema: "public",
                table: "request_for_quotes",
                newName: "SubmissionDeadline");

            migrationBuilder.RenameColumn(
                name: "scope_of_work",
                schema: "public",
                table: "request_for_quotes",
                newName: "ScopeOfWork");

            migrationBuilder.RenameColumn(
                name: "rfq_number",
                schema: "public",
                table: "request_for_quotes",
                newName: "RFQNumber");

            migrationBuilder.RenameColumn(
                name: "required_delivery_date",
                schema: "public",
                table: "request_for_quotes",
                newName: "RequiredDeliveryDate");

            migrationBuilder.RenameColumn(
                name: "related_agreement_id",
                schema: "public",
                table: "request_for_quotes",
                newName: "RelatedAgreementId");

            migrationBuilder.RenameColumn(
                name: "originating_requisition_id",
                schema: "public",
                table: "request_for_quotes",
                newName: "OriginatingRequisitionId");

            migrationBuilder.RenameColumn(
                name: "modified_at",
                schema: "public",
                table: "request_for_quotes",
                newName: "ModifiedAt");

            migrationBuilder.RenameColumn(
                name: "is_deleted",
                schema: "public",
                table: "request_for_quotes",
                newName: "IsDeleted");

            migrationBuilder.RenameColumn(
                name: "currency_code",
                schema: "public",
                table: "request_for_quotes",
                newName: "CurrencyCode");

            migrationBuilder.RenameColumn(
                name: "created_by_user_id",
                schema: "public",
                table: "request_for_quotes",
                newName: "CreatedByUserId");

            migrationBuilder.RenameColumn(
                name: "created_at",
                schema: "public",
                table: "request_for_quotes",
                newName: "CreatedAt");

            migrationBuilder.RenameColumn(
                name: "contact_person_email",
                schema: "public",
                table: "request_for_quotes",
                newName: "ContactPersonEmail");

            migrationBuilder.RenameColumn(
                name: "communication_method",
                schema: "public",
                table: "request_for_quotes",
                newName: "CommunicationMethod");

            migrationBuilder.RenameColumn(
                name: "awarded_vendor_id",
                schema: "public",
                table: "request_for_quotes",
                newName: "AwardedVendorId");

            migrationBuilder.RenameIndex(
                name: "ix_request_for_quotes_status",
                schema: "public",
                table: "request_for_quotes",
                newName: "IX_request_for_quotes_Status");

            migrationBuilder.RenameIndex(
                name: "ix_request_for_quotes_tenant_id_rfq_number",
                schema: "public",
                table: "request_for_quotes",
                newName: "IX_request_for_quotes_TenantId_RFQNumber");

            migrationBuilder.RenameIndex(
                name: "ix_request_for_quotes_tenant_id",
                schema: "public",
                table: "request_for_quotes",
                newName: "IX_request_for_quotes_TenantId");

            migrationBuilder.RenameIndex(
                name: "ix_request_for_quotes_submission_deadline",
                schema: "public",
                table: "request_for_quotes",
                newName: "IX_request_for_quotes_SubmissionDeadline");

            migrationBuilder.RenameIndex(
                name: "ix_request_for_quotes_related_agreement_id",
                schema: "public",
                table: "request_for_quotes",
                newName: "IX_request_for_quotes_RelatedAgreementId");

            migrationBuilder.RenameIndex(
                name: "ix_request_for_quotes_originating_requisition_id",
                schema: "public",
                table: "request_for_quotes",
                newName: "IX_request_for_quotes_OriginatingRequisitionId");

            migrationBuilder.RenameIndex(
                name: "ix_request_for_quotes_is_deleted",
                schema: "public",
                table: "request_for_quotes",
                newName: "IX_request_for_quotes_IsDeleted");

            migrationBuilder.RenameIndex(
                name: "ix_request_for_quotes_created_by_user_id",
                schema: "public",
                table: "request_for_quotes",
                newName: "IX_request_for_quotes_CreatedByUserId");

            migrationBuilder.RenameIndex(
                name: "ix_request_for_quotes_awarded_vendor_id",
                schema: "public",
                table: "request_for_quotes",
                newName: "IX_request_for_quotes_AwardedVendorId");

            migrationBuilder.RenameColumn(
                name: "quantity",
                schema: "public",
                table: "request_for_quote_lines",
                newName: "Quantity");

            migrationBuilder.RenameColumn(
                name: "notes",
                schema: "public",
                table: "request_for_quote_lines",
                newName: "Notes");

            migrationBuilder.RenameColumn(
                name: "description",
                schema: "public",
                table: "request_for_quote_lines",
                newName: "Description");

            migrationBuilder.RenameColumn(
                name: "id",
                schema: "public",
                table: "request_for_quote_lines",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "vendor_product_id",
                schema: "public",
                table: "request_for_quote_lines",
                newName: "VendorProductId");

            migrationBuilder.RenameColumn(
                name: "unit_of_measure",
                schema: "public",
                table: "request_for_quote_lines",
                newName: "UnitOfMeasure");

            migrationBuilder.RenameColumn(
                name: "tenant_id",
                schema: "public",
                table: "request_for_quote_lines",
                newName: "TenantId");

            migrationBuilder.RenameColumn(
                name: "technical_specifications",
                schema: "public",
                table: "request_for_quote_lines",
                newName: "TechnicalSpecifications");

            migrationBuilder.RenameColumn(
                name: "sample_required",
                schema: "public",
                table: "request_for_quote_lines",
                newName: "SampleRequired");

            migrationBuilder.RenameColumn(
                name: "request_for_quote_id",
                schema: "public",
                table: "request_for_quote_lines",
                newName: "RequestForQuoteId");

            migrationBuilder.RenameColumn(
                name: "product_definition_id",
                schema: "public",
                table: "request_for_quote_lines",
                newName: "ProductDefinitionId");

            migrationBuilder.RenameColumn(
                name: "preferred_incoterm",
                schema: "public",
                table: "request_for_quote_lines",
                newName: "PreferredIncoterm");

            migrationBuilder.RenameColumn(
                name: "modified_at",
                schema: "public",
                table: "request_for_quote_lines",
                newName: "ModifiedAt");

            migrationBuilder.RenameColumn(
                name: "minimum_order_quantity",
                schema: "public",
                table: "request_for_quote_lines",
                newName: "MinimumOrderQuantity");

            migrationBuilder.RenameColumn(
                name: "line_number",
                schema: "public",
                table: "request_for_quote_lines",
                newName: "LineNumber");

            migrationBuilder.RenameColumn(
                name: "is_substitute_allowed",
                schema: "public",
                table: "request_for_quote_lines",
                newName: "IsSubstituteAllowed");

            migrationBuilder.RenameColumn(
                name: "is_deleted",
                schema: "public",
                table: "request_for_quote_lines",
                newName: "IsDeleted");

            migrationBuilder.RenameColumn(
                name: "created_at",
                schema: "public",
                table: "request_for_quote_lines",
                newName: "CreatedAt");

            migrationBuilder.RenameColumn(
                name: "alternate_item_proposal",
                schema: "public",
                table: "request_for_quote_lines",
                newName: "AlternateItemProposal");

            migrationBuilder.RenameIndex(
                name: "ix_request_for_quote_lines_vendor_product_id",
                schema: "public",
                table: "request_for_quote_lines",
                newName: "IX_request_for_quote_lines_VendorProductId");

            migrationBuilder.RenameIndex(
                name: "ix_request_for_quote_lines_tenant_id",
                schema: "public",
                table: "request_for_quote_lines",
                newName: "IX_request_for_quote_lines_TenantId");

            migrationBuilder.RenameIndex(
                name: "ix_request_for_quote_lines_request_for_quote_id_line_number",
                schema: "public",
                table: "request_for_quote_lines",
                newName: "IX_request_for_quote_lines_RequestForQuoteId_LineNumber");

            migrationBuilder.RenameIndex(
                name: "ix_request_for_quote_lines_product_definition_id",
                schema: "public",
                table: "request_for_quote_lines",
                newName: "IX_request_for_quote_lines_ProductDefinitionId");

            migrationBuilder.RenameIndex(
                name: "ix_request_for_quote_lines_is_deleted",
                schema: "public",
                table: "request_for_quote_lines",
                newName: "IX_request_for_quote_lines_IsDeleted");

            migrationBuilder.RenameColumn(
                name: "status",
                table: "PurchaseRequisitions",
                newName: "Status");

            migrationBuilder.RenameColumn(
                name: "notes",
                table: "PurchaseRequisitions",
                newName: "Notes");

            migrationBuilder.RenameColumn(
                name: "justification",
                table: "PurchaseRequisitions",
                newName: "Justification");

            migrationBuilder.RenameColumn(
                name: "department",
                table: "PurchaseRequisitions",
                newName: "Department");

            migrationBuilder.RenameColumn(
                name: "id",
                table: "PurchaseRequisitions",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "tenant_id",
                table: "PurchaseRequisitions",
                newName: "TenantId");

            migrationBuilder.RenameColumn(
                name: "requisition_number",
                table: "PurchaseRequisitions",
                newName: "RequisitionNumber");

            migrationBuilder.RenameColumn(
                name: "requestor_user_id",
                table: "PurchaseRequisitions",
                newName: "RequestorUserId");

            migrationBuilder.RenameColumn(
                name: "requestor_name",
                table: "PurchaseRequisitions",
                newName: "RequestorName");

            migrationBuilder.RenameColumn(
                name: "requestor_email",
                table: "PurchaseRequisitions",
                newName: "RequestorEmail");

            migrationBuilder.RenameColumn(
                name: "request_date",
                table: "PurchaseRequisitions",
                newName: "RequestDate");

            migrationBuilder.RenameColumn(
                name: "modified_at",
                table: "PurchaseRequisitions",
                newName: "ModifiedAt");

            migrationBuilder.RenameColumn(
                name: "date_needed",
                table: "PurchaseRequisitions",
                newName: "DateNeeded");

            migrationBuilder.RenameColumn(
                name: "currency_code",
                table: "PurchaseRequisitions",
                newName: "CurrencyCode");

            migrationBuilder.RenameColumn(
                name: "created_at",
                table: "PurchaseRequisitions",
                newName: "CreatedAt");

            migrationBuilder.RenameColumn(
                name: "associated_purchase_order_id",
                table: "PurchaseRequisitions",
                newName: "AssociatedPurchaseOrderId");

            migrationBuilder.RenameIndex(
                name: "ix_purchase_requisitions_tenant_id_requisition_number",
                table: "PurchaseRequisitions",
                newName: "IX_PurchaseRequisitions_TenantId_RequisitionNumber");

            migrationBuilder.RenameIndex(
                name: "ix_purchase_requisitions_tenant_id",
                table: "PurchaseRequisitions",
                newName: "IX_PurchaseRequisitions_TenantId");

            migrationBuilder.RenameIndex(
                name: "ix_purchase_requisitions_status",
                table: "PurchaseRequisitions",
                newName: "IX_PurchaseRequisitions_Status");

            migrationBuilder.RenameIndex(
                name: "ix_purchase_requisitions_requestor_user_id",
                table: "PurchaseRequisitions",
                newName: "IX_PurchaseRequisitions_RequestorUserId");

            migrationBuilder.RenameIndex(
                name: "ix_purchase_requisitions_request_date",
                table: "PurchaseRequisitions",
                newName: "IX_PurchaseRequisitions_RequestDate");

            migrationBuilder.RenameIndex(
                name: "ix_purchase_requisitions_associated_purchase_order_id",
                table: "PurchaseRequisitions",
                newName: "IX_PurchaseRequisitions_AssociatedPurchaseOrderId");

            migrationBuilder.RenameColumn(
                name: "quantity",
                table: "PurchaseRequisitionLines",
                newName: "Quantity");

            migrationBuilder.RenameColumn(
                name: "notes",
                table: "PurchaseRequisitionLines",
                newName: "Notes");

            migrationBuilder.RenameColumn(
                name: "description",
                table: "PurchaseRequisitionLines",
                newName: "Description");

            migrationBuilder.RenameColumn(
                name: "id",
                table: "PurchaseRequisitionLines",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "vendor_product_id",
                table: "PurchaseRequisitionLines",
                newName: "VendorProductId");

            migrationBuilder.RenameColumn(
                name: "unit_of_measure",
                table: "PurchaseRequisitionLines",
                newName: "UnitOfMeasure");

            migrationBuilder.RenameColumn(
                name: "tenant_id",
                table: "PurchaseRequisitionLines",
                newName: "TenantId");

            migrationBuilder.RenameColumn(
                name: "suggested_vendor_id",
                table: "PurchaseRequisitionLines",
                newName: "SuggestedVendorId");

            migrationBuilder.RenameColumn(
                name: "purchase_requisition_id",
                table: "PurchaseRequisitionLines",
                newName: "PurchaseRequisitionId");

            migrationBuilder.RenameColumn(
                name: "product_definition_id",
                table: "PurchaseRequisitionLines",
                newName: "ProductDefinitionId");

            migrationBuilder.RenameColumn(
                name: "modified_at",
                table: "PurchaseRequisitionLines",
                newName: "ModifiedAt");

            migrationBuilder.RenameColumn(
                name: "line_number",
                table: "PurchaseRequisitionLines",
                newName: "LineNumber");

            migrationBuilder.RenameColumn(
                name: "gl_account_code",
                table: "PurchaseRequisitionLines",
                newName: "GLAccountCode");

            migrationBuilder.RenameColumn(
                name: "date_needed",
                table: "PurchaseRequisitionLines",
                newName: "DateNeeded");

            migrationBuilder.RenameColumn(
                name: "created_at",
                table: "PurchaseRequisitionLines",
                newName: "CreatedAt");

            migrationBuilder.RenameIndex(
                name: "ix_purchase_requisition_lines_vendor_product_id",
                table: "PurchaseRequisitionLines",
                newName: "IX_PurchaseRequisitionLines_VendorProductId");

            migrationBuilder.RenameIndex(
                name: "ix_purchase_requisition_lines_tenant_id",
                table: "PurchaseRequisitionLines",
                newName: "IX_PurchaseRequisitionLines_TenantId");

            migrationBuilder.RenameIndex(
                name: "ix_purchase_requisition_lines_suggested_vendor_id",
                table: "PurchaseRequisitionLines",
                newName: "IX_PurchaseRequisitionLines_SuggestedVendorId");

            migrationBuilder.RenameIndex(
                name: "ix_purchase_requisition_lines_purchase_requisition_id_line_number",
                table: "PurchaseRequisitionLines",
                newName: "IX_PurchaseRequisitionLines_PurchaseRequisitionId_LineNumber");

            migrationBuilder.RenameIndex(
                name: "ix_purchase_requisition_lines_product_definition_id",
                table: "PurchaseRequisitionLines",
                newName: "IX_PurchaseRequisitionLines_ProductDefinitionId");

            migrationBuilder.RenameIndex(
                name: "ix_purchase_requisition_lines_gl_account_code",
                table: "PurchaseRequisitionLines",
                newName: "IX_PurchaseRequisitionLines_GLAccountCode");

            migrationBuilder.RenameColumn(
                name: "quantity",
                table: "PurchaseOrderLines",
                newName: "Quantity");

            migrationBuilder.RenameColumn(
                name: "notes",
                table: "PurchaseOrderLines",
                newName: "Notes");

            migrationBuilder.RenameColumn(
                name: "id",
                table: "PurchaseOrderLines",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "vendor_product_id",
                table: "PurchaseOrderLines",
                newName: "VendorProductId");

            migrationBuilder.RenameColumn(
                name: "unit_of_measure_snapshot",
                table: "PurchaseOrderLines",
                newName: "UnitOfMeasureSnapshot");

            migrationBuilder.RenameColumn(
                name: "sku_snapshot",
                table: "PurchaseOrderLines",
                newName: "SkuSnapshot");

            migrationBuilder.RenameColumn(
                name: "purchase_order_id",
                table: "PurchaseOrderLines",
                newName: "PurchaseOrderId");

            migrationBuilder.RenameColumn(
                name: "modified_at",
                table: "PurchaseOrderLines",
                newName: "ModifiedAt");

            migrationBuilder.RenameColumn(
                name: "description_snapshot",
                table: "PurchaseOrderLines",
                newName: "DescriptionSnapshot");

            migrationBuilder.RenameColumn(
                name: "created_at",
                table: "PurchaseOrderLines",
                newName: "CreatedAt");

            migrationBuilder.RenameIndex(
                name: "ix_purchase_order_lines_vendor_product_id",
                table: "PurchaseOrderLines",
                newName: "IX_PurchaseOrderLines_VendorProductId");

            migrationBuilder.RenameIndex(
                name: "ix_purchase_order_lines_purchase_order_id",
                table: "PurchaseOrderLines",
                newName: "IX_PurchaseOrderLines_PurchaseOrderId");

            migrationBuilder.RenameColumn(
                name: "status",
                table: "purchase_orders",
                newName: "Status");

            migrationBuilder.RenameColumn(
                name: "id",
                table: "purchase_orders",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "vendor_id",
                table: "purchase_orders",
                newName: "VendorId");

            migrationBuilder.RenameColumn(
                name: "requisition_id",
                table: "purchase_orders",
                newName: "RequisitionId");

            migrationBuilder.RenameColumn(
                name: "payment_terms",
                table: "purchase_orders",
                newName: "PaymentTerms");

            migrationBuilder.RenameColumn(
                name: "order_number",
                table: "purchase_orders",
                newName: "OrderNumber");

            migrationBuilder.RenameColumn(
                name: "order_date",
                table: "purchase_orders",
                newName: "OrderDate");

            migrationBuilder.RenameColumn(
                name: "modified_at",
                table: "purchase_orders",
                newName: "ModifiedAt");

            migrationBuilder.RenameColumn(
                name: "delivery_date",
                table: "purchase_orders",
                newName: "DeliveryDate");

            migrationBuilder.RenameColumn(
                name: "currency_code",
                table: "purchase_orders",
                newName: "CurrencyCode");

            migrationBuilder.RenameColumn(
                name: "created_at",
                table: "purchase_orders",
                newName: "CreatedAt");

            migrationBuilder.RenameColumn(
                name: "contract_id",
                table: "purchase_orders",
                newName: "ContractId");

            migrationBuilder.RenameIndex(
                name: "ix_purchase_orders_status",
                table: "purchase_orders",
                newName: "IX_purchase_orders_Status");

            migrationBuilder.RenameIndex(
                name: "ix_purchase_orders_vendor_id",
                table: "purchase_orders",
                newName: "IX_purchase_orders_VendorId");

            migrationBuilder.RenameIndex(
                name: "ix_purchase_orders_requisition_id",
                table: "purchase_orders",
                newName: "IX_purchase_orders_RequisitionId");

            migrationBuilder.RenameIndex(
                name: "ix_purchase_orders_order_number",
                table: "purchase_orders",
                newName: "IX_purchase_orders_OrderNumber");

            migrationBuilder.RenameIndex(
                name: "ix_purchase_orders_order_date",
                table: "purchase_orders",
                newName: "IX_purchase_orders_OrderDate");

            migrationBuilder.RenameIndex(
                name: "ix_purchase_orders_contract_id",
                table: "purchase_orders",
                newName: "IX_purchase_orders_ContractId");

            migrationBuilder.RenameColumn(
                name: "status",
                schema: "public",
                table: "projects",
                newName: "Status");

            migrationBuilder.RenameColumn(
                name: "name",
                schema: "public",
                table: "projects",
                newName: "Name");

            migrationBuilder.RenameColumn(
                name: "description",
                schema: "public",
                table: "projects",
                newName: "Description");

            migrationBuilder.RenameColumn(
                name: "id",
                schema: "public",
                table: "projects",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "tenant_id",
                schema: "public",
                table: "projects",
                newName: "TenantId");

            migrationBuilder.RenameColumn(
                name: "start_date",
                schema: "public",
                table: "projects",
                newName: "StartDate");

            migrationBuilder.RenameColumn(
                name: "project_code",
                schema: "public",
                table: "projects",
                newName: "ProjectCode");

            migrationBuilder.RenameColumn(
                name: "modified_at",
                schema: "public",
                table: "projects",
                newName: "ModifiedAt");

            migrationBuilder.RenameColumn(
                name: "is_deleted",
                schema: "public",
                table: "projects",
                newName: "IsDeleted");

            migrationBuilder.RenameColumn(
                name: "end_date",
                schema: "public",
                table: "projects",
                newName: "EndDate");

            migrationBuilder.RenameColumn(
                name: "created_at",
                schema: "public",
                table: "projects",
                newName: "CreatedAt");

            migrationBuilder.RenameIndex(
                name: "ix_projects_status",
                schema: "public",
                table: "projects",
                newName: "IX_projects_Status");

            migrationBuilder.RenameIndex(
                name: "ix_projects_name",
                schema: "public",
                table: "projects",
                newName: "IX_projects_Name");

            migrationBuilder.RenameIndex(
                name: "ix_projects_tenant_id_project_code",
                schema: "public",
                table: "projects",
                newName: "IX_projects_TenantId_ProjectCode");

            migrationBuilder.RenameIndex(
                name: "ix_projects_tenant_id",
                schema: "public",
                table: "projects",
                newName: "IX_projects_TenantId");

            migrationBuilder.RenameIndex(
                name: "ix_projects_start_date",
                schema: "public",
                table: "projects",
                newName: "IX_projects_StartDate");

            migrationBuilder.RenameIndex(
                name: "ix_projects_is_deleted",
                schema: "public",
                table: "projects",
                newName: "IX_projects_IsDeleted");

            migrationBuilder.RenameIndex(
                name: "ix_projects_end_date",
                schema: "public",
                table: "projects",
                newName: "IX_projects_EndDate");

            migrationBuilder.RenameColumn(
                name: "name",
                schema: "public",
                table: "products",
                newName: "Name");

            migrationBuilder.RenameColumn(
                name: "description",
                schema: "public",
                table: "products",
                newName: "Description");

            migrationBuilder.RenameColumn(
                name: "id",
                schema: "public",
                table: "products",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "unit_of_measure",
                schema: "public",
                table: "products",
                newName: "UnitOfMeasure");

            migrationBuilder.RenameColumn(
                name: "tenant_id",
                schema: "public",
                table: "products",
                newName: "TenantId");

            migrationBuilder.RenameColumn(
                name: "product_code",
                schema: "public",
                table: "products",
                newName: "ProductCode");

            migrationBuilder.RenameColumn(
                name: "modified_at",
                schema: "public",
                table: "products",
                newName: "ModifiedAt");

            migrationBuilder.RenameColumn(
                name: "is_deleted",
                schema: "public",
                table: "products",
                newName: "IsDeleted");

            migrationBuilder.RenameColumn(
                name: "is_active",
                schema: "public",
                table: "products",
                newName: "IsActive");

            migrationBuilder.RenameColumn(
                name: "created_at",
                schema: "public",
                table: "products",
                newName: "CreatedAt");

            migrationBuilder.RenameColumn(
                name: "category_id",
                schema: "public",
                table: "products",
                newName: "CategoryId");

            migrationBuilder.RenameColumn(
                name: "version",
                table: "product_definitions",
                newName: "Version");

            migrationBuilder.RenameColumn(
                name: "upc",
                table: "product_definitions",
                newName: "Upc");

            migrationBuilder.RenameColumn(
                name: "sku",
                table: "product_definitions",
                newName: "Sku");

            migrationBuilder.RenameColumn(
                name: "name",
                table: "product_definitions",
                newName: "Name");

            migrationBuilder.RenameColumn(
                name: "gtin",
                table: "product_definitions",
                newName: "Gtin");

            migrationBuilder.RenameColumn(
                name: "ean",
                table: "product_definitions",
                newName: "Ean");

            migrationBuilder.RenameColumn(
                name: "description",
                table: "product_definitions",
                newName: "Description");

            migrationBuilder.RenameColumn(
                name: "id",
                table: "product_definitions",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "tenant_id",
                table: "product_definitions",
                newName: "TenantId");

            migrationBuilder.RenameColumn(
                name: "modified_at",
                table: "product_definitions",
                newName: "ModifiedAt");

            migrationBuilder.RenameColumn(
                name: "lifecycle_state",
                table: "product_definitions",
                newName: "LifecycleState");

            migrationBuilder.RenameColumn(
                name: "is_deleted",
                table: "product_definitions",
                newName: "IsDeleted");

            migrationBuilder.RenameColumn(
                name: "created_at",
                table: "product_definitions",
                newName: "CreatedAt");

            migrationBuilder.RenameColumn(
                name: "category_id",
                table: "product_definitions",
                newName: "CategoryId");

            migrationBuilder.RenameColumn(
                name: "attributes_json",
                table: "product_definitions",
                newName: "AttributesJson");

            migrationBuilder.RenameIndex(
                name: "ix_product_definitions_upc",
                table: "product_definitions",
                newName: "IX_product_definitions_Upc");

            migrationBuilder.RenameIndex(
                name: "ix_product_definitions_sku",
                table: "product_definitions",
                newName: "IX_product_definitions_Sku");

            migrationBuilder.RenameIndex(
                name: "ix_product_definitions_gtin",
                table: "product_definitions",
                newName: "IX_product_definitions_Gtin");

            migrationBuilder.RenameIndex(
                name: "ix_product_definitions_ean",
                table: "product_definitions",
                newName: "IX_product_definitions_Ean");

            migrationBuilder.RenameIndex(
                name: "ix_product_definitions_is_deleted",
                table: "product_definitions",
                newName: "IX_product_definitions_IsDeleted");

            migrationBuilder.RenameIndex(
                name: "ix_product_definitions_category_id",
                table: "product_definitions",
                newName: "IX_product_definitions_CategoryId");

            migrationBuilder.RenameColumn(
                name: "version",
                table: "procurement_workflows",
                newName: "Version");

            migrationBuilder.RenameColumn(
                name: "name",
                table: "procurement_workflows",
                newName: "Name");

            migrationBuilder.RenameColumn(
                name: "description",
                table: "procurement_workflows",
                newName: "Description");

            migrationBuilder.RenameColumn(
                name: "id",
                table: "procurement_workflows",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "workflow_type",
                table: "procurement_workflows",
                newName: "WorkflowType");

            migrationBuilder.RenameColumn(
                name: "workflow_name",
                table: "procurement_workflows",
                newName: "WorkflowName");

            migrationBuilder.RenameColumn(
                name: "tenant_id",
                table: "procurement_workflows",
                newName: "TenantId");

            migrationBuilder.RenameColumn(
                name: "subject_document_type",
                table: "procurement_workflows",
                newName: "SubjectDocumentType");

            migrationBuilder.RenameColumn(
                name: "subject_document_id",
                table: "procurement_workflows",
                newName: "SubjectDocumentId");

            migrationBuilder.RenameColumn(
                name: "started_date",
                table: "procurement_workflows",
                newName: "StartedDate");

            migrationBuilder.RenameColumn(
                name: "modified_at",
                table: "procurement_workflows",
                newName: "ModifiedAt");

            migrationBuilder.RenameColumn(
                name: "is_active",
                table: "procurement_workflows",
                newName: "IsActive");

            migrationBuilder.RenameColumn(
                name: "initiated_by_user_id",
                table: "procurement_workflows",
                newName: "InitiatedByUserId");

            migrationBuilder.RenameColumn(
                name: "initiated_by_name",
                table: "procurement_workflows",
                newName: "InitiatedByName");

            migrationBuilder.RenameColumn(
                name: "current_status",
                table: "procurement_workflows",
                newName: "CurrentStatus");

            migrationBuilder.RenameColumn(
                name: "created_at",
                table: "procurement_workflows",
                newName: "CreatedAt");

            migrationBuilder.RenameColumn(
                name: "completed_date",
                table: "procurement_workflows",
                newName: "CompletedDate");

            migrationBuilder.RenameIndex(
                name: "ix_procurement_workflows_name",
                table: "procurement_workflows",
                newName: "IX_procurement_workflows_Name");

            migrationBuilder.RenameIndex(
                name: "ix_procurement_workflows_workflow_type",
                table: "procurement_workflows",
                newName: "IX_procurement_workflows_WorkflowType");

            migrationBuilder.RenameIndex(
                name: "ix_procurement_workflows_tenant_id_workflow_type_name",
                table: "procurement_workflows",
                newName: "IX_procurement_workflows_TenantId_WorkflowType_Name");

            migrationBuilder.RenameIndex(
                name: "ix_procurement_workflows_tenant_id",
                table: "procurement_workflows",
                newName: "IX_procurement_workflows_TenantId");

            migrationBuilder.RenameIndex(
                name: "ix_procurement_workflows_is_active",
                table: "procurement_workflows",
                newName: "IX_procurement_workflows_IsActive");

            migrationBuilder.RenameColumn(
                name: "status",
                table: "procurement_workflow_steps",
                newName: "Status");

            migrationBuilder.RenameColumn(
                name: "comments",
                table: "procurement_workflow_steps",
                newName: "Comments");

            migrationBuilder.RenameColumn(
                name: "id",
                table: "procurement_workflow_steps",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "workflow_id",
                table: "procurement_workflow_steps",
                newName: "WorkflowId");

            migrationBuilder.RenameColumn(
                name: "step_order",
                table: "procurement_workflow_steps",
                newName: "StepOrder");

            migrationBuilder.RenameColumn(
                name: "step_name",
                table: "procurement_workflow_steps",
                newName: "StepName");

            migrationBuilder.RenameColumn(
                name: "sla_duration",
                table: "procurement_workflow_steps",
                newName: "SlaDuration");

            migrationBuilder.RenameColumn(
                name: "sequence_order",
                table: "procurement_workflow_steps",
                newName: "SequenceOrder");

            migrationBuilder.RenameColumn(
                name: "procurement_workflow_id",
                table: "procurement_workflow_steps",
                newName: "ProcurementWorkflowId");

            migrationBuilder.RenameColumn(
                name: "modified_at",
                table: "procurement_workflow_steps",
                newName: "ModifiedAt");

            migrationBuilder.RenameColumn(
                name: "created_at",
                table: "procurement_workflow_steps",
                newName: "CreatedAt");

            migrationBuilder.RenameColumn(
                name: "condition_expression",
                table: "procurement_workflow_steps",
                newName: "ConditionExpression");

            migrationBuilder.RenameColumn(
                name: "assignee_user_id",
                table: "procurement_workflow_steps",
                newName: "AssigneeUserId");

            migrationBuilder.RenameColumn(
                name: "assignee_name",
                table: "procurement_workflow_steps",
                newName: "AssigneeName");

            migrationBuilder.RenameColumn(
                name: "assigned_date",
                table: "procurement_workflow_steps",
                newName: "AssignedDate");

            migrationBuilder.RenameColumn(
                name: "approver_user_id",
                table: "procurement_workflow_steps",
                newName: "ApproverUserId");

            migrationBuilder.RenameColumn(
                name: "approver_role_id",
                table: "procurement_workflow_steps",
                newName: "ApproverRoleId");

            migrationBuilder.RenameColumn(
                name: "action_date",
                table: "procurement_workflow_steps",
                newName: "ActionDate");

            migrationBuilder.RenameIndex(
                name: "ix_procurement_workflow_steps_workflow_id",
                table: "procurement_workflow_steps",
                newName: "IX_procurement_workflow_steps_WorkflowId");

            migrationBuilder.RenameIndex(
                name: "ix_procurement_workflow_steps_procurement_workflow_id_step_ord",
                table: "procurement_workflow_steps",
                newName: "IX_procurement_workflow_steps_ProcurementWorkflowId_StepOrder");

            migrationBuilder.RenameIndex(
                name: "ix_procurement_workflow_steps_procurement_workflow_id",
                table: "procurement_workflow_steps",
                newName: "IX_procurement_workflow_steps_ProcurementWorkflowId");

            migrationBuilder.RenameIndex(
                name: "ix_procurement_workflow_steps_approver_user_id",
                table: "procurement_workflow_steps",
                newName: "IX_procurement_workflow_steps_ApproverUserId");

            migrationBuilder.RenameIndex(
                name: "ix_procurement_workflow_steps_approver_role_id",
                table: "procurement_workflow_steps",
                newName: "IX_procurement_workflow_steps_ApproverRoleId");

            migrationBuilder.RenameColumn(
                name: "status",
                table: "payment_transactions",
                newName: "Status");

            migrationBuilder.RenameColumn(
                name: "notes",
                table: "payment_transactions",
                newName: "Notes");

            migrationBuilder.RenameColumn(
                name: "amount",
                table: "payment_transactions",
                newName: "Amount");

            migrationBuilder.RenameColumn(
                name: "id",
                table: "payment_transactions",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "vendor_id",
                table: "payment_transactions",
                newName: "VendorId");

            migrationBuilder.RenameColumn(
                name: "transaction_reference",
                table: "payment_transactions",
                newName: "TransactionReference");

            migrationBuilder.RenameColumn(
                name: "tenant_id",
                table: "payment_transactions",
                newName: "TenantId");

            migrationBuilder.RenameColumn(
                name: "payment_method",
                table: "payment_transactions",
                newName: "PaymentMethod");

            migrationBuilder.RenameColumn(
                name: "payment_date",
                table: "payment_transactions",
                newName: "PaymentDate");

            migrationBuilder.RenameColumn(
                name: "modified_at",
                table: "payment_transactions",
                newName: "ModifiedAt");

            migrationBuilder.RenameColumn(
                name: "invoice_id",
                table: "payment_transactions",
                newName: "InvoiceId");

            migrationBuilder.RenameColumn(
                name: "currency_code",
                table: "payment_transactions",
                newName: "CurrencyCode");

            migrationBuilder.RenameColumn(
                name: "created_at",
                table: "payment_transactions",
                newName: "CreatedAt");

            migrationBuilder.RenameColumn(
                name: "bank_reference",
                table: "payment_transactions",
                newName: "BankReference");

            migrationBuilder.RenameIndex(
                name: "ix_payment_transactions_status",
                table: "payment_transactions",
                newName: "IX_payment_transactions_Status");

            migrationBuilder.RenameIndex(
                name: "ix_payment_transactions_vendor_id",
                table: "payment_transactions",
                newName: "IX_payment_transactions_VendorId");

            migrationBuilder.RenameIndex(
                name: "ix_payment_transactions_tenant_id",
                table: "payment_transactions",
                newName: "IX_payment_transactions_TenantId");

            migrationBuilder.RenameIndex(
                name: "ix_payment_transactions_payment_date",
                table: "payment_transactions",
                newName: "IX_payment_transactions_PaymentDate");

            migrationBuilder.RenameIndex(
                name: "ix_payment_transactions_invoice_id",
                table: "payment_transactions",
                newName: "IX_payment_transactions_InvoiceId");

            migrationBuilder.RenameColumn(
                name: "subtotal",
                table: "invoices",
                newName: "Subtotal");

            migrationBuilder.RenameColumn(
                name: "status",
                table: "invoices",
                newName: "Status");

            migrationBuilder.RenameColumn(
                name: "notes",
                table: "invoices",
                newName: "Notes");

            migrationBuilder.RenameColumn(
                name: "id",
                table: "invoices",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "vendor_id",
                table: "invoices",
                newName: "VendorId");

            migrationBuilder.RenameColumn(
                name: "total_amount",
                table: "invoices",
                newName: "TotalAmount");

            migrationBuilder.RenameColumn(
                name: "tenant_id",
                table: "invoices",
                newName: "TenantId");

            migrationBuilder.RenameColumn(
                name: "tax_amount",
                table: "invoices",
                newName: "TaxAmount");

            migrationBuilder.RenameColumn(
                name: "sub_total",
                table: "invoices",
                newName: "SubTotal");

            migrationBuilder.RenameColumn(
                name: "purchase_order_id",
                table: "invoices",
                newName: "PurchaseOrderId");

            migrationBuilder.RenameColumn(
                name: "payment_terms",
                table: "invoices",
                newName: "PaymentTerms");

            migrationBuilder.RenameColumn(
                name: "payment_date",
                table: "invoices",
                newName: "PaymentDate");

            migrationBuilder.RenameColumn(
                name: "modified_at",
                table: "invoices",
                newName: "ModifiedAt");

            migrationBuilder.RenameColumn(
                name: "invoice_number",
                table: "invoices",
                newName: "InvoiceNumber");

            migrationBuilder.RenameColumn(
                name: "invoice_date",
                table: "invoices",
                newName: "InvoiceDate");

            migrationBuilder.RenameColumn(
                name: "due_date",
                table: "invoices",
                newName: "DueDate");

            migrationBuilder.RenameColumn(
                name: "customer_id",
                table: "invoices",
                newName: "CustomerId");

            migrationBuilder.RenameColumn(
                name: "currency_code",
                table: "invoices",
                newName: "CurrencyCode");

            migrationBuilder.RenameColumn(
                name: "created_at",
                table: "invoices",
                newName: "CreatedAt");

            migrationBuilder.RenameColumn(
                name: "amount_paid",
                table: "invoices",
                newName: "AmountPaid");

            migrationBuilder.RenameIndex(
                name: "ix_invoices_status",
                table: "invoices",
                newName: "IX_invoices_Status");

            migrationBuilder.RenameIndex(
                name: "ix_invoices_vendor_id_invoice_number",
                table: "invoices",
                newName: "IX_invoices_VendorId_InvoiceNumber");

            migrationBuilder.RenameIndex(
                name: "ix_invoices_vendor_id",
                table: "invoices",
                newName: "IX_invoices_VendorId");

            migrationBuilder.RenameIndex(
                name: "ix_invoices_tenant_id",
                table: "invoices",
                newName: "IX_invoices_TenantId");

            migrationBuilder.RenameIndex(
                name: "ix_invoices_purchase_order_id",
                table: "invoices",
                newName: "IX_invoices_PurchaseOrderId");

            migrationBuilder.RenameIndex(
                name: "ix_invoices_invoice_date",
                table: "invoices",
                newName: "IX_invoices_InvoiceDate");

            migrationBuilder.RenameIndex(
                name: "ix_invoices_due_date",
                table: "invoices",
                newName: "IX_invoices_DueDate");

            migrationBuilder.RenameIndex(
                name: "ix_invoices_customer_id",
                table: "invoices",
                newName: "IX_invoices_CustomerId");

            migrationBuilder.RenameColumn(
                name: "quantity",
                table: "invoice_lines",
                newName: "Quantity");

            migrationBuilder.RenameColumn(
                name: "notes",
                table: "invoice_lines",
                newName: "Notes");

            migrationBuilder.RenameColumn(
                name: "id",
                table: "invoice_lines",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "description",
                table: "invoice_lines",
                newName: "Description");

            migrationBuilder.RenameColumn(
                name: "unit_price",
                table: "invoice_lines",
                newName: "UnitPrice");

            migrationBuilder.RenameColumn(
                name: "unit_of_measure",
                table: "invoice_lines",
                newName: "UnitOfMeasure");

            migrationBuilder.RenameColumn(
                name: "tax_rate",
                table: "invoice_lines",
                newName: "TaxRate");

            migrationBuilder.RenameColumn(
                name: "tax_amount",
                table: "invoice_lines",
                newName: "TaxAmount");

            migrationBuilder.RenameColumn(
                name: "purchase_order_line_id",
                table: "invoice_lines",
                newName: "PurchaseOrderLineId");

            migrationBuilder.RenameColumn(
                name: "product_id",
                table: "invoice_lines",
                newName: "ProductId");

            migrationBuilder.RenameColumn(
                name: "modified_at",
                table: "invoice_lines",
                newName: "ModifiedAt");

            migrationBuilder.RenameColumn(
                name: "line_total",
                table: "invoice_lines",
                newName: "LineTotal");

            migrationBuilder.RenameColumn(
                name: "created_at",
                table: "invoice_lines",
                newName: "CreatedAt");

            migrationBuilder.RenameColumn(
                name: "line_number",
                table: "invoice_lines",
                newName: "LineNumber");

            migrationBuilder.RenameColumn(
                name: "invoice_id",
                table: "invoice_lines",
                newName: "InvoiceId");

            migrationBuilder.RenameIndex(
                name: "ix_invoice_lines_purchase_order_line_id",
                table: "invoice_lines",
                newName: "IX_invoice_lines_PurchaseOrderLineId");

            migrationBuilder.RenameIndex(
                name: "ix_invoice_lines_product_id",
                table: "invoice_lines",
                newName: "IX_invoice_lines_ProductId");

            migrationBuilder.RenameColumn(
                name: "status",
                table: "goods_receipt_notes",
                newName: "Status");

            migrationBuilder.RenameColumn(
                name: "notes",
                table: "goods_receipt_notes",
                newName: "Notes");

            migrationBuilder.RenameColumn(
                name: "id",
                table: "goods_receipt_notes",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "vendor_id",
                table: "goods_receipt_notes",
                newName: "VendorId");

            migrationBuilder.RenameColumn(
                name: "tenant_id",
                table: "goods_receipt_notes",
                newName: "TenantId");

            migrationBuilder.RenameColumn(
                name: "receiving_location",
                table: "goods_receipt_notes",
                newName: "ReceivingLocation");

            migrationBuilder.RenameColumn(
                name: "received_by_user_id",
                table: "goods_receipt_notes",
                newName: "ReceivedByUserId");

            migrationBuilder.RenameColumn(
                name: "received_by_name",
                table: "goods_receipt_notes",
                newName: "ReceivedByName");

            migrationBuilder.RenameColumn(
                name: "receipt_date",
                table: "goods_receipt_notes",
                newName: "ReceiptDate");

            migrationBuilder.RenameColumn(
                name: "purchase_order_id",
                table: "goods_receipt_notes",
                newName: "PurchaseOrderId");

            migrationBuilder.RenameColumn(
                name: "modified_at",
                table: "goods_receipt_notes",
                newName: "ModifiedAt");

            migrationBuilder.RenameColumn(
                name: "inspection_date",
                table: "goods_receipt_notes",
                newName: "InspectionDate");

            migrationBuilder.RenameColumn(
                name: "grn_number",
                table: "goods_receipt_notes",
                newName: "GrnNumber");

            migrationBuilder.RenameColumn(
                name: "goods_receipt_note_number",
                table: "goods_receipt_notes",
                newName: "GoodsReceiptNoteNumber");

            migrationBuilder.RenameColumn(
                name: "delivery_note_id",
                table: "goods_receipt_notes",
                newName: "DeliveryNoteId");

            migrationBuilder.RenameColumn(
                name: "created_at",
                table: "goods_receipt_notes",
                newName: "CreatedAt");

            migrationBuilder.RenameIndex(
                name: "ix_goods_receipt_notes_status",
                table: "goods_receipt_notes",
                newName: "IX_goods_receipt_notes_Status");

            migrationBuilder.RenameIndex(
                name: "ix_goods_receipt_notes_vendor_id",
                table: "goods_receipt_notes",
                newName: "IX_goods_receipt_notes_VendorId");

            migrationBuilder.RenameIndex(
                name: "ix_goods_receipt_notes_tenant_id",
                table: "goods_receipt_notes",
                newName: "IX_goods_receipt_notes_TenantId");

            migrationBuilder.RenameIndex(
                name: "ix_goods_receipt_notes_received_by_user_id",
                table: "goods_receipt_notes",
                newName: "IX_goods_receipt_notes_ReceivedByUserId");

            migrationBuilder.RenameIndex(
                name: "ix_goods_receipt_notes_receipt_date",
                table: "goods_receipt_notes",
                newName: "IX_goods_receipt_notes_ReceiptDate");

            migrationBuilder.RenameIndex(
                name: "ix_goods_receipt_notes_purchase_order_id",
                table: "goods_receipt_notes",
                newName: "IX_goods_receipt_notes_PurchaseOrderId");

            migrationBuilder.RenameIndex(
                name: "ix_goods_receipt_notes_goods_receipt_note_number",
                table: "goods_receipt_notes",
                newName: "IX_goods_receipt_notes_GoodsReceiptNoteNumber");

            migrationBuilder.RenameIndex(
                name: "ix_goods_receipt_notes_delivery_note_id",
                table: "goods_receipt_notes",
                newName: "IX_goods_receipt_notes_DeliveryNoteId");

            migrationBuilder.RenameColumn(
                name: "notes",
                table: "goods_receipt_note_lines",
                newName: "Notes");

            migrationBuilder.RenameColumn(
                name: "id",
                table: "goods_receipt_note_lines",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "unit_of_measure",
                table: "goods_receipt_note_lines",
                newName: "UnitOfMeasure");

            migrationBuilder.RenameColumn(
                name: "rejection_reason",
                table: "goods_receipt_note_lines",
                newName: "RejectionReason");

            migrationBuilder.RenameColumn(
                name: "quantity_rejected",
                table: "goods_receipt_note_lines",
                newName: "QuantityRejected");

            migrationBuilder.RenameColumn(
                name: "quantity_received",
                table: "goods_receipt_note_lines",
                newName: "QuantityReceived");

            migrationBuilder.RenameColumn(
                name: "quantity_accepted",
                table: "goods_receipt_note_lines",
                newName: "QuantityAccepted");

            migrationBuilder.RenameColumn(
                name: "quality_control_status",
                table: "goods_receipt_note_lines",
                newName: "QualityControlStatus");

            migrationBuilder.RenameColumn(
                name: "put_away_location",
                table: "goods_receipt_note_lines",
                newName: "PutAwayLocation");

            migrationBuilder.RenameColumn(
                name: "purchase_order_line_id",
                table: "goods_receipt_note_lines",
                newName: "PurchaseOrderLineId");

            migrationBuilder.RenameColumn(
                name: "product_sku_snapshot",
                table: "goods_receipt_note_lines",
                newName: "ProductSkuSnapshot");

            migrationBuilder.RenameColumn(
                name: "product_id",
                table: "goods_receipt_note_lines",
                newName: "ProductId");

            migrationBuilder.RenameColumn(
                name: "product_description_snapshot",
                table: "goods_receipt_note_lines",
                newName: "ProductDescriptionSnapshot");

            migrationBuilder.RenameColumn(
                name: "modified_at",
                table: "goods_receipt_note_lines",
                newName: "ModifiedAt");

            migrationBuilder.RenameColumn(
                name: "lot_number",
                table: "goods_receipt_note_lines",
                newName: "LotNumber");

            migrationBuilder.RenameColumn(
                name: "inspection_completed",
                table: "goods_receipt_note_lines",
                newName: "InspectionCompleted");

            migrationBuilder.RenameColumn(
                name: "expiry_date",
                table: "goods_receipt_note_lines",
                newName: "ExpiryDate");

            migrationBuilder.RenameColumn(
                name: "delivery_note_line_line_number",
                table: "goods_receipt_note_lines",
                newName: "DeliveryNoteLineLineNumber");

            migrationBuilder.RenameColumn(
                name: "delivery_note_line_id",
                table: "goods_receipt_note_lines",
                newName: "DeliveryNoteLineId");

            migrationBuilder.RenameColumn(
                name: "delivery_note_line_delivery_note_id",
                table: "goods_receipt_note_lines",
                newName: "DeliveryNoteLineDeliveryNoteId");

            migrationBuilder.RenameColumn(
                name: "created_at",
                table: "goods_receipt_note_lines",
                newName: "CreatedAt");

            migrationBuilder.RenameColumn(
                name: "batch_number",
                table: "goods_receipt_note_lines",
                newName: "BatchNumber");

            migrationBuilder.RenameColumn(
                name: "line_number",
                table: "goods_receipt_note_lines",
                newName: "LineNumber");

            migrationBuilder.RenameColumn(
                name: "goods_receipt_note_id",
                table: "goods_receipt_note_lines",
                newName: "GoodsReceiptNoteId");

            migrationBuilder.RenameIndex(
                name: "ix_goods_receipt_note_lines_quality_control_status",
                table: "goods_receipt_note_lines",
                newName: "IX_goods_receipt_note_lines_QualityControlStatus");

            migrationBuilder.RenameIndex(
                name: "ix_goods_receipt_note_lines_purchase_order_line_id",
                table: "goods_receipt_note_lines",
                newName: "IX_goods_receipt_note_lines_PurchaseOrderLineId");

            migrationBuilder.RenameIndex(
                name: "ix_goods_receipt_note_lines_product_id",
                table: "goods_receipt_note_lines",
                newName: "IX_goods_receipt_note_lines_ProductId");

            migrationBuilder.RenameIndex(
                name: "ix_goods_receipt_note_lines_expiry_date",
                table: "goods_receipt_note_lines",
                newName: "IX_goods_receipt_note_lines_ExpiryDate");

            migrationBuilder.RenameIndex(
                name: "ix_goods_receipt_note_lines_delivery_note_line_delivery_note_i",
                table: "goods_receipt_note_lines",
                newName: "IX_goods_receipt_note_lines_DeliveryNoteLineDeliveryNoteId_Del~");

            migrationBuilder.RenameColumn(
                name: "name",
                schema: "public",
                table: "departments",
                newName: "Name");

            migrationBuilder.RenameColumn(
                name: "description",
                schema: "public",
                table: "departments",
                newName: "Description");

            migrationBuilder.RenameColumn(
                name: "code",
                schema: "public",
                table: "departments",
                newName: "Code");

            migrationBuilder.RenameColumn(
                name: "id",
                schema: "public",
                table: "departments",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "tenant_id",
                schema: "public",
                table: "departments",
                newName: "TenantId");

            migrationBuilder.RenameColumn(
                name: "modified_at",
                schema: "public",
                table: "departments",
                newName: "ModifiedAt");

            migrationBuilder.RenameColumn(
                name: "created_at",
                schema: "public",
                table: "departments",
                newName: "CreatedAt");

            migrationBuilder.RenameIndex(
                name: "ix_departments_name",
                schema: "public",
                table: "departments",
                newName: "IX_departments_Name");

            migrationBuilder.RenameIndex(
                name: "ix_departments_tenant_id_code",
                schema: "public",
                table: "departments",
                newName: "IX_departments_TenantId_Code");

            migrationBuilder.RenameIndex(
                name: "ix_departments_tenant_id",
                schema: "public",
                table: "departments",
                newName: "IX_departments_TenantId");

            migrationBuilder.RenameColumn(
                name: "status",
                table: "delivery_notes",
                newName: "Status");

            migrationBuilder.RenameColumn(
                name: "notes",
                table: "delivery_notes",
                newName: "Notes");

            migrationBuilder.RenameColumn(
                name: "id",
                table: "delivery_notes",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "vendor_id",
                table: "delivery_notes",
                newName: "VendorId");

            migrationBuilder.RenameColumn(
                name: "tenant_id",
                table: "delivery_notes",
                newName: "TenantId");

            migrationBuilder.RenameColumn(
                name: "shipment_date",
                table: "delivery_notes",
                newName: "ShipmentDate");

            migrationBuilder.RenameColumn(
                name: "sales_order_id",
                table: "delivery_notes",
                newName: "SalesOrderId");

            migrationBuilder.RenameColumn(
                name: "received_by",
                table: "delivery_notes",
                newName: "ReceivedBy");

            migrationBuilder.RenameColumn(
                name: "purchase_order_id",
                table: "delivery_notes",
                newName: "PurchaseOrderId");

            migrationBuilder.RenameColumn(
                name: "modified_at",
                table: "delivery_notes",
                newName: "ModifiedAt");

            migrationBuilder.RenameColumn(
                name: "delivery_note_number",
                table: "delivery_notes",
                newName: "DeliveryNoteNumber");

            migrationBuilder.RenameColumn(
                name: "delivery_date",
                table: "delivery_notes",
                newName: "DeliveryDate");

            migrationBuilder.RenameColumn(
                name: "created_at",
                table: "delivery_notes",
                newName: "CreatedAt");

            migrationBuilder.RenameIndex(
                name: "ix_delivery_notes_status",
                table: "delivery_notes",
                newName: "IX_delivery_notes_Status");

            migrationBuilder.RenameIndex(
                name: "ix_delivery_notes_vendor_id",
                table: "delivery_notes",
                newName: "IX_delivery_notes_VendorId");

            migrationBuilder.RenameIndex(
                name: "ix_delivery_notes_tenant_id",
                table: "delivery_notes",
                newName: "IX_delivery_notes_TenantId");

            migrationBuilder.RenameIndex(
                name: "ix_delivery_notes_sales_order_id",
                table: "delivery_notes",
                newName: "IX_delivery_notes_SalesOrderId");

            migrationBuilder.RenameIndex(
                name: "ix_delivery_notes_purchase_order_id",
                table: "delivery_notes",
                newName: "IX_delivery_notes_PurchaseOrderId");

            migrationBuilder.RenameIndex(
                name: "ix_delivery_notes_delivery_note_number",
                table: "delivery_notes",
                newName: "IX_delivery_notes_DeliveryNoteNumber");

            migrationBuilder.RenameIndex(
                name: "ix_delivery_notes_delivery_date",
                table: "delivery_notes",
                newName: "IX_delivery_notes_DeliveryDate");

            migrationBuilder.RenameColumn(
                name: "notes",
                table: "delivery_note_lines",
                newName: "Notes");

            migrationBuilder.RenameColumn(
                name: "id",
                table: "delivery_note_lines",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "description",
                table: "delivery_note_lines",
                newName: "Description");

            migrationBuilder.RenameColumn(
                name: "unit_of_measure",
                table: "delivery_note_lines",
                newName: "UnitOfMeasure");

            migrationBuilder.RenameColumn(
                name: "serial_number",
                table: "delivery_note_lines",
                newName: "SerialNumber");

            migrationBuilder.RenameColumn(
                name: "sales_order_line_number",
                table: "delivery_note_lines",
                newName: "SalesOrderLineNumber");

            migrationBuilder.RenameColumn(
                name: "sales_order_id",
                table: "delivery_note_lines",
                newName: "SalesOrderId");

            migrationBuilder.RenameColumn(
                name: "quantity_shipped",
                table: "delivery_note_lines",
                newName: "QuantityShipped");

            migrationBuilder.RenameColumn(
                name: "purchase_order_line_id",
                table: "delivery_note_lines",
                newName: "PurchaseOrderLineId");

            migrationBuilder.RenameColumn(
                name: "product_sku_snapshot",
                table: "delivery_note_lines",
                newName: "ProductSkuSnapshot");

            migrationBuilder.RenameColumn(
                name: "product_id",
                table: "delivery_note_lines",
                newName: "ProductId");

            migrationBuilder.RenameColumn(
                name: "modified_at",
                table: "delivery_note_lines",
                newName: "ModifiedAt");

            migrationBuilder.RenameColumn(
                name: "created_at",
                table: "delivery_note_lines",
                newName: "CreatedAt");

            migrationBuilder.RenameColumn(
                name: "batch_number",
                table: "delivery_note_lines",
                newName: "BatchNumber");

            migrationBuilder.RenameColumn(
                name: "line_number",
                table: "delivery_note_lines",
                newName: "LineNumber");

            migrationBuilder.RenameColumn(
                name: "delivery_note_id",
                table: "delivery_note_lines",
                newName: "DeliveryNoteId");

            migrationBuilder.RenameIndex(
                name: "ix_delivery_note_lines_sales_order_id_sales_order_line_number",
                table: "delivery_note_lines",
                newName: "IX_delivery_note_lines_SalesOrderId_SalesOrderLineNumber");

            migrationBuilder.RenameIndex(
                name: "ix_delivery_note_lines_purchase_order_line_id",
                table: "delivery_note_lines",
                newName: "IX_delivery_note_lines_PurchaseOrderLineId");

            migrationBuilder.RenameIndex(
                name: "ix_delivery_note_lines_product_id",
                table: "delivery_note_lines",
                newName: "IX_delivery_note_lines_ProductId");

            migrationBuilder.RenameColumn(
                name: "id",
                schema: "public",
                table: "customers",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "tenant_id",
                schema: "public",
                table: "customers",
                newName: "TenantId");

            migrationBuilder.RenameColumn(
                name: "modified_at",
                schema: "public",
                table: "customers",
                newName: "ModifiedAt");

            migrationBuilder.RenameColumn(
                name: "is_deleted",
                schema: "public",
                table: "customers",
                newName: "IsDeleted");

            migrationBuilder.RenameColumn(
                name: "created_at",
                schema: "public",
                table: "customers",
                newName: "CreatedAt");

            migrationBuilder.RenameColumn(
                name: "assigned_sales_rep_id",
                schema: "public",
                table: "customers",
                newName: "AssignedSalesRepId");

            migrationBuilder.RenameIndex(
                name: "ix_customers_tax_identifier",
                schema: "public",
                table: "customers",
                newName: "IX_customers_tax_identifier");

            migrationBuilder.RenameIndex(
                name: "ix_customers_name",
                schema: "public",
                table: "customers",
                newName: "IX_customers_name");

            migrationBuilder.RenameIndex(
                name: "ix_customers_is_active",
                schema: "public",
                table: "customers",
                newName: "IX_customers_is_active");

            migrationBuilder.RenameIndex(
                name: "ix_customers_customer_type",
                schema: "public",
                table: "customers",
                newName: "IX_customers_customer_type");

            migrationBuilder.RenameIndex(
                name: "ix_customers_tenant_id_customer_code",
                schema: "public",
                table: "customers",
                newName: "IX_customers_TenantId_customer_code");

            migrationBuilder.RenameIndex(
                name: "ix_customers_tenant_id",
                schema: "public",
                table: "customers",
                newName: "IX_customers_TenantId");

            migrationBuilder.RenameIndex(
                name: "ix_customers_is_deleted",
                schema: "public",
                table: "customers",
                newName: "IX_customers_IsDeleted");

            migrationBuilder.RenameIndex(
                name: "ix_customers_assigned_sales_rep_id",
                schema: "public",
                table: "customers",
                newName: "IX_customers_AssignedSalesRepId");

            migrationBuilder.RenameColumn(
                name: "version",
                table: "contracts",
                newName: "Version");

            migrationBuilder.RenameColumn(
                name: "title",
                table: "contracts",
                newName: "Title");

            migrationBuilder.RenameColumn(
                name: "status",
                table: "contracts",
                newName: "Status");

            migrationBuilder.RenameColumn(
                name: "id",
                table: "contracts",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "vendor_performance_score_snapshot",
                table: "contracts",
                newName: "VendorPerformanceScoreSnapshot");

            migrationBuilder.RenameColumn(
                name: "vendor_id",
                table: "contracts",
                newName: "VendorId");

            migrationBuilder.RenameColumn(
                name: "terms_and_conditions",
                table: "contracts",
                newName: "TermsAndConditions");

            migrationBuilder.RenameColumn(
                name: "termination_penalty_terms",
                table: "contracts",
                newName: "TerminationPenaltyTerms");

            migrationBuilder.RenameColumn(
                name: "tenant_id",
                table: "contracts",
                newName: "TenantId");

            migrationBuilder.RenameColumn(
                name: "start_date",
                table: "contracts",
                newName: "StartDate");

            migrationBuilder.RenameColumn(
                name: "sla_details_json",
                table: "contracts",
                newName: "SlaDetailsJson");

            migrationBuilder.RenameColumn(
                name: "renewal_terms",
                table: "contracts",
                newName: "RenewalTerms");

            migrationBuilder.RenameColumn(
                name: "payment_terms",
                table: "contracts",
                newName: "PaymentTerms");

            migrationBuilder.RenameColumn(
                name: "modified_at",
                table: "contracts",
                newName: "ModifiedAt");

            migrationBuilder.RenameColumn(
                name: "milestones_json",
                table: "contracts",
                newName: "MilestonesJson");

            migrationBuilder.RenameColumn(
                name: "is_deleted",
                table: "contracts",
                newName: "IsDeleted");

            migrationBuilder.RenameColumn(
                name: "is_auto_renew",
                table: "contracts",
                newName: "IsAutoRenew");

            migrationBuilder.RenameColumn(
                name: "end_date",
                table: "contracts",
                newName: "EndDate");

            migrationBuilder.RenameColumn(
                name: "created_at",
                table: "contracts",
                newName: "CreatedAt");

            migrationBuilder.RenameColumn(
                name: "contract_type",
                table: "contracts",
                newName: "ContractType");

            migrationBuilder.RenameColumn(
                name: "contract_number",
                table: "contracts",
                newName: "ContractNumber");

            migrationBuilder.RenameColumn(
                name: "compliance_document_links_json",
                table: "contracts",
                newName: "ComplianceDocumentLinksJson");

            migrationBuilder.RenameIndex(
                name: "ix_contracts_status",
                table: "contracts",
                newName: "IX_contracts_Status");

            migrationBuilder.RenameIndex(
                name: "ix_contracts_vendor_id",
                table: "contracts",
                newName: "IX_contracts_VendorId");

            migrationBuilder.RenameIndex(
                name: "ix_contracts_tenant_id",
                table: "contracts",
                newName: "IX_contracts_TenantId");

            migrationBuilder.RenameIndex(
                name: "ix_contracts_start_date",
                table: "contracts",
                newName: "IX_contracts_StartDate");

            migrationBuilder.RenameIndex(
                name: "ix_contracts_is_deleted",
                table: "contracts",
                newName: "IX_contracts_IsDeleted");

            migrationBuilder.RenameIndex(
                name: "ix_contracts_end_date",
                table: "contracts",
                newName: "IX_contracts_EndDate");

            migrationBuilder.RenameIndex(
                name: "ix_contracts_contract_number",
                table: "contracts",
                newName: "IX_contracts_ContractNumber");

            migrationBuilder.RenameColumn(
                name: "name",
                schema: "public",
                table: "categories",
                newName: "Name");

            migrationBuilder.RenameColumn(
                name: "description",
                schema: "public",
                table: "categories",
                newName: "Description");

            migrationBuilder.RenameColumn(
                name: "code",
                schema: "public",
                table: "categories",
                newName: "Code");

            migrationBuilder.RenameColumn(
                name: "id",
                schema: "public",
                table: "categories",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "unspsc_code",
                schema: "public",
                table: "categories",
                newName: "UnspscCode");

            migrationBuilder.RenameColumn(
                name: "tenant_id",
                schema: "public",
                table: "categories",
                newName: "TenantId");

            migrationBuilder.RenameColumn(
                name: "parent_category_id",
                schema: "public",
                table: "categories",
                newName: "ParentCategoryId");

            migrationBuilder.RenameColumn(
                name: "modified_at",
                schema: "public",
                table: "categories",
                newName: "ModifiedAt");

            migrationBuilder.RenameColumn(
                name: "created_at",
                schema: "public",
                table: "categories",
                newName: "CreatedAt");

            migrationBuilder.RenameIndex(
                name: "ix_categories_tenant_id",
                schema: "public",
                table: "categories",
                newName: "IX_categories_TenantId");

            migrationBuilder.RenameIndex(
                name: "ix_categories_parent_category_id",
                schema: "public",
                table: "categories",
                newName: "IX_categories_ParentCategoryId");

            migrationBuilder.RenameColumn(
                name: "version",
                schema: "public",
                table: "budgets",
                newName: "Version");

            migrationBuilder.RenameColumn(
                name: "status",
                schema: "public",
                table: "budgets",
                newName: "Status");

            migrationBuilder.RenameColumn(
                name: "name",
                schema: "public",
                table: "budgets",
                newName: "Name");

            migrationBuilder.RenameColumn(
                name: "description",
                schema: "public",
                table: "budgets",
                newName: "Description");

            migrationBuilder.RenameColumn(
                name: "id",
                schema: "public",
                table: "budgets",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "workflow_instance_id",
                schema: "public",
                table: "budgets",
                newName: "WorkflowInstanceId");

            migrationBuilder.RenameColumn(
                name: "tenant_id",
                schema: "public",
                table: "budgets",
                newName: "TenantId");

            migrationBuilder.RenameColumn(
                name: "start_date",
                schema: "public",
                table: "budgets",
                newName: "StartDate");

            migrationBuilder.RenameColumn(
                name: "modified_at",
                schema: "public",
                table: "budgets",
                newName: "ModifiedAt");

            migrationBuilder.RenameColumn(
                name: "is_rolling_forecast",
                schema: "public",
                table: "budgets",
                newName: "IsRollingForecast");

            migrationBuilder.RenameColumn(
                name: "is_deleted",
                schema: "public",
                table: "budgets",
                newName: "IsDeleted");

            migrationBuilder.RenameColumn(
                name: "forecast_periods_json",
                schema: "public",
                table: "budgets",
                newName: "ForecastPeriodsJson");

            migrationBuilder.RenameColumn(
                name: "fiscal_year",
                schema: "public",
                table: "budgets",
                newName: "FiscalYear");

            migrationBuilder.RenameColumn(
                name: "end_date",
                schema: "public",
                table: "budgets",
                newName: "EndDate");

            migrationBuilder.RenameColumn(
                name: "currency_code",
                schema: "public",
                table: "budgets",
                newName: "CurrencyCode");

            migrationBuilder.RenameColumn(
                name: "created_by_id",
                schema: "public",
                table: "budgets",
                newName: "CreatedById");

            migrationBuilder.RenameColumn(
                name: "created_at",
                schema: "public",
                table: "budgets",
                newName: "CreatedAt");

            migrationBuilder.RenameColumn(
                name: "approved_by_id",
                schema: "public",
                table: "budgets",
                newName: "ApprovedById");

            migrationBuilder.RenameColumn(
                name: "allocation_rules_json",
                schema: "public",
                table: "budgets",
                newName: "AllocationRulesJson");

            migrationBuilder.RenameIndex(
                name: "ix_budgets_status",
                schema: "public",
                table: "budgets",
                newName: "IX_budgets_Status");

            migrationBuilder.RenameIndex(
                name: "ix_budgets_name",
                schema: "public",
                table: "budgets",
                newName: "IX_budgets_Name");

            migrationBuilder.RenameIndex(
                name: "ix_budgets_workflow_instance_id",
                schema: "public",
                table: "budgets",
                newName: "IX_budgets_WorkflowInstanceId");

            migrationBuilder.RenameIndex(
                name: "ix_budgets_tenant_id",
                schema: "public",
                table: "budgets",
                newName: "IX_budgets_TenantId");

            migrationBuilder.RenameIndex(
                name: "ix_budgets_start_date",
                schema: "public",
                table: "budgets",
                newName: "IX_budgets_StartDate");

            migrationBuilder.RenameIndex(
                name: "ix_budgets_is_deleted",
                schema: "public",
                table: "budgets",
                newName: "IX_budgets_IsDeleted");

            migrationBuilder.RenameIndex(
                name: "ix_budgets_fiscal_year",
                schema: "public",
                table: "budgets",
                newName: "IX_budgets_FiscalYear");

            migrationBuilder.RenameIndex(
                name: "ix_budgets_end_date",
                schema: "public",
                table: "budgets",
                newName: "IX_budgets_EndDate");

            migrationBuilder.RenameIndex(
                name: "ix_budgets_created_by_id",
                schema: "public",
                table: "budgets",
                newName: "IX_budgets_CreatedById");

            migrationBuilder.RenameIndex(
                name: "ix_budgets_approved_by_id",
                schema: "public",
                table: "budgets",
                newName: "IX_budgets_ApprovedById");

            migrationBuilder.RenameColumn(
                name: "status",
                schema: "public",
                table: "budget_allocations",
                newName: "Status");

            migrationBuilder.RenameColumn(
                name: "description",
                schema: "public",
                table: "budget_allocations",
                newName: "Description");

            migrationBuilder.RenameColumn(
                name: "id",
                schema: "public",
                table: "budget_allocations",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "tenant_id",
                schema: "public",
                table: "budget_allocations",
                newName: "TenantId");

            migrationBuilder.RenameColumn(
                name: "modified_at",
                schema: "public",
                table: "budget_allocations",
                newName: "ModifiedAt");

            migrationBuilder.RenameColumn(
                name: "fiscal_period_identifier",
                schema: "public",
                table: "budget_allocations",
                newName: "FiscalPeriodIdentifier");

            migrationBuilder.RenameColumn(
                name: "department_id",
                schema: "public",
                table: "budget_allocations",
                newName: "DepartmentId");

            migrationBuilder.RenameColumn(
                name: "created_at",
                schema: "public",
                table: "budget_allocations",
                newName: "CreatedAt");

            migrationBuilder.RenameColumn(
                name: "budget_id",
                schema: "public",
                table: "budget_allocations",
                newName: "BudgetId");

            migrationBuilder.RenameColumn(
                name: "allocation_date",
                schema: "public",
                table: "budget_allocations",
                newName: "AllocationDate");

            migrationBuilder.RenameIndex(
                name: "ix_budget_allocations_status",
                schema: "public",
                table: "budget_allocations",
                newName: "IX_budget_allocations_Status");

            migrationBuilder.RenameIndex(
                name: "ix_budget_allocations_tenant_id",
                schema: "public",
                table: "budget_allocations",
                newName: "IX_budget_allocations_TenantId");

            migrationBuilder.RenameIndex(
                name: "ix_budget_allocations_fiscal_period_identifier",
                schema: "public",
                table: "budget_allocations",
                newName: "IX_budget_allocations_FiscalPeriodIdentifier");

            migrationBuilder.RenameIndex(
                name: "ix_budget_allocations_department_id",
                schema: "public",
                table: "budget_allocations",
                newName: "IX_budget_allocations_DepartmentId");

            migrationBuilder.RenameIndex(
                name: "ix_budget_allocations_budget_id_fiscal_period_identifier",
                schema: "public",
                table: "budget_allocations",
                newName: "IX_budget_allocations_BudgetId_FiscalPeriodIdentifier");

            migrationBuilder.RenameIndex(
                name: "ix_budget_allocations_budget_id",
                schema: "public",
                table: "budget_allocations",
                newName: "IX_budget_allocations_BudgetId");

            migrationBuilder.RenameColumn(
                name: "value",
                table: "AspNetUserTokens",
                newName: "Value");

            migrationBuilder.RenameColumn(
                name: "name",
                table: "AspNetUserTokens",
                newName: "Name");

            migrationBuilder.RenameColumn(
                name: "login_provider",
                table: "AspNetUserTokens",
                newName: "LoginProvider");

            migrationBuilder.RenameColumn(
                name: "user_id",
                table: "AspNetUserTokens",
                newName: "UserId");

            migrationBuilder.RenameColumn(
                name: "email",
                table: "AspNetUsers",
                newName: "Email");

            migrationBuilder.RenameColumn(
                name: "id",
                table: "AspNetUsers",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "user_name",
                table: "AspNetUsers",
                newName: "UserName");

            migrationBuilder.RenameColumn(
                name: "two_factor_enabled",
                table: "AspNetUsers",
                newName: "TwoFactorEnabled");

            migrationBuilder.RenameColumn(
                name: "security_stamp",
                table: "AspNetUsers",
                newName: "SecurityStamp");

            migrationBuilder.RenameColumn(
                name: "phone_number_confirmed",
                table: "AspNetUsers",
                newName: "PhoneNumberConfirmed");

            migrationBuilder.RenameColumn(
                name: "phone_number",
                table: "AspNetUsers",
                newName: "PhoneNumber");

            migrationBuilder.RenameColumn(
                name: "password_hash",
                table: "AspNetUsers",
                newName: "PasswordHash");

            migrationBuilder.RenameColumn(
                name: "normalized_user_name",
                table: "AspNetUsers",
                newName: "NormalizedUserName");

            migrationBuilder.RenameColumn(
                name: "normalized_email",
                table: "AspNetUsers",
                newName: "NormalizedEmail");

            migrationBuilder.RenameColumn(
                name: "lockout_end",
                table: "AspNetUsers",
                newName: "LockoutEnd");

            migrationBuilder.RenameColumn(
                name: "lockout_enabled",
                table: "AspNetUsers",
                newName: "LockoutEnabled");

            migrationBuilder.RenameColumn(
                name: "email_confirmed",
                table: "AspNetUsers",
                newName: "EmailConfirmed");

            migrationBuilder.RenameColumn(
                name: "concurrency_stamp",
                table: "AspNetUsers",
                newName: "ConcurrencyStamp");

            migrationBuilder.RenameColumn(
                name: "access_failed_count",
                table: "AspNetUsers",
                newName: "AccessFailedCount");

            migrationBuilder.RenameColumn(
                name: "role_id",
                table: "AspNetUserRoles",
                newName: "RoleId");

            migrationBuilder.RenameColumn(
                name: "user_id",
                table: "AspNetUserRoles",
                newName: "UserId");

            migrationBuilder.RenameIndex(
                name: "ix_asp_net_user_roles_role_id",
                table: "AspNetUserRoles",
                newName: "IX_AspNetUserRoles_RoleId");

            migrationBuilder.RenameColumn(
                name: "user_id",
                table: "AspNetUserLogins",
                newName: "UserId");

            migrationBuilder.RenameColumn(
                name: "provider_display_name",
                table: "AspNetUserLogins",
                newName: "ProviderDisplayName");

            migrationBuilder.RenameColumn(
                name: "provider_key",
                table: "AspNetUserLogins",
                newName: "ProviderKey");

            migrationBuilder.RenameColumn(
                name: "login_provider",
                table: "AspNetUserLogins",
                newName: "LoginProvider");

            migrationBuilder.RenameIndex(
                name: "ix_asp_net_user_logins_user_id",
                table: "AspNetUserLogins",
                newName: "IX_AspNetUserLogins_UserId");

            migrationBuilder.RenameColumn(
                name: "id",
                table: "AspNetUserClaims",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "user_id",
                table: "AspNetUserClaims",
                newName: "UserId");

            migrationBuilder.RenameColumn(
                name: "claim_value",
                table: "AspNetUserClaims",
                newName: "ClaimValue");

            migrationBuilder.RenameColumn(
                name: "claim_type",
                table: "AspNetUserClaims",
                newName: "ClaimType");

            migrationBuilder.RenameIndex(
                name: "ix_asp_net_user_claims_user_id",
                table: "AspNetUserClaims",
                newName: "IX_AspNetUserClaims_UserId");

            migrationBuilder.RenameColumn(
                name: "name",
                table: "AspNetRoles",
                newName: "Name");

            migrationBuilder.RenameColumn(
                name: "id",
                table: "AspNetRoles",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "normalized_name",
                table: "AspNetRoles",
                newName: "NormalizedName");

            migrationBuilder.RenameColumn(
                name: "concurrency_stamp",
                table: "AspNetRoles",
                newName: "ConcurrencyStamp");

            migrationBuilder.RenameColumn(
                name: "id",
                table: "AspNetRoleClaims",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "role_id",
                table: "AspNetRoleClaims",
                newName: "RoleId");

            migrationBuilder.RenameColumn(
                name: "claim_value",
                table: "AspNetRoleClaims",
                newName: "ClaimValue");

            migrationBuilder.RenameColumn(
                name: "claim_type",
                table: "AspNetRoleClaims",
                newName: "ClaimType");

            migrationBuilder.RenameIndex(
                name: "ix_asp_net_role_claims_role_id",
                table: "AspNetRoleClaims",
                newName: "IX_AspNetRoleClaims_RoleId");

            migrationBuilder.RenameColumn(
                name: "url",
                table: "DocumentLink",
                newName: "Url");

            migrationBuilder.RenameColumn(
                name: "name",
                table: "DocumentLink",
                newName: "Name");

            migrationBuilder.RenameColumn(
                name: "description",
                table: "DocumentLink",
                newName: "Description");

            migrationBuilder.RenameColumn(
                name: "document_type",
                table: "DocumentLink",
                newName: "DocumentType");

            migrationBuilder.AddColumn<DateTime>(
                name: "CreatedDate",
                table: "test_entities",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<string>(
                name: "Description",
                table: "test_entities",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<int>(
                name: "TestNumber",
                table: "test_entities",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddPrimaryKey(
                name: "PK_vendors",
                table: "vendors",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_vendor_proposals",
                table: "vendor_proposals",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_vendor_products",
                table: "vendor_products",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_test_entities",
                table: "test_entities",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_tenants",
                schema: "public",
                table: "tenants",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_tenant_products",
                table: "tenant_products",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_technical_submittals",
                table: "technical_submittals",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_suppliers",
                table: "suppliers",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_submittal_reviews",
                table: "submittal_reviews",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_sales_territory_representatives",
                table: "sales_territory_representatives",
                columns: new[] { "SalesTerritoryId", "RepresentativeId" });

            migrationBuilder.AddPrimaryKey(
                name: "PK_sales_territories",
                table: "sales_territories",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_sales_orders",
                schema: "public",
                table: "sales_orders",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_sales_order_lines",
                table: "sales_order_lines",
                columns: new[] { "SalesOrderId", "LineNumber" });

            migrationBuilder.AddPrimaryKey(
                name: "PK_return_authorizations",
                table: "return_authorizations",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_return_authorization_lines",
                table: "return_authorization_lines",
                columns: new[] { "ReturnAuthorizationId", "LineNumber" });

            migrationBuilder.AddPrimaryKey(
                name: "PK_requests_for_proposal",
                table: "requests_for_proposal",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_requests_for_information",
                table: "requests_for_information",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_request_for_quotes",
                schema: "public",
                table: "request_for_quotes",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_request_for_quote_lines",
                schema: "public",
                table: "request_for_quote_lines",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_PurchaseRequisitions",
                table: "PurchaseRequisitions",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_PurchaseRequisitionLines",
                table: "PurchaseRequisitionLines",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_PurchaseOrderLines",
                table: "PurchaseOrderLines",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_purchase_orders",
                table: "purchase_orders",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_projects",
                schema: "public",
                table: "projects",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_products",
                schema: "public",
                table: "products",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_product_definitions",
                table: "product_definitions",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_procurement_workflows",
                table: "procurement_workflows",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_procurement_workflow_steps",
                table: "procurement_workflow_steps",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_payment_transactions",
                table: "payment_transactions",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_invoices",
                table: "invoices",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_invoice_lines",
                table: "invoice_lines",
                columns: new[] { "InvoiceId", "LineNumber" });

            migrationBuilder.AddPrimaryKey(
                name: "PK_goods_receipt_notes",
                table: "goods_receipt_notes",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_goods_receipt_note_lines",
                table: "goods_receipt_note_lines",
                columns: new[] { "GoodsReceiptNoteId", "LineNumber" });

            migrationBuilder.AddPrimaryKey(
                name: "PK_departments",
                schema: "public",
                table: "departments",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_delivery_notes",
                table: "delivery_notes",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_delivery_note_lines",
                table: "delivery_note_lines",
                columns: new[] { "DeliveryNoteId", "LineNumber" });

            migrationBuilder.AddPrimaryKey(
                name: "PK_customers",
                schema: "public",
                table: "customers",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_contracts",
                table: "contracts",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_categories",
                schema: "public",
                table: "categories",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_budgets",
                schema: "public",
                table: "budgets",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_budget_allocations",
                schema: "public",
                table: "budget_allocations",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_AspNetUserTokens",
                table: "AspNetUserTokens",
                columns: new[] { "UserId", "LoginProvider", "Name" });

            migrationBuilder.AddPrimaryKey(
                name: "PK_AspNetUsers",
                table: "AspNetUsers",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_AspNetUserRoles",
                table: "AspNetUserRoles",
                columns: new[] { "UserId", "RoleId" });

            migrationBuilder.AddPrimaryKey(
                name: "PK_AspNetUserLogins",
                table: "AspNetUserLogins",
                columns: new[] { "LoginProvider", "ProviderKey" });

            migrationBuilder.AddPrimaryKey(
                name: "PK_AspNetUserClaims",
                table: "AspNetUserClaims",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_AspNetRoles",
                table: "AspNetRoles",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_AspNetRoleClaims",
                table: "AspNetRoleClaims",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_AspNetRoleClaims_AspNetRoles_RoleId",
                table: "AspNetRoleClaims",
                column: "RoleId",
                principalTable: "AspNetRoles",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_AspNetUserClaims_AspNetUsers_UserId",
                table: "AspNetUserClaims",
                column: "UserId",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_AspNetUserLogins_AspNetUsers_UserId",
                table: "AspNetUserLogins",
                column: "UserId",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_AspNetUserRoles_AspNetRoles_RoleId",
                table: "AspNetUserRoles",
                column: "RoleId",
                principalTable: "AspNetRoles",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_AspNetUserRoles_AspNetUsers_UserId",
                table: "AspNetUserRoles",
                column: "UserId",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_AspNetUserTokens_AspNetUsers_UserId",
                table: "AspNetUserTokens",
                column: "UserId",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_budget_allocations_budgets_BudgetId",
                schema: "public",
                table: "budget_allocations",
                column: "BudgetId",
                principalSchema: "public",
                principalTable: "budgets",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_budget_allocations_departments_DepartmentId",
                schema: "public",
                table: "budget_allocations",
                column: "DepartmentId",
                principalSchema: "public",
                principalTable: "departments",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_categories_categories_ParentCategoryId",
                schema: "public",
                table: "categories",
                column: "ParentCategoryId",
                principalSchema: "public",
                principalTable: "categories",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_contracts_vendors_VendorId",
                table: "contracts",
                column: "VendorId",
                principalTable: "vendors",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_delivery_note_lines_PurchaseOrderLines_PurchaseOrderLineId",
                table: "delivery_note_lines",
                column: "PurchaseOrderLineId",
                principalTable: "PurchaseOrderLines",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_delivery_note_lines_delivery_notes_DeliveryNoteId",
                table: "delivery_note_lines",
                column: "DeliveryNoteId",
                principalTable: "delivery_notes",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_delivery_note_lines_product_definitions_ProductId",
                table: "delivery_note_lines",
                column: "ProductId",
                principalTable: "product_definitions",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_delivery_note_lines_sales_order_lines_SalesOrderId_SalesOrd~",
                table: "delivery_note_lines",
                columns: new[] { "SalesOrderId", "SalesOrderLineNumber" },
                principalTable: "sales_order_lines",
                principalColumns: new[] { "SalesOrderId", "LineNumber" },
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_delivery_notes_purchase_orders_PurchaseOrderId",
                table: "delivery_notes",
                column: "PurchaseOrderId",
                principalTable: "purchase_orders",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_delivery_notes_sales_orders_SalesOrderId",
                table: "delivery_notes",
                column: "SalesOrderId",
                principalSchema: "public",
                principalTable: "sales_orders",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_delivery_notes_vendors_VendorId",
                table: "delivery_notes",
                column: "VendorId",
                principalTable: "vendors",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_goods_receipt_note_lines_PurchaseOrderLines_PurchaseOrderLi~",
                table: "goods_receipt_note_lines",
                column: "PurchaseOrderLineId",
                principalTable: "PurchaseOrderLines",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_goods_receipt_note_lines_delivery_note_lines_DeliveryNoteLi~",
                table: "goods_receipt_note_lines",
                columns: new[] { "DeliveryNoteLineDeliveryNoteId", "DeliveryNoteLineLineNumber" },
                principalTable: "delivery_note_lines",
                principalColumns: new[] { "DeliveryNoteId", "LineNumber" });

            migrationBuilder.AddForeignKey(
                name: "FK_goods_receipt_note_lines_goods_receipt_notes_GoodsReceiptNo~",
                table: "goods_receipt_note_lines",
                column: "GoodsReceiptNoteId",
                principalTable: "goods_receipt_notes",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_goods_receipt_note_lines_product_definitions_ProductId",
                table: "goods_receipt_note_lines",
                column: "ProductId",
                principalTable: "product_definitions",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_goods_receipt_notes_AspNetUsers_ReceivedByUserId",
                table: "goods_receipt_notes",
                column: "ReceivedByUserId",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_goods_receipt_notes_delivery_notes_DeliveryNoteId",
                table: "goods_receipt_notes",
                column: "DeliveryNoteId",
                principalTable: "delivery_notes",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_goods_receipt_notes_purchase_orders_PurchaseOrderId",
                table: "goods_receipt_notes",
                column: "PurchaseOrderId",
                principalTable: "purchase_orders",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_goods_receipt_notes_vendors_VendorId",
                table: "goods_receipt_notes",
                column: "VendorId",
                principalTable: "vendors",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_invoice_lines_PurchaseOrderLines_PurchaseOrderLineId",
                table: "invoice_lines",
                column: "PurchaseOrderLineId",
                principalTable: "PurchaseOrderLines",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_invoice_lines_invoices_InvoiceId",
                table: "invoice_lines",
                column: "InvoiceId",
                principalTable: "invoices",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_invoice_lines_product_definitions_ProductId",
                table: "invoice_lines",
                column: "ProductId",
                principalTable: "product_definitions",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_invoices_customers_CustomerId",
                table: "invoices",
                column: "CustomerId",
                principalSchema: "public",
                principalTable: "customers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_invoices_purchase_orders_PurchaseOrderId",
                table: "invoices",
                column: "PurchaseOrderId",
                principalTable: "purchase_orders",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_invoices_vendors_VendorId",
                table: "invoices",
                column: "VendorId",
                principalTable: "vendors",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_payment_transactions_invoices_InvoiceId",
                table: "payment_transactions",
                column: "InvoiceId",
                principalTable: "invoices",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_payment_transactions_vendors_VendorId",
                table: "payment_transactions",
                column: "VendorId",
                principalTable: "vendors",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_procurement_workflow_steps_AspNetUsers_ApproverUserId",
                table: "procurement_workflow_steps",
                column: "ApproverUserId",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_procurement_workflow_steps_procurement_workflows_Procuremen~",
                table: "procurement_workflow_steps",
                column: "ProcurementWorkflowId",
                principalTable: "procurement_workflows",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_procurement_workflow_steps_procurement_workflows_WorkflowId",
                table: "procurement_workflow_steps",
                column: "WorkflowId",
                principalTable: "procurement_workflows",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_product_definitions_categories_CategoryId",
                table: "product_definitions",
                column: "CategoryId",
                principalSchema: "public",
                principalTable: "categories",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_products_categories_CategoryId",
                schema: "public",
                table: "products",
                column: "CategoryId",
                principalSchema: "public",
                principalTable: "categories",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_purchase_orders_PurchaseRequisitions_RequisitionId",
                table: "purchase_orders",
                column: "RequisitionId",
                principalTable: "PurchaseRequisitions",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_purchase_orders_contracts_ContractId",
                table: "purchase_orders",
                column: "ContractId",
                principalTable: "contracts",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_purchase_orders_vendors_VendorId",
                table: "purchase_orders",
                column: "VendorId",
                principalTable: "vendors",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_PurchaseOrderLines_purchase_orders_PurchaseOrderId",
                table: "PurchaseOrderLines",
                column: "PurchaseOrderId",
                principalTable: "purchase_orders",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_PurchaseOrderLines_vendor_products_VendorProductId",
                table: "PurchaseOrderLines",
                column: "VendorProductId",
                principalTable: "vendor_products",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_PurchaseRequisitionLines_PurchaseRequisitions_PurchaseRequi~",
                table: "PurchaseRequisitionLines",
                column: "PurchaseRequisitionId",
                principalTable: "PurchaseRequisitions",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_PurchaseRequisitionLines_product_definitions_ProductDefinit~",
                table: "PurchaseRequisitionLines",
                column: "ProductDefinitionId",
                principalTable: "product_definitions",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_PurchaseRequisitionLines_vendor_products_VendorProductId",
                table: "PurchaseRequisitionLines",
                column: "VendorProductId",
                principalTable: "vendor_products",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_PurchaseRequisitionLines_vendors_SuggestedVendorId",
                table: "PurchaseRequisitionLines",
                column: "SuggestedVendorId",
                principalTable: "vendors",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_PurchaseRequisitions_purchase_orders_AssociatedPurchaseOrde~",
                table: "PurchaseRequisitions",
                column: "AssociatedPurchaseOrderId",
                principalTable: "purchase_orders",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_request_for_quote_lines_product_definitions_ProductDefiniti~",
                schema: "public",
                table: "request_for_quote_lines",
                column: "ProductDefinitionId",
                principalTable: "product_definitions",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_request_for_quote_lines_request_for_quotes_RequestForQuoteId",
                schema: "public",
                table: "request_for_quote_lines",
                column: "RequestForQuoteId",
                principalSchema: "public",
                principalTable: "request_for_quotes",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_request_for_quote_lines_vendor_products_VendorProductId",
                schema: "public",
                table: "request_for_quote_lines",
                column: "VendorProductId",
                principalTable: "vendor_products",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_request_for_quotes_PurchaseRequisitions_OriginatingRequisit~",
                schema: "public",
                table: "request_for_quotes",
                column: "OriginatingRequisitionId",
                principalTable: "PurchaseRequisitions",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_request_for_quotes_contracts_RelatedAgreementId",
                schema: "public",
                table: "request_for_quotes",
                column: "RelatedAgreementId",
                principalTable: "contracts",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_request_for_quotes_vendors_AwardedVendorId",
                schema: "public",
                table: "request_for_quotes",
                column: "AwardedVendorId",
                principalTable: "vendors",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_requests_for_information_projects_ProjectId",
                table: "requests_for_information",
                column: "ProjectId",
                principalSchema: "public",
                principalTable: "projects",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_requests_for_proposal_contracts_AwardedContractId",
                table: "requests_for_proposal",
                column: "AwardedContractId",
                principalTable: "contracts",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_requests_for_proposal_projects_ProjectId",
                table: "requests_for_proposal",
                column: "ProjectId",
                principalSchema: "public",
                principalTable: "projects",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_requests_for_proposal_vendors_AwardedVendorId",
                table: "requests_for_proposal",
                column: "AwardedVendorId",
                principalTable: "vendors",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_return_authorization_lines_invoice_lines_InvoiceId_InvoiceL~",
                table: "return_authorization_lines",
                columns: new[] { "InvoiceId", "InvoiceLineNumber" },
                principalTable: "invoice_lines",
                principalColumns: new[] { "InvoiceId", "LineNumber" },
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_return_authorization_lines_product_definitions_ProductDefin~",
                table: "return_authorization_lines",
                column: "ProductDefinitionId",
                principalTable: "product_definitions",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_return_authorization_lines_products_ProductId",
                table: "return_authorization_lines",
                column: "ProductId",
                principalSchema: "public",
                principalTable: "products",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_return_authorization_lines_return_authorizations_ReturnAuth~",
                table: "return_authorization_lines",
                column: "ReturnAuthorizationId",
                principalTable: "return_authorizations",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_return_authorization_lines_sales_order_lines_OriginalSalesO~",
                table: "return_authorization_lines",
                columns: new[] { "OriginalSalesOrderLineSalesOrderId", "OriginalSalesOrderLineLineNumber" },
                principalTable: "sales_order_lines",
                principalColumns: new[] { "SalesOrderId", "LineNumber" });

            migrationBuilder.AddForeignKey(
                name: "FK_return_authorization_lines_sales_order_lines_SalesOrderId_S~",
                table: "return_authorization_lines",
                columns: new[] { "SalesOrderId", "SalesOrderLineNumber" },
                principalTable: "sales_order_lines",
                principalColumns: new[] { "SalesOrderId", "LineNumber" },
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_return_authorization_lines_vendor_products_VendorProductId",
                table: "return_authorization_lines",
                column: "VendorProductId",
                principalTable: "vendor_products",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_return_authorizations_customers_CustomerId",
                table: "return_authorizations",
                column: "CustomerId",
                principalSchema: "public",
                principalTable: "customers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_return_authorizations_invoices_InvoiceId",
                table: "return_authorizations",
                column: "InvoiceId",
                principalTable: "invoices",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_return_authorizations_sales_orders_SalesOrderId",
                table: "return_authorizations",
                column: "SalesOrderId",
                principalSchema: "public",
                principalTable: "sales_orders",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_sales_order_lines_products_ProductId",
                table: "sales_order_lines",
                column: "ProductId",
                principalSchema: "public",
                principalTable: "products",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_sales_order_lines_projects_ProjectId",
                table: "sales_order_lines",
                column: "ProjectId",
                principalSchema: "public",
                principalTable: "projects",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_sales_order_lines_projects_ProjectId1",
                table: "sales_order_lines",
                column: "ProjectId1",
                principalSchema: "public",
                principalTable: "projects",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_sales_order_lines_sales_order_lines_ParentSalesOrderLineSal~",
                table: "sales_order_lines",
                columns: new[] { "ParentSalesOrderLineSalesOrderId", "ParentSalesOrderLineLineNumber" },
                principalTable: "sales_order_lines",
                principalColumns: new[] { "SalesOrderId", "LineNumber" });

            migrationBuilder.AddForeignKey(
                name: "FK_sales_order_lines_sales_orders_SalesOrderId",
                table: "sales_order_lines",
                column: "SalesOrderId",
                principalSchema: "public",
                principalTable: "sales_orders",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_sales_order_lines_vendor_products_VendorProductId",
                table: "sales_order_lines",
                column: "VendorProductId",
                principalTable: "vendor_products",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_sales_orders_customers_CustomerId",
                schema: "public",
                table: "sales_orders",
                column: "CustomerId",
                principalSchema: "public",
                principalTable: "customers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_sales_orders_return_authorizations_RelatedReturnAuthorizati~",
                schema: "public",
                table: "sales_orders",
                column: "RelatedReturnAuthorizationId",
                principalTable: "return_authorizations",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_sales_orders_sales_territories_SalesTerritoryId",
                schema: "public",
                table: "sales_orders",
                column: "SalesTerritoryId",
                principalTable: "sales_territories",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_sales_orders_sales_territories_SalesTerritoryId1",
                schema: "public",
                table: "sales_orders",
                column: "SalesTerritoryId1",
                principalTable: "sales_territories",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_sales_territories_sales_territories_ParentTerritoryId",
                table: "sales_territories",
                column: "ParentTerritoryId",
                principalTable: "sales_territories",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_sales_territory_representatives_AspNetUsers_RepresentativeId",
                table: "sales_territory_representatives",
                column: "RepresentativeId",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_sales_territory_representatives_sales_territories_SalesTerr~",
                table: "sales_territory_representatives",
                column: "SalesTerritoryId",
                principalTable: "sales_territories",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_submittal_reviews_AspNetUsers_ReviewerId",
                table: "submittal_reviews",
                column: "ReviewerId",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_submittal_reviews_technical_submittals_TechnicalSubmittalId",
                table: "submittal_reviews",
                column: "TechnicalSubmittalId",
                principalTable: "technical_submittals",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_submittal_reviews_technical_submittals_TechnicalSubmittalId1",
                table: "submittal_reviews",
                column: "TechnicalSubmittalId1",
                principalTable: "technical_submittals",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_suppliers_vendors_VendorId",
                table: "suppliers",
                column: "VendorId",
                principalTable: "vendors",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_technical_submittals_AspNetUsers_SubmittedByUserId",
                table: "technical_submittals",
                column: "SubmittedByUserId",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_technical_submittals_PurchaseOrderLines_PurchaseOrderLineId",
                table: "technical_submittals",
                column: "PurchaseOrderLineId",
                principalTable: "PurchaseOrderLines",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_technical_submittals_contracts_ContractId",
                table: "technical_submittals",
                column: "ContractId",
                principalTable: "contracts",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_technical_submittals_projects_ProjectId",
                table: "technical_submittals",
                column: "ProjectId",
                principalSchema: "public",
                principalTable: "projects",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_technical_submittals_vendors_VendorId",
                table: "technical_submittals",
                column: "VendorId",
                principalTable: "vendors",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_vendor_products_product_definitions_ProductDefinitionId",
                table: "vendor_products",
                column: "ProductDefinitionId",
                principalTable: "product_definitions",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_vendor_products_vendors_VendorId",
                table: "vendor_products",
                column: "VendorId",
                principalTable: "vendors",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_vendor_proposals_requests_for_proposal_RequestForProposalId",
                table: "vendor_proposals",
                column: "RequestForProposalId",
                principalTable: "requests_for_proposal",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_vendor_proposals_vendors_VendorId",
                table: "vendor_proposals",
                column: "VendorId",
                principalTable: "vendors",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AspNetRoleClaims_AspNetRoles_RoleId",
                table: "AspNetRoleClaims");

            migrationBuilder.DropForeignKey(
                name: "FK_AspNetUserClaims_AspNetUsers_UserId",
                table: "AspNetUserClaims");

            migrationBuilder.DropForeignKey(
                name: "FK_AspNetUserLogins_AspNetUsers_UserId",
                table: "AspNetUserLogins");

            migrationBuilder.DropForeignKey(
                name: "FK_AspNetUserRoles_AspNetRoles_RoleId",
                table: "AspNetUserRoles");

            migrationBuilder.DropForeignKey(
                name: "FK_AspNetUserRoles_AspNetUsers_UserId",
                table: "AspNetUserRoles");

            migrationBuilder.DropForeignKey(
                name: "FK_AspNetUserTokens_AspNetUsers_UserId",
                table: "AspNetUserTokens");

            migrationBuilder.DropForeignKey(
                name: "FK_budget_allocations_budgets_BudgetId",
                schema: "public",
                table: "budget_allocations");

            migrationBuilder.DropForeignKey(
                name: "FK_budget_allocations_departments_DepartmentId",
                schema: "public",
                table: "budget_allocations");

            migrationBuilder.DropForeignKey(
                name: "FK_categories_categories_ParentCategoryId",
                schema: "public",
                table: "categories");

            migrationBuilder.DropForeignKey(
                name: "FK_contracts_vendors_VendorId",
                table: "contracts");

            migrationBuilder.DropForeignKey(
                name: "FK_delivery_note_lines_PurchaseOrderLines_PurchaseOrderLineId",
                table: "delivery_note_lines");

            migrationBuilder.DropForeignKey(
                name: "FK_delivery_note_lines_delivery_notes_DeliveryNoteId",
                table: "delivery_note_lines");

            migrationBuilder.DropForeignKey(
                name: "FK_delivery_note_lines_product_definitions_ProductId",
                table: "delivery_note_lines");

            migrationBuilder.DropForeignKey(
                name: "FK_delivery_note_lines_sales_order_lines_SalesOrderId_SalesOrd~",
                table: "delivery_note_lines");

            migrationBuilder.DropForeignKey(
                name: "FK_delivery_notes_purchase_orders_PurchaseOrderId",
                table: "delivery_notes");

            migrationBuilder.DropForeignKey(
                name: "FK_delivery_notes_sales_orders_SalesOrderId",
                table: "delivery_notes");

            migrationBuilder.DropForeignKey(
                name: "FK_delivery_notes_vendors_VendorId",
                table: "delivery_notes");

            migrationBuilder.DropForeignKey(
                name: "FK_goods_receipt_note_lines_PurchaseOrderLines_PurchaseOrderLi~",
                table: "goods_receipt_note_lines");

            migrationBuilder.DropForeignKey(
                name: "FK_goods_receipt_note_lines_delivery_note_lines_DeliveryNoteLi~",
                table: "goods_receipt_note_lines");

            migrationBuilder.DropForeignKey(
                name: "FK_goods_receipt_note_lines_goods_receipt_notes_GoodsReceiptNo~",
                table: "goods_receipt_note_lines");

            migrationBuilder.DropForeignKey(
                name: "FK_goods_receipt_note_lines_product_definitions_ProductId",
                table: "goods_receipt_note_lines");

            migrationBuilder.DropForeignKey(
                name: "FK_goods_receipt_notes_AspNetUsers_ReceivedByUserId",
                table: "goods_receipt_notes");

            migrationBuilder.DropForeignKey(
                name: "FK_goods_receipt_notes_delivery_notes_DeliveryNoteId",
                table: "goods_receipt_notes");

            migrationBuilder.DropForeignKey(
                name: "FK_goods_receipt_notes_purchase_orders_PurchaseOrderId",
                table: "goods_receipt_notes");

            migrationBuilder.DropForeignKey(
                name: "FK_goods_receipt_notes_vendors_VendorId",
                table: "goods_receipt_notes");

            migrationBuilder.DropForeignKey(
                name: "FK_invoice_lines_PurchaseOrderLines_PurchaseOrderLineId",
                table: "invoice_lines");

            migrationBuilder.DropForeignKey(
                name: "FK_invoice_lines_invoices_InvoiceId",
                table: "invoice_lines");

            migrationBuilder.DropForeignKey(
                name: "FK_invoice_lines_product_definitions_ProductId",
                table: "invoice_lines");

            migrationBuilder.DropForeignKey(
                name: "FK_invoices_customers_CustomerId",
                table: "invoices");

            migrationBuilder.DropForeignKey(
                name: "FK_invoices_purchase_orders_PurchaseOrderId",
                table: "invoices");

            migrationBuilder.DropForeignKey(
                name: "FK_invoices_vendors_VendorId",
                table: "invoices");

            migrationBuilder.DropForeignKey(
                name: "FK_payment_transactions_invoices_InvoiceId",
                table: "payment_transactions");

            migrationBuilder.DropForeignKey(
                name: "FK_payment_transactions_vendors_VendorId",
                table: "payment_transactions");

            migrationBuilder.DropForeignKey(
                name: "FK_procurement_workflow_steps_AspNetUsers_ApproverUserId",
                table: "procurement_workflow_steps");

            migrationBuilder.DropForeignKey(
                name: "FK_procurement_workflow_steps_procurement_workflows_Procuremen~",
                table: "procurement_workflow_steps");

            migrationBuilder.DropForeignKey(
                name: "FK_procurement_workflow_steps_procurement_workflows_WorkflowId",
                table: "procurement_workflow_steps");

            migrationBuilder.DropForeignKey(
                name: "FK_product_definitions_categories_CategoryId",
                table: "product_definitions");

            migrationBuilder.DropForeignKey(
                name: "FK_products_categories_CategoryId",
                schema: "public",
                table: "products");

            migrationBuilder.DropForeignKey(
                name: "FK_purchase_orders_PurchaseRequisitions_RequisitionId",
                table: "purchase_orders");

            migrationBuilder.DropForeignKey(
                name: "FK_purchase_orders_contracts_ContractId",
                table: "purchase_orders");

            migrationBuilder.DropForeignKey(
                name: "FK_purchase_orders_vendors_VendorId",
                table: "purchase_orders");

            migrationBuilder.DropForeignKey(
                name: "FK_PurchaseOrderLines_purchase_orders_PurchaseOrderId",
                table: "PurchaseOrderLines");

            migrationBuilder.DropForeignKey(
                name: "FK_PurchaseOrderLines_vendor_products_VendorProductId",
                table: "PurchaseOrderLines");

            migrationBuilder.DropForeignKey(
                name: "FK_PurchaseRequisitionLines_PurchaseRequisitions_PurchaseRequi~",
                table: "PurchaseRequisitionLines");

            migrationBuilder.DropForeignKey(
                name: "FK_PurchaseRequisitionLines_product_definitions_ProductDefinit~",
                table: "PurchaseRequisitionLines");

            migrationBuilder.DropForeignKey(
                name: "FK_PurchaseRequisitionLines_vendor_products_VendorProductId",
                table: "PurchaseRequisitionLines");

            migrationBuilder.DropForeignKey(
                name: "FK_PurchaseRequisitionLines_vendors_SuggestedVendorId",
                table: "PurchaseRequisitionLines");

            migrationBuilder.DropForeignKey(
                name: "FK_PurchaseRequisitions_purchase_orders_AssociatedPurchaseOrde~",
                table: "PurchaseRequisitions");

            migrationBuilder.DropForeignKey(
                name: "FK_request_for_quote_lines_product_definitions_ProductDefiniti~",
                schema: "public",
                table: "request_for_quote_lines");

            migrationBuilder.DropForeignKey(
                name: "FK_request_for_quote_lines_request_for_quotes_RequestForQuoteId",
                schema: "public",
                table: "request_for_quote_lines");

            migrationBuilder.DropForeignKey(
                name: "FK_request_for_quote_lines_vendor_products_VendorProductId",
                schema: "public",
                table: "request_for_quote_lines");

            migrationBuilder.DropForeignKey(
                name: "FK_request_for_quotes_PurchaseRequisitions_OriginatingRequisit~",
                schema: "public",
                table: "request_for_quotes");

            migrationBuilder.DropForeignKey(
                name: "FK_request_for_quotes_contracts_RelatedAgreementId",
                schema: "public",
                table: "request_for_quotes");

            migrationBuilder.DropForeignKey(
                name: "FK_request_for_quotes_vendors_AwardedVendorId",
                schema: "public",
                table: "request_for_quotes");

            migrationBuilder.DropForeignKey(
                name: "FK_requests_for_information_projects_ProjectId",
                table: "requests_for_information");

            migrationBuilder.DropForeignKey(
                name: "FK_requests_for_proposal_contracts_AwardedContractId",
                table: "requests_for_proposal");

            migrationBuilder.DropForeignKey(
                name: "FK_requests_for_proposal_projects_ProjectId",
                table: "requests_for_proposal");

            migrationBuilder.DropForeignKey(
                name: "FK_requests_for_proposal_vendors_AwardedVendorId",
                table: "requests_for_proposal");

            migrationBuilder.DropForeignKey(
                name: "FK_return_authorization_lines_invoice_lines_InvoiceId_InvoiceL~",
                table: "return_authorization_lines");

            migrationBuilder.DropForeignKey(
                name: "FK_return_authorization_lines_product_definitions_ProductDefin~",
                table: "return_authorization_lines");

            migrationBuilder.DropForeignKey(
                name: "FK_return_authorization_lines_products_ProductId",
                table: "return_authorization_lines");

            migrationBuilder.DropForeignKey(
                name: "FK_return_authorization_lines_return_authorizations_ReturnAuth~",
                table: "return_authorization_lines");

            migrationBuilder.DropForeignKey(
                name: "FK_return_authorization_lines_sales_order_lines_OriginalSalesO~",
                table: "return_authorization_lines");

            migrationBuilder.DropForeignKey(
                name: "FK_return_authorization_lines_sales_order_lines_SalesOrderId_S~",
                table: "return_authorization_lines");

            migrationBuilder.DropForeignKey(
                name: "FK_return_authorization_lines_vendor_products_VendorProductId",
                table: "return_authorization_lines");

            migrationBuilder.DropForeignKey(
                name: "FK_return_authorizations_customers_CustomerId",
                table: "return_authorizations");

            migrationBuilder.DropForeignKey(
                name: "FK_return_authorizations_invoices_InvoiceId",
                table: "return_authorizations");

            migrationBuilder.DropForeignKey(
                name: "FK_return_authorizations_sales_orders_SalesOrderId",
                table: "return_authorizations");

            migrationBuilder.DropForeignKey(
                name: "FK_sales_order_lines_products_ProductId",
                table: "sales_order_lines");

            migrationBuilder.DropForeignKey(
                name: "FK_sales_order_lines_projects_ProjectId",
                table: "sales_order_lines");

            migrationBuilder.DropForeignKey(
                name: "FK_sales_order_lines_projects_ProjectId1",
                table: "sales_order_lines");

            migrationBuilder.DropForeignKey(
                name: "FK_sales_order_lines_sales_order_lines_ParentSalesOrderLineSal~",
                table: "sales_order_lines");

            migrationBuilder.DropForeignKey(
                name: "FK_sales_order_lines_sales_orders_SalesOrderId",
                table: "sales_order_lines");

            migrationBuilder.DropForeignKey(
                name: "FK_sales_order_lines_vendor_products_VendorProductId",
                table: "sales_order_lines");

            migrationBuilder.DropForeignKey(
                name: "FK_sales_orders_customers_CustomerId",
                schema: "public",
                table: "sales_orders");

            migrationBuilder.DropForeignKey(
                name: "FK_sales_orders_return_authorizations_RelatedReturnAuthorizati~",
                schema: "public",
                table: "sales_orders");

            migrationBuilder.DropForeignKey(
                name: "FK_sales_orders_sales_territories_SalesTerritoryId",
                schema: "public",
                table: "sales_orders");

            migrationBuilder.DropForeignKey(
                name: "FK_sales_orders_sales_territories_SalesTerritoryId1",
                schema: "public",
                table: "sales_orders");

            migrationBuilder.DropForeignKey(
                name: "FK_sales_territories_sales_territories_ParentTerritoryId",
                table: "sales_territories");

            migrationBuilder.DropForeignKey(
                name: "FK_sales_territory_representatives_AspNetUsers_RepresentativeId",
                table: "sales_territory_representatives");

            migrationBuilder.DropForeignKey(
                name: "FK_sales_territory_representatives_sales_territories_SalesTerr~",
                table: "sales_territory_representatives");

            migrationBuilder.DropForeignKey(
                name: "FK_submittal_reviews_AspNetUsers_ReviewerId",
                table: "submittal_reviews");

            migrationBuilder.DropForeignKey(
                name: "FK_submittal_reviews_technical_submittals_TechnicalSubmittalId",
                table: "submittal_reviews");

            migrationBuilder.DropForeignKey(
                name: "FK_submittal_reviews_technical_submittals_TechnicalSubmittalId1",
                table: "submittal_reviews");

            migrationBuilder.DropForeignKey(
                name: "FK_suppliers_vendors_VendorId",
                table: "suppliers");

            migrationBuilder.DropForeignKey(
                name: "FK_technical_submittals_AspNetUsers_SubmittedByUserId",
                table: "technical_submittals");

            migrationBuilder.DropForeignKey(
                name: "FK_technical_submittals_PurchaseOrderLines_PurchaseOrderLineId",
                table: "technical_submittals");

            migrationBuilder.DropForeignKey(
                name: "FK_technical_submittals_contracts_ContractId",
                table: "technical_submittals");

            migrationBuilder.DropForeignKey(
                name: "FK_technical_submittals_projects_ProjectId",
                table: "technical_submittals");

            migrationBuilder.DropForeignKey(
                name: "FK_technical_submittals_vendors_VendorId",
                table: "technical_submittals");

            migrationBuilder.DropForeignKey(
                name: "FK_vendor_products_product_definitions_ProductDefinitionId",
                table: "vendor_products");

            migrationBuilder.DropForeignKey(
                name: "FK_vendor_products_vendors_VendorId",
                table: "vendor_products");

            migrationBuilder.DropForeignKey(
                name: "FK_vendor_proposals_requests_for_proposal_RequestForProposalId",
                table: "vendor_proposals");

            migrationBuilder.DropForeignKey(
                name: "FK_vendor_proposals_vendors_VendorId",
                table: "vendor_proposals");

            migrationBuilder.DropPrimaryKey(
                name: "PK_vendors",
                table: "vendors");

            migrationBuilder.DropPrimaryKey(
                name: "PK_vendor_proposals",
                table: "vendor_proposals");

            migrationBuilder.DropPrimaryKey(
                name: "PK_vendor_products",
                table: "vendor_products");

            migrationBuilder.DropPrimaryKey(
                name: "PK_test_entities",
                table: "test_entities");

            migrationBuilder.DropPrimaryKey(
                name: "PK_tenants",
                schema: "public",
                table: "tenants");

            migrationBuilder.DropPrimaryKey(
                name: "PK_tenant_products",
                table: "tenant_products");

            migrationBuilder.DropPrimaryKey(
                name: "PK_technical_submittals",
                table: "technical_submittals");

            migrationBuilder.DropPrimaryKey(
                name: "PK_suppliers",
                table: "suppliers");

            migrationBuilder.DropPrimaryKey(
                name: "PK_submittal_reviews",
                table: "submittal_reviews");

            migrationBuilder.DropPrimaryKey(
                name: "PK_sales_territory_representatives",
                table: "sales_territory_representatives");

            migrationBuilder.DropPrimaryKey(
                name: "PK_sales_territories",
                table: "sales_territories");

            migrationBuilder.DropPrimaryKey(
                name: "PK_sales_orders",
                schema: "public",
                table: "sales_orders");

            migrationBuilder.DropPrimaryKey(
                name: "PK_sales_order_lines",
                table: "sales_order_lines");

            migrationBuilder.DropPrimaryKey(
                name: "PK_return_authorizations",
                table: "return_authorizations");

            migrationBuilder.DropPrimaryKey(
                name: "PK_return_authorization_lines",
                table: "return_authorization_lines");

            migrationBuilder.DropPrimaryKey(
                name: "PK_requests_for_proposal",
                table: "requests_for_proposal");

            migrationBuilder.DropPrimaryKey(
                name: "PK_requests_for_information",
                table: "requests_for_information");

            migrationBuilder.DropPrimaryKey(
                name: "PK_request_for_quotes",
                schema: "public",
                table: "request_for_quotes");

            migrationBuilder.DropPrimaryKey(
                name: "PK_request_for_quote_lines",
                schema: "public",
                table: "request_for_quote_lines");

            migrationBuilder.DropPrimaryKey(
                name: "PK_PurchaseRequisitions",
                table: "PurchaseRequisitions");

            migrationBuilder.DropPrimaryKey(
                name: "PK_PurchaseRequisitionLines",
                table: "PurchaseRequisitionLines");

            migrationBuilder.DropPrimaryKey(
                name: "PK_PurchaseOrderLines",
                table: "PurchaseOrderLines");

            migrationBuilder.DropPrimaryKey(
                name: "PK_purchase_orders",
                table: "purchase_orders");

            migrationBuilder.DropPrimaryKey(
                name: "PK_projects",
                schema: "public",
                table: "projects");

            migrationBuilder.DropPrimaryKey(
                name: "PK_products",
                schema: "public",
                table: "products");

            migrationBuilder.DropPrimaryKey(
                name: "PK_product_definitions",
                table: "product_definitions");

            migrationBuilder.DropPrimaryKey(
                name: "PK_procurement_workflows",
                table: "procurement_workflows");

            migrationBuilder.DropPrimaryKey(
                name: "PK_procurement_workflow_steps",
                table: "procurement_workflow_steps");

            migrationBuilder.DropPrimaryKey(
                name: "PK_payment_transactions",
                table: "payment_transactions");

            migrationBuilder.DropPrimaryKey(
                name: "PK_invoices",
                table: "invoices");

            migrationBuilder.DropPrimaryKey(
                name: "PK_invoice_lines",
                table: "invoice_lines");

            migrationBuilder.DropPrimaryKey(
                name: "PK_goods_receipt_notes",
                table: "goods_receipt_notes");

            migrationBuilder.DropPrimaryKey(
                name: "PK_goods_receipt_note_lines",
                table: "goods_receipt_note_lines");

            migrationBuilder.DropPrimaryKey(
                name: "PK_departments",
                schema: "public",
                table: "departments");

            migrationBuilder.DropPrimaryKey(
                name: "PK_delivery_notes",
                table: "delivery_notes");

            migrationBuilder.DropPrimaryKey(
                name: "PK_delivery_note_lines",
                table: "delivery_note_lines");

            migrationBuilder.DropPrimaryKey(
                name: "PK_customers",
                schema: "public",
                table: "customers");

            migrationBuilder.DropPrimaryKey(
                name: "PK_contracts",
                table: "contracts");

            migrationBuilder.DropPrimaryKey(
                name: "PK_categories",
                schema: "public",
                table: "categories");

            migrationBuilder.DropPrimaryKey(
                name: "PK_budgets",
                schema: "public",
                table: "budgets");

            migrationBuilder.DropPrimaryKey(
                name: "PK_budget_allocations",
                schema: "public",
                table: "budget_allocations");

            migrationBuilder.DropPrimaryKey(
                name: "PK_AspNetUserTokens",
                table: "AspNetUserTokens");

            migrationBuilder.DropPrimaryKey(
                name: "PK_AspNetUsers",
                table: "AspNetUsers");

            migrationBuilder.DropPrimaryKey(
                name: "PK_AspNetUserRoles",
                table: "AspNetUserRoles");

            migrationBuilder.DropPrimaryKey(
                name: "PK_AspNetUserLogins",
                table: "AspNetUserLogins");

            migrationBuilder.DropPrimaryKey(
                name: "PK_AspNetUserClaims",
                table: "AspNetUserClaims");

            migrationBuilder.DropPrimaryKey(
                name: "PK_AspNetRoles",
                table: "AspNetRoles");

            migrationBuilder.DropPrimaryKey(
                name: "PK_AspNetRoleClaims",
                table: "AspNetRoleClaims");

            migrationBuilder.DropColumn(
                name: "CreatedDate",
                table: "test_entities");

            migrationBuilder.DropColumn(
                name: "Description",
                table: "test_entities");

            migrationBuilder.DropColumn(
                name: "TestNumber",
                table: "test_entities");

            migrationBuilder.RenameTable(
                name: "DocumentLink",
                newName: "document_link");

            migrationBuilder.RenameColumn(
                name: "Website",
                table: "vendors",
                newName: "website");

            migrationBuilder.RenameColumn(
                name: "Status",
                table: "vendors",
                newName: "status");

            migrationBuilder.RenameColumn(
                name: "Name",
                table: "vendors",
                newName: "name");

            migrationBuilder.RenameColumn(
                name: "Id",
                table: "vendors",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "VendorCode",
                table: "vendors",
                newName: "vendor_code");

            migrationBuilder.RenameColumn(
                name: "VatNumber",
                table: "vendors",
                newName: "vat_number");

            migrationBuilder.RenameColumn(
                name: "TaxId",
                table: "vendors",
                newName: "tax_id");

            migrationBuilder.RenameColumn(
                name: "PhoneNumber",
                table: "vendors",
                newName: "phone_number");

            migrationBuilder.RenameColumn(
                name: "ModifiedAt",
                table: "vendors",
                newName: "modified_at");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                table: "vendors",
                newName: "created_at");

            migrationBuilder.RenameColumn(
                name: "ContactName",
                table: "vendors",
                newName: "contact_name");

            migrationBuilder.RenameColumn(
                name: "ContactEmail",
                table: "vendors",
                newName: "contact_email");

            migrationBuilder.RenameColumn(
                name: "CommercialRegistrationNumber",
                table: "vendors",
                newName: "commercial_registration_number");

            migrationBuilder.RenameIndex(
                name: "IX_vendors_Status",
                table: "vendors",
                newName: "ix_vendors_status");

            migrationBuilder.RenameIndex(
                name: "IX_vendors_Name",
                table: "vendors",
                newName: "ix_vendors_name");

            migrationBuilder.RenameIndex(
                name: "IX_vendors_VendorCode",
                table: "vendors",
                newName: "ix_vendors_vendor_code");

            migrationBuilder.RenameIndex(
                name: "IX_vendors_VatNumber",
                table: "vendors",
                newName: "ix_vendors_vat_number");

            migrationBuilder.RenameIndex(
                name: "IX_vendors_TaxId",
                table: "vendors",
                newName: "ix_vendors_tax_id");

            migrationBuilder.RenameIndex(
                name: "IX_vendors_CommercialRegistrationNumber",
                table: "vendors",
                newName: "ix_vendors_commercial_registration_number");

            migrationBuilder.RenameColumn(
                name: "Version",
                table: "vendor_proposals",
                newName: "version");

            migrationBuilder.RenameColumn(
                name: "Status",
                table: "vendor_proposals",
                newName: "status");

            migrationBuilder.RenameColumn(
                name: "Comments",
                table: "vendor_proposals",
                newName: "comments");

            migrationBuilder.RenameColumn(
                name: "Id",
                table: "vendor_proposals",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "VendorId",
                table: "vendor_proposals",
                newName: "vendor_id");

            migrationBuilder.RenameColumn(
                name: "ValueAddedServices",
                table: "vendor_proposals",
                newName: "value_added_services");

            migrationBuilder.RenameColumn(
                name: "ValidityPeriodDays",
                table: "vendor_proposals",
                newName: "validity_period_days");

            migrationBuilder.RenameColumn(
                name: "ValidityEndDate",
                table: "vendor_proposals",
                newName: "validity_end_date");

            migrationBuilder.RenameColumn(
                name: "TotalProposedValue",
                table: "vendor_proposals",
                newName: "total_proposed_value");

            migrationBuilder.RenameColumn(
                name: "TenantId",
                table: "vendor_proposals",
                newName: "tenant_id");

            migrationBuilder.RenameColumn(
                name: "SustainabilityCommitments",
                table: "vendor_proposals",
                newName: "sustainability_commitments");

            migrationBuilder.RenameColumn(
                name: "SubmissionDate",
                table: "vendor_proposals",
                newName: "submission_date");

            migrationBuilder.RenameColumn(
                name: "SubcontractorDisclosures",
                table: "vendor_proposals",
                newName: "subcontractor_disclosures");

            migrationBuilder.RenameColumn(
                name: "RiskSharingClauses",
                table: "vendor_proposals",
                newName: "risk_sharing_clauses");

            migrationBuilder.RenameColumn(
                name: "RequestForProposalId",
                table: "vendor_proposals",
                newName: "request_for_proposal_id");

            migrationBuilder.RenameColumn(
                name: "PerformanceGuarantees",
                table: "vendor_proposals",
                newName: "performance_guarantees");

            migrationBuilder.RenameColumn(
                name: "ModifiedAt",
                table: "vendor_proposals",
                newName: "modified_at");

            migrationBuilder.RenameColumn(
                name: "IsDeleted",
                table: "vendor_proposals",
                newName: "is_deleted");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                table: "vendor_proposals",
                newName: "created_at");

            migrationBuilder.RenameColumn(
                name: "ComplianceCertificationsJson",
                table: "vendor_proposals",
                newName: "compliance_certifications_json");

            migrationBuilder.RenameColumn(
                name: "AlternatePaymentTerms",
                table: "vendor_proposals",
                newName: "alternate_payment_terms");

            migrationBuilder.RenameIndex(
                name: "IX_vendor_proposals_Status",
                table: "vendor_proposals",
                newName: "ix_vendor_proposals_status");

            migrationBuilder.RenameIndex(
                name: "IX_vendor_proposals_VendorId",
                table: "vendor_proposals",
                newName: "ix_vendor_proposals_vendor_id");

            migrationBuilder.RenameIndex(
                name: "IX_vendor_proposals_TenantId",
                table: "vendor_proposals",
                newName: "ix_vendor_proposals_tenant_id");

            migrationBuilder.RenameIndex(
                name: "IX_vendor_proposals_SubmissionDate",
                table: "vendor_proposals",
                newName: "ix_vendor_proposals_submission_date");

            migrationBuilder.RenameIndex(
                name: "IX_vendor_proposals_RequestForProposalId_VendorId",
                table: "vendor_proposals",
                newName: "ix_vendor_proposals_request_for_proposal_id_vendor_id");

            migrationBuilder.RenameIndex(
                name: "IX_vendor_proposals_RequestForProposalId",
                table: "vendor_proposals",
                newName: "ix_vendor_proposals_request_for_proposal_id");

            migrationBuilder.RenameColumn(
                name: "Id",
                table: "vendor_products",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "VendorSku",
                table: "vendor_products",
                newName: "vendor_sku");

            migrationBuilder.RenameColumn(
                name: "VendorId",
                table: "vendor_products",
                newName: "vendor_id");

            migrationBuilder.RenameColumn(
                name: "UnitOfMeasure",
                table: "vendor_products",
                newName: "unit_of_measure");

            migrationBuilder.RenameColumn(
                name: "ProductDefinitionId",
                table: "vendor_products",
                newName: "product_definition_id");

            migrationBuilder.RenameColumn(
                name: "PackSize",
                table: "vendor_products",
                newName: "pack_size");

            migrationBuilder.RenameColumn(
                name: "ModifiedAt",
                table: "vendor_products",
                newName: "modified_at");

            migrationBuilder.RenameColumn(
                name: "LeadTimeDays",
                table: "vendor_products",
                newName: "lead_time_days");

            migrationBuilder.RenameColumn(
                name: "IsActive",
                table: "vendor_products",
                newName: "is_active");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                table: "vendor_products",
                newName: "created_at");

            migrationBuilder.RenameIndex(
                name: "IX_vendor_products_VendorId_VendorSku",
                table: "vendor_products",
                newName: "ix_vendor_products_vendor_id_vendor_sku");

            migrationBuilder.RenameIndex(
                name: "IX_vendor_products_VendorId_ProductDefinitionId_UnitOfMeasure_~",
                table: "vendor_products",
                newName: "ix_vendor_products_vendor_id_product_definition_id_unit_of_mea");

            migrationBuilder.RenameIndex(
                name: "IX_vendor_products_ProductDefinitionId",
                table: "vendor_products",
                newName: "ix_vendor_products_product_definition_id");

            migrationBuilder.RenameColumn(
                name: "Name",
                table: "test_entities",
                newName: "name");

            migrationBuilder.RenameColumn(
                name: "Id",
                table: "test_entities",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "TenantId",
                table: "test_entities",
                newName: "tenant_id");

            migrationBuilder.RenameColumn(
                name: "Settings",
                schema: "public",
                table: "tenants",
                newName: "settings");

            migrationBuilder.RenameColumn(
                name: "Name",
                schema: "public",
                table: "tenants",
                newName: "name");

            migrationBuilder.RenameColumn(
                name: "Identifier",
                schema: "public",
                table: "tenants",
                newName: "identifier");

            migrationBuilder.RenameColumn(
                name: "Country",
                schema: "public",
                table: "tenants",
                newName: "country");

            migrationBuilder.RenameColumn(
                name: "City",
                schema: "public",
                table: "tenants",
                newName: "city");

            migrationBuilder.RenameColumn(
                name: "Id",
                schema: "public",
                table: "tenants",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "SubscriptionPlan",
                schema: "public",
                table: "tenants",
                newName: "subscription_plan");

            migrationBuilder.RenameColumn(
                name: "PostalCode",
                schema: "public",
                table: "tenants",
                newName: "postal_code");

            migrationBuilder.RenameColumn(
                name: "ModifiedAt",
                schema: "public",
                table: "tenants",
                newName: "modified_at");

            migrationBuilder.RenameColumn(
                name: "IsActive",
                schema: "public",
                table: "tenants",
                newName: "is_active");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                schema: "public",
                table: "tenants",
                newName: "created_at");

            migrationBuilder.RenameColumn(
                name: "ContactEmail",
                schema: "public",
                table: "tenants",
                newName: "contact_email");

            migrationBuilder.RenameColumn(
                name: "AddressLine1",
                schema: "public",
                table: "tenants",
                newName: "address_line1");

            migrationBuilder.RenameColumn(
                name: "SKU",
                table: "tenant_products",
                newName: "sku");

            migrationBuilder.RenameColumn(
                name: "Price",
                table: "tenant_products",
                newName: "price");

            migrationBuilder.RenameColumn(
                name: "Name",
                table: "tenant_products",
                newName: "name");

            migrationBuilder.RenameColumn(
                name: "Description",
                table: "tenant_products",
                newName: "description");

            migrationBuilder.RenameColumn(
                name: "Id",
                table: "tenant_products",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "TenantId",
                table: "tenant_products",
                newName: "tenant_id");

            migrationBuilder.RenameColumn(
                name: "ModifiedAt",
                table: "tenant_products",
                newName: "modified_at");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                table: "tenant_products",
                newName: "created_at");

            migrationBuilder.RenameIndex(
                name: "IX_tenant_products_TenantId",
                table: "tenant_products",
                newName: "ix_tenant_products_tenant_id");

            migrationBuilder.RenameColumn(
                name: "Title",
                table: "technical_submittals",
                newName: "title");

            migrationBuilder.RenameColumn(
                name: "Status",
                table: "technical_submittals",
                newName: "status");

            migrationBuilder.RenameColumn(
                name: "Revision",
                table: "technical_submittals",
                newName: "revision");

            migrationBuilder.RenameColumn(
                name: "Description",
                table: "technical_submittals",
                newName: "description");

            migrationBuilder.RenameColumn(
                name: "Id",
                table: "technical_submittals",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "VendorId",
                table: "technical_submittals",
                newName: "vendor_id");

            migrationBuilder.RenameColumn(
                name: "TestPlanId",
                table: "technical_submittals",
                newName: "test_plan_id");

            migrationBuilder.RenameColumn(
                name: "TenantId",
                table: "technical_submittals",
                newName: "tenant_id");

            migrationBuilder.RenameColumn(
                name: "SubmittedDocuments",
                table: "technical_submittals",
                newName: "submitted_documents");

            migrationBuilder.RenameColumn(
                name: "SubmittedDate",
                table: "technical_submittals",
                newName: "submitted_date");

            migrationBuilder.RenameColumn(
                name: "SubmittedByUserId",
                table: "technical_submittals",
                newName: "submitted_by_user_id");

            migrationBuilder.RenameColumn(
                name: "SubmittedById",
                table: "technical_submittals",
                newName: "submitted_by_id");

            migrationBuilder.RenameColumn(
                name: "SubmittalType",
                table: "technical_submittals",
                newName: "submittal_type");

            migrationBuilder.RenameColumn(
                name: "SubmittalNumber",
                table: "technical_submittals",
                newName: "submittal_number");

            migrationBuilder.RenameColumn(
                name: "SpecificationSection",
                table: "technical_submittals",
                newName: "specification_section");

            migrationBuilder.RenameColumn(
                name: "SpecificationId",
                table: "technical_submittals",
                newName: "specification_id");

            migrationBuilder.RenameColumn(
                name: "SignedOffDate",
                table: "technical_submittals",
                newName: "signed_off_date");

            migrationBuilder.RenameColumn(
                name: "SignedOffById",
                table: "technical_submittals",
                newName: "signed_off_by_id");

            migrationBuilder.RenameColumn(
                name: "ReviewStartDate",
                table: "technical_submittals",
                newName: "review_start_date");

            migrationBuilder.RenameColumn(
                name: "ReviewDueDate",
                table: "technical_submittals",
                newName: "review_due_date");

            migrationBuilder.RenameColumn(
                name: "ReviewCompletionDate",
                table: "technical_submittals",
                newName: "review_completion_date");

            migrationBuilder.RenameColumn(
                name: "ResubmissionCount",
                table: "technical_submittals",
                newName: "resubmission_count");

            migrationBuilder.RenameColumn(
                name: "RequiredDate",
                table: "technical_submittals",
                newName: "required_date");

            migrationBuilder.RenameColumn(
                name: "RelatedNCRReference",
                table: "technical_submittals",
                newName: "related_ncr_reference");

            migrationBuilder.RenameColumn(
                name: "RelatedITPReference",
                table: "technical_submittals",
                newName: "related_itp_reference");

            migrationBuilder.RenameColumn(
                name: "PurchaseOrderLineId",
                table: "technical_submittals",
                newName: "purchase_order_line_id");

            migrationBuilder.RenameColumn(
                name: "ProjectId",
                table: "technical_submittals",
                newName: "project_id");

            migrationBuilder.RenameColumn(
                name: "NonConformanceReportId",
                table: "technical_submittals",
                newName: "non_conformance_report_id");

            migrationBuilder.RenameColumn(
                name: "ModifiedAt",
                table: "technical_submittals",
                newName: "modified_at");

            migrationBuilder.RenameColumn(
                name: "MaxResubmissions",
                table: "technical_submittals",
                newName: "max_resubmissions");

            migrationBuilder.RenameColumn(
                name: "IsFinalDocumentation",
                table: "technical_submittals",
                newName: "is_final_documentation");

            migrationBuilder.RenameColumn(
                name: "IsDeleted",
                table: "technical_submittals",
                newName: "is_deleted");

            migrationBuilder.RenameColumn(
                name: "IsAsBuilt",
                table: "technical_submittals",
                newName: "is_as_built");

            migrationBuilder.RenameColumn(
                name: "FinalSignOffDate",
                table: "technical_submittals",
                newName: "final_sign_off_date");

            migrationBuilder.RenameColumn(
                name: "FinalSignOffById",
                table: "technical_submittals",
                newName: "final_sign_off_by_id");

            migrationBuilder.RenameColumn(
                name: "CycleCount",
                table: "technical_submittals",
                newName: "cycle_count");

            migrationBuilder.RenameColumn(
                name: "CurrentReviewerId",
                table: "technical_submittals",
                newName: "current_reviewer_id");

            migrationBuilder.RenameColumn(
                name: "CurrentReviewCycle",
                table: "technical_submittals",
                newName: "current_review_cycle");

            migrationBuilder.RenameColumn(
                name: "CurrentOverallDisposition",
                table: "technical_submittals",
                newName: "current_overall_disposition");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                table: "technical_submittals",
                newName: "created_at");

            migrationBuilder.RenameColumn(
                name: "ContractId",
                table: "technical_submittals",
                newName: "contract_id");

            migrationBuilder.RenameIndex(
                name: "IX_technical_submittals_Status",
                table: "technical_submittals",
                newName: "ix_technical_submittals_status");

            migrationBuilder.RenameIndex(
                name: "IX_technical_submittals_VendorId",
                table: "technical_submittals",
                newName: "ix_technical_submittals_vendor_id");

            migrationBuilder.RenameIndex(
                name: "IX_technical_submittals_TenantId",
                table: "technical_submittals",
                newName: "ix_technical_submittals_tenant_id");

            migrationBuilder.RenameIndex(
                name: "IX_technical_submittals_SubmittedDate",
                table: "technical_submittals",
                newName: "ix_technical_submittals_submitted_date");

            migrationBuilder.RenameIndex(
                name: "IX_technical_submittals_SubmittedByUserId",
                table: "technical_submittals",
                newName: "ix_technical_submittals_submitted_by_user_id");

            migrationBuilder.RenameIndex(
                name: "IX_technical_submittals_SubmittalNumber",
                table: "technical_submittals",
                newName: "ix_technical_submittals_submittal_number");

            migrationBuilder.RenameIndex(
                name: "IX_technical_submittals_SpecificationId",
                table: "technical_submittals",
                newName: "ix_technical_submittals_specification_id");

            migrationBuilder.RenameIndex(
                name: "IX_technical_submittals_RequiredDate",
                table: "technical_submittals",
                newName: "ix_technical_submittals_required_date");

            migrationBuilder.RenameIndex(
                name: "IX_technical_submittals_PurchaseOrderLineId",
                table: "technical_submittals",
                newName: "ix_technical_submittals_purchase_order_line_id");

            migrationBuilder.RenameIndex(
                name: "IX_technical_submittals_ProjectId_SubmittalNumber_Revision",
                table: "technical_submittals",
                newName: "ix_technical_submittals_project_id_submittal_number_revision");

            migrationBuilder.RenameIndex(
                name: "IX_technical_submittals_ProjectId",
                table: "technical_submittals",
                newName: "ix_technical_submittals_project_id");

            migrationBuilder.RenameIndex(
                name: "IX_technical_submittals_ContractId",
                table: "technical_submittals",
                newName: "ix_technical_submittals_contract_id");

            migrationBuilder.RenameColumn(
                name: "Status",
                table: "suppliers",
                newName: "status");

            migrationBuilder.RenameColumn(
                name: "Id",
                table: "suppliers",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "VendorId",
                table: "suppliers",
                newName: "vendor_id");

            migrationBuilder.RenameColumn(
                name: "SustainabilityScore",
                table: "suppliers",
                newName: "sustainability_score");

            migrationBuilder.RenameColumn(
                name: "SupplierName",
                table: "suppliers",
                newName: "supplier_name");

            migrationBuilder.RenameColumn(
                name: "RiskRating",
                table: "suppliers",
                newName: "risk_rating");

            migrationBuilder.RenameColumn(
                name: "ModifiedAt",
                table: "suppliers",
                newName: "modified_at");

            migrationBuilder.RenameColumn(
                name: "IsCsrCompliant",
                table: "suppliers",
                newName: "is_csr_compliant");

            migrationBuilder.RenameColumn(
                name: "IsContractManufacturer",
                table: "suppliers",
                newName: "is_contract_manufacturer");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                table: "suppliers",
                newName: "created_at");

            migrationBuilder.RenameColumn(
                name: "CapacityUtilizationPercent",
                table: "suppliers",
                newName: "capacity_utilization_percent");

            migrationBuilder.RenameColumn(
                name: "AverageLeadTimeDays",
                table: "suppliers",
                newName: "average_lead_time_days");

            migrationBuilder.RenameColumn(
                name: "EmergencyContacts",
                table: "suppliers",
                newName: "emergency_contacts");

            migrationBuilder.RenameIndex(
                name: "IX_suppliers_VendorId",
                table: "suppliers",
                newName: "ix_suppliers_vendor_id");

            migrationBuilder.RenameIndex(
                name: "IX_suppliers_RiskRating",
                table: "suppliers",
                newName: "ix_suppliers_risk_rating");

            migrationBuilder.RenameIndex(
                name: "IX_suppliers_IsContractManufacturer",
                table: "suppliers",
                newName: "ix_suppliers_is_contract_manufacturer");

            migrationBuilder.RenameColumn(
                name: "Disposition",
                table: "submittal_reviews",
                newName: "disposition");

            migrationBuilder.RenameColumn(
                name: "Comments",
                table: "submittal_reviews",
                newName: "comments");

            migrationBuilder.RenameColumn(
                name: "Id",
                table: "submittal_reviews",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "TechnicalSubmittalId1",
                table: "submittal_reviews",
                newName: "technical_submittal_id1");

            migrationBuilder.RenameColumn(
                name: "TechnicalSubmittalId",
                table: "submittal_reviews",
                newName: "technical_submittal_id");

            migrationBuilder.RenameColumn(
                name: "ReviewerName",
                table: "submittal_reviews",
                newName: "reviewer_name");

            migrationBuilder.RenameColumn(
                name: "ReviewerId",
                table: "submittal_reviews",
                newName: "reviewer_id");

            migrationBuilder.RenameColumn(
                name: "ReviewerGuid",
                table: "submittal_reviews",
                newName: "reviewer_guid");

            migrationBuilder.RenameColumn(
                name: "ReviewDate",
                table: "submittal_reviews",
                newName: "review_date");

            migrationBuilder.RenameColumn(
                name: "ReviewCycle",
                table: "submittal_reviews",
                newName: "review_cycle");

            migrationBuilder.RenameColumn(
                name: "ModifiedAt",
                table: "submittal_reviews",
                newName: "modified_at");

            migrationBuilder.RenameColumn(
                name: "MarkupDocument",
                table: "submittal_reviews",
                newName: "markup_document");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                table: "submittal_reviews",
                newName: "created_at");

            migrationBuilder.RenameIndex(
                name: "IX_submittal_reviews_Disposition",
                table: "submittal_reviews",
                newName: "ix_submittal_reviews_disposition");

            migrationBuilder.RenameIndex(
                name: "IX_submittal_reviews_TechnicalSubmittalId1",
                table: "submittal_reviews",
                newName: "ix_submittal_reviews_technical_submittal_id1");

            migrationBuilder.RenameIndex(
                name: "IX_submittal_reviews_TechnicalSubmittalId",
                table: "submittal_reviews",
                newName: "ix_submittal_reviews_technical_submittal_id");

            migrationBuilder.RenameIndex(
                name: "IX_submittal_reviews_ReviewerId",
                table: "submittal_reviews",
                newName: "ix_submittal_reviews_reviewer_id");

            migrationBuilder.RenameIndex(
                name: "IX_submittal_reviews_ReviewDate",
                table: "submittal_reviews",
                newName: "ix_submittal_reviews_review_date");

            migrationBuilder.RenameColumn(
                name: "RepresentativeId",
                table: "sales_territory_representatives",
                newName: "representative_id");

            migrationBuilder.RenameColumn(
                name: "SalesTerritoryId",
                table: "sales_territory_representatives",
                newName: "sales_territory_id");

            migrationBuilder.RenameIndex(
                name: "IX_sales_territory_representatives_RepresentativeId",
                table: "sales_territory_representatives",
                newName: "ix_sales_territory_representatives_representative_id");

            migrationBuilder.RenameColumn(
                name: "Name",
                table: "sales_territories",
                newName: "name");

            migrationBuilder.RenameColumn(
                name: "Description",
                table: "sales_territories",
                newName: "description");

            migrationBuilder.RenameColumn(
                name: "Code",
                table: "sales_territories",
                newName: "code");

            migrationBuilder.RenameColumn(
                name: "Id",
                table: "sales_territories",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "TerritoryCode",
                table: "sales_territories",
                newName: "territory_code");

            migrationBuilder.RenameColumn(
                name: "TenantId",
                table: "sales_territories",
                newName: "tenant_id");

            migrationBuilder.RenameColumn(
                name: "PrimarySalespersonId",
                table: "sales_territories",
                newName: "primary_salesperson_id");

            migrationBuilder.RenameColumn(
                name: "ParentTerritoryId",
                table: "sales_territories",
                newName: "parent_territory_id");

            migrationBuilder.RenameColumn(
                name: "ModifiedAt",
                table: "sales_territories",
                newName: "modified_at");

            migrationBuilder.RenameColumn(
                name: "IsDeleted",
                table: "sales_territories",
                newName: "is_deleted");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                table: "sales_territories",
                newName: "created_at");

            migrationBuilder.RenameIndex(
                name: "IX_sales_territories_Name",
                table: "sales_territories",
                newName: "ix_sales_territories_name");

            migrationBuilder.RenameIndex(
                name: "IX_sales_territories_TerritoryCode",
                table: "sales_territories",
                newName: "ix_sales_territories_territory_code");

            migrationBuilder.RenameIndex(
                name: "IX_sales_territories_TenantId",
                table: "sales_territories",
                newName: "ix_sales_territories_tenant_id");

            migrationBuilder.RenameIndex(
                name: "IX_sales_territories_ParentTerritoryId",
                table: "sales_territories",
                newName: "ix_sales_territories_parent_territory_id");

            migrationBuilder.RenameColumn(
                name: "Status",
                schema: "public",
                table: "sales_orders",
                newName: "status");

            migrationBuilder.RenameColumn(
                name: "Id",
                schema: "public",
                table: "sales_orders",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "TenantId",
                schema: "public",
                table: "sales_orders",
                newName: "tenant_id");

            migrationBuilder.RenameColumn(
                name: "SalespersonId",
                schema: "public",
                table: "sales_orders",
                newName: "salesperson_id");

            migrationBuilder.RenameColumn(
                name: "SalesTerritoryId1",
                schema: "public",
                table: "sales_orders",
                newName: "sales_territory_id1");

            migrationBuilder.RenameColumn(
                name: "SalesTerritoryId",
                schema: "public",
                table: "sales_orders",
                newName: "sales_territory_id");

            migrationBuilder.RenameColumn(
                name: "RelatedReturnAuthorizationId",
                schema: "public",
                table: "sales_orders",
                newName: "related_return_authorization_id");

            migrationBuilder.RenameColumn(
                name: "OrderNumber",
                schema: "public",
                table: "sales_orders",
                newName: "order_number");

            migrationBuilder.RenameColumn(
                name: "OrderDate",
                schema: "public",
                table: "sales_orders",
                newName: "order_date");

            migrationBuilder.RenameColumn(
                name: "ModifiedAt",
                schema: "public",
                table: "sales_orders",
                newName: "modified_at");

            migrationBuilder.RenameColumn(
                name: "IsDropShipment",
                schema: "public",
                table: "sales_orders",
                newName: "is_drop_shipment");

            migrationBuilder.RenameColumn(
                name: "IsDeleted",
                schema: "public",
                table: "sales_orders",
                newName: "is_deleted");

            migrationBuilder.RenameColumn(
                name: "IsCreditApproved",
                schema: "public",
                table: "sales_orders",
                newName: "is_credit_approved");

            migrationBuilder.RenameColumn(
                name: "IsAtpConfirmed",
                schema: "public",
                table: "sales_orders",
                newName: "is_atp_confirmed");

            migrationBuilder.RenameColumn(
                name: "EdiTransactionReference",
                schema: "public",
                table: "sales_orders",
                newName: "edi_transaction_reference");

            migrationBuilder.RenameColumn(
                name: "CustomerId",
                schema: "public",
                table: "sales_orders",
                newName: "customer_id");

            migrationBuilder.RenameColumn(
                name: "CurrencyCode",
                schema: "public",
                table: "sales_orders",
                newName: "currency_code");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                schema: "public",
                table: "sales_orders",
                newName: "created_at");

            migrationBuilder.RenameColumn(
                name: "CommissionRate",
                schema: "public",
                table: "sales_orders",
                newName: "commission_rate");

            migrationBuilder.RenameColumn(
                name: "AtpCheckDate",
                schema: "public",
                table: "sales_orders",
                newName: "atp_check_date");

            migrationBuilder.RenameIndex(
                name: "IX_sales_orders_Status",
                schema: "public",
                table: "sales_orders",
                newName: "ix_sales_orders_status");

            migrationBuilder.RenameIndex(
                name: "IX_sales_orders_TenantId_OrderNumber",
                schema: "public",
                table: "sales_orders",
                newName: "ix_sales_orders_tenant_id_order_number");

            migrationBuilder.RenameIndex(
                name: "IX_sales_orders_TenantId",
                schema: "public",
                table: "sales_orders",
                newName: "ix_sales_orders_tenant_id");

            migrationBuilder.RenameIndex(
                name: "IX_sales_orders_SalesTerritoryId1",
                schema: "public",
                table: "sales_orders",
                newName: "ix_sales_orders_sales_territory_id1");

            migrationBuilder.RenameIndex(
                name: "IX_sales_orders_SalesTerritoryId",
                schema: "public",
                table: "sales_orders",
                newName: "ix_sales_orders_sales_territory_id");

            migrationBuilder.RenameIndex(
                name: "IX_sales_orders_SalespersonId",
                schema: "public",
                table: "sales_orders",
                newName: "ix_sales_orders_salesperson_id");

            migrationBuilder.RenameIndex(
                name: "IX_sales_orders_RelatedReturnAuthorizationId",
                schema: "public",
                table: "sales_orders",
                newName: "ix_sales_orders_related_return_authorization_id");

            migrationBuilder.RenameIndex(
                name: "IX_sales_orders_OrderDate",
                schema: "public",
                table: "sales_orders",
                newName: "ix_sales_orders_order_date");

            migrationBuilder.RenameIndex(
                name: "IX_sales_orders_IsDeleted",
                schema: "public",
                table: "sales_orders",
                newName: "ix_sales_orders_is_deleted");

            migrationBuilder.RenameIndex(
                name: "IX_sales_orders_EdiTransactionReference",
                schema: "public",
                table: "sales_orders",
                newName: "ix_sales_orders_edi_transaction_reference");

            migrationBuilder.RenameIndex(
                name: "IX_sales_orders_CustomerId",
                schema: "public",
                table: "sales_orders",
                newName: "ix_sales_orders_customer_id");

            migrationBuilder.RenameColumn(
                name: "Quantity",
                table: "sales_order_lines",
                newName: "quantity");

            migrationBuilder.RenameColumn(
                name: "Id",
                table: "sales_order_lines",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "Description",
                table: "sales_order_lines",
                newName: "description");

            migrationBuilder.RenameColumn(
                name: "WarrantyEndDate",
                table: "sales_order_lines",
                newName: "warranty_end_date");

            migrationBuilder.RenameColumn(
                name: "VendorProductId",
                table: "sales_order_lines",
                newName: "vendor_product_id");

            migrationBuilder.RenameColumn(
                name: "UnitPriceCurrency",
                table: "sales_order_lines",
                newName: "unit_price_currency");

            migrationBuilder.RenameColumn(
                name: "UnitPriceAmount",
                table: "sales_order_lines",
                newName: "unit_price_amount");

            migrationBuilder.RenameColumn(
                name: "UnitOfMeasure",
                table: "sales_order_lines",
                newName: "unit_of_measure");

            migrationBuilder.RenameColumn(
                name: "TenantId",
                table: "sales_order_lines",
                newName: "tenant_id");

            migrationBuilder.RenameColumn(
                name: "SkuSnapshot",
                table: "sales_order_lines",
                newName: "sku_snapshot");

            migrationBuilder.RenameColumn(
                name: "ReservedSerialNumbersJson",
                table: "sales_order_lines",
                newName: "reserved_serial_numbers_json");

            migrationBuilder.RenameColumn(
                name: "RequestedDeliveryDate",
                table: "sales_order_lines",
                newName: "requested_delivery_date");

            migrationBuilder.RenameColumn(
                name: "QuantityBackordered",
                table: "sales_order_lines",
                newName: "quantity_backordered");

            migrationBuilder.RenameColumn(
                name: "ProjectId1",
                table: "sales_order_lines",
                newName: "project_id1");

            migrationBuilder.RenameColumn(
                name: "ProjectId",
                table: "sales_order_lines",
                newName: "project_id");

            migrationBuilder.RenameColumn(
                name: "ProductId",
                table: "sales_order_lines",
                newName: "product_id");

            migrationBuilder.RenameColumn(
                name: "ParentSalesOrderLineSalesOrderId",
                table: "sales_order_lines",
                newName: "parent_sales_order_line_sales_order_id");

            migrationBuilder.RenameColumn(
                name: "ParentSalesOrderLineLineNumber",
                table: "sales_order_lines",
                newName: "parent_sales_order_line_line_number");

            migrationBuilder.RenameColumn(
                name: "ParentSalesOrderLineId",
                table: "sales_order_lines",
                newName: "parent_sales_order_line_id");

            migrationBuilder.RenameColumn(
                name: "ModifiedAt",
                table: "sales_order_lines",
                newName: "modified_at");

            migrationBuilder.RenameColumn(
                name: "LineTotalCurrency",
                table: "sales_order_lines",
                newName: "line_total_currency");

            migrationBuilder.RenameColumn(
                name: "LineTotalAmount",
                table: "sales_order_lines",
                newName: "line_total_amount");

            migrationBuilder.RenameColumn(
                name: "IsKitComponent",
                table: "sales_order_lines",
                newName: "is_kit_component");

            migrationBuilder.RenameColumn(
                name: "IsDeleted",
                table: "sales_order_lines",
                newName: "is_deleted");

            migrationBuilder.RenameColumn(
                name: "DiscountAmountValue",
                table: "sales_order_lines",
                newName: "discount_amount_value");

            migrationBuilder.RenameColumn(
                name: "DiscountAmountCurrency",
                table: "sales_order_lines",
                newName: "discount_amount_currency");

            migrationBuilder.RenameColumn(
                name: "DescriptionSnapshot",
                table: "sales_order_lines",
                newName: "description_snapshot");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                table: "sales_order_lines",
                newName: "created_at");

            migrationBuilder.RenameColumn(
                name: "CostCode",
                table: "sales_order_lines",
                newName: "cost_code");

            migrationBuilder.RenameColumn(
                name: "AppliedDiscountDescription",
                table: "sales_order_lines",
                newName: "applied_discount_description");

            migrationBuilder.RenameColumn(
                name: "LineNumber",
                table: "sales_order_lines",
                newName: "line_number");

            migrationBuilder.RenameColumn(
                name: "SalesOrderId",
                table: "sales_order_lines",
                newName: "sales_order_id");

            migrationBuilder.RenameIndex(
                name: "IX_sales_order_lines_VendorProductId",
                table: "sales_order_lines",
                newName: "ix_sales_order_lines_vendor_product_id");

            migrationBuilder.RenameIndex(
                name: "IX_sales_order_lines_ProjectId1",
                table: "sales_order_lines",
                newName: "ix_sales_order_lines_project_id1");

            migrationBuilder.RenameIndex(
                name: "IX_sales_order_lines_ProjectId",
                table: "sales_order_lines",
                newName: "ix_sales_order_lines_project_id");

            migrationBuilder.RenameIndex(
                name: "IX_sales_order_lines_ProductId",
                table: "sales_order_lines",
                newName: "ix_sales_order_lines_product_id");

            migrationBuilder.RenameIndex(
                name: "IX_sales_order_lines_ParentSalesOrderLineSalesOrderId_ParentSa~",
                table: "sales_order_lines",
                newName: "ix_sales_order_lines_parent_sales_order_line_sales_order_id_pa");

            migrationBuilder.RenameColumn(
                name: "Status",
                table: "return_authorizations",
                newName: "status");

            migrationBuilder.RenameColumn(
                name: "Notes",
                table: "return_authorizations",
                newName: "notes");

            migrationBuilder.RenameColumn(
                name: "Id",
                table: "return_authorizations",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "TenantId",
                table: "return_authorizations",
                newName: "tenant_id");

            migrationBuilder.RenameColumn(
                name: "ShippingInstructions",
                table: "return_authorizations",
                newName: "shipping_instructions");

            migrationBuilder.RenameColumn(
                name: "SalesOrderId",
                table: "return_authorizations",
                newName: "sales_order_id");

            migrationBuilder.RenameColumn(
                name: "RmaNumber",
                table: "return_authorizations",
                newName: "rma_number");

            migrationBuilder.RenameColumn(
                name: "RequestedAction",
                table: "return_authorizations",
                newName: "requested_action");

            migrationBuilder.RenameColumn(
                name: "RequestDate",
                table: "return_authorizations",
                newName: "request_date");

            migrationBuilder.RenameColumn(
                name: "ReasonForReturn",
                table: "return_authorizations",
                newName: "reason_for_return");

            migrationBuilder.RenameColumn(
                name: "OriginalSalesOrderId",
                table: "return_authorizations",
                newName: "original_sales_order_id");

            migrationBuilder.RenameColumn(
                name: "ModifiedAt",
                table: "return_authorizations",
                newName: "modified_at");

            migrationBuilder.RenameColumn(
                name: "IsDeleted",
                table: "return_authorizations",
                newName: "is_deleted");

            migrationBuilder.RenameColumn(
                name: "InvoiceId",
                table: "return_authorizations",
                newName: "invoice_id");

            migrationBuilder.RenameColumn(
                name: "ExpiryDate",
                table: "return_authorizations",
                newName: "expiry_date");

            migrationBuilder.RenameColumn(
                name: "CustomerId",
                table: "return_authorizations",
                newName: "customer_id");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                table: "return_authorizations",
                newName: "created_at");

            migrationBuilder.RenameColumn(
                name: "AuthorizationDate",
                table: "return_authorizations",
                newName: "authorization_date");

            migrationBuilder.RenameIndex(
                name: "IX_return_authorizations_Status",
                table: "return_authorizations",
                newName: "ix_return_authorizations_status");

            migrationBuilder.RenameIndex(
                name: "IX_return_authorizations_TenantId",
                table: "return_authorizations",
                newName: "ix_return_authorizations_tenant_id");

            migrationBuilder.RenameIndex(
                name: "IX_return_authorizations_SalesOrderId",
                table: "return_authorizations",
                newName: "ix_return_authorizations_sales_order_id");

            migrationBuilder.RenameIndex(
                name: "IX_return_authorizations_RmaNumber",
                table: "return_authorizations",
                newName: "ix_return_authorizations_rma_number");

            migrationBuilder.RenameIndex(
                name: "IX_return_authorizations_RequestDate",
                table: "return_authorizations",
                newName: "ix_return_authorizations_request_date");

            migrationBuilder.RenameIndex(
                name: "IX_return_authorizations_InvoiceId",
                table: "return_authorizations",
                newName: "ix_return_authorizations_invoice_id");

            migrationBuilder.RenameIndex(
                name: "IX_return_authorizations_CustomerId",
                table: "return_authorizations",
                newName: "ix_return_authorizations_customer_id");

            migrationBuilder.RenameColumn(
                name: "Id",
                table: "return_authorization_lines",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "VendorProductId",
                table: "return_authorization_lines",
                newName: "vendor_product_id");

            migrationBuilder.RenameColumn(
                name: "UnitOfMeasure",
                table: "return_authorization_lines",
                newName: "unit_of_measure");

            migrationBuilder.RenameColumn(
                name: "SkuSnapshot",
                table: "return_authorization_lines",
                newName: "sku_snapshot");

            migrationBuilder.RenameColumn(
                name: "SalesOrderLineNumber",
                table: "return_authorization_lines",
                newName: "sales_order_line_number");

            migrationBuilder.RenameColumn(
                name: "SalesOrderId",
                table: "return_authorization_lines",
                newName: "sales_order_id");

            migrationBuilder.RenameColumn(
                name: "RequestedAction",
                table: "return_authorization_lines",
                newName: "requested_action");

            migrationBuilder.RenameColumn(
                name: "ReasonForReturn",
                table: "return_authorization_lines",
                newName: "reason_for_return");

            migrationBuilder.RenameColumn(
                name: "QuantityReceived",
                table: "return_authorization_lines",
                newName: "quantity_received");

            migrationBuilder.RenameColumn(
                name: "QuantityAuthorized",
                table: "return_authorization_lines",
                newName: "quantity_authorized");

            migrationBuilder.RenameColumn(
                name: "ProductId",
                table: "return_authorization_lines",
                newName: "product_id");

            migrationBuilder.RenameColumn(
                name: "ProductDefinitionId",
                table: "return_authorization_lines",
                newName: "product_definition_id");

            migrationBuilder.RenameColumn(
                name: "OriginalSalesOrderLineSalesOrderId",
                table: "return_authorization_lines",
                newName: "original_sales_order_line_sales_order_id");

            migrationBuilder.RenameColumn(
                name: "OriginalSalesOrderLineLineNumber",
                table: "return_authorization_lines",
                newName: "original_sales_order_line_line_number");

            migrationBuilder.RenameColumn(
                name: "OriginalSalesOrderLineId",
                table: "return_authorization_lines",
                newName: "original_sales_order_line_id");

            migrationBuilder.RenameColumn(
                name: "ModifiedAt",
                table: "return_authorization_lines",
                newName: "modified_at");

            migrationBuilder.RenameColumn(
                name: "ItemCondition",
                table: "return_authorization_lines",
                newName: "item_condition");

            migrationBuilder.RenameColumn(
                name: "InvoiceLineNumber",
                table: "return_authorization_lines",
                newName: "invoice_line_number");

            migrationBuilder.RenameColumn(
                name: "InvoiceId",
                table: "return_authorization_lines",
                newName: "invoice_id");

            migrationBuilder.RenameColumn(
                name: "DescriptionSnapshot",
                table: "return_authorization_lines",
                newName: "description_snapshot");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                table: "return_authorization_lines",
                newName: "created_at");

            migrationBuilder.RenameColumn(
                name: "LineNumber",
                table: "return_authorization_lines",
                newName: "line_number");

            migrationBuilder.RenameColumn(
                name: "ReturnAuthorizationId",
                table: "return_authorization_lines",
                newName: "return_authorization_id");

            migrationBuilder.RenameIndex(
                name: "IX_return_authorization_lines_VendorProductId",
                table: "return_authorization_lines",
                newName: "ix_return_authorization_lines_vendor_product_id");

            migrationBuilder.RenameIndex(
                name: "IX_return_authorization_lines_SalesOrderId_SalesOrderLineNumber",
                table: "return_authorization_lines",
                newName: "ix_return_authorization_lines_sales_order_id_sales_order_line_");

            migrationBuilder.RenameIndex(
                name: "IX_return_authorization_lines_ProductId",
                table: "return_authorization_lines",
                newName: "ix_return_authorization_lines_product_id");

            migrationBuilder.RenameIndex(
                name: "IX_return_authorization_lines_ProductDefinitionId",
                table: "return_authorization_lines",
                newName: "ix_return_authorization_lines_product_definition_id");

            migrationBuilder.RenameIndex(
                name: "IX_return_authorization_lines_OriginalSalesOrderLineSalesOrder~",
                table: "return_authorization_lines",
                newName: "ix_return_authorization_lines_original_sales_order_line_sales_");

            migrationBuilder.RenameIndex(
                name: "IX_return_authorization_lines_InvoiceId_InvoiceLineNumber",
                table: "return_authorization_lines",
                newName: "ix_return_authorization_lines_invoice_id_invoice_line_number");

            migrationBuilder.RenameColumn(
                name: "Title",
                table: "requests_for_proposal",
                newName: "title");

            migrationBuilder.RenameColumn(
                name: "Status",
                table: "requests_for_proposal",
                newName: "status");

            migrationBuilder.RenameColumn(
                name: "Department",
                table: "requests_for_proposal",
                newName: "department");

            migrationBuilder.RenameColumn(
                name: "Id",
                table: "requests_for_proposal",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "TenantId",
                table: "requests_for_proposal",
                newName: "tenant_id");

            migrationBuilder.RenameColumn(
                name: "SubmissionDeadline",
                table: "requests_for_proposal",
                newName: "submission_deadline");

            migrationBuilder.RenameColumn(
                name: "ScopeOfWork",
                table: "requests_for_proposal",
                newName: "scope_of_work");

            migrationBuilder.RenameColumn(
                name: "RfpNumber",
                table: "requests_for_proposal",
                newName: "rfp_number");

            migrationBuilder.RenameColumn(
                name: "QuestionDeadline",
                table: "requests_for_proposal",
                newName: "question_deadline");

            migrationBuilder.RenameColumn(
                name: "ProjectId",
                table: "requests_for_proposal",
                newName: "project_id");

            migrationBuilder.RenameColumn(
                name: "ModifiedAt",
                table: "requests_for_proposal",
                newName: "modified_at");

            migrationBuilder.RenameColumn(
                name: "IssuedDate",
                table: "requests_for_proposal",
                newName: "issued_date");

            migrationBuilder.RenameColumn(
                name: "IssuedByUserId",
                table: "requests_for_proposal",
                newName: "issued_by_user_id");

            migrationBuilder.RenameColumn(
                name: "IssuedByName",
                table: "requests_for_proposal",
                newName: "issued_by_name");

            migrationBuilder.RenameColumn(
                name: "IssueDate",
                table: "requests_for_proposal",
                newName: "issue_date");

            migrationBuilder.RenameColumn(
                name: "ExpectedContractStartDate",
                table: "requests_for_proposal",
                newName: "expected_contract_start_date");

            migrationBuilder.RenameColumn(
                name: "ExpectedContractDuration",
                table: "requests_for_proposal",
                newName: "expected_contract_duration");

            migrationBuilder.RenameColumn(
                name: "EvaluationCriteria",
                table: "requests_for_proposal",
                newName: "evaluation_criteria");

            migrationBuilder.RenameColumn(
                name: "DecisionDate",
                table: "requests_for_proposal",
                newName: "decision_date");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                table: "requests_for_proposal",
                newName: "created_at");

            migrationBuilder.RenameColumn(
                name: "CompletedDate",
                table: "requests_for_proposal",
                newName: "completed_date");

            migrationBuilder.RenameColumn(
                name: "AwardedVendorId",
                table: "requests_for_proposal",
                newName: "awarded_vendor_id");

            migrationBuilder.RenameColumn(
                name: "AwardedContractId",
                table: "requests_for_proposal",
                newName: "awarded_contract_id");

            migrationBuilder.RenameIndex(
                name: "IX_requests_for_proposal_Status",
                table: "requests_for_proposal",
                newName: "ix_requests_for_proposal_status");

            migrationBuilder.RenameIndex(
                name: "IX_requests_for_proposal_TenantId",
                table: "requests_for_proposal",
                newName: "ix_requests_for_proposal_tenant_id");

            migrationBuilder.RenameIndex(
                name: "IX_requests_for_proposal_SubmissionDeadline",
                table: "requests_for_proposal",
                newName: "ix_requests_for_proposal_submission_deadline");

            migrationBuilder.RenameIndex(
                name: "IX_requests_for_proposal_RfpNumber",
                table: "requests_for_proposal",
                newName: "ix_requests_for_proposal_rfp_number");

            migrationBuilder.RenameIndex(
                name: "IX_requests_for_proposal_ProjectId",
                table: "requests_for_proposal",
                newName: "ix_requests_for_proposal_project_id");

            migrationBuilder.RenameIndex(
                name: "IX_requests_for_proposal_AwardedVendorId",
                table: "requests_for_proposal",
                newName: "ix_requests_for_proposal_awarded_vendor_id");

            migrationBuilder.RenameIndex(
                name: "IX_requests_for_proposal_AwardedContractId",
                table: "requests_for_proposal",
                newName: "ix_requests_for_proposal_awarded_contract_id");

            migrationBuilder.RenameColumn(
                name: "Title",
                table: "requests_for_information",
                newName: "title");

            migrationBuilder.RenameColumn(
                name: "Status",
                table: "requests_for_information",
                newName: "status");

            migrationBuilder.RenameColumn(
                name: "Notes",
                table: "requests_for_information",
                newName: "notes");

            migrationBuilder.RenameColumn(
                name: "Description",
                table: "requests_for_information",
                newName: "description");

            migrationBuilder.RenameColumn(
                name: "Id",
                table: "requests_for_information",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "TenantId",
                table: "requests_for_information",
                newName: "tenant_id");

            migrationBuilder.RenameColumn(
                name: "TargetAudienceDescription",
                table: "requests_for_information",
                newName: "target_audience_description");

            migrationBuilder.RenameColumn(
                name: "RfiNumber",
                table: "requests_for_information",
                newName: "rfi_number");

            migrationBuilder.RenameColumn(
                name: "ResponseDueDate",
                table: "requests_for_information",
                newName: "response_due_date");

            migrationBuilder.RenameColumn(
                name: "ResponseDeadline",
                table: "requests_for_information",
                newName: "response_deadline");

            migrationBuilder.RenameColumn(
                name: "ProjectId",
                table: "requests_for_information",
                newName: "project_id");

            migrationBuilder.RenameColumn(
                name: "ModifiedAt",
                table: "requests_for_information",
                newName: "modified_at");

            migrationBuilder.RenameColumn(
                name: "IssuedDate",
                table: "requests_for_information",
                newName: "issued_date");

            migrationBuilder.RenameColumn(
                name: "IssuedByUserId",
                table: "requests_for_information",
                newName: "issued_by_user_id");

            migrationBuilder.RenameColumn(
                name: "IssuedByName",
                table: "requests_for_information",
                newName: "issued_by_name");

            migrationBuilder.RenameColumn(
                name: "IssueDate",
                table: "requests_for_information",
                newName: "issue_date");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                table: "requests_for_information",
                newName: "created_at");

            migrationBuilder.RenameIndex(
                name: "IX_requests_for_information_Status",
                table: "requests_for_information",
                newName: "ix_requests_for_information_status");

            migrationBuilder.RenameIndex(
                name: "IX_requests_for_information_TenantId",
                table: "requests_for_information",
                newName: "ix_requests_for_information_tenant_id");

            migrationBuilder.RenameIndex(
                name: "IX_requests_for_information_RfiNumber",
                table: "requests_for_information",
                newName: "ix_requests_for_information_rfi_number");

            migrationBuilder.RenameIndex(
                name: "IX_requests_for_information_ResponseDeadline",
                table: "requests_for_information",
                newName: "ix_requests_for_information_response_deadline");

            migrationBuilder.RenameIndex(
                name: "IX_requests_for_information_ProjectId",
                table: "requests_for_information",
                newName: "ix_requests_for_information_project_id");

            migrationBuilder.RenameColumn(
                name: "Title",
                schema: "public",
                table: "request_for_quotes",
                newName: "title");

            migrationBuilder.RenameColumn(
                name: "Status",
                schema: "public",
                table: "request_for_quotes",
                newName: "status");

            migrationBuilder.RenameColumn(
                name: "Description",
                schema: "public",
                table: "request_for_quotes",
                newName: "description");

            migrationBuilder.RenameColumn(
                name: "Id",
                schema: "public",
                table: "request_for_quotes",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "VendorReferenceInstructions",
                schema: "public",
                table: "request_for_quotes",
                newName: "vendor_reference_instructions");

            migrationBuilder.RenameColumn(
                name: "TermsAndConditions",
                schema: "public",
                table: "request_for_quotes",
                newName: "terms_and_conditions");

            migrationBuilder.RenameColumn(
                name: "TenantId",
                schema: "public",
                table: "request_for_quotes",
                newName: "tenant_id");

            migrationBuilder.RenameColumn(
                name: "SubmissionDeadline",
                schema: "public",
                table: "request_for_quotes",
                newName: "submission_deadline");

            migrationBuilder.RenameColumn(
                name: "ScopeOfWork",
                schema: "public",
                table: "request_for_quotes",
                newName: "scope_of_work");

            migrationBuilder.RenameColumn(
                name: "RequiredDeliveryDate",
                schema: "public",
                table: "request_for_quotes",
                newName: "required_delivery_date");

            migrationBuilder.RenameColumn(
                name: "RelatedAgreementId",
                schema: "public",
                table: "request_for_quotes",
                newName: "related_agreement_id");

            migrationBuilder.RenameColumn(
                name: "RFQNumber",
                schema: "public",
                table: "request_for_quotes",
                newName: "rfq_number");

            migrationBuilder.RenameColumn(
                name: "OriginatingRequisitionId",
                schema: "public",
                table: "request_for_quotes",
                newName: "originating_requisition_id");

            migrationBuilder.RenameColumn(
                name: "ModifiedAt",
                schema: "public",
                table: "request_for_quotes",
                newName: "modified_at");

            migrationBuilder.RenameColumn(
                name: "IsDeleted",
                schema: "public",
                table: "request_for_quotes",
                newName: "is_deleted");

            migrationBuilder.RenameColumn(
                name: "CurrencyCode",
                schema: "public",
                table: "request_for_quotes",
                newName: "currency_code");

            migrationBuilder.RenameColumn(
                name: "CreatedByUserId",
                schema: "public",
                table: "request_for_quotes",
                newName: "created_by_user_id");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                schema: "public",
                table: "request_for_quotes",
                newName: "created_at");

            migrationBuilder.RenameColumn(
                name: "ContactPersonEmail",
                schema: "public",
                table: "request_for_quotes",
                newName: "contact_person_email");

            migrationBuilder.RenameColumn(
                name: "CommunicationMethod",
                schema: "public",
                table: "request_for_quotes",
                newName: "communication_method");

            migrationBuilder.RenameColumn(
                name: "AwardedVendorId",
                schema: "public",
                table: "request_for_quotes",
                newName: "awarded_vendor_id");

            migrationBuilder.RenameIndex(
                name: "IX_request_for_quotes_Status",
                schema: "public",
                table: "request_for_quotes",
                newName: "ix_request_for_quotes_status");

            migrationBuilder.RenameIndex(
                name: "IX_request_for_quotes_TenantId_RFQNumber",
                schema: "public",
                table: "request_for_quotes",
                newName: "ix_request_for_quotes_tenant_id_rfq_number");

            migrationBuilder.RenameIndex(
                name: "IX_request_for_quotes_TenantId",
                schema: "public",
                table: "request_for_quotes",
                newName: "ix_request_for_quotes_tenant_id");

            migrationBuilder.RenameIndex(
                name: "IX_request_for_quotes_SubmissionDeadline",
                schema: "public",
                table: "request_for_quotes",
                newName: "ix_request_for_quotes_submission_deadline");

            migrationBuilder.RenameIndex(
                name: "IX_request_for_quotes_RelatedAgreementId",
                schema: "public",
                table: "request_for_quotes",
                newName: "ix_request_for_quotes_related_agreement_id");

            migrationBuilder.RenameIndex(
                name: "IX_request_for_quotes_OriginatingRequisitionId",
                schema: "public",
                table: "request_for_quotes",
                newName: "ix_request_for_quotes_originating_requisition_id");

            migrationBuilder.RenameIndex(
                name: "IX_request_for_quotes_IsDeleted",
                schema: "public",
                table: "request_for_quotes",
                newName: "ix_request_for_quotes_is_deleted");

            migrationBuilder.RenameIndex(
                name: "IX_request_for_quotes_CreatedByUserId",
                schema: "public",
                table: "request_for_quotes",
                newName: "ix_request_for_quotes_created_by_user_id");

            migrationBuilder.RenameIndex(
                name: "IX_request_for_quotes_AwardedVendorId",
                schema: "public",
                table: "request_for_quotes",
                newName: "ix_request_for_quotes_awarded_vendor_id");

            migrationBuilder.RenameColumn(
                name: "Quantity",
                schema: "public",
                table: "request_for_quote_lines",
                newName: "quantity");

            migrationBuilder.RenameColumn(
                name: "Notes",
                schema: "public",
                table: "request_for_quote_lines",
                newName: "notes");

            migrationBuilder.RenameColumn(
                name: "Description",
                schema: "public",
                table: "request_for_quote_lines",
                newName: "description");

            migrationBuilder.RenameColumn(
                name: "Id",
                schema: "public",
                table: "request_for_quote_lines",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "VendorProductId",
                schema: "public",
                table: "request_for_quote_lines",
                newName: "vendor_product_id");

            migrationBuilder.RenameColumn(
                name: "UnitOfMeasure",
                schema: "public",
                table: "request_for_quote_lines",
                newName: "unit_of_measure");

            migrationBuilder.RenameColumn(
                name: "TenantId",
                schema: "public",
                table: "request_for_quote_lines",
                newName: "tenant_id");

            migrationBuilder.RenameColumn(
                name: "TechnicalSpecifications",
                schema: "public",
                table: "request_for_quote_lines",
                newName: "technical_specifications");

            migrationBuilder.RenameColumn(
                name: "SampleRequired",
                schema: "public",
                table: "request_for_quote_lines",
                newName: "sample_required");

            migrationBuilder.RenameColumn(
                name: "RequestForQuoteId",
                schema: "public",
                table: "request_for_quote_lines",
                newName: "request_for_quote_id");

            migrationBuilder.RenameColumn(
                name: "ProductDefinitionId",
                schema: "public",
                table: "request_for_quote_lines",
                newName: "product_definition_id");

            migrationBuilder.RenameColumn(
                name: "PreferredIncoterm",
                schema: "public",
                table: "request_for_quote_lines",
                newName: "preferred_incoterm");

            migrationBuilder.RenameColumn(
                name: "ModifiedAt",
                schema: "public",
                table: "request_for_quote_lines",
                newName: "modified_at");

            migrationBuilder.RenameColumn(
                name: "MinimumOrderQuantity",
                schema: "public",
                table: "request_for_quote_lines",
                newName: "minimum_order_quantity");

            migrationBuilder.RenameColumn(
                name: "LineNumber",
                schema: "public",
                table: "request_for_quote_lines",
                newName: "line_number");

            migrationBuilder.RenameColumn(
                name: "IsSubstituteAllowed",
                schema: "public",
                table: "request_for_quote_lines",
                newName: "is_substitute_allowed");

            migrationBuilder.RenameColumn(
                name: "IsDeleted",
                schema: "public",
                table: "request_for_quote_lines",
                newName: "is_deleted");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                schema: "public",
                table: "request_for_quote_lines",
                newName: "created_at");

            migrationBuilder.RenameColumn(
                name: "AlternateItemProposal",
                schema: "public",
                table: "request_for_quote_lines",
                newName: "alternate_item_proposal");

            migrationBuilder.RenameIndex(
                name: "IX_request_for_quote_lines_VendorProductId",
                schema: "public",
                table: "request_for_quote_lines",
                newName: "ix_request_for_quote_lines_vendor_product_id");

            migrationBuilder.RenameIndex(
                name: "IX_request_for_quote_lines_TenantId",
                schema: "public",
                table: "request_for_quote_lines",
                newName: "ix_request_for_quote_lines_tenant_id");

            migrationBuilder.RenameIndex(
                name: "IX_request_for_quote_lines_RequestForQuoteId_LineNumber",
                schema: "public",
                table: "request_for_quote_lines",
                newName: "ix_request_for_quote_lines_request_for_quote_id_line_number");

            migrationBuilder.RenameIndex(
                name: "IX_request_for_quote_lines_ProductDefinitionId",
                schema: "public",
                table: "request_for_quote_lines",
                newName: "ix_request_for_quote_lines_product_definition_id");

            migrationBuilder.RenameIndex(
                name: "IX_request_for_quote_lines_IsDeleted",
                schema: "public",
                table: "request_for_quote_lines",
                newName: "ix_request_for_quote_lines_is_deleted");

            migrationBuilder.RenameColumn(
                name: "Status",
                table: "PurchaseRequisitions",
                newName: "status");

            migrationBuilder.RenameColumn(
                name: "Notes",
                table: "PurchaseRequisitions",
                newName: "notes");

            migrationBuilder.RenameColumn(
                name: "Justification",
                table: "PurchaseRequisitions",
                newName: "justification");

            migrationBuilder.RenameColumn(
                name: "Department",
                table: "PurchaseRequisitions",
                newName: "department");

            migrationBuilder.RenameColumn(
                name: "Id",
                table: "PurchaseRequisitions",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "TenantId",
                table: "PurchaseRequisitions",
                newName: "tenant_id");

            migrationBuilder.RenameColumn(
                name: "RequisitionNumber",
                table: "PurchaseRequisitions",
                newName: "requisition_number");

            migrationBuilder.RenameColumn(
                name: "RequestorUserId",
                table: "PurchaseRequisitions",
                newName: "requestor_user_id");

            migrationBuilder.RenameColumn(
                name: "RequestorName",
                table: "PurchaseRequisitions",
                newName: "requestor_name");

            migrationBuilder.RenameColumn(
                name: "RequestorEmail",
                table: "PurchaseRequisitions",
                newName: "requestor_email");

            migrationBuilder.RenameColumn(
                name: "RequestDate",
                table: "PurchaseRequisitions",
                newName: "request_date");

            migrationBuilder.RenameColumn(
                name: "ModifiedAt",
                table: "PurchaseRequisitions",
                newName: "modified_at");

            migrationBuilder.RenameColumn(
                name: "DateNeeded",
                table: "PurchaseRequisitions",
                newName: "date_needed");

            migrationBuilder.RenameColumn(
                name: "CurrencyCode",
                table: "PurchaseRequisitions",
                newName: "currency_code");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                table: "PurchaseRequisitions",
                newName: "created_at");

            migrationBuilder.RenameColumn(
                name: "AssociatedPurchaseOrderId",
                table: "PurchaseRequisitions",
                newName: "associated_purchase_order_id");

            migrationBuilder.RenameIndex(
                name: "IX_PurchaseRequisitions_TenantId_RequisitionNumber",
                table: "PurchaseRequisitions",
                newName: "ix_purchase_requisitions_tenant_id_requisition_number");

            migrationBuilder.RenameIndex(
                name: "IX_PurchaseRequisitions_TenantId",
                table: "PurchaseRequisitions",
                newName: "ix_purchase_requisitions_tenant_id");

            migrationBuilder.RenameIndex(
                name: "IX_PurchaseRequisitions_Status",
                table: "PurchaseRequisitions",
                newName: "ix_purchase_requisitions_status");

            migrationBuilder.RenameIndex(
                name: "IX_PurchaseRequisitions_RequestorUserId",
                table: "PurchaseRequisitions",
                newName: "ix_purchase_requisitions_requestor_user_id");

            migrationBuilder.RenameIndex(
                name: "IX_PurchaseRequisitions_RequestDate",
                table: "PurchaseRequisitions",
                newName: "ix_purchase_requisitions_request_date");

            migrationBuilder.RenameIndex(
                name: "IX_PurchaseRequisitions_AssociatedPurchaseOrderId",
                table: "PurchaseRequisitions",
                newName: "ix_purchase_requisitions_associated_purchase_order_id");

            migrationBuilder.RenameColumn(
                name: "Quantity",
                table: "PurchaseRequisitionLines",
                newName: "quantity");

            migrationBuilder.RenameColumn(
                name: "Notes",
                table: "PurchaseRequisitionLines",
                newName: "notes");

            migrationBuilder.RenameColumn(
                name: "Description",
                table: "PurchaseRequisitionLines",
                newName: "description");

            migrationBuilder.RenameColumn(
                name: "Id",
                table: "PurchaseRequisitionLines",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "VendorProductId",
                table: "PurchaseRequisitionLines",
                newName: "vendor_product_id");

            migrationBuilder.RenameColumn(
                name: "UnitOfMeasure",
                table: "PurchaseRequisitionLines",
                newName: "unit_of_measure");

            migrationBuilder.RenameColumn(
                name: "TenantId",
                table: "PurchaseRequisitionLines",
                newName: "tenant_id");

            migrationBuilder.RenameColumn(
                name: "SuggestedVendorId",
                table: "PurchaseRequisitionLines",
                newName: "suggested_vendor_id");

            migrationBuilder.RenameColumn(
                name: "PurchaseRequisitionId",
                table: "PurchaseRequisitionLines",
                newName: "purchase_requisition_id");

            migrationBuilder.RenameColumn(
                name: "ProductDefinitionId",
                table: "PurchaseRequisitionLines",
                newName: "product_definition_id");

            migrationBuilder.RenameColumn(
                name: "ModifiedAt",
                table: "PurchaseRequisitionLines",
                newName: "modified_at");

            migrationBuilder.RenameColumn(
                name: "LineNumber",
                table: "PurchaseRequisitionLines",
                newName: "line_number");

            migrationBuilder.RenameColumn(
                name: "GLAccountCode",
                table: "PurchaseRequisitionLines",
                newName: "gl_account_code");

            migrationBuilder.RenameColumn(
                name: "DateNeeded",
                table: "PurchaseRequisitionLines",
                newName: "date_needed");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                table: "PurchaseRequisitionLines",
                newName: "created_at");

            migrationBuilder.RenameIndex(
                name: "IX_PurchaseRequisitionLines_VendorProductId",
                table: "PurchaseRequisitionLines",
                newName: "ix_purchase_requisition_lines_vendor_product_id");

            migrationBuilder.RenameIndex(
                name: "IX_PurchaseRequisitionLines_TenantId",
                table: "PurchaseRequisitionLines",
                newName: "ix_purchase_requisition_lines_tenant_id");

            migrationBuilder.RenameIndex(
                name: "IX_PurchaseRequisitionLines_SuggestedVendorId",
                table: "PurchaseRequisitionLines",
                newName: "ix_purchase_requisition_lines_suggested_vendor_id");

            migrationBuilder.RenameIndex(
                name: "IX_PurchaseRequisitionLines_PurchaseRequisitionId_LineNumber",
                table: "PurchaseRequisitionLines",
                newName: "ix_purchase_requisition_lines_purchase_requisition_id_line_number");

            migrationBuilder.RenameIndex(
                name: "IX_PurchaseRequisitionLines_ProductDefinitionId",
                table: "PurchaseRequisitionLines",
                newName: "ix_purchase_requisition_lines_product_definition_id");

            migrationBuilder.RenameIndex(
                name: "IX_PurchaseRequisitionLines_GLAccountCode",
                table: "PurchaseRequisitionLines",
                newName: "ix_purchase_requisition_lines_gl_account_code");

            migrationBuilder.RenameColumn(
                name: "Quantity",
                table: "PurchaseOrderLines",
                newName: "quantity");

            migrationBuilder.RenameColumn(
                name: "Notes",
                table: "PurchaseOrderLines",
                newName: "notes");

            migrationBuilder.RenameColumn(
                name: "Id",
                table: "PurchaseOrderLines",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "VendorProductId",
                table: "PurchaseOrderLines",
                newName: "vendor_product_id");

            migrationBuilder.RenameColumn(
                name: "UnitOfMeasureSnapshot",
                table: "PurchaseOrderLines",
                newName: "unit_of_measure_snapshot");

            migrationBuilder.RenameColumn(
                name: "SkuSnapshot",
                table: "PurchaseOrderLines",
                newName: "sku_snapshot");

            migrationBuilder.RenameColumn(
                name: "PurchaseOrderId",
                table: "PurchaseOrderLines",
                newName: "purchase_order_id");

            migrationBuilder.RenameColumn(
                name: "ModifiedAt",
                table: "PurchaseOrderLines",
                newName: "modified_at");

            migrationBuilder.RenameColumn(
                name: "DescriptionSnapshot",
                table: "PurchaseOrderLines",
                newName: "description_snapshot");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                table: "PurchaseOrderLines",
                newName: "created_at");

            migrationBuilder.RenameIndex(
                name: "IX_PurchaseOrderLines_VendorProductId",
                table: "PurchaseOrderLines",
                newName: "ix_purchase_order_lines_vendor_product_id");

            migrationBuilder.RenameIndex(
                name: "IX_PurchaseOrderLines_PurchaseOrderId",
                table: "PurchaseOrderLines",
                newName: "ix_purchase_order_lines_purchase_order_id");

            migrationBuilder.RenameColumn(
                name: "Status",
                table: "purchase_orders",
                newName: "status");

            migrationBuilder.RenameColumn(
                name: "Id",
                table: "purchase_orders",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "VendorId",
                table: "purchase_orders",
                newName: "vendor_id");

            migrationBuilder.RenameColumn(
                name: "RequisitionId",
                table: "purchase_orders",
                newName: "requisition_id");

            migrationBuilder.RenameColumn(
                name: "PaymentTerms",
                table: "purchase_orders",
                newName: "payment_terms");

            migrationBuilder.RenameColumn(
                name: "OrderNumber",
                table: "purchase_orders",
                newName: "order_number");

            migrationBuilder.RenameColumn(
                name: "OrderDate",
                table: "purchase_orders",
                newName: "order_date");

            migrationBuilder.RenameColumn(
                name: "ModifiedAt",
                table: "purchase_orders",
                newName: "modified_at");

            migrationBuilder.RenameColumn(
                name: "DeliveryDate",
                table: "purchase_orders",
                newName: "delivery_date");

            migrationBuilder.RenameColumn(
                name: "CurrencyCode",
                table: "purchase_orders",
                newName: "currency_code");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                table: "purchase_orders",
                newName: "created_at");

            migrationBuilder.RenameColumn(
                name: "ContractId",
                table: "purchase_orders",
                newName: "contract_id");

            migrationBuilder.RenameIndex(
                name: "IX_purchase_orders_Status",
                table: "purchase_orders",
                newName: "ix_purchase_orders_status");

            migrationBuilder.RenameIndex(
                name: "IX_purchase_orders_VendorId",
                table: "purchase_orders",
                newName: "ix_purchase_orders_vendor_id");

            migrationBuilder.RenameIndex(
                name: "IX_purchase_orders_RequisitionId",
                table: "purchase_orders",
                newName: "ix_purchase_orders_requisition_id");

            migrationBuilder.RenameIndex(
                name: "IX_purchase_orders_OrderNumber",
                table: "purchase_orders",
                newName: "ix_purchase_orders_order_number");

            migrationBuilder.RenameIndex(
                name: "IX_purchase_orders_OrderDate",
                table: "purchase_orders",
                newName: "ix_purchase_orders_order_date");

            migrationBuilder.RenameIndex(
                name: "IX_purchase_orders_ContractId",
                table: "purchase_orders",
                newName: "ix_purchase_orders_contract_id");

            migrationBuilder.RenameColumn(
                name: "Status",
                schema: "public",
                table: "projects",
                newName: "status");

            migrationBuilder.RenameColumn(
                name: "Name",
                schema: "public",
                table: "projects",
                newName: "name");

            migrationBuilder.RenameColumn(
                name: "Description",
                schema: "public",
                table: "projects",
                newName: "description");

            migrationBuilder.RenameColumn(
                name: "Id",
                schema: "public",
                table: "projects",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "TenantId",
                schema: "public",
                table: "projects",
                newName: "tenant_id");

            migrationBuilder.RenameColumn(
                name: "StartDate",
                schema: "public",
                table: "projects",
                newName: "start_date");

            migrationBuilder.RenameColumn(
                name: "ProjectCode",
                schema: "public",
                table: "projects",
                newName: "project_code");

            migrationBuilder.RenameColumn(
                name: "ModifiedAt",
                schema: "public",
                table: "projects",
                newName: "modified_at");

            migrationBuilder.RenameColumn(
                name: "IsDeleted",
                schema: "public",
                table: "projects",
                newName: "is_deleted");

            migrationBuilder.RenameColumn(
                name: "EndDate",
                schema: "public",
                table: "projects",
                newName: "end_date");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                schema: "public",
                table: "projects",
                newName: "created_at");

            migrationBuilder.RenameIndex(
                name: "IX_projects_Status",
                schema: "public",
                table: "projects",
                newName: "ix_projects_status");

            migrationBuilder.RenameIndex(
                name: "IX_projects_Name",
                schema: "public",
                table: "projects",
                newName: "ix_projects_name");

            migrationBuilder.RenameIndex(
                name: "IX_projects_TenantId_ProjectCode",
                schema: "public",
                table: "projects",
                newName: "ix_projects_tenant_id_project_code");

            migrationBuilder.RenameIndex(
                name: "IX_projects_TenantId",
                schema: "public",
                table: "projects",
                newName: "ix_projects_tenant_id");

            migrationBuilder.RenameIndex(
                name: "IX_projects_StartDate",
                schema: "public",
                table: "projects",
                newName: "ix_projects_start_date");

            migrationBuilder.RenameIndex(
                name: "IX_projects_IsDeleted",
                schema: "public",
                table: "projects",
                newName: "ix_projects_is_deleted");

            migrationBuilder.RenameIndex(
                name: "IX_projects_EndDate",
                schema: "public",
                table: "projects",
                newName: "ix_projects_end_date");

            migrationBuilder.RenameColumn(
                name: "Name",
                schema: "public",
                table: "products",
                newName: "name");

            migrationBuilder.RenameColumn(
                name: "Description",
                schema: "public",
                table: "products",
                newName: "description");

            migrationBuilder.RenameColumn(
                name: "Id",
                schema: "public",
                table: "products",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "UnitOfMeasure",
                schema: "public",
                table: "products",
                newName: "unit_of_measure");

            migrationBuilder.RenameColumn(
                name: "TenantId",
                schema: "public",
                table: "products",
                newName: "tenant_id");

            migrationBuilder.RenameColumn(
                name: "ProductCode",
                schema: "public",
                table: "products",
                newName: "product_code");

            migrationBuilder.RenameColumn(
                name: "ModifiedAt",
                schema: "public",
                table: "products",
                newName: "modified_at");

            migrationBuilder.RenameColumn(
                name: "IsDeleted",
                schema: "public",
                table: "products",
                newName: "is_deleted");

            migrationBuilder.RenameColumn(
                name: "IsActive",
                schema: "public",
                table: "products",
                newName: "is_active");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                schema: "public",
                table: "products",
                newName: "created_at");

            migrationBuilder.RenameColumn(
                name: "CategoryId",
                schema: "public",
                table: "products",
                newName: "category_id");

            migrationBuilder.RenameColumn(
                name: "Version",
                table: "product_definitions",
                newName: "version");

            migrationBuilder.RenameColumn(
                name: "Upc",
                table: "product_definitions",
                newName: "upc");

            migrationBuilder.RenameColumn(
                name: "Sku",
                table: "product_definitions",
                newName: "sku");

            migrationBuilder.RenameColumn(
                name: "Name",
                table: "product_definitions",
                newName: "name");

            migrationBuilder.RenameColumn(
                name: "Gtin",
                table: "product_definitions",
                newName: "gtin");

            migrationBuilder.RenameColumn(
                name: "Ean",
                table: "product_definitions",
                newName: "ean");

            migrationBuilder.RenameColumn(
                name: "Description",
                table: "product_definitions",
                newName: "description");

            migrationBuilder.RenameColumn(
                name: "Id",
                table: "product_definitions",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "TenantId",
                table: "product_definitions",
                newName: "tenant_id");

            migrationBuilder.RenameColumn(
                name: "ModifiedAt",
                table: "product_definitions",
                newName: "modified_at");

            migrationBuilder.RenameColumn(
                name: "LifecycleState",
                table: "product_definitions",
                newName: "lifecycle_state");

            migrationBuilder.RenameColumn(
                name: "IsDeleted",
                table: "product_definitions",
                newName: "is_deleted");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                table: "product_definitions",
                newName: "created_at");

            migrationBuilder.RenameColumn(
                name: "CategoryId",
                table: "product_definitions",
                newName: "category_id");

            migrationBuilder.RenameColumn(
                name: "AttributesJson",
                table: "product_definitions",
                newName: "attributes_json");

            migrationBuilder.RenameIndex(
                name: "IX_product_definitions_Upc",
                table: "product_definitions",
                newName: "ix_product_definitions_upc");

            migrationBuilder.RenameIndex(
                name: "IX_product_definitions_Sku",
                table: "product_definitions",
                newName: "ix_product_definitions_sku");

            migrationBuilder.RenameIndex(
                name: "IX_product_definitions_Gtin",
                table: "product_definitions",
                newName: "ix_product_definitions_gtin");

            migrationBuilder.RenameIndex(
                name: "IX_product_definitions_Ean",
                table: "product_definitions",
                newName: "ix_product_definitions_ean");

            migrationBuilder.RenameIndex(
                name: "IX_product_definitions_IsDeleted",
                table: "product_definitions",
                newName: "ix_product_definitions_is_deleted");

            migrationBuilder.RenameIndex(
                name: "IX_product_definitions_CategoryId",
                table: "product_definitions",
                newName: "ix_product_definitions_category_id");

            migrationBuilder.RenameColumn(
                name: "Version",
                table: "procurement_workflows",
                newName: "version");

            migrationBuilder.RenameColumn(
                name: "Name",
                table: "procurement_workflows",
                newName: "name");

            migrationBuilder.RenameColumn(
                name: "Description",
                table: "procurement_workflows",
                newName: "description");

            migrationBuilder.RenameColumn(
                name: "Id",
                table: "procurement_workflows",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "WorkflowType",
                table: "procurement_workflows",
                newName: "workflow_type");

            migrationBuilder.RenameColumn(
                name: "WorkflowName",
                table: "procurement_workflows",
                newName: "workflow_name");

            migrationBuilder.RenameColumn(
                name: "TenantId",
                table: "procurement_workflows",
                newName: "tenant_id");

            migrationBuilder.RenameColumn(
                name: "SubjectDocumentType",
                table: "procurement_workflows",
                newName: "subject_document_type");

            migrationBuilder.RenameColumn(
                name: "SubjectDocumentId",
                table: "procurement_workflows",
                newName: "subject_document_id");

            migrationBuilder.RenameColumn(
                name: "StartedDate",
                table: "procurement_workflows",
                newName: "started_date");

            migrationBuilder.RenameColumn(
                name: "ModifiedAt",
                table: "procurement_workflows",
                newName: "modified_at");

            migrationBuilder.RenameColumn(
                name: "IsActive",
                table: "procurement_workflows",
                newName: "is_active");

            migrationBuilder.RenameColumn(
                name: "InitiatedByUserId",
                table: "procurement_workflows",
                newName: "initiated_by_user_id");

            migrationBuilder.RenameColumn(
                name: "InitiatedByName",
                table: "procurement_workflows",
                newName: "initiated_by_name");

            migrationBuilder.RenameColumn(
                name: "CurrentStatus",
                table: "procurement_workflows",
                newName: "current_status");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                table: "procurement_workflows",
                newName: "created_at");

            migrationBuilder.RenameColumn(
                name: "CompletedDate",
                table: "procurement_workflows",
                newName: "completed_date");

            migrationBuilder.RenameIndex(
                name: "IX_procurement_workflows_Name",
                table: "procurement_workflows",
                newName: "ix_procurement_workflows_name");

            migrationBuilder.RenameIndex(
                name: "IX_procurement_workflows_WorkflowType",
                table: "procurement_workflows",
                newName: "ix_procurement_workflows_workflow_type");

            migrationBuilder.RenameIndex(
                name: "IX_procurement_workflows_TenantId_WorkflowType_Name",
                table: "procurement_workflows",
                newName: "ix_procurement_workflows_tenant_id_workflow_type_name");

            migrationBuilder.RenameIndex(
                name: "IX_procurement_workflows_TenantId",
                table: "procurement_workflows",
                newName: "ix_procurement_workflows_tenant_id");

            migrationBuilder.RenameIndex(
                name: "IX_procurement_workflows_IsActive",
                table: "procurement_workflows",
                newName: "ix_procurement_workflows_is_active");

            migrationBuilder.RenameColumn(
                name: "Status",
                table: "procurement_workflow_steps",
                newName: "status");

            migrationBuilder.RenameColumn(
                name: "Comments",
                table: "procurement_workflow_steps",
                newName: "comments");

            migrationBuilder.RenameColumn(
                name: "Id",
                table: "procurement_workflow_steps",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "WorkflowId",
                table: "procurement_workflow_steps",
                newName: "workflow_id");

            migrationBuilder.RenameColumn(
                name: "StepOrder",
                table: "procurement_workflow_steps",
                newName: "step_order");

            migrationBuilder.RenameColumn(
                name: "StepName",
                table: "procurement_workflow_steps",
                newName: "step_name");

            migrationBuilder.RenameColumn(
                name: "SlaDuration",
                table: "procurement_workflow_steps",
                newName: "sla_duration");

            migrationBuilder.RenameColumn(
                name: "SequenceOrder",
                table: "procurement_workflow_steps",
                newName: "sequence_order");

            migrationBuilder.RenameColumn(
                name: "ProcurementWorkflowId",
                table: "procurement_workflow_steps",
                newName: "procurement_workflow_id");

            migrationBuilder.RenameColumn(
                name: "ModifiedAt",
                table: "procurement_workflow_steps",
                newName: "modified_at");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                table: "procurement_workflow_steps",
                newName: "created_at");

            migrationBuilder.RenameColumn(
                name: "ConditionExpression",
                table: "procurement_workflow_steps",
                newName: "condition_expression");

            migrationBuilder.RenameColumn(
                name: "AssigneeUserId",
                table: "procurement_workflow_steps",
                newName: "assignee_user_id");

            migrationBuilder.RenameColumn(
                name: "AssigneeName",
                table: "procurement_workflow_steps",
                newName: "assignee_name");

            migrationBuilder.RenameColumn(
                name: "AssignedDate",
                table: "procurement_workflow_steps",
                newName: "assigned_date");

            migrationBuilder.RenameColumn(
                name: "ApproverUserId",
                table: "procurement_workflow_steps",
                newName: "approver_user_id");

            migrationBuilder.RenameColumn(
                name: "ApproverRoleId",
                table: "procurement_workflow_steps",
                newName: "approver_role_id");

            migrationBuilder.RenameColumn(
                name: "ActionDate",
                table: "procurement_workflow_steps",
                newName: "action_date");

            migrationBuilder.RenameIndex(
                name: "IX_procurement_workflow_steps_WorkflowId",
                table: "procurement_workflow_steps",
                newName: "ix_procurement_workflow_steps_workflow_id");

            migrationBuilder.RenameIndex(
                name: "IX_procurement_workflow_steps_ProcurementWorkflowId_StepOrder",
                table: "procurement_workflow_steps",
                newName: "ix_procurement_workflow_steps_procurement_workflow_id_step_ord");

            migrationBuilder.RenameIndex(
                name: "IX_procurement_workflow_steps_ProcurementWorkflowId",
                table: "procurement_workflow_steps",
                newName: "ix_procurement_workflow_steps_procurement_workflow_id");

            migrationBuilder.RenameIndex(
                name: "IX_procurement_workflow_steps_ApproverUserId",
                table: "procurement_workflow_steps",
                newName: "ix_procurement_workflow_steps_approver_user_id");

            migrationBuilder.RenameIndex(
                name: "IX_procurement_workflow_steps_ApproverRoleId",
                table: "procurement_workflow_steps",
                newName: "ix_procurement_workflow_steps_approver_role_id");

            migrationBuilder.RenameColumn(
                name: "Status",
                table: "payment_transactions",
                newName: "status");

            migrationBuilder.RenameColumn(
                name: "Notes",
                table: "payment_transactions",
                newName: "notes");

            migrationBuilder.RenameColumn(
                name: "Amount",
                table: "payment_transactions",
                newName: "amount");

            migrationBuilder.RenameColumn(
                name: "Id",
                table: "payment_transactions",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "VendorId",
                table: "payment_transactions",
                newName: "vendor_id");

            migrationBuilder.RenameColumn(
                name: "TransactionReference",
                table: "payment_transactions",
                newName: "transaction_reference");

            migrationBuilder.RenameColumn(
                name: "TenantId",
                table: "payment_transactions",
                newName: "tenant_id");

            migrationBuilder.RenameColumn(
                name: "PaymentMethod",
                table: "payment_transactions",
                newName: "payment_method");

            migrationBuilder.RenameColumn(
                name: "PaymentDate",
                table: "payment_transactions",
                newName: "payment_date");

            migrationBuilder.RenameColumn(
                name: "ModifiedAt",
                table: "payment_transactions",
                newName: "modified_at");

            migrationBuilder.RenameColumn(
                name: "InvoiceId",
                table: "payment_transactions",
                newName: "invoice_id");

            migrationBuilder.RenameColumn(
                name: "CurrencyCode",
                table: "payment_transactions",
                newName: "currency_code");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                table: "payment_transactions",
                newName: "created_at");

            migrationBuilder.RenameColumn(
                name: "BankReference",
                table: "payment_transactions",
                newName: "bank_reference");

            migrationBuilder.RenameIndex(
                name: "IX_payment_transactions_Status",
                table: "payment_transactions",
                newName: "ix_payment_transactions_status");

            migrationBuilder.RenameIndex(
                name: "IX_payment_transactions_VendorId",
                table: "payment_transactions",
                newName: "ix_payment_transactions_vendor_id");

            migrationBuilder.RenameIndex(
                name: "IX_payment_transactions_TenantId",
                table: "payment_transactions",
                newName: "ix_payment_transactions_tenant_id");

            migrationBuilder.RenameIndex(
                name: "IX_payment_transactions_PaymentDate",
                table: "payment_transactions",
                newName: "ix_payment_transactions_payment_date");

            migrationBuilder.RenameIndex(
                name: "IX_payment_transactions_InvoiceId",
                table: "payment_transactions",
                newName: "ix_payment_transactions_invoice_id");

            migrationBuilder.RenameColumn(
                name: "Subtotal",
                table: "invoices",
                newName: "subtotal");

            migrationBuilder.RenameColumn(
                name: "Status",
                table: "invoices",
                newName: "status");

            migrationBuilder.RenameColumn(
                name: "Notes",
                table: "invoices",
                newName: "notes");

            migrationBuilder.RenameColumn(
                name: "Id",
                table: "invoices",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "VendorId",
                table: "invoices",
                newName: "vendor_id");

            migrationBuilder.RenameColumn(
                name: "TotalAmount",
                table: "invoices",
                newName: "total_amount");

            migrationBuilder.RenameColumn(
                name: "TenantId",
                table: "invoices",
                newName: "tenant_id");

            migrationBuilder.RenameColumn(
                name: "TaxAmount",
                table: "invoices",
                newName: "tax_amount");

            migrationBuilder.RenameColumn(
                name: "SubTotal",
                table: "invoices",
                newName: "sub_total");

            migrationBuilder.RenameColumn(
                name: "PurchaseOrderId",
                table: "invoices",
                newName: "purchase_order_id");

            migrationBuilder.RenameColumn(
                name: "PaymentTerms",
                table: "invoices",
                newName: "payment_terms");

            migrationBuilder.RenameColumn(
                name: "PaymentDate",
                table: "invoices",
                newName: "payment_date");

            migrationBuilder.RenameColumn(
                name: "ModifiedAt",
                table: "invoices",
                newName: "modified_at");

            migrationBuilder.RenameColumn(
                name: "InvoiceNumber",
                table: "invoices",
                newName: "invoice_number");

            migrationBuilder.RenameColumn(
                name: "InvoiceDate",
                table: "invoices",
                newName: "invoice_date");

            migrationBuilder.RenameColumn(
                name: "DueDate",
                table: "invoices",
                newName: "due_date");

            migrationBuilder.RenameColumn(
                name: "CustomerId",
                table: "invoices",
                newName: "customer_id");

            migrationBuilder.RenameColumn(
                name: "CurrencyCode",
                table: "invoices",
                newName: "currency_code");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                table: "invoices",
                newName: "created_at");

            migrationBuilder.RenameColumn(
                name: "AmountPaid",
                table: "invoices",
                newName: "amount_paid");

            migrationBuilder.RenameIndex(
                name: "IX_invoices_Status",
                table: "invoices",
                newName: "ix_invoices_status");

            migrationBuilder.RenameIndex(
                name: "IX_invoices_VendorId_InvoiceNumber",
                table: "invoices",
                newName: "ix_invoices_vendor_id_invoice_number");

            migrationBuilder.RenameIndex(
                name: "IX_invoices_VendorId",
                table: "invoices",
                newName: "ix_invoices_vendor_id");

            migrationBuilder.RenameIndex(
                name: "IX_invoices_TenantId",
                table: "invoices",
                newName: "ix_invoices_tenant_id");

            migrationBuilder.RenameIndex(
                name: "IX_invoices_PurchaseOrderId",
                table: "invoices",
                newName: "ix_invoices_purchase_order_id");

            migrationBuilder.RenameIndex(
                name: "IX_invoices_InvoiceDate",
                table: "invoices",
                newName: "ix_invoices_invoice_date");

            migrationBuilder.RenameIndex(
                name: "IX_invoices_DueDate",
                table: "invoices",
                newName: "ix_invoices_due_date");

            migrationBuilder.RenameIndex(
                name: "IX_invoices_CustomerId",
                table: "invoices",
                newName: "ix_invoices_customer_id");

            migrationBuilder.RenameColumn(
                name: "Quantity",
                table: "invoice_lines",
                newName: "quantity");

            migrationBuilder.RenameColumn(
                name: "Notes",
                table: "invoice_lines",
                newName: "notes");

            migrationBuilder.RenameColumn(
                name: "Id",
                table: "invoice_lines",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "Description",
                table: "invoice_lines",
                newName: "description");

            migrationBuilder.RenameColumn(
                name: "UnitPrice",
                table: "invoice_lines",
                newName: "unit_price");

            migrationBuilder.RenameColumn(
                name: "UnitOfMeasure",
                table: "invoice_lines",
                newName: "unit_of_measure");

            migrationBuilder.RenameColumn(
                name: "TaxRate",
                table: "invoice_lines",
                newName: "tax_rate");

            migrationBuilder.RenameColumn(
                name: "TaxAmount",
                table: "invoice_lines",
                newName: "tax_amount");

            migrationBuilder.RenameColumn(
                name: "PurchaseOrderLineId",
                table: "invoice_lines",
                newName: "purchase_order_line_id");

            migrationBuilder.RenameColumn(
                name: "ProductId",
                table: "invoice_lines",
                newName: "product_id");

            migrationBuilder.RenameColumn(
                name: "ModifiedAt",
                table: "invoice_lines",
                newName: "modified_at");

            migrationBuilder.RenameColumn(
                name: "LineTotal",
                table: "invoice_lines",
                newName: "line_total");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                table: "invoice_lines",
                newName: "created_at");

            migrationBuilder.RenameColumn(
                name: "LineNumber",
                table: "invoice_lines",
                newName: "line_number");

            migrationBuilder.RenameColumn(
                name: "InvoiceId",
                table: "invoice_lines",
                newName: "invoice_id");

            migrationBuilder.RenameIndex(
                name: "IX_invoice_lines_PurchaseOrderLineId",
                table: "invoice_lines",
                newName: "ix_invoice_lines_purchase_order_line_id");

            migrationBuilder.RenameIndex(
                name: "IX_invoice_lines_ProductId",
                table: "invoice_lines",
                newName: "ix_invoice_lines_product_id");

            migrationBuilder.RenameColumn(
                name: "Status",
                table: "goods_receipt_notes",
                newName: "status");

            migrationBuilder.RenameColumn(
                name: "Notes",
                table: "goods_receipt_notes",
                newName: "notes");

            migrationBuilder.RenameColumn(
                name: "Id",
                table: "goods_receipt_notes",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "VendorId",
                table: "goods_receipt_notes",
                newName: "vendor_id");

            migrationBuilder.RenameColumn(
                name: "TenantId",
                table: "goods_receipt_notes",
                newName: "tenant_id");

            migrationBuilder.RenameColumn(
                name: "ReceivingLocation",
                table: "goods_receipt_notes",
                newName: "receiving_location");

            migrationBuilder.RenameColumn(
                name: "ReceivedByUserId",
                table: "goods_receipt_notes",
                newName: "received_by_user_id");

            migrationBuilder.RenameColumn(
                name: "ReceivedByName",
                table: "goods_receipt_notes",
                newName: "received_by_name");

            migrationBuilder.RenameColumn(
                name: "ReceiptDate",
                table: "goods_receipt_notes",
                newName: "receipt_date");

            migrationBuilder.RenameColumn(
                name: "PurchaseOrderId",
                table: "goods_receipt_notes",
                newName: "purchase_order_id");

            migrationBuilder.RenameColumn(
                name: "ModifiedAt",
                table: "goods_receipt_notes",
                newName: "modified_at");

            migrationBuilder.RenameColumn(
                name: "InspectionDate",
                table: "goods_receipt_notes",
                newName: "inspection_date");

            migrationBuilder.RenameColumn(
                name: "GrnNumber",
                table: "goods_receipt_notes",
                newName: "grn_number");

            migrationBuilder.RenameColumn(
                name: "GoodsReceiptNoteNumber",
                table: "goods_receipt_notes",
                newName: "goods_receipt_note_number");

            migrationBuilder.RenameColumn(
                name: "DeliveryNoteId",
                table: "goods_receipt_notes",
                newName: "delivery_note_id");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                table: "goods_receipt_notes",
                newName: "created_at");

            migrationBuilder.RenameIndex(
                name: "IX_goods_receipt_notes_Status",
                table: "goods_receipt_notes",
                newName: "ix_goods_receipt_notes_status");

            migrationBuilder.RenameIndex(
                name: "IX_goods_receipt_notes_VendorId",
                table: "goods_receipt_notes",
                newName: "ix_goods_receipt_notes_vendor_id");

            migrationBuilder.RenameIndex(
                name: "IX_goods_receipt_notes_TenantId",
                table: "goods_receipt_notes",
                newName: "ix_goods_receipt_notes_tenant_id");

            migrationBuilder.RenameIndex(
                name: "IX_goods_receipt_notes_ReceivedByUserId",
                table: "goods_receipt_notes",
                newName: "ix_goods_receipt_notes_received_by_user_id");

            migrationBuilder.RenameIndex(
                name: "IX_goods_receipt_notes_ReceiptDate",
                table: "goods_receipt_notes",
                newName: "ix_goods_receipt_notes_receipt_date");

            migrationBuilder.RenameIndex(
                name: "IX_goods_receipt_notes_PurchaseOrderId",
                table: "goods_receipt_notes",
                newName: "ix_goods_receipt_notes_purchase_order_id");

            migrationBuilder.RenameIndex(
                name: "IX_goods_receipt_notes_GoodsReceiptNoteNumber",
                table: "goods_receipt_notes",
                newName: "ix_goods_receipt_notes_goods_receipt_note_number");

            migrationBuilder.RenameIndex(
                name: "IX_goods_receipt_notes_DeliveryNoteId",
                table: "goods_receipt_notes",
                newName: "ix_goods_receipt_notes_delivery_note_id");

            migrationBuilder.RenameColumn(
                name: "Notes",
                table: "goods_receipt_note_lines",
                newName: "notes");

            migrationBuilder.RenameColumn(
                name: "Id",
                table: "goods_receipt_note_lines",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "UnitOfMeasure",
                table: "goods_receipt_note_lines",
                newName: "unit_of_measure");

            migrationBuilder.RenameColumn(
                name: "RejectionReason",
                table: "goods_receipt_note_lines",
                newName: "rejection_reason");

            migrationBuilder.RenameColumn(
                name: "QuantityRejected",
                table: "goods_receipt_note_lines",
                newName: "quantity_rejected");

            migrationBuilder.RenameColumn(
                name: "QuantityReceived",
                table: "goods_receipt_note_lines",
                newName: "quantity_received");

            migrationBuilder.RenameColumn(
                name: "QuantityAccepted",
                table: "goods_receipt_note_lines",
                newName: "quantity_accepted");

            migrationBuilder.RenameColumn(
                name: "QualityControlStatus",
                table: "goods_receipt_note_lines",
                newName: "quality_control_status");

            migrationBuilder.RenameColumn(
                name: "PutAwayLocation",
                table: "goods_receipt_note_lines",
                newName: "put_away_location");

            migrationBuilder.RenameColumn(
                name: "PurchaseOrderLineId",
                table: "goods_receipt_note_lines",
                newName: "purchase_order_line_id");

            migrationBuilder.RenameColumn(
                name: "ProductSkuSnapshot",
                table: "goods_receipt_note_lines",
                newName: "product_sku_snapshot");

            migrationBuilder.RenameColumn(
                name: "ProductId",
                table: "goods_receipt_note_lines",
                newName: "product_id");

            migrationBuilder.RenameColumn(
                name: "ProductDescriptionSnapshot",
                table: "goods_receipt_note_lines",
                newName: "product_description_snapshot");

            migrationBuilder.RenameColumn(
                name: "ModifiedAt",
                table: "goods_receipt_note_lines",
                newName: "modified_at");

            migrationBuilder.RenameColumn(
                name: "LotNumber",
                table: "goods_receipt_note_lines",
                newName: "lot_number");

            migrationBuilder.RenameColumn(
                name: "InspectionCompleted",
                table: "goods_receipt_note_lines",
                newName: "inspection_completed");

            migrationBuilder.RenameColumn(
                name: "ExpiryDate",
                table: "goods_receipt_note_lines",
                newName: "expiry_date");

            migrationBuilder.RenameColumn(
                name: "DeliveryNoteLineLineNumber",
                table: "goods_receipt_note_lines",
                newName: "delivery_note_line_line_number");

            migrationBuilder.RenameColumn(
                name: "DeliveryNoteLineId",
                table: "goods_receipt_note_lines",
                newName: "delivery_note_line_id");

            migrationBuilder.RenameColumn(
                name: "DeliveryNoteLineDeliveryNoteId",
                table: "goods_receipt_note_lines",
                newName: "delivery_note_line_delivery_note_id");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                table: "goods_receipt_note_lines",
                newName: "created_at");

            migrationBuilder.RenameColumn(
                name: "BatchNumber",
                table: "goods_receipt_note_lines",
                newName: "batch_number");

            migrationBuilder.RenameColumn(
                name: "LineNumber",
                table: "goods_receipt_note_lines",
                newName: "line_number");

            migrationBuilder.RenameColumn(
                name: "GoodsReceiptNoteId",
                table: "goods_receipt_note_lines",
                newName: "goods_receipt_note_id");

            migrationBuilder.RenameIndex(
                name: "IX_goods_receipt_note_lines_QualityControlStatus",
                table: "goods_receipt_note_lines",
                newName: "ix_goods_receipt_note_lines_quality_control_status");

            migrationBuilder.RenameIndex(
                name: "IX_goods_receipt_note_lines_PurchaseOrderLineId",
                table: "goods_receipt_note_lines",
                newName: "ix_goods_receipt_note_lines_purchase_order_line_id");

            migrationBuilder.RenameIndex(
                name: "IX_goods_receipt_note_lines_ProductId",
                table: "goods_receipt_note_lines",
                newName: "ix_goods_receipt_note_lines_product_id");

            migrationBuilder.RenameIndex(
                name: "IX_goods_receipt_note_lines_ExpiryDate",
                table: "goods_receipt_note_lines",
                newName: "ix_goods_receipt_note_lines_expiry_date");

            migrationBuilder.RenameIndex(
                name: "IX_goods_receipt_note_lines_DeliveryNoteLineDeliveryNoteId_Del~",
                table: "goods_receipt_note_lines",
                newName: "ix_goods_receipt_note_lines_delivery_note_line_delivery_note_i");

            migrationBuilder.RenameColumn(
                name: "Name",
                schema: "public",
                table: "departments",
                newName: "name");

            migrationBuilder.RenameColumn(
                name: "Description",
                schema: "public",
                table: "departments",
                newName: "description");

            migrationBuilder.RenameColumn(
                name: "Code",
                schema: "public",
                table: "departments",
                newName: "code");

            migrationBuilder.RenameColumn(
                name: "Id",
                schema: "public",
                table: "departments",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "TenantId",
                schema: "public",
                table: "departments",
                newName: "tenant_id");

            migrationBuilder.RenameColumn(
                name: "ModifiedAt",
                schema: "public",
                table: "departments",
                newName: "modified_at");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                schema: "public",
                table: "departments",
                newName: "created_at");

            migrationBuilder.RenameIndex(
                name: "IX_departments_Name",
                schema: "public",
                table: "departments",
                newName: "ix_departments_name");

            migrationBuilder.RenameIndex(
                name: "IX_departments_TenantId_Code",
                schema: "public",
                table: "departments",
                newName: "ix_departments_tenant_id_code");

            migrationBuilder.RenameIndex(
                name: "IX_departments_TenantId",
                schema: "public",
                table: "departments",
                newName: "ix_departments_tenant_id");

            migrationBuilder.RenameColumn(
                name: "Status",
                table: "delivery_notes",
                newName: "status");

            migrationBuilder.RenameColumn(
                name: "Notes",
                table: "delivery_notes",
                newName: "notes");

            migrationBuilder.RenameColumn(
                name: "Id",
                table: "delivery_notes",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "VendorId",
                table: "delivery_notes",
                newName: "vendor_id");

            migrationBuilder.RenameColumn(
                name: "TenantId",
                table: "delivery_notes",
                newName: "tenant_id");

            migrationBuilder.RenameColumn(
                name: "ShipmentDate",
                table: "delivery_notes",
                newName: "shipment_date");

            migrationBuilder.RenameColumn(
                name: "SalesOrderId",
                table: "delivery_notes",
                newName: "sales_order_id");

            migrationBuilder.RenameColumn(
                name: "ReceivedBy",
                table: "delivery_notes",
                newName: "received_by");

            migrationBuilder.RenameColumn(
                name: "PurchaseOrderId",
                table: "delivery_notes",
                newName: "purchase_order_id");

            migrationBuilder.RenameColumn(
                name: "ModifiedAt",
                table: "delivery_notes",
                newName: "modified_at");

            migrationBuilder.RenameColumn(
                name: "DeliveryNoteNumber",
                table: "delivery_notes",
                newName: "delivery_note_number");

            migrationBuilder.RenameColumn(
                name: "DeliveryDate",
                table: "delivery_notes",
                newName: "delivery_date");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                table: "delivery_notes",
                newName: "created_at");

            migrationBuilder.RenameIndex(
                name: "IX_delivery_notes_Status",
                table: "delivery_notes",
                newName: "ix_delivery_notes_status");

            migrationBuilder.RenameIndex(
                name: "IX_delivery_notes_VendorId",
                table: "delivery_notes",
                newName: "ix_delivery_notes_vendor_id");

            migrationBuilder.RenameIndex(
                name: "IX_delivery_notes_TenantId",
                table: "delivery_notes",
                newName: "ix_delivery_notes_tenant_id");

            migrationBuilder.RenameIndex(
                name: "IX_delivery_notes_SalesOrderId",
                table: "delivery_notes",
                newName: "ix_delivery_notes_sales_order_id");

            migrationBuilder.RenameIndex(
                name: "IX_delivery_notes_PurchaseOrderId",
                table: "delivery_notes",
                newName: "ix_delivery_notes_purchase_order_id");

            migrationBuilder.RenameIndex(
                name: "IX_delivery_notes_DeliveryNoteNumber",
                table: "delivery_notes",
                newName: "ix_delivery_notes_delivery_note_number");

            migrationBuilder.RenameIndex(
                name: "IX_delivery_notes_DeliveryDate",
                table: "delivery_notes",
                newName: "ix_delivery_notes_delivery_date");

            migrationBuilder.RenameColumn(
                name: "Notes",
                table: "delivery_note_lines",
                newName: "notes");

            migrationBuilder.RenameColumn(
                name: "Id",
                table: "delivery_note_lines",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "Description",
                table: "delivery_note_lines",
                newName: "description");

            migrationBuilder.RenameColumn(
                name: "UnitOfMeasure",
                table: "delivery_note_lines",
                newName: "unit_of_measure");

            migrationBuilder.RenameColumn(
                name: "SerialNumber",
                table: "delivery_note_lines",
                newName: "serial_number");

            migrationBuilder.RenameColumn(
                name: "SalesOrderLineNumber",
                table: "delivery_note_lines",
                newName: "sales_order_line_number");

            migrationBuilder.RenameColumn(
                name: "SalesOrderId",
                table: "delivery_note_lines",
                newName: "sales_order_id");

            migrationBuilder.RenameColumn(
                name: "QuantityShipped",
                table: "delivery_note_lines",
                newName: "quantity_shipped");

            migrationBuilder.RenameColumn(
                name: "PurchaseOrderLineId",
                table: "delivery_note_lines",
                newName: "purchase_order_line_id");

            migrationBuilder.RenameColumn(
                name: "ProductSkuSnapshot",
                table: "delivery_note_lines",
                newName: "product_sku_snapshot");

            migrationBuilder.RenameColumn(
                name: "ProductId",
                table: "delivery_note_lines",
                newName: "product_id");

            migrationBuilder.RenameColumn(
                name: "ModifiedAt",
                table: "delivery_note_lines",
                newName: "modified_at");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                table: "delivery_note_lines",
                newName: "created_at");

            migrationBuilder.RenameColumn(
                name: "BatchNumber",
                table: "delivery_note_lines",
                newName: "batch_number");

            migrationBuilder.RenameColumn(
                name: "LineNumber",
                table: "delivery_note_lines",
                newName: "line_number");

            migrationBuilder.RenameColumn(
                name: "DeliveryNoteId",
                table: "delivery_note_lines",
                newName: "delivery_note_id");

            migrationBuilder.RenameIndex(
                name: "IX_delivery_note_lines_SalesOrderId_SalesOrderLineNumber",
                table: "delivery_note_lines",
                newName: "ix_delivery_note_lines_sales_order_id_sales_order_line_number");

            migrationBuilder.RenameIndex(
                name: "IX_delivery_note_lines_PurchaseOrderLineId",
                table: "delivery_note_lines",
                newName: "ix_delivery_note_lines_purchase_order_line_id");

            migrationBuilder.RenameIndex(
                name: "IX_delivery_note_lines_ProductId",
                table: "delivery_note_lines",
                newName: "ix_delivery_note_lines_product_id");

            migrationBuilder.RenameColumn(
                name: "Id",
                schema: "public",
                table: "customers",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "TenantId",
                schema: "public",
                table: "customers",
                newName: "tenant_id");

            migrationBuilder.RenameColumn(
                name: "ModifiedAt",
                schema: "public",
                table: "customers",
                newName: "modified_at");

            migrationBuilder.RenameColumn(
                name: "IsDeleted",
                schema: "public",
                table: "customers",
                newName: "is_deleted");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                schema: "public",
                table: "customers",
                newName: "created_at");

            migrationBuilder.RenameColumn(
                name: "AssignedSalesRepId",
                schema: "public",
                table: "customers",
                newName: "assigned_sales_rep_id");

            migrationBuilder.RenameIndex(
                name: "IX_customers_tax_identifier",
                schema: "public",
                table: "customers",
                newName: "ix_customers_tax_identifier");

            migrationBuilder.RenameIndex(
                name: "IX_customers_name",
                schema: "public",
                table: "customers",
                newName: "ix_customers_name");

            migrationBuilder.RenameIndex(
                name: "IX_customers_is_active",
                schema: "public",
                table: "customers",
                newName: "ix_customers_is_active");

            migrationBuilder.RenameIndex(
                name: "IX_customers_customer_type",
                schema: "public",
                table: "customers",
                newName: "ix_customers_customer_type");

            migrationBuilder.RenameIndex(
                name: "IX_customers_TenantId_customer_code",
                schema: "public",
                table: "customers",
                newName: "ix_customers_tenant_id_customer_code");

            migrationBuilder.RenameIndex(
                name: "IX_customers_TenantId",
                schema: "public",
                table: "customers",
                newName: "ix_customers_tenant_id");

            migrationBuilder.RenameIndex(
                name: "IX_customers_IsDeleted",
                schema: "public",
                table: "customers",
                newName: "ix_customers_is_deleted");

            migrationBuilder.RenameIndex(
                name: "IX_customers_AssignedSalesRepId",
                schema: "public",
                table: "customers",
                newName: "ix_customers_assigned_sales_rep_id");

            migrationBuilder.RenameColumn(
                name: "Version",
                table: "contracts",
                newName: "version");

            migrationBuilder.RenameColumn(
                name: "Title",
                table: "contracts",
                newName: "title");

            migrationBuilder.RenameColumn(
                name: "Status",
                table: "contracts",
                newName: "status");

            migrationBuilder.RenameColumn(
                name: "Id",
                table: "contracts",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "VendorPerformanceScoreSnapshot",
                table: "contracts",
                newName: "vendor_performance_score_snapshot");

            migrationBuilder.RenameColumn(
                name: "VendorId",
                table: "contracts",
                newName: "vendor_id");

            migrationBuilder.RenameColumn(
                name: "TermsAndConditions",
                table: "contracts",
                newName: "terms_and_conditions");

            migrationBuilder.RenameColumn(
                name: "TerminationPenaltyTerms",
                table: "contracts",
                newName: "termination_penalty_terms");

            migrationBuilder.RenameColumn(
                name: "TenantId",
                table: "contracts",
                newName: "tenant_id");

            migrationBuilder.RenameColumn(
                name: "StartDate",
                table: "contracts",
                newName: "start_date");

            migrationBuilder.RenameColumn(
                name: "SlaDetailsJson",
                table: "contracts",
                newName: "sla_details_json");

            migrationBuilder.RenameColumn(
                name: "RenewalTerms",
                table: "contracts",
                newName: "renewal_terms");

            migrationBuilder.RenameColumn(
                name: "PaymentTerms",
                table: "contracts",
                newName: "payment_terms");

            migrationBuilder.RenameColumn(
                name: "ModifiedAt",
                table: "contracts",
                newName: "modified_at");

            migrationBuilder.RenameColumn(
                name: "MilestonesJson",
                table: "contracts",
                newName: "milestones_json");

            migrationBuilder.RenameColumn(
                name: "IsDeleted",
                table: "contracts",
                newName: "is_deleted");

            migrationBuilder.RenameColumn(
                name: "IsAutoRenew",
                table: "contracts",
                newName: "is_auto_renew");

            migrationBuilder.RenameColumn(
                name: "EndDate",
                table: "contracts",
                newName: "end_date");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                table: "contracts",
                newName: "created_at");

            migrationBuilder.RenameColumn(
                name: "ContractType",
                table: "contracts",
                newName: "contract_type");

            migrationBuilder.RenameColumn(
                name: "ContractNumber",
                table: "contracts",
                newName: "contract_number");

            migrationBuilder.RenameColumn(
                name: "ComplianceDocumentLinksJson",
                table: "contracts",
                newName: "compliance_document_links_json");

            migrationBuilder.RenameIndex(
                name: "IX_contracts_Status",
                table: "contracts",
                newName: "ix_contracts_status");

            migrationBuilder.RenameIndex(
                name: "IX_contracts_VendorId",
                table: "contracts",
                newName: "ix_contracts_vendor_id");

            migrationBuilder.RenameIndex(
                name: "IX_contracts_TenantId",
                table: "contracts",
                newName: "ix_contracts_tenant_id");

            migrationBuilder.RenameIndex(
                name: "IX_contracts_StartDate",
                table: "contracts",
                newName: "ix_contracts_start_date");

            migrationBuilder.RenameIndex(
                name: "IX_contracts_IsDeleted",
                table: "contracts",
                newName: "ix_contracts_is_deleted");

            migrationBuilder.RenameIndex(
                name: "IX_contracts_EndDate",
                table: "contracts",
                newName: "ix_contracts_end_date");

            migrationBuilder.RenameIndex(
                name: "IX_contracts_ContractNumber",
                table: "contracts",
                newName: "ix_contracts_contract_number");

            migrationBuilder.RenameColumn(
                name: "Name",
                schema: "public",
                table: "categories",
                newName: "name");

            migrationBuilder.RenameColumn(
                name: "Description",
                schema: "public",
                table: "categories",
                newName: "description");

            migrationBuilder.RenameColumn(
                name: "Code",
                schema: "public",
                table: "categories",
                newName: "code");

            migrationBuilder.RenameColumn(
                name: "Id",
                schema: "public",
                table: "categories",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "UnspscCode",
                schema: "public",
                table: "categories",
                newName: "unspsc_code");

            migrationBuilder.RenameColumn(
                name: "TenantId",
                schema: "public",
                table: "categories",
                newName: "tenant_id");

            migrationBuilder.RenameColumn(
                name: "ParentCategoryId",
                schema: "public",
                table: "categories",
                newName: "parent_category_id");

            migrationBuilder.RenameColumn(
                name: "ModifiedAt",
                schema: "public",
                table: "categories",
                newName: "modified_at");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                schema: "public",
                table: "categories",
                newName: "created_at");

            migrationBuilder.RenameIndex(
                name: "IX_categories_TenantId",
                schema: "public",
                table: "categories",
                newName: "ix_categories_tenant_id");

            migrationBuilder.RenameIndex(
                name: "IX_categories_ParentCategoryId",
                schema: "public",
                table: "categories",
                newName: "ix_categories_parent_category_id");

            migrationBuilder.RenameColumn(
                name: "Version",
                schema: "public",
                table: "budgets",
                newName: "version");

            migrationBuilder.RenameColumn(
                name: "Status",
                schema: "public",
                table: "budgets",
                newName: "status");

            migrationBuilder.RenameColumn(
                name: "Name",
                schema: "public",
                table: "budgets",
                newName: "name");

            migrationBuilder.RenameColumn(
                name: "Description",
                schema: "public",
                table: "budgets",
                newName: "description");

            migrationBuilder.RenameColumn(
                name: "Id",
                schema: "public",
                table: "budgets",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "WorkflowInstanceId",
                schema: "public",
                table: "budgets",
                newName: "workflow_instance_id");

            migrationBuilder.RenameColumn(
                name: "TenantId",
                schema: "public",
                table: "budgets",
                newName: "tenant_id");

            migrationBuilder.RenameColumn(
                name: "StartDate",
                schema: "public",
                table: "budgets",
                newName: "start_date");

            migrationBuilder.RenameColumn(
                name: "ModifiedAt",
                schema: "public",
                table: "budgets",
                newName: "modified_at");

            migrationBuilder.RenameColumn(
                name: "IsRollingForecast",
                schema: "public",
                table: "budgets",
                newName: "is_rolling_forecast");

            migrationBuilder.RenameColumn(
                name: "IsDeleted",
                schema: "public",
                table: "budgets",
                newName: "is_deleted");

            migrationBuilder.RenameColumn(
                name: "ForecastPeriodsJson",
                schema: "public",
                table: "budgets",
                newName: "forecast_periods_json");

            migrationBuilder.RenameColumn(
                name: "FiscalYear",
                schema: "public",
                table: "budgets",
                newName: "fiscal_year");

            migrationBuilder.RenameColumn(
                name: "EndDate",
                schema: "public",
                table: "budgets",
                newName: "end_date");

            migrationBuilder.RenameColumn(
                name: "CurrencyCode",
                schema: "public",
                table: "budgets",
                newName: "currency_code");

            migrationBuilder.RenameColumn(
                name: "CreatedById",
                schema: "public",
                table: "budgets",
                newName: "created_by_id");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                schema: "public",
                table: "budgets",
                newName: "created_at");

            migrationBuilder.RenameColumn(
                name: "ApprovedById",
                schema: "public",
                table: "budgets",
                newName: "approved_by_id");

            migrationBuilder.RenameColumn(
                name: "AllocationRulesJson",
                schema: "public",
                table: "budgets",
                newName: "allocation_rules_json");

            migrationBuilder.RenameIndex(
                name: "IX_budgets_Status",
                schema: "public",
                table: "budgets",
                newName: "ix_budgets_status");

            migrationBuilder.RenameIndex(
                name: "IX_budgets_Name",
                schema: "public",
                table: "budgets",
                newName: "ix_budgets_name");

            migrationBuilder.RenameIndex(
                name: "IX_budgets_WorkflowInstanceId",
                schema: "public",
                table: "budgets",
                newName: "ix_budgets_workflow_instance_id");

            migrationBuilder.RenameIndex(
                name: "IX_budgets_TenantId",
                schema: "public",
                table: "budgets",
                newName: "ix_budgets_tenant_id");

            migrationBuilder.RenameIndex(
                name: "IX_budgets_StartDate",
                schema: "public",
                table: "budgets",
                newName: "ix_budgets_start_date");

            migrationBuilder.RenameIndex(
                name: "IX_budgets_IsDeleted",
                schema: "public",
                table: "budgets",
                newName: "ix_budgets_is_deleted");

            migrationBuilder.RenameIndex(
                name: "IX_budgets_FiscalYear",
                schema: "public",
                table: "budgets",
                newName: "ix_budgets_fiscal_year");

            migrationBuilder.RenameIndex(
                name: "IX_budgets_EndDate",
                schema: "public",
                table: "budgets",
                newName: "ix_budgets_end_date");

            migrationBuilder.RenameIndex(
                name: "IX_budgets_CreatedById",
                schema: "public",
                table: "budgets",
                newName: "ix_budgets_created_by_id");

            migrationBuilder.RenameIndex(
                name: "IX_budgets_ApprovedById",
                schema: "public",
                table: "budgets",
                newName: "ix_budgets_approved_by_id");

            migrationBuilder.RenameColumn(
                name: "Status",
                schema: "public",
                table: "budget_allocations",
                newName: "status");

            migrationBuilder.RenameColumn(
                name: "Description",
                schema: "public",
                table: "budget_allocations",
                newName: "description");

            migrationBuilder.RenameColumn(
                name: "Id",
                schema: "public",
                table: "budget_allocations",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "TenantId",
                schema: "public",
                table: "budget_allocations",
                newName: "tenant_id");

            migrationBuilder.RenameColumn(
                name: "ModifiedAt",
                schema: "public",
                table: "budget_allocations",
                newName: "modified_at");

            migrationBuilder.RenameColumn(
                name: "FiscalPeriodIdentifier",
                schema: "public",
                table: "budget_allocations",
                newName: "fiscal_period_identifier");

            migrationBuilder.RenameColumn(
                name: "DepartmentId",
                schema: "public",
                table: "budget_allocations",
                newName: "department_id");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                schema: "public",
                table: "budget_allocations",
                newName: "created_at");

            migrationBuilder.RenameColumn(
                name: "BudgetId",
                schema: "public",
                table: "budget_allocations",
                newName: "budget_id");

            migrationBuilder.RenameColumn(
                name: "AllocationDate",
                schema: "public",
                table: "budget_allocations",
                newName: "allocation_date");

            migrationBuilder.RenameIndex(
                name: "IX_budget_allocations_Status",
                schema: "public",
                table: "budget_allocations",
                newName: "ix_budget_allocations_status");

            migrationBuilder.RenameIndex(
                name: "IX_budget_allocations_TenantId",
                schema: "public",
                table: "budget_allocations",
                newName: "ix_budget_allocations_tenant_id");

            migrationBuilder.RenameIndex(
                name: "IX_budget_allocations_FiscalPeriodIdentifier",
                schema: "public",
                table: "budget_allocations",
                newName: "ix_budget_allocations_fiscal_period_identifier");

            migrationBuilder.RenameIndex(
                name: "IX_budget_allocations_DepartmentId",
                schema: "public",
                table: "budget_allocations",
                newName: "ix_budget_allocations_department_id");

            migrationBuilder.RenameIndex(
                name: "IX_budget_allocations_BudgetId_FiscalPeriodIdentifier",
                schema: "public",
                table: "budget_allocations",
                newName: "ix_budget_allocations_budget_id_fiscal_period_identifier");

            migrationBuilder.RenameIndex(
                name: "IX_budget_allocations_BudgetId",
                schema: "public",
                table: "budget_allocations",
                newName: "ix_budget_allocations_budget_id");

            migrationBuilder.RenameColumn(
                name: "Value",
                table: "AspNetUserTokens",
                newName: "value");

            migrationBuilder.RenameColumn(
                name: "Name",
                table: "AspNetUserTokens",
                newName: "name");

            migrationBuilder.RenameColumn(
                name: "LoginProvider",
                table: "AspNetUserTokens",
                newName: "login_provider");

            migrationBuilder.RenameColumn(
                name: "UserId",
                table: "AspNetUserTokens",
                newName: "user_id");

            migrationBuilder.RenameColumn(
                name: "Email",
                table: "AspNetUsers",
                newName: "email");

            migrationBuilder.RenameColumn(
                name: "Id",
                table: "AspNetUsers",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "UserName",
                table: "AspNetUsers",
                newName: "user_name");

            migrationBuilder.RenameColumn(
                name: "TwoFactorEnabled",
                table: "AspNetUsers",
                newName: "two_factor_enabled");

            migrationBuilder.RenameColumn(
                name: "SecurityStamp",
                table: "AspNetUsers",
                newName: "security_stamp");

            migrationBuilder.RenameColumn(
                name: "PhoneNumberConfirmed",
                table: "AspNetUsers",
                newName: "phone_number_confirmed");

            migrationBuilder.RenameColumn(
                name: "PhoneNumber",
                table: "AspNetUsers",
                newName: "phone_number");

            migrationBuilder.RenameColumn(
                name: "PasswordHash",
                table: "AspNetUsers",
                newName: "password_hash");

            migrationBuilder.RenameColumn(
                name: "NormalizedUserName",
                table: "AspNetUsers",
                newName: "normalized_user_name");

            migrationBuilder.RenameColumn(
                name: "NormalizedEmail",
                table: "AspNetUsers",
                newName: "normalized_email");

            migrationBuilder.RenameColumn(
                name: "LockoutEnd",
                table: "AspNetUsers",
                newName: "lockout_end");

            migrationBuilder.RenameColumn(
                name: "LockoutEnabled",
                table: "AspNetUsers",
                newName: "lockout_enabled");

            migrationBuilder.RenameColumn(
                name: "EmailConfirmed",
                table: "AspNetUsers",
                newName: "email_confirmed");

            migrationBuilder.RenameColumn(
                name: "ConcurrencyStamp",
                table: "AspNetUsers",
                newName: "concurrency_stamp");

            migrationBuilder.RenameColumn(
                name: "AccessFailedCount",
                table: "AspNetUsers",
                newName: "access_failed_count");

            migrationBuilder.RenameColumn(
                name: "RoleId",
                table: "AspNetUserRoles",
                newName: "role_id");

            migrationBuilder.RenameColumn(
                name: "UserId",
                table: "AspNetUserRoles",
                newName: "user_id");

            migrationBuilder.RenameIndex(
                name: "IX_AspNetUserRoles_RoleId",
                table: "AspNetUserRoles",
                newName: "ix_asp_net_user_roles_role_id");

            migrationBuilder.RenameColumn(
                name: "UserId",
                table: "AspNetUserLogins",
                newName: "user_id");

            migrationBuilder.RenameColumn(
                name: "ProviderDisplayName",
                table: "AspNetUserLogins",
                newName: "provider_display_name");

            migrationBuilder.RenameColumn(
                name: "ProviderKey",
                table: "AspNetUserLogins",
                newName: "provider_key");

            migrationBuilder.RenameColumn(
                name: "LoginProvider",
                table: "AspNetUserLogins",
                newName: "login_provider");

            migrationBuilder.RenameIndex(
                name: "IX_AspNetUserLogins_UserId",
                table: "AspNetUserLogins",
                newName: "ix_asp_net_user_logins_user_id");

            migrationBuilder.RenameColumn(
                name: "Id",
                table: "AspNetUserClaims",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "UserId",
                table: "AspNetUserClaims",
                newName: "user_id");

            migrationBuilder.RenameColumn(
                name: "ClaimValue",
                table: "AspNetUserClaims",
                newName: "claim_value");

            migrationBuilder.RenameColumn(
                name: "ClaimType",
                table: "AspNetUserClaims",
                newName: "claim_type");

            migrationBuilder.RenameIndex(
                name: "IX_AspNetUserClaims_UserId",
                table: "AspNetUserClaims",
                newName: "ix_asp_net_user_claims_user_id");

            migrationBuilder.RenameColumn(
                name: "Name",
                table: "AspNetRoles",
                newName: "name");

            migrationBuilder.RenameColumn(
                name: "Id",
                table: "AspNetRoles",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "NormalizedName",
                table: "AspNetRoles",
                newName: "normalized_name");

            migrationBuilder.RenameColumn(
                name: "ConcurrencyStamp",
                table: "AspNetRoles",
                newName: "concurrency_stamp");

            migrationBuilder.RenameColumn(
                name: "Id",
                table: "AspNetRoleClaims",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "RoleId",
                table: "AspNetRoleClaims",
                newName: "role_id");

            migrationBuilder.RenameColumn(
                name: "ClaimValue",
                table: "AspNetRoleClaims",
                newName: "claim_value");

            migrationBuilder.RenameColumn(
                name: "ClaimType",
                table: "AspNetRoleClaims",
                newName: "claim_type");

            migrationBuilder.RenameIndex(
                name: "IX_AspNetRoleClaims_RoleId",
                table: "AspNetRoleClaims",
                newName: "ix_asp_net_role_claims_role_id");

            migrationBuilder.RenameColumn(
                name: "Url",
                table: "document_link",
                newName: "url");

            migrationBuilder.RenameColumn(
                name: "Name",
                table: "document_link",
                newName: "name");

            migrationBuilder.RenameColumn(
                name: "Description",
                table: "document_link",
                newName: "description");

            migrationBuilder.RenameColumn(
                name: "DocumentType",
                table: "document_link",
                newName: "document_type");

            migrationBuilder.AddPrimaryKey(
                name: "pk_vendors",
                table: "vendors",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_vendor_proposals",
                table: "vendor_proposals",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_vendor_products",
                table: "vendor_products",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_test_entities",
                table: "test_entities",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_tenants",
                schema: "public",
                table: "tenants",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_tenant_products",
                table: "tenant_products",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_technical_submittals",
                table: "technical_submittals",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_suppliers",
                table: "suppliers",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_submittal_reviews",
                table: "submittal_reviews",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_sales_territory_representatives",
                table: "sales_territory_representatives",
                columns: new[] { "sales_territory_id", "representative_id" });

            migrationBuilder.AddPrimaryKey(
                name: "pk_sales_territories",
                table: "sales_territories",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_sales_orders",
                schema: "public",
                table: "sales_orders",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_sales_order_lines",
                table: "sales_order_lines",
                columns: new[] { "sales_order_id", "line_number" });

            migrationBuilder.AddPrimaryKey(
                name: "pk_return_authorizations",
                table: "return_authorizations",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_return_authorization_lines",
                table: "return_authorization_lines",
                columns: new[] { "return_authorization_id", "line_number" });

            migrationBuilder.AddPrimaryKey(
                name: "pk_requests_for_proposal",
                table: "requests_for_proposal",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_requests_for_information",
                table: "requests_for_information",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_request_for_quotes",
                schema: "public",
                table: "request_for_quotes",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_request_for_quote_lines",
                schema: "public",
                table: "request_for_quote_lines",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_purchase_requisitions",
                table: "PurchaseRequisitions",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_purchase_requisition_lines",
                table: "PurchaseRequisitionLines",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_purchase_order_lines",
                table: "PurchaseOrderLines",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_purchase_orders",
                table: "purchase_orders",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_projects",
                schema: "public",
                table: "projects",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_products",
                schema: "public",
                table: "products",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_product_definitions",
                table: "product_definitions",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_procurement_workflows",
                table: "procurement_workflows",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_procurement_workflow_steps",
                table: "procurement_workflow_steps",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_payment_transactions",
                table: "payment_transactions",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_invoices",
                table: "invoices",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_invoice_lines",
                table: "invoice_lines",
                columns: new[] { "invoice_id", "line_number" });

            migrationBuilder.AddPrimaryKey(
                name: "pk_goods_receipt_notes",
                table: "goods_receipt_notes",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_goods_receipt_note_lines",
                table: "goods_receipt_note_lines",
                columns: new[] { "goods_receipt_note_id", "line_number" });

            migrationBuilder.AddPrimaryKey(
                name: "pk_departments",
                schema: "public",
                table: "departments",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_delivery_notes",
                table: "delivery_notes",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_delivery_note_lines",
                table: "delivery_note_lines",
                columns: new[] { "delivery_note_id", "line_number" });

            migrationBuilder.AddPrimaryKey(
                name: "pk_customers",
                schema: "public",
                table: "customers",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_contracts",
                table: "contracts",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_categories",
                schema: "public",
                table: "categories",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_budgets",
                schema: "public",
                table: "budgets",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_budget_allocations",
                schema: "public",
                table: "budget_allocations",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_asp_net_user_tokens",
                table: "AspNetUserTokens",
                columns: new[] { "user_id", "login_provider", "name" });

            migrationBuilder.AddPrimaryKey(
                name: "pk_asp_net_users",
                table: "AspNetUsers",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_asp_net_user_roles",
                table: "AspNetUserRoles",
                columns: new[] { "user_id", "role_id" });

            migrationBuilder.AddPrimaryKey(
                name: "pk_asp_net_user_logins",
                table: "AspNetUserLogins",
                columns: new[] { "login_provider", "provider_key" });

            migrationBuilder.AddPrimaryKey(
                name: "pk_asp_net_user_claims",
                table: "AspNetUserClaims",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_asp_net_roles",
                table: "AspNetRoles",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_asp_net_role_claims",
                table: "AspNetRoleClaims",
                column: "id");

            migrationBuilder.AddForeignKey(
                name: "fk_asp_net_role_claims_asp_net_roles_role_id",
                table: "AspNetRoleClaims",
                column: "role_id",
                principalTable: "AspNetRoles",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_asp_net_user_claims_asp_net_users_user_id",
                table: "AspNetUserClaims",
                column: "user_id",
                principalTable: "AspNetUsers",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_asp_net_user_logins_asp_net_users_user_id",
                table: "AspNetUserLogins",
                column: "user_id",
                principalTable: "AspNetUsers",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_asp_net_user_roles_asp_net_roles_role_id",
                table: "AspNetUserRoles",
                column: "role_id",
                principalTable: "AspNetRoles",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_asp_net_user_roles_asp_net_users_user_id",
                table: "AspNetUserRoles",
                column: "user_id",
                principalTable: "AspNetUsers",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_asp_net_user_tokens_asp_net_users_user_id",
                table: "AspNetUserTokens",
                column: "user_id",
                principalTable: "AspNetUsers",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_budget_allocations_budgets_budget_id",
                schema: "public",
                table: "budget_allocations",
                column: "budget_id",
                principalSchema: "public",
                principalTable: "budgets",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_budget_allocations_departments_department_id",
                schema: "public",
                table: "budget_allocations",
                column: "department_id",
                principalSchema: "public",
                principalTable: "departments",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_categories_categories_parent_category_id",
                schema: "public",
                table: "categories",
                column: "parent_category_id",
                principalSchema: "public",
                principalTable: "categories",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_contracts_vendors_vendor_id",
                table: "contracts",
                column: "vendor_id",
                principalTable: "vendors",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_delivery_note_lines_delivery_notes_delivery_note_id",
                table: "delivery_note_lines",
                column: "delivery_note_id",
                principalTable: "delivery_notes",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_delivery_note_lines_product_definitions_product_id",
                table: "delivery_note_lines",
                column: "product_id",
                principalTable: "product_definitions",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_delivery_note_lines_purchase_order_lines_purchase_order_lin",
                table: "delivery_note_lines",
                column: "purchase_order_line_id",
                principalTable: "PurchaseOrderLines",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_delivery_note_lines_sales_order_lines_sales_order_id_sales_",
                table: "delivery_note_lines",
                columns: new[] { "sales_order_id", "sales_order_line_number" },
                principalTable: "sales_order_lines",
                principalColumns: new[] { "sales_order_id", "line_number" },
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_delivery_notes_purchase_orders_purchase_order_id",
                table: "delivery_notes",
                column: "purchase_order_id",
                principalTable: "purchase_orders",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_delivery_notes_sales_orders_sales_order_id",
                table: "delivery_notes",
                column: "sales_order_id",
                principalSchema: "public",
                principalTable: "sales_orders",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_delivery_notes_vendors_vendor_id",
                table: "delivery_notes",
                column: "vendor_id",
                principalTable: "vendors",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_goods_receipt_note_lines_delivery_note_lines_delivery_note_",
                table: "goods_receipt_note_lines",
                columns: new[] { "delivery_note_line_delivery_note_id", "delivery_note_line_line_number" },
                principalTable: "delivery_note_lines",
                principalColumns: new[] { "delivery_note_id", "line_number" });

            migrationBuilder.AddForeignKey(
                name: "fk_goods_receipt_note_lines_goods_receipt_notes_goods_receipt_",
                table: "goods_receipt_note_lines",
                column: "goods_receipt_note_id",
                principalTable: "goods_receipt_notes",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_goods_receipt_note_lines_product_definitions_product_id",
                table: "goods_receipt_note_lines",
                column: "product_id",
                principalTable: "product_definitions",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_goods_receipt_note_lines_purchase_order_lines_purchase_orde",
                table: "goods_receipt_note_lines",
                column: "purchase_order_line_id",
                principalTable: "PurchaseOrderLines",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_goods_receipt_notes_asp_net_users_received_by_user_id",
                table: "goods_receipt_notes",
                column: "received_by_user_id",
                principalTable: "AspNetUsers",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_goods_receipt_notes_delivery_notes_delivery_note_id",
                table: "goods_receipt_notes",
                column: "delivery_note_id",
                principalTable: "delivery_notes",
                principalColumn: "id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "fk_goods_receipt_notes_purchase_orders_purchase_order_id",
                table: "goods_receipt_notes",
                column: "purchase_order_id",
                principalTable: "purchase_orders",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_goods_receipt_notes_vendors_vendor_id",
                table: "goods_receipt_notes",
                column: "vendor_id",
                principalTable: "vendors",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_invoice_lines_invoices_invoice_id",
                table: "invoice_lines",
                column: "invoice_id",
                principalTable: "invoices",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_invoice_lines_product_definitions_product_id",
                table: "invoice_lines",
                column: "product_id",
                principalTable: "product_definitions",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_invoice_lines_purchase_order_lines_purchase_order_line_id",
                table: "invoice_lines",
                column: "purchase_order_line_id",
                principalTable: "PurchaseOrderLines",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_invoices_customers_customer_id",
                table: "invoices",
                column: "customer_id",
                principalSchema: "public",
                principalTable: "customers",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_invoices_purchase_orders_purchase_order_id",
                table: "invoices",
                column: "purchase_order_id",
                principalTable: "purchase_orders",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_invoices_vendors_vendor_id",
                table: "invoices",
                column: "vendor_id",
                principalTable: "vendors",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_payment_transactions_invoices_invoice_id",
                table: "payment_transactions",
                column: "invoice_id",
                principalTable: "invoices",
                principalColumn: "id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "fk_payment_transactions_vendors_vendor_id",
                table: "payment_transactions",
                column: "vendor_id",
                principalTable: "vendors",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_procurement_workflow_steps_asp_net_users_approver_user_id",
                table: "procurement_workflow_steps",
                column: "approver_user_id",
                principalTable: "AspNetUsers",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_procurement_workflow_steps_procurement_workflows_procuremen",
                table: "procurement_workflow_steps",
                column: "procurement_workflow_id",
                principalTable: "procurement_workflows",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_procurement_workflow_steps_procurement_workflows_workflow_id",
                table: "procurement_workflow_steps",
                column: "workflow_id",
                principalTable: "procurement_workflows",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_product_definitions_categories_category_id",
                table: "product_definitions",
                column: "category_id",
                principalSchema: "public",
                principalTable: "categories",
                principalColumn: "id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "fk_products_categories_category_id",
                schema: "public",
                table: "products",
                column: "category_id",
                principalSchema: "public",
                principalTable: "categories",
                principalColumn: "id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "fk_purchase_orders_contracts_contract_id",
                table: "purchase_orders",
                column: "contract_id",
                principalTable: "contracts",
                principalColumn: "id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "fk_purchase_orders_purchase_requisitions_requisition_id",
                table: "purchase_orders",
                column: "requisition_id",
                principalTable: "PurchaseRequisitions",
                principalColumn: "id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "fk_purchase_orders_vendors_vendor_id",
                table: "purchase_orders",
                column: "vendor_id",
                principalTable: "vendors",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_purchase_order_lines_purchase_orders_purchase_order_id",
                table: "PurchaseOrderLines",
                column: "purchase_order_id",
                principalTable: "purchase_orders",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_purchase_order_lines_vendor_products_vendor_product_id",
                table: "PurchaseOrderLines",
                column: "vendor_product_id",
                principalTable: "vendor_products",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_purchase_requisition_lines_product_definitions_product_defini",
                table: "PurchaseRequisitionLines",
                column: "product_definition_id",
                principalTable: "product_definitions",
                principalColumn: "id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "fk_purchase_requisition_lines_purchase_requisitions_purchase_requ",
                table: "PurchaseRequisitionLines",
                column: "purchase_requisition_id",
                principalTable: "PurchaseRequisitions",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_purchase_requisition_lines_vendor_products_vendor_product_id",
                table: "PurchaseRequisitionLines",
                column: "vendor_product_id",
                principalTable: "vendor_products",
                principalColumn: "id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "fk_purchase_requisition_lines_vendors_suggested_vendor_id",
                table: "PurchaseRequisitionLines",
                column: "suggested_vendor_id",
                principalTable: "vendors",
                principalColumn: "id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "fk_purchase_requisitions_purchase_orders_associated_purchase_or",
                table: "PurchaseRequisitions",
                column: "associated_purchase_order_id",
                principalTable: "purchase_orders",
                principalColumn: "id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "fk_request_for_quote_lines_product_definitions_product_definit",
                schema: "public",
                table: "request_for_quote_lines",
                column: "product_definition_id",
                principalTable: "product_definitions",
                principalColumn: "id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "fk_request_for_quote_lines_requests_for_quote_request_for_quot",
                schema: "public",
                table: "request_for_quote_lines",
                column: "request_for_quote_id",
                principalSchema: "public",
                principalTable: "request_for_quotes",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_request_for_quote_lines_vendor_products_vendor_product_id",
                schema: "public",
                table: "request_for_quote_lines",
                column: "vendor_product_id",
                principalTable: "vendor_products",
                principalColumn: "id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "fk_request_for_quotes_contracts_related_agreement_id",
                schema: "public",
                table: "request_for_quotes",
                column: "related_agreement_id",
                principalTable: "contracts",
                principalColumn: "id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "fk_request_for_quotes_purchase_requisitions_originating_requisi",
                schema: "public",
                table: "request_for_quotes",
                column: "originating_requisition_id",
                principalTable: "PurchaseRequisitions",
                principalColumn: "id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "fk_request_for_quotes_vendors_awarded_vendor_id",
                schema: "public",
                table: "request_for_quotes",
                column: "awarded_vendor_id",
                principalTable: "vendors",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_requests_for_information_projects_project_id",
                table: "requests_for_information",
                column: "project_id",
                principalSchema: "public",
                principalTable: "projects",
                principalColumn: "id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "fk_requests_for_proposal_contracts_awarded_contract_id",
                table: "requests_for_proposal",
                column: "awarded_contract_id",
                principalTable: "contracts",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "fk_requests_for_proposal_projects_project_id",
                table: "requests_for_proposal",
                column: "project_id",
                principalSchema: "public",
                principalTable: "projects",
                principalColumn: "id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "fk_requests_for_proposal_vendors_awarded_vendor_id",
                table: "requests_for_proposal",
                column: "awarded_vendor_id",
                principalTable: "vendors",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "fk_return_authorization_lines_invoice_lines_invoice_id_invoice",
                table: "return_authorization_lines",
                columns: new[] { "invoice_id", "invoice_line_number" },
                principalTable: "invoice_lines",
                principalColumns: new[] { "invoice_id", "line_number" },
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_return_authorization_lines_product_definitions_product_defi",
                table: "return_authorization_lines",
                column: "product_definition_id",
                principalTable: "product_definitions",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "fk_return_authorization_lines_products_product_id",
                table: "return_authorization_lines",
                column: "product_id",
                principalSchema: "public",
                principalTable: "products",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_return_authorization_lines_return_authorizations_return_aut",
                table: "return_authorization_lines",
                column: "return_authorization_id",
                principalTable: "return_authorizations",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_return_authorization_lines_sales_order_lines_original_sales",
                table: "return_authorization_lines",
                columns: new[] { "original_sales_order_line_sales_order_id", "original_sales_order_line_line_number" },
                principalTable: "sales_order_lines",
                principalColumns: new[] { "sales_order_id", "line_number" });

            migrationBuilder.AddForeignKey(
                name: "fk_return_authorization_lines_sales_order_lines_sales_order_id",
                table: "return_authorization_lines",
                columns: new[] { "sales_order_id", "sales_order_line_number" },
                principalTable: "sales_order_lines",
                principalColumns: new[] { "sales_order_id", "line_number" },
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_return_authorization_lines_vendor_products_vendor_product_id",
                table: "return_authorization_lines",
                column: "vendor_product_id",
                principalTable: "vendor_products",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "fk_return_authorizations_customers_customer_id",
                table: "return_authorizations",
                column: "customer_id",
                principalSchema: "public",
                principalTable: "customers",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_return_authorizations_invoices_invoice_id",
                table: "return_authorizations",
                column: "invoice_id",
                principalTable: "invoices",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_return_authorizations_sales_orders_sales_order_id",
                table: "return_authorizations",
                column: "sales_order_id",
                principalSchema: "public",
                principalTable: "sales_orders",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_sales_order_lines_products_product_id",
                table: "sales_order_lines",
                column: "product_id",
                principalSchema: "public",
                principalTable: "products",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_sales_order_lines_projects_project_id",
                table: "sales_order_lines",
                column: "project_id",
                principalSchema: "public",
                principalTable: "projects",
                principalColumn: "id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "fk_sales_order_lines_projects_project_id1",
                table: "sales_order_lines",
                column: "project_id1",
                principalSchema: "public",
                principalTable: "projects",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "fk_sales_order_lines_sales_order_lines_parent_sales_order_line",
                table: "sales_order_lines",
                columns: new[] { "parent_sales_order_line_sales_order_id", "parent_sales_order_line_line_number" },
                principalTable: "sales_order_lines",
                principalColumns: new[] { "sales_order_id", "line_number" });

            migrationBuilder.AddForeignKey(
                name: "fk_sales_order_lines_sales_orders_sales_order_id",
                table: "sales_order_lines",
                column: "sales_order_id",
                principalSchema: "public",
                principalTable: "sales_orders",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_sales_order_lines_vendor_products_vendor_product_id",
                table: "sales_order_lines",
                column: "vendor_product_id",
                principalTable: "vendor_products",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_sales_orders_customers_customer_id",
                schema: "public",
                table: "sales_orders",
                column: "customer_id",
                principalSchema: "public",
                principalTable: "customers",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_sales_orders_return_authorizations_related_return_authoriza",
                schema: "public",
                table: "sales_orders",
                column: "related_return_authorization_id",
                principalTable: "return_authorizations",
                principalColumn: "id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "fk_sales_orders_sales_territories_sales_territory_id",
                schema: "public",
                table: "sales_orders",
                column: "sales_territory_id",
                principalTable: "sales_territories",
                principalColumn: "id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "fk_sales_orders_sales_territories_sales_territory_id1",
                schema: "public",
                table: "sales_orders",
                column: "sales_territory_id1",
                principalTable: "sales_territories",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "fk_sales_territories_sales_territories_parent_territory_id",
                table: "sales_territories",
                column: "parent_territory_id",
                principalTable: "sales_territories",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_sales_territory_representatives_asp_net_users_representative_",
                table: "sales_territory_representatives",
                column: "representative_id",
                principalTable: "AspNetUsers",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_sales_territory_representatives_sales_territories_sales_ter",
                table: "sales_territory_representatives",
                column: "sales_territory_id",
                principalTable: "sales_territories",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_submittal_reviews_asp_net_users_reviewer_id",
                table: "submittal_reviews",
                column: "reviewer_id",
                principalTable: "AspNetUsers",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_submittal_reviews_technical_submittals_technical_submittal_",
                table: "submittal_reviews",
                column: "technical_submittal_id",
                principalTable: "technical_submittals",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_submittal_reviews_technical_submittals_technical_submittal_1",
                table: "submittal_reviews",
                column: "technical_submittal_id1",
                principalTable: "technical_submittals",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "fk_suppliers_vendors_vendor_id",
                table: "suppliers",
                column: "vendor_id",
                principalTable: "vendors",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_technical_submittals_asp_net_users_submitted_by_user_id",
                table: "technical_submittals",
                column: "submitted_by_user_id",
                principalTable: "AspNetUsers",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_technical_submittals_contracts_contract_id",
                table: "technical_submittals",
                column: "contract_id",
                principalTable: "contracts",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "fk_technical_submittals_projects_project_id",
                table: "technical_submittals",
                column: "project_id",
                principalSchema: "public",
                principalTable: "projects",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_technical_submittals_purchase_order_lines_purchase_order_li",
                table: "technical_submittals",
                column: "purchase_order_line_id",
                principalTable: "PurchaseOrderLines",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "fk_technical_submittals_vendors_vendor_id",
                table: "technical_submittals",
                column: "vendor_id",
                principalTable: "vendors",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_vendor_products_product_definitions_product_definition_id",
                table: "vendor_products",
                column: "product_definition_id",
                principalTable: "product_definitions",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_vendor_products_vendors_vendor_id",
                table: "vendor_products",
                column: "vendor_id",
                principalTable: "vendors",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_vendor_proposals_requests_for_proposal_request_for_proposal",
                table: "vendor_proposals",
                column: "request_for_proposal_id",
                principalTable: "requests_for_proposal",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_vendor_proposals_vendors_vendor_id",
                table: "vendor_proposals",
                column: "vendor_id",
                principalTable: "vendors",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
