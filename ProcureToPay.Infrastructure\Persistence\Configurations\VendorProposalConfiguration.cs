using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProcureToPay.Domain.Entities;
using ProcureToPay.Domain.Enums; // Assuming VendorProposalStatus enum exists
using ProcureToPay.Infrastructure.Persistence.Extensions;

namespace ProcureToPay.Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// Configuration for the VendorProposal entity for EF Core and PostgreSQL.
    /// </summary>
    public class VendorProposalConfiguration : IEntityTypeConfiguration<VendorProposal>
    {
        public void Configure(EntityTypeBuilder<VendorProposal> builder)
        {
            // Table Mapping
            builder.ToTable("vendor_proposals");

            // Primary Key
            builder.HasKey(vp => vp.Id);
            builder.Property(vp => vp.Id).ValueGeneratedOnAdd();

            // Properties
            builder.Property(vp => vp.SubmissionDate)
                .IsRequired()
                .HasColumnType("timestamp without time zone");

            builder.Property(vp => vp.ValidityEndDate)
                .HasColumnType("timestamp without time zone") // Or "date"
                .IsRequired(false); // Proposal might not have an expiry

            builder.Property(vp => vp.Status)
                .IsRequired()
                .HasConversion<string>() // Or int
                .HasMaxLength(50); // e.g., Submitted, Evaluating, Awarded, Rejected

            builder.Property(vp => vp.TotalProposedValue)
                .HasColumnType("numeric(18, 2)")
                .IsRequired(false); // Might be detailed in lines or not applicable

            // Ignore Money property
            builder.Ignore(vp => vp.ProposedTotalValue);

            builder.Property(vp => vp.Comments)
                .HasColumnType("text")
                .IsRequired(false);

            // Foreign Keys (Assuming Guid or appropriate types)
            builder.Property(vp => vp.RequestForProposalId).IsRequired();
            builder.Property(vp => vp.VendorId).IsRequired();

            builder.Property(vp => vp.TenantId).IsRequired();


            // --- Concurrency Token ---
            builder.UseXminAsConcurrencyToken();


            // --- Relationships ---
            // Link to RFP (Many-to-One)
            builder.HasOne(vp => vp.RequestForProposal)
                   .WithMany(rfp => rfp.VendorProposals) // Matches RFP config
                   .HasForeignKey(vp => vp.RequestForProposalId)
                   .IsRequired()
                   .OnDelete(DeleteBehavior.Restrict); // Prevent deleting RFP if proposals exist (or Cascade?)

            // Link to Vendor (Many-to-One)
            builder.HasOne(vp => vp.Vendor)
                   .WithMany(v => v.VendorProposals) // Assuming Vendor has collection
                   .HasForeignKey(vp => vp.VendorId)
                   .IsRequired()
                   .OnDelete(DeleteBehavior.Restrict); // Prevent deleting vendor if proposals exist

            // Consider relationship to Proposal Lines if modeled separately
            // builder.HasMany(vp => vp.Lines)...


            // --- Indexes ---
            builder.HasIndex(vp => vp.Status);
            builder.HasIndex(vp => vp.SubmissionDate);
            builder.HasIndex(vp => vp.RequestForProposalId);
            builder.HasIndex(vp => vp.VendorId);
            builder.HasIndex(vp => vp.TenantId);
            // Unique constraint: One proposal per vendor per RFP
            builder.HasIndex(vp => new { vp.RequestForProposalId, vp.VendorId }).IsUnique();


            // --- TODO ---
            // TODO: Verify FK types (RequestForProposalId, VendorId) match related entities.
            // TODO: Define VendorProposalStatus enum.
            // TODO: Model and configure Proposal Lines if needed.
            // TODO: Confirm delete behavior for RFP relationship (Restrict or Cascade).
        }
    }
}
