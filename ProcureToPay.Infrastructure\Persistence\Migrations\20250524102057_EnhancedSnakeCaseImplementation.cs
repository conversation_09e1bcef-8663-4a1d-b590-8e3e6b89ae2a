﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ProcureToPay.Infrastructure.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class EnhancedSnakeCaseImplementation : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "s_k_u",
                table: "tenant_products",
                newName: "sku");

            migrationBuilder.RenameColumn(
                name: "related_n_c_r_reference",
                table: "technical_submittals",
                newName: "related_ncr_reference");

            migrationBuilder.RenameColumn(
                name: "related_i_t_p_reference",
                table: "technical_submittals",
                newName: "related_itp_reference");

            migrationBuilder.RenameColumn(
                name: "r_f_q_number",
                schema: "public",
                table: "request_for_quotes",
                newName: "rfq_number");

            migrationBuilder.RenameIndex(
                name: "IX_request_for_quotes_tenant_id_r_f_q_number",
                schema: "public",
                table: "request_for_quotes",
                newName: "IX_request_for_quotes_tenant_id_rfq_number");

            migrationBuilder.RenameColumn(
                name: "g_l_account_code",
                table: "purchase_requisition_lines",
                newName: "gl_account_code");

            migrationBuilder.RenameIndex(
                name: "IX_purchase_requisition_lines_g_l_account_code",
                table: "purchase_requisition_lines",
                newName: "IX_purchase_requisition_lines_gl_account_code");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "sku",
                table: "tenant_products",
                newName: "s_k_u");

            migrationBuilder.RenameColumn(
                name: "related_ncr_reference",
                table: "technical_submittals",
                newName: "related_n_c_r_reference");

            migrationBuilder.RenameColumn(
                name: "related_itp_reference",
                table: "technical_submittals",
                newName: "related_i_t_p_reference");

            migrationBuilder.RenameColumn(
                name: "rfq_number",
                schema: "public",
                table: "request_for_quotes",
                newName: "r_f_q_number");

            migrationBuilder.RenameIndex(
                name: "IX_request_for_quotes_tenant_id_rfq_number",
                schema: "public",
                table: "request_for_quotes",
                newName: "IX_request_for_quotes_tenant_id_r_f_q_number");

            migrationBuilder.RenameColumn(
                name: "gl_account_code",
                table: "purchase_requisition_lines",
                newName: "g_l_account_code");

            migrationBuilder.RenameIndex(
                name: "IX_purchase_requisition_lines_gl_account_code",
                table: "purchase_requisition_lines",
                newName: "IX_purchase_requisition_lines_g_l_account_code");
        }
    }
}
