using System;
using System.Collections.Generic;
using ProcureToPay.Domain.Enums; // Assuming SupplierStatus, SupplierRiskRating enums exist here
using ProcureToPay.Domain.ValueObjects; // Assuming ContactPerson record exists here
using ProcureToPay.Domain.Exceptions; // Assuming DomainStateException exists here
using ProcureToPay.Domain.Events;
// Assuming BaseEntity<Guid> exists in ProcureToPay.Domain.Entities
// Assuming Vendor entity exists in ProcureToPay.Domain.Entities

namespace ProcureToPay.Domain.Entities
{
    /// <summary>
    /// Represents detailed supplier information, often extending a core Vendor record.
    /// Contains data related to risk, compliance, performance, and specific contacts.
    /// </summary>
    public class Supplier : BaseEntity<Guid>
    {
        /// <summary>
        /// The legal or commonly known name of the supplier entity. May differ from Vendor name.
        /// </summary>
        public string SupplierName { get; private set; } = null!;

        /// <summary>
        /// Current operational status of the supplier relationship.
        /// </summary>
        public SupplierStatus Status { get; private set; }

        /// <summary>
        /// Classification of the supplier's risk level using the defined enum.
        /// </summary>
        public SupplierRiskRating? RiskRating { get; private set; } // Corrected: Use Enum, nullable

        /// <summary>
        /// A score or metric representing the supplier's performance in sustainable sourcing practices (e.g., 0-100).
        /// </summary>
        public decimal? SustainabilityScore { get; private set; }

        /// <summary>
        /// Flag indicating if this supplier acts as a contract manufacturer.
        /// </summary>
        public bool IsContractManufacturer { get; private set; }

        /// <summary>
        /// Average lead time in days for this supplier, based on performance tracking.
        /// </summary>
        public int? AverageLeadTimeDays { get; private set; }

        /// <summary>
        /// Flag indicating if the supplier meets Corporate Social Responsibility (CSR) compliance standards.
        /// </summary>
        public bool IsCsrCompliant { get; private set; }

        /// <summary>
        /// Reported or estimated capacity utilization percentage for the supplier (e.g., 0-100).
        /// </summary>
        public decimal? CapacityUtilizationPercent { get; private set; }

        /// <summary>
        /// A list representing the hierarchy of emergency contacts at the supplier.
        /// Stored as JSONB in the database via configuration.
        /// </summary>
        public List<ValueObjects.ContactPerson> EmergencyContacts { get; private set; } = new List<ValueObjects.ContactPerson>();

        // --- Relationships ---

        /// <summary>
        /// Foreign Key to the core Vendor record this supplier profile extends.
        /// </summary>
        public Guid VendorId { get; private set; }

        /// <summary>
        /// Navigation property to the core Vendor record. Virtual for lazy loading.
        /// </summary>
        public virtual Vendor Vendor { get; private set; } = null!;


        /// <summary>
        /// Private constructor for EF Core.
        /// </summary>
        private Supplier() : base(Guid.NewGuid()) { }

        /// <summary>
        /// Creates a new Supplier profile associated with a Vendor.
        /// </summary>
        /// <param name="id">Supplier identifier.</param>
        /// <param name="vendorId">ID of the Vendor this profile extends.</param>
        /// <param name="supplierName">Legal or common name of the supplier.</param>
        /// <param name="status">Initial status.</param>
        /// <param name="riskRating">Optional initial risk rating (using Enum).</param> // Corrected: Parameter type
        /// <param name="sustainabilityScore">Optional initial sustainability score.</param>
        /// <param name="isContractManufacturer">Is this a contract manufacturer?</param>
        /// <param name="averageLeadTimeDays">Optional initial average lead time.</param>
        /// <param name="isCsrCompliant">Initial CSR compliance status.</param>
        /// <param name="capacityUtilizationPercent">Optional initial capacity utilization.</param>
        public Supplier(
            Guid id,
            Guid vendorId,
            string supplierName,
            SupplierStatus status = SupplierStatus.Active,
            SupplierRiskRating? riskRating = null, // Corrected: Parameter type
            decimal? sustainabilityScore = null,
            bool isContractManufacturer = false,
            int? averageLeadTimeDays = null,
            bool isCsrCompliant = false,
            decimal? capacityUtilizationPercent = null) : base(id)
        {
            if (vendorId == Guid.Empty) throw new ArgumentException("VendorId cannot be empty.", nameof(vendorId));
            if (string.IsNullOrWhiteSpace(supplierName)) throw new ArgumentNullException(nameof(supplierName));

            // Add validation for score/percent ranges during creation
            if (sustainabilityScore.HasValue && (sustainabilityScore < 0 || sustainabilityScore > 100))
                throw new ArgumentOutOfRangeException(nameof(sustainabilityScore), "Sustainability score must be between 0 and 100.");
            if (capacityUtilizationPercent.HasValue && (capacityUtilizationPercent < 0 || capacityUtilizationPercent > 100))
                throw new ArgumentOutOfRangeException(nameof(capacityUtilizationPercent), "Capacity utilization must be between 0 and 100.");
            if (averageLeadTimeDays.HasValue && averageLeadTimeDays < 0)
                throw new ArgumentOutOfRangeException(nameof(averageLeadTimeDays), "Lead time cannot be negative.");

            VendorId = vendorId;
            SupplierName = supplierName;
            Status = status;
            RiskRating = riskRating; // Assign Enum
            SustainabilityScore = sustainabilityScore;
            IsContractManufacturer = isContractManufacturer;
            AverageLeadTimeDays = averageLeadTimeDays;
            IsCsrCompliant = isCsrCompliant;
            CapacityUtilizationPercent = capacityUtilizationPercent;
            EmergencyContacts = new List<ValueObjects.ContactPerson>(); // Initialize empty list

            // AddDomainEvent(new SupplierProfileCreatedEvent(this.Id, vendorId)); // Example event
        }

        // --- Domain Methods ---

        public void UpdateStatus(SupplierStatus newStatus)
        {
            if (Status == newStatus) return;
            Status = newStatus;
            AddDomainEvent(new SupplierStatusUpdatedEvent(this.Id, newStatus)); // Implemented event call
        }

        public void UpdateRiskRating(SupplierRiskRating? rating) // Corrected: Parameter type
        {
            if (RiskRating == rating) return;
            RiskRating = rating;
            AddDomainEvent(new SupplierRiskRatingUpdatedEvent(this.Id, rating)); // Implemented event call
        }

        public void UpdatePerformanceMetrics(int? leadTime, decimal? sustainabilityScore, decimal? capacityUtilization)
        {
            // Added validation
            if (leadTime.HasValue && leadTime < 0)
                throw new ArgumentOutOfRangeException(nameof(leadTime), "Lead time cannot be negative.");
            if (sustainabilityScore.HasValue && (sustainabilityScore < 0 || sustainabilityScore > 100))
                throw new ArgumentOutOfRangeException(nameof(sustainabilityScore), "Sustainability score must be between 0 and 100.");
            if (capacityUtilization.HasValue && (capacityUtilization < 0 || capacityUtilization > 100))
                throw new ArgumentOutOfRangeException(nameof(capacityUtilization), "Capacity utilization must be between 0 and 100.");

            bool changed = false;
            if (AverageLeadTimeDays != leadTime)
            {
                AverageLeadTimeDays = leadTime;
                changed = true;
            }
            if (SustainabilityScore != sustainabilityScore)
            {
                SustainabilityScore = sustainabilityScore;
                changed = true;
            }
            if (CapacityUtilizationPercent != capacityUtilization)
            {
                CapacityUtilizationPercent = capacityUtilization;
                changed = true;
            }

            if (changed) AddDomainEvent(new SupplierPerformanceMetricsUpdatedEvent(this.Id, leadTime, sustainabilityScore, capacityUtilization)); // Implemented event call
        }

        public void UpdateCompliance(bool isCsrCompliant)
        {
            if (IsCsrCompliant == isCsrCompliant) return;
            IsCsrCompliant = isCsrCompliant;
            AddDomainEvent(new SupplierComplianceUpdatedEvent(this.Id, isCsrCompliant)); // Implemented event call
        }

        public void SetContractManufacturer(bool isCm)
        {
            if (IsContractManufacturer == isCm) return;
            IsContractManufacturer = isCm;
            AddDomainEvent(new SupplierContractManufacturerStatusSetEvent(this.Id, isCm)); // Implemented event call
        }

        public void AddEmergencyContact(ValueObjects.ContactPerson contact)
        {
            ArgumentNullException.ThrowIfNull(contact);
            // Optional: Add validation to prevent duplicates
            if (EmergencyContacts.Any(c => c.Name == contact.Name && c.Email == contact.Email)) return;

            EmergencyContacts.Add(contact);
            AddDomainEvent(new SupplierEmergencyContactAddedEvent(this.Id, contact)); // Implemented event call
        }

        public void RemoveEmergencyContact(ValueObjects.ContactPerson contact)
        {
            ArgumentNullException.ThrowIfNull(contact);
            var contactToRemove = EmergencyContacts.FirstOrDefault(c => c.Name == contact.Name && c.Email == contact.Email);
            if (contactToRemove != null)
            {
                EmergencyContacts.Remove(contactToRemove);
                AddDomainEvent(new SupplierEmergencyContactRemovedEvent(this.Id, contact));
            }
        }

        public void ReplaceEmergencyContacts(List<ValueObjects.ContactPerson> contacts)
        {
            ArgumentNullException.ThrowIfNull(contacts);
            // Consider more sophisticated comparison if needed
            EmergencyContacts = new List<ValueObjects.ContactPerson>(contacts); // Replace entire list
            AddDomainEvent(new SupplierEmergencyContactsReplacedEvent(this.Id)); // Implemented event call
        }

        // Add other methods as needed...
    }

    // ContactPerson moved to ValueObjects namespace

    // --- Placeholder Domain Event Definitions (Place in Domain/Events) ---
    /*
    namespace ProcureToPay.Domain.Events
    {
        // Base class or marker interface for domain events might exist
        public record SupplierProfileCreatedEvent(Guid SupplierId, Guid VendorId);
        public record SupplierStatusUpdatedEvent(Guid SupplierId, SupplierStatus NewStatus);
        public record SupplierRiskRatingUpdatedEvent(Guid SupplierId, SupplierRiskRating? NewRating);
        public record SupplierPerformanceMetricsUpdatedEvent(Guid SupplierId, int? LeadTime, decimal? SustainabilityScore, decimal? CapacityUtilization);
        public record SupplierComplianceUpdatedEvent(Guid SupplierId, bool IsCsrCompliant);
        public record SupplierContractManufacturerStatusSetEvent(Guid SupplierId, bool IsContractManufacturer);
        public record SupplierEmergencyContactAddedEvent(Guid SupplierId, ContactPerson Contact);
        public record SupplierEmergencyContactRemovedEvent(Guid SupplierId, ContactPerson Contact);
        public record SupplierEmergencyContactsReplacedEvent(Guid SupplierId);
    }
    */
}
