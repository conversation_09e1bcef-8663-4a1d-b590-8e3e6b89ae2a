using System;
using System.ComponentModel.DataAnnotations;
using ProcureToPay.Domain.Entities; // Required for BaseEntity

namespace ProcureToPay.Domain.Entities;

/// <summary>
/// Represents a Tenant (customer organization) in a multi-tenant system.
/// Provides isolation for data belonging to different organizations.
/// </summary>
public class Tenant : BaseEntity<Guid>
{
    /// <summary>
    /// The official name of the tenant organization.
    /// </summary>
    [Required]
    [MaxLength(200)]
    public string Name { get; private set; } = string.Empty;

    /// <summary>
    /// A unique identifier or slug for the tenant, often used in URLs or lookups.
    /// Must be unique across all tenants. Should be validated for format (e.g., lowercase, no spaces).
    /// EF Core configuration should add a unique index on this property.
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string Identifier { get; private set; } = string.Empty;

    /// <summary>
    /// Indicates whether the tenant account is active and able to use the system.
    /// </summary>
    [Required]
    public bool IsActive { get; private set; }

    /// <summary>
    /// The subscription plan or tier the tenant is on (e.g., "Free", "Basic", "Enterprise").
    /// </summary>
    [MaxLength(50)]
    public string? SubscriptionPlan { get; private set; }

    /// <summary>
    /// Primary administrative contact email for the tenant.
    /// </summary>
    [MaxLength(254)]
    public string? ContactEmail { get; private set; }

    /// <summary>
    /// Optional field to store tenant-specific settings, potentially as JSON string.
    /// </summary>
    public string? Settings { get; private set; } // Consider a more structured approach if settings are complex

    // --- Address Properties ---
    // TODO: Consider replacing with Address Value Object.
    [MaxLength(200)]
    public string? AddressLine1 { get; private set; }
    [MaxLength(100)]
    public string? City { get; private set; }
    [MaxLength(20)]
    public string? PostalCode { get; private set; }
    [MaxLength(100)]
    public string? Country { get; private set; }

    // Note: Collections of tenant-specific data (Users, Vendors, POs etc.) 
    // are typically NOT placed here. Instead, those entities will have a TenantId 
    // foreign key pointing back to this Tenant.

    /// <summary>
    /// Private constructor for EF Core hydration.
    /// </summary>
    private Tenant() : base(Guid.Empty) { }

    /// <summary>
    /// Creates a new Tenant, initially inactive.
    /// </summary>
    /// <param name="name">The tenant's name.</param>
    /// <param name="identifier">The unique tenant identifier (slug).</param>
    /// <param name="subscriptionPlan">Optional subscription plan.</param>
    /// <param name="contactEmail">Optional contact email.</param>
    public Tenant(string name, string identifier, string? subscriptionPlan = null, string? contactEmail = null) : base(Guid.NewGuid())
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(name);
        ValidateAndSetIdentifier(identifier); // Use helper for identifier validation/setting

        Name = name;
        SubscriptionPlan = subscriptionPlan;
        ContactEmail = contactEmail; // TODO: Add email validation
        IsActive = false; // Tenants start inactive by default, require activation
    }

    // --- Domain Methods ---

    /// <summary>
    /// Activates the tenant account.
    /// </summary>
    public void Activate()
    {
        if (IsActive) return; // Already active
        IsActive = true;
        // TODO: Raise Domain Event? TenantActivatedEvent(this.Id)
    }

    /// <summary>
    /// Deactivates the tenant account.
    /// </summary>
    public void Deactivate()
    {
        if (!IsActive) return; // Already inactive
        IsActive = false;
        // TODO: Raise Domain Event? TenantDeactivatedEvent(this.Id)
    }

    /// <summary>
    /// Updates the tenant's name.
    /// </summary>
    public void UpdateName(string name)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(name);
        Name = name;
    }

    /// <summary>
    /// Updates the tenant's unique identifier (slug). Requires careful validation.
    /// </summary>
    public void UpdateIdentifier(string identifier)
    {
        // Consider if changing the identifier should be allowed, might break URLs etc.
        // If allowed, ensure uniqueness check happens (likely in Application/Infrastructure layer).
        ValidateAndSetIdentifier(identifier);
    }

    /// <summary>
    /// Updates the tenant's subscription plan.
    /// </summary>
    public void UpdateSubscription(string? subscriptionPlan)
    {
        // Add validation for allowed plan names?
        SubscriptionPlan = subscriptionPlan;
    }

    /// <summary>
    /// Updates the tenant's contact email.
    /// </summary>
    public void UpdateContactEmail(string? contactEmail)
    {
        // TODO: Add email validation
        ContactEmail = contactEmail;
    }

    /// <summary>
    /// Updates tenant-specific settings (example using JSON string).
    /// </summary>
    /// <param name="settingsJson">A JSON string representing settings.</param>
    public void UpdateSettings(string? settingsJson)
    {
        // TODO: Add JSON validation if possible/needed
        Settings = settingsJson;
    }

    /// <summary>
    /// Updates the tenant's address.
    /// </summary>
    public void UpdateAddress(string? line1, string? city, string? postal, string? country)
    {
        // TODO: Consider Address Value Object
        AddressLine1 = line1;
        City = city;
        PostalCode = postal;
        Country = country;
    }

    // --- Private Helper Methods ---

    private void ValidateAndSetIdentifier(string identifier)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(identifier);
        // Basic validation: lowercase, no spaces, maybe dashes allowed?
        // More robust validation might use Regex. Uniqueness check must happen externally.
        if (identifier.Any(char.IsUpper) || identifier.Any(char.IsWhiteSpace))
        {
            throw new ArgumentException("Tenant identifier must be lowercase with no spaces.", nameof(identifier));
        }
        Identifier = identifier;
    }
}
