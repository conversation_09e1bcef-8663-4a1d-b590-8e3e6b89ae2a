using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using ProcureToPay.Domain.Entities; // Required for BaseEntity and related entities
using ProcureToPay.Domain.Enums; // Required for DeliveryNoteStatus
using ProcureToPay.Domain.ValueObjects; // Required for Address and CarrierInformation

namespace ProcureToPay.Domain.Entities;

/// <summary>
/// Represents a Delivery Note issued by a Vendor accompanying a shipment of goods,
/// usually referencing a Purchase Order.
/// </summary>
public class DeliveryNote : BaseEntity<Guid>
{
    /// <summary>
    /// Tenant identifier for multi-tenancy.
    /// </summary>
    [Required]
    public Guid TenantId { get; private set; }

    /// <summary>
    /// The delivery note number provided by the vendor/shipper.
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string DeliveryNoteNumber { get; private set; } = string.Empty;

    /// <summary>
    /// Current status of the delivery note.
    /// </summary>
    [Required]
    public DeliveryNoteStatus Status { get; private set; }

    /// <summary>
    /// Foreign Key to the Purchase Order this delivery relates to.
    /// </summary>
    public Guid? PurchaseOrderId { get; private set; }

    /// <summary>
    /// Navigation property to the related Purchase Order. Virtual for lazy loading.
    /// </summary>
    public virtual PurchaseOrder PurchaseOrder { get; private set; } = null!;

    /// <summary>
    /// Foreign Key to the Vendor who sent the shipment.
    /// </summary>
    [Required]
    public Guid VendorId { get; private set; }

    /// <summary>
    /// Navigation property to the sending Vendor. Virtual for lazy loading.
    /// </summary>
    public virtual Vendor Vendor { get; private set; } = null!;

    /// <summary>
    /// Optional Foreign Key to a related Sales Order.
    /// </summary>
    public Guid? SalesOrderId { get; private set; }

    /// <summary>
    /// Optional Navigation property to a related Sales Order.
    /// </summary>
    public virtual SalesOrder? SalesOrder { get; private set; }

    /// <summary>
    /// The date the goods were shipped by the vendor.
    /// </summary>
    public DateTime? ShipmentDate { get; private set; }

    /// <summary>
    /// The date the goods were actually received/delivered. Null until received.
    /// </summary>
    public DateTime? DeliveryDate { get; private set; }

    /// <summary>
    /// Name or identifier of the person who received the delivery. Null until received.
    /// </summary>
    [MaxLength(150)]
    public string? ReceivedBy { get; private set; }

    /// <summary>
    /// Shipping carrier details (Value Object).
    /// </summary>
    public CarrierInformation? CarrierInfo { get; private set; }

    /// <summary>
    /// Shipping Address details (Value Object).
    /// </summary>
    public Address? ShippingAddress { get; private set; }

    /// <summary>
    /// Billing Address details (Value Object).
    /// </summary>
    public Address? BillingAddress { get; private set; }

    /// <summary>
    /// Optional notes related to the delivery (e.g., condition of goods, partial shipment info).
    /// </summary>
    public string? Notes { get; private set; }

    // Collection of line items detailing products and quantities delivered. Encapsulated list.
    private readonly List<DeliveryNoteLine> _lines = new();
    public virtual IReadOnlyCollection<DeliveryNoteLine> Lines => _lines.AsReadOnly();

    /// <summary>
    /// Private constructor for EF Core hydration.
    /// </summary>
    private DeliveryNote() : base(Guid.Empty) { }

    /// <summary>
    /// Creates a new Delivery Note.
    /// </summary>
    public DeliveryNote(
        Guid tenantId,
        string deliveryNoteNumber,
        Guid vendorId,
        Guid? purchaseOrderId = null,
        DateTime? shipmentDate = null,
        Guid? salesOrderId = null,
        CarrierInformation? carrierInfo = null,
        Address? shippingAddress = null,
        Address? billingAddress = null,
        string? notes = null
        ) : base(Guid.NewGuid())
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(deliveryNoteNumber);
        if (tenantId == Guid.Empty) throw new ArgumentException("TenantId cannot be empty.", nameof(tenantId));
        if (purchaseOrderId == Guid.Empty) throw new ArgumentException("PurchaseOrderId cannot be empty.", nameof(purchaseOrderId));
        if (vendorId == Guid.Empty) throw new ArgumentException("VendorId cannot be empty.", nameof(vendorId));

        TenantId = tenantId;
        DeliveryNoteNumber = deliveryNoteNumber;
        PurchaseOrderId = purchaseOrderId;
        VendorId = vendorId;
        SalesOrderId = salesOrderId;
        ShipmentDate = shipmentDate;
        CarrierInfo = carrierInfo;
        ShippingAddress = shippingAddress;
        BillingAddress = billingAddress;
        Notes = notes;
        Status = DeliveryNoteStatus.Pending;
        DeliveryDate = null; // Not delivered upon creation
        ReceivedBy = null;
    }

    // --- Domain Methods ---

    /// <summary>
    /// Adds a line item to the delivery note.
    /// </summary>
    public void AddLine(int lineNumber, Guid? purchaseOrderLineId, Guid productId, string productSkuSnapshot, string description, decimal quantityShipped, string unitOfMeasure, string? batchNumber = null, string? serialNumber = null, Guid? salesOrderLineId = null)
    {
        if (Status == DeliveryNoteStatus.Received || Status == DeliveryNoteStatus.Cancelled)
            throw new InvalidOperationException($"Cannot add lines when delivery note status is {Status}.");

        // TODO: Optionally check if quantityShipped exceeds remaining quantity on the PO line?

        var newLine = new DeliveryNoteLine(this.Id, purchaseOrderLineId, productId, productSkuSnapshot, description, quantityShipped, lineNumber, unitOfMeasure, batchNumber, serialNumber, salesOrderLineId, null);
        _lines.Add(newLine);
    }

    /// <summary>
    /// Marks the delivery note as received.
    /// </summary>
    /// <param name="deliveryDate">The date the delivery was received.</param>
    /// <param name="receivedBy">Name/ID of the person receiving.</param>
    /// <param name="notes">Optional receiving notes.</param>
    public void MarkAsReceived(DateTime deliveryDate, string receivedBy, string? notes = null)
    {
        if (Status == DeliveryNoteStatus.Received || Status == DeliveryNoteStatus.Cancelled)
            throw new InvalidOperationException($"Delivery note status is already {Status}.");
        if (ShipmentDate.HasValue && deliveryDate < ShipmentDate.Value)
            throw new ArgumentOutOfRangeException(nameof(deliveryDate), "Delivery date cannot be before shipment date.");
        ArgumentException.ThrowIfNullOrWhiteSpace(receivedBy);

        DeliveryDate = deliveryDate;
        ReceivedBy = receivedBy;
        Status = DeliveryNoteStatus.Received;

        if (!string.IsNullOrWhiteSpace(notes))
        {
            Notes = string.IsNullOrWhiteSpace(Notes) ? notes : $"{Notes}\nReceived Notes: {notes}";
        }
        // TODO: Raise Domain Event? DeliveryReceivedEvent(this.Id)
    }

    /// <summary>
    /// Updates shipping information using the Value Object.
    /// </summary>
    public void UpdateShippingInfo(CarrierInformation? carrierInfo)
    {
        if (Status == DeliveryNoteStatus.Received || Status == DeliveryNoteStatus.Cancelled)
            throw new InvalidOperationException($"Cannot update shipping info when delivery note status is {Status}.");

        CarrierInfo = carrierInfo;

        // If status was Pending, update to Shipped if carrier info is provided
        if (Status == DeliveryNoteStatus.Pending && carrierInfo is not null && !string.IsNullOrWhiteSpace(carrierInfo.Name))
        {
            Status = DeliveryNoteStatus.Shipped;
        }
    }

    /// <summary>
    /// Updates the shipping address.
    /// </summary>
    public void UpdateShippingAddress(Address? shippingAddress)
    {
        if (Status == DeliveryNoteStatus.Received || Status == DeliveryNoteStatus.Cancelled)
            throw new InvalidOperationException($"Cannot update shipping address when delivery note status is {Status}.");
        ShippingAddress = shippingAddress;
    }

    /// <summary>
    /// Updates the billing address.
    /// </summary>
    public void UpdateBillingAddress(Address? billingAddress)
    {
        if (Status == DeliveryNoteStatus.Received || Status == DeliveryNoteStatus.Cancelled)
            throw new InvalidOperationException($"Cannot update billing address when delivery note status is {Status}.");
        BillingAddress = billingAddress;
    }

    /// <summary>
    /// Marks the delivery note as shipped.
    /// </summary>
    public void MarkAsShipped()
    {
        if (Status != DeliveryNoteStatus.Pending)
            throw new InvalidOperationException($"Cannot mark as shipped when status is {Status}.");
        Status = DeliveryNoteStatus.Shipped;
        // TODO: Raise Domain Event? DeliveryShippedEvent(this.Id)
    }

    /// <summary>
    /// Cancels the delivery note.
    /// </summary>
    public void Cancel(string reason)
    {
        if (Status == DeliveryNoteStatus.Received)
            throw new InvalidOperationException($"Cannot cancel a received delivery note.");
        ArgumentException.ThrowIfNullOrWhiteSpace(reason);

        Status = DeliveryNoteStatus.Cancelled;
        Notes = string.IsNullOrWhiteSpace(Notes) ? $"Cancelled: {reason}" : $"{Notes}\nCancelled: {reason}";
        // TODO: Raise Domain Event? DeliveryCancelledEvent(this.Id)
    }
}
