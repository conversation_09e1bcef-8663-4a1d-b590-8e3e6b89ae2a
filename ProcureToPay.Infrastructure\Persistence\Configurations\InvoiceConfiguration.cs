using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProcureToPay.Domain.Entities;
using ProcureToPay.Domain.Enums; // Assuming InvoiceStatus enum exists
using ProcureToPay.Infrastructure.Persistence.Extensions;

namespace ProcureToPay.Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// Configuration for the Invoice entity for EF Core and PostgreSQL.
    /// </summary>
    public class InvoiceConfiguration : IEntityTypeConfiguration<Invoice>
    {
        public void Configure(EntityTypeBuilder<Invoice> builder)
        {
            // Table Mapping
            builder.ToTable("invoices");

            // Primary Key
            builder.HasKey(i => i.Id);
            builder.Property(i => i.Id).ValueGeneratedOnAdd();

            // Properties
            builder.Property(i => i.InvoiceNumber)
                .IsRequired()
                .HasMaxLength(100); // Adjust length as needed

            builder.Property(i => i.InvoiceDate)
                .IsRequired()
                .HasColumnType("timestamp without time zone"); // Or "date"

            builder.Property(i => i.DueDate)
                .IsRequired()
                .HasColumnType("timestamp without time zone"); // Or "date"

            builder.Property(i => i.PaymentDate)
                .HasColumnType("timestamp without time zone") // Or "date"
                .IsRequired(false); // Null until paid

            builder.Property(i => i.Status)
                .IsRequired()
                .HasConversion<string>() // Or int
                .HasMaxLength(50);

            builder.Property(i => i.Subtotal)
                .HasColumnType("numeric(18, 2)")
                .IsRequired();

            builder.Property(i => i.TaxAmount)
                .HasColumnType("numeric(18, 2)")
                .IsRequired()
                .HasDefaultValue(0m);

            builder.Property(i => i.TotalAmount)
                .HasColumnType("numeric(18, 2)")
                .IsRequired();

            builder.Property(i => i.PaymentTerms)
                .HasMaxLength(255)
                .IsRequired(false);

            // Foreign Keys (Assuming Guid or appropriate types)
            builder.Property(i => i.VendorId).IsRequired();
            builder.Property(i => i.PurchaseOrderId).IsRequired(false); // Invoice might not link directly to PO

            builder.Property(i => i.TenantId).IsRequired();


            // --- Concurrency Token ---
            builder.UseXminAsConcurrencyToken();


            // --- Relationships ---
            // Header-Lines (One-to-Many)
            builder.HasMany(i => i.Lines)
                   .WithOne(l => l.Invoice) // Assuming InvoiceLine has Invoice nav prop
                   .HasForeignKey(l => l.InvoiceId)
                   .IsRequired()
                   .OnDelete(DeleteBehavior.Cascade); // Deleting header deletes lines

            // Link to Vendor (Many-to-One)
            builder.HasOne(i => i.Vendor)
                   .WithMany(v => v.Invoices) // Assuming Vendor has collection
                   .HasForeignKey(i => i.VendorId)
                   .IsRequired()
                   .OnDelete(DeleteBehavior.Restrict); // Prevent deleting vendor if invoices exist

            // Link to PO (Many-to-One, Optional)
            builder.HasOne(i => i.PurchaseOrder)
                   .WithMany(po => po.Invoices) // Assuming PO has collection
                   .HasForeignKey(i => i.PurchaseOrderId)
                   .IsRequired(false)
                   .OnDelete(DeleteBehavior.Restrict); // Or SetNull? Review rule.

            // Link to Payments (One-to-Many or Many-to-Many via Join Table)
            // Assuming One Invoice -> Many PaymentTransactions directly linked
            builder.HasMany(i => i.PaymentTransactions)
                   .WithOne(pt => pt.Invoice) // Assuming PaymentTransaction has Invoice nav prop
                   .HasForeignKey(pt => pt.InvoiceId)
                   .IsRequired(false) // A payment might not be linked to a single invoice initially?
                   .OnDelete(DeleteBehavior.SetNull); // If invoice deleted, nullify link in payment (or Restrict?)
            // If payments can apply to multiple invoices, a join entity is needed.


            // --- Indexes ---
            // Unique constraint for Vendor + Invoice Number
            builder.HasIndex(i => new { i.VendorId, i.InvoiceNumber }).IsUnique();
            builder.HasIndex(i => i.Status);
            builder.HasIndex(i => i.InvoiceDate);
            builder.HasIndex(i => i.DueDate);
            builder.HasIndex(i => i.VendorId); // Included in composite index, but potentially useful alone
            builder.HasIndex(i => i.PurchaseOrderId);
            builder.HasIndex(i => i.TenantId);


            // --- TODO ---
            // TODO: Verify FK types (VendorId, PurchaseOrderId) match related entities.
            // TODO: Confirm relationship between Invoice and PaymentTransaction (1:M or M:M) and adjust config.
            // TODO: Consider if InvoiceNumber should be unique per Tenant instead of per Vendor.
        }
    }
}
