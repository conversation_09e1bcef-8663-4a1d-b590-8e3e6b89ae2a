using Microsoft.AspNetCore.Http;
using System.Security.Claims;
using ProcureToPay.Domain.Interfaces;
using System;

namespace ProcureToPay.Infrastructure.Persistence
{
    /// <summary>
    /// Strategy for resolving the current tenant ID in different application contexts.
    /// </summary>
    public class TenantResolutionStrategy
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private static readonly Guid DefaultTenantId = Guid.Parse("11111111-1111-1111-1111-111111111111");

        // Claim type for tenant ID in user claims
        private const string TenantIdClaimType = "tenant_id";

        // Header name for tenant ID in request headers
        private const string TenantIdHeaderName = "X-TenantId";

        /// <summary>
        /// Initializes a new instance of the <see cref="TenantResolutionStrategy"/> class.
        /// </summary>
        /// <param name="httpContextAccessor">The HTTP context accessor.</param>
        public TenantResolutionStrategy(IHttpContextAccessor httpContextAccessor)
        {
            _httpContextAccessor = httpContextAccessor;
        }

        /// <summary>
        /// Gets the current tenant ID based on the available context.
        /// </summary>
        /// <returns>The current tenant ID, or the default tenant ID if no context is available.</returns>
        public Guid? GetCurrentTenantId()
        {
            // If HttpContext is available, try to get tenant from it
            if (_httpContextAccessor.HttpContext != null)
            {
                // Try to get tenant ID from user claims
                var tenantIdFromClaims = GetTenantIdFromClaims();
                if (tenantIdFromClaims.HasValue)
                {
                    return tenantIdFromClaims.Value;
                }

                // Try to get tenant ID from request headers
                var tenantIdFromHeaders = GetTenantIdFromHeaders();
                if (tenantIdFromHeaders.HasValue)
                {
                    return tenantIdFromHeaders.Value;
                }

                // Try to get tenant ID from route values
                var tenantIdFromRoute = GetTenantIdFromRoute();
                if (tenantIdFromRoute.HasValue)
                {
                    return tenantIdFromRoute.Value;
                }

                // If all resolution strategies fail, return the default tenant ID
                return DefaultTenantId;
            }

            // If no HttpContext is available (background processing, migrations, etc.),
            // return the default tenant ID
            return DefaultTenantId;
        }

        /// <summary>
        /// Gets the tenant ID from user claims.
        /// </summary>
        /// <returns>The tenant ID if found in claims, otherwise null.</returns>
        private Guid? GetTenantIdFromClaims()
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext?.User?.Identity?.IsAuthenticated != true)
            {
                return null;
            }

            var tenantClaim = httpContext.User.FindFirst(TenantIdClaimType);
            if (tenantClaim != null && Guid.TryParse(tenantClaim.Value, out var tenantId))
            {
                return tenantId;
            }

            return null;
        }

        /// <summary>
        /// Gets the tenant ID from request headers.
        /// </summary>
        /// <returns>The tenant ID if found in headers, otherwise null.</returns>
        private Guid? GetTenantIdFromHeaders()
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext == null)
            {
                return null;
            }

            if (httpContext.Request.Headers.TryGetValue(TenantIdHeaderName, out var tenantIdHeader) &&
                Guid.TryParse(tenantIdHeader, out var tenantId))
            {
                return tenantId;
            }

            return null;
        }

        /// <summary>
        /// Gets the tenant ID from route values.
        /// </summary>
        /// <returns>The tenant ID if found in route values, otherwise null.</returns>
        private Guid? GetTenantIdFromRoute()
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext == null)
            {
                return null;
            }

            // In ASP.NET Core, route values are typically accessed through HttpContext.GetRouteValue
            // or through controller context. For this simplified implementation, we'll just check
            // if there's a path segment that might indicate a tenant.

            var path = httpContext.Request.Path.Value;
            if (!string.IsNullOrEmpty(path))
            {
                // Simple check for a tenant identifier in the path
                // In a real application, you would have a more sophisticated approach
                var segments = path.Split('/', StringSplitOptions.RemoveEmptyEntries);
                if (segments.Length > 0 && segments[0].Equals("tenant", StringComparison.OrdinalIgnoreCase) &&
                    segments.Length > 1)
                {
                    string tenantIdentifier = segments[1];
                    // In a real application, you would look up the tenant ID by identifier in the database
                    // For now, we'll just return the default tenant ID if the identifier is not empty
                    if (!string.IsNullOrEmpty(tenantIdentifier))
                    {
                        return DefaultTenantId;
                    }
                }
            }

            return null;
        }
    }
}
