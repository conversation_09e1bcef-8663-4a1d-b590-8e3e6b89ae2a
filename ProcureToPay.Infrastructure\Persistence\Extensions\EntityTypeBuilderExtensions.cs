using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace ProcureToPay.Infrastructure.Persistence.Extensions
{
    /// <summary>
    /// Extension methods for EntityTypeBuilder to simplify common configuration patterns.
    /// </summary>
    public static class EntityTypeBuilderExtensions
    {
        /// <summary>
        /// Configures the entity to use PostgreSQL's xmin system column for optimistic concurrency control.
        /// </summary>
        /// <typeparam name="TEntity">The entity type being configured.</typeparam>
        /// <param name="builder">The entity type builder.</param>
        /// <returns>The entity type builder for method chaining.</returns>
        public static EntityTypeBuilder<TEntity> UseXminAsConcurrencyToken<TEntity>(this EntityTypeBuilder<TEntity> builder)
            where TEntity : class
        {
            builder.Property<uint>("xmin")
                   .HasColumnType("xid")
                   .ValueGeneratedOnAddOrUpdate()
                   .IsConcurrencyToken();

            return builder;
        }
    }
}
