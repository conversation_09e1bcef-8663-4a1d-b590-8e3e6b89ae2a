﻿CREATE TABLE IF NOT EXISTS "__EFMigrationsHistory" (
    "MigrationId" character varying(150) NOT NULL,
    "ProductVersion" character varying(32) NOT NULL,
    CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY ("MigrationId")
);

START TRANSACTION;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE TABLE "AspNetRoles" (
        "Id" text NOT NULL,
        "Name" character varying(256),
        "NormalizedName" character varying(256),
        "ConcurrencyStamp" text,
        CONSTRAINT "PK_AspNetRoles" PRIMARY KEY ("Id")
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE TABLE "AspNetUsers" (
        "Id" text NOT NULL,
        "UserName" character varying(256),
        "NormalizedUserName" character varying(256),
        "Email" character varying(256),
        "NormalizedEmail" character varying(256),
        "EmailConfirmed" boolean NOT NULL,
        "PasswordHash" text,
        "SecurityStamp" text,
        "ConcurrencyStamp" text,
        "PhoneNumber" text,
        "PhoneNumberConfirmed" boolean NOT NULL,
        "TwoFactorEnabled" boolean NOT NULL,
        "LockoutEnd" timestamp with time zone,
        "LockoutEnabled" boolean NOT NULL,
        "AccessFailedCount" integer NOT NULL,
        CONSTRAINT "PK_AspNetUsers" PRIMARY KEY ("Id")
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE TABLE public.budgets (
        "Id" uuid NOT NULL,
        "TenantId" uuid NOT NULL,
        "Name" character varying(200) NOT NULL,
        "Description" text,
        "FiscalYear" integer NOT NULL,
        "StartDate" timestamp without time zone NOT NULL,
        "EndDate" timestamp without time zone NOT NULL,
        baseline_amount numeric(18,4) NOT NULL,
        forecast_amount numeric(18,4),
        "CurrencyCode" character varying(3) NOT NULL,
        "Status" character varying(50) NOT NULL,
        "Version" integer NOT NULL DEFAULT 1,
        "IsRollingForecast" boolean NOT NULL DEFAULT FALSE,
        "ForecastPeriodsJson" jsonb,
        "AllocationRulesJson" jsonb,
        "WorkflowInstanceId" uuid,
        "CreatedById" character varying(450) NOT NULL,
        "ApprovedById" character varying(450),
        "IsDeleted" boolean NOT NULL DEFAULT FALSE,
        "CreatedAt" timestamp with time zone NOT NULL,
        "ModifiedAt" timestamp with time zone,
        CONSTRAINT "PK_budgets" PRIMARY KEY ("Id")
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE TABLE "Categories" (
        "Id" uuid NOT NULL,
        "Name" character varying(200) NOT NULL,
        "Description" character varying(1000),
        "Code" character varying(50),
        "UnspscCode" character varying(20),
        "ParentCategoryId" uuid,
        "CreatedAt" timestamp with time zone NOT NULL,
        "ModifiedAt" timestamp with time zone,
        CONSTRAINT "PK_Categories" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_Categories_Categories_ParentCategoryId" FOREIGN KEY ("ParentCategoryId") REFERENCES "Categories" ("Id") ON DELETE RESTRICT
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE TABLE public.customers (
        "Id" uuid NOT NULL,
        "TenantId" uuid NOT NULL,
        customer_code character varying(50) NOT NULL,
        name text NOT NULL,
        legal_name text,
        tax_identifier character varying(50),
        is_active boolean NOT NULL DEFAULT TRUE,
        credit_limit_amount numeric(18,2),
        credit_limit_currency_code character varying(3),
        default_payment_terms character varying(100),
        default_currency_code character varying(3),
        website text,
        customer_type character varying(50) NOT NULL,
        billing_street character varying(255) NOT NULL,
        billing_city character varying(100) NOT NULL,
        billing_state character varying(100) NOT NULL,
        billing_country character varying(100) NOT NULL,
        billing_postal_code character varying(20) NOT NULL,
        shipping_street character varying(255) NOT NULL,
        shipping_city character varying(100) NOT NULL,
        shipping_state character varying(100) NOT NULL,
        shipping_country character varying(100) NOT NULL,
        shipping_postal_code character varying(20) NOT NULL,
        primary_contact_name character varying(150) NOT NULL,
        primary_contact_role character varying(100),
        primary_contact_email character varying(254),
        primary_contact_phone character varying(50),
        "AssignedSalesRepId" character varying(450),
        "IsDeleted" boolean NOT NULL DEFAULT FALSE,
        "CreatedAt" timestamp with time zone NOT NULL,
        "ModifiedAt" timestamp with time zone,
        CONSTRAINT "PK_customers" PRIMARY KEY ("Id")
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE TABLE public.departments (
        "Id" uuid NOT NULL,
        "TenantId" uuid NOT NULL,
        "Name" character varying(200) NOT NULL,
        "Code" character varying(50),
        "Description" text,
        "CreatedAt" timestamp with time zone NOT NULL,
        "ModifiedAt" timestamp with time zone,
        CONSTRAINT "PK_departments" PRIMARY KEY ("Id")
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE TABLE "DocumentLink" (
        "Name" character varying(255) NOT NULL,
        "Url" character varying(1000) NOT NULL,
        "DocumentType" character varying(100) NOT NULL,
        "Description" character varying(500)
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE TABLE procurement_workflows (
        "Id" uuid NOT NULL,
        "TenantId" uuid NOT NULL,
        "WorkflowName" character varying(150) NOT NULL,
        "Name" character varying(150) NOT NULL,
        "Description" text,
        "SubjectDocumentId" uuid NOT NULL,
        "SubjectDocumentType" character varying(50) NOT NULL,
        "WorkflowType" character varying(50) NOT NULL,
        "IsActive" boolean NOT NULL DEFAULT TRUE,
        "Version" integer,
        "CurrentStatus" integer NOT NULL,
        "StartedDate" timestamp with time zone NOT NULL,
        "CompletedDate" timestamp with time zone,
        "InitiatedByUserId" character varying(450),
        "InitiatedByName" character varying(150),
        "CreatedAt" timestamp with time zone NOT NULL,
        "ModifiedAt" timestamp with time zone,
        CONSTRAINT "PK_procurement_workflows" PRIMARY KEY ("Id")
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE TABLE public.projects (
        "Id" uuid NOT NULL,
        "TenantId" uuid NOT NULL,
        "ProjectCode" character varying(50) NOT NULL,
        "Name" character varying(200) NOT NULL,
        "Description" character varying(2000),
        "Status" character varying(50) NOT NULL,
        "StartDate" timestamp with time zone,
        "EndDate" timestamp with time zone,
        "BudgetAmount" numeric(18,2),
        "BudgetCurrencyCode" character varying(3),
        "IsDeleted" boolean NOT NULL DEFAULT FALSE,
        "CreatedAt" timestamp with time zone NOT NULL,
        "ModifiedAt" timestamp with time zone,
        CONSTRAINT "PK_projects" PRIMARY KEY ("Id")
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE TABLE sales_territories (
        "Id" uuid NOT NULL,
        "TenantId" uuid NOT NULL,
        "Name" character varying(150) NOT NULL,
        "Code" text,
        "TerritoryCode" character varying(50) NOT NULL,
        "ParentTerritoryId" uuid,
        "Description" text,
        "PrimarySalespersonId" text,
        "IsDeleted" boolean NOT NULL,
        "CreatedAt" timestamp with time zone NOT NULL,
        "ModifiedAt" timestamp with time zone,
        CONSTRAINT "PK_sales_territories" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_sales_territories_sales_territories_ParentTerritoryId" FOREIGN KEY ("ParentTerritoryId") REFERENCES sales_territories ("Id") ON DELETE RESTRICT
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE TABLE tenant_products (
        "Id" uuid NOT NULL,
        "Name" character varying(200) NOT NULL,
        "SKU" character varying(50),
        "Description" character varying(1000),
        "Price" numeric(18,2) NOT NULL,
        "TenantId" uuid NOT NULL,
        "CreatedAt" timestamp with time zone NOT NULL,
        "ModifiedAt" timestamp with time zone,
        CONSTRAINT "PK_tenant_products" PRIMARY KEY ("Id")
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE TABLE "Tenants" (
        "Id" uuid NOT NULL,
        "Name" character varying(200) NOT NULL,
        "Identifier" character varying(100) NOT NULL,
        "IsActive" boolean NOT NULL,
        "SubscriptionPlan" character varying(50),
        "ContactEmail" character varying(254),
        "Settings" text,
        "AddressLine1" character varying(200),
        "City" character varying(100),
        "PostalCode" character varying(20),
        "Country" character varying(100),
        "CreatedAt" timestamp with time zone NOT NULL,
        "ModifiedAt" timestamp with time zone,
        CONSTRAINT "PK_Tenants" PRIMARY KEY ("Id")
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE TABLE test_entities (
        "Id" uuid NOT NULL,
        "Name" character varying(200) NOT NULL,
        "TenantId" uuid NOT NULL,
        CONSTRAINT "PK_test_entities" PRIMARY KEY ("Id")
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE TABLE "Vendors" (
        "Id" uuid NOT NULL,
        "Name" character varying(250) NOT NULL,
        "VendorCode" character varying(50),
        "Status" character varying(50) NOT NULL DEFAULT 'Pending',
        "VatNumber" character varying(50),
        "CommercialRegistrationNumber" character varying(50),
        "TaxId" character varying(50),
        "ContactName" character varying(150),
        "ContactEmail" character varying(254),
        "PhoneNumber" character varying(50),
        "Website" character varying(500),
        "PrimaryAddress_Street" character varying(200) NOT NULL,
        "PrimaryAddress_City" character varying(100) NOT NULL,
        "PrimaryAddress_State" character varying(100) NOT NULL,
        "PrimaryAddress_Country" character varying(100) NOT NULL,
        "PrimaryAddress_PostalCode" character varying(20) NOT NULL,
        "CreatedAt" timestamp with time zone NOT NULL,
        "ModifiedAt" timestamp with time zone,
        CONSTRAINT "PK_Vendors" PRIMARY KEY ("Id")
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE TABLE "AspNetRoleClaims" (
        "Id" integer GENERATED BY DEFAULT AS IDENTITY,
        "RoleId" text NOT NULL,
        "ClaimType" text,
        "ClaimValue" text,
        CONSTRAINT "PK_AspNetRoleClaims" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_AspNetRoleClaims_AspNetRoles_RoleId" FOREIGN KEY ("RoleId") REFERENCES "AspNetRoles" ("Id") ON DELETE CASCADE
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE TABLE "AspNetUserClaims" (
        "Id" integer GENERATED BY DEFAULT AS IDENTITY,
        "UserId" text NOT NULL,
        "ClaimType" text,
        "ClaimValue" text,
        CONSTRAINT "PK_AspNetUserClaims" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_AspNetUserClaims_AspNetUsers_UserId" FOREIGN KEY ("UserId") REFERENCES "AspNetUsers" ("Id") ON DELETE CASCADE
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE TABLE "AspNetUserLogins" (
        "LoginProvider" text NOT NULL,
        "ProviderKey" text NOT NULL,
        "ProviderDisplayName" text,
        "UserId" text NOT NULL,
        CONSTRAINT "PK_AspNetUserLogins" PRIMARY KEY ("LoginProvider", "ProviderKey"),
        CONSTRAINT "FK_AspNetUserLogins_AspNetUsers_UserId" FOREIGN KEY ("UserId") REFERENCES "AspNetUsers" ("Id") ON DELETE CASCADE
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE TABLE "AspNetUserRoles" (
        "UserId" text NOT NULL,
        "RoleId" text NOT NULL,
        CONSTRAINT "PK_AspNetUserRoles" PRIMARY KEY ("UserId", "RoleId"),
        CONSTRAINT "FK_AspNetUserRoles_AspNetRoles_RoleId" FOREIGN KEY ("RoleId") REFERENCES "AspNetRoles" ("Id") ON DELETE CASCADE,
        CONSTRAINT "FK_AspNetUserRoles_AspNetUsers_UserId" FOREIGN KEY ("UserId") REFERENCES "AspNetUsers" ("Id") ON DELETE CASCADE
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE TABLE "AspNetUserTokens" (
        "UserId" text NOT NULL,
        "LoginProvider" text NOT NULL,
        "Name" text NOT NULL,
        "Value" text,
        CONSTRAINT "PK_AspNetUserTokens" PRIMARY KEY ("UserId", "LoginProvider", "Name"),
        CONSTRAINT "FK_AspNetUserTokens_AspNetUsers_UserId" FOREIGN KEY ("UserId") REFERENCES "AspNetUsers" ("Id") ON DELETE CASCADE
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE TABLE "ProductDefinitions" (
        "Id" uuid NOT NULL,
        "Name" character varying(250) NOT NULL,
        "Description" character varying(2000),
        "Sku" character varying(100) NOT NULL,
        "Gtin" character varying(14),
        "Upc" character varying(12),
        "Ean" character varying(13),
        "LifecycleState" character varying(50) NOT NULL DEFAULT 'Active',
        "CategoryId" uuid,
        "IsDeleted" boolean NOT NULL DEFAULT FALSE,
        "Version" integer NOT NULL DEFAULT 1,
        "AttributesJson" jsonb,
        "TenantId" uuid,
        "CreatedAt" timestamp with time zone NOT NULL,
        "ModifiedAt" timestamp with time zone,
        CONSTRAINT "PK_ProductDefinitions" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_ProductDefinitions_Categories_CategoryId" FOREIGN KEY ("CategoryId") REFERENCES "Categories" ("Id") ON DELETE SET NULL
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE TABLE public.products (
        "Id" uuid NOT NULL,
        "Name" character varying(255) NOT NULL,
        "ProductCode" character varying(50) NOT NULL,
        "Description" text,
        "UnitOfMeasure" character varying(20) NOT NULL,
        "IsActive" boolean NOT NULL DEFAULT TRUE,
        "IsDeleted" boolean NOT NULL DEFAULT FALSE,
        "CategoryId" uuid,
        "TenantId" uuid NOT NULL,
        "CreatedAt" timestamp with time zone NOT NULL,
        "ModifiedAt" timestamp with time zone,
        CONSTRAINT "PK_products" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_products_Categories_CategoryId" FOREIGN KEY ("CategoryId") REFERENCES "Categories" ("Id") ON DELETE SET NULL
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE TABLE public.budget_allocations (
        "Id" uuid NOT NULL,
        "BudgetId" uuid NOT NULL,
        "TenantId" uuid NOT NULL,
        "DepartmentId" uuid NOT NULL,
        allocated_amount numeric(18,2) NOT NULL,
        allocated_currency_code character varying(3) NOT NULL,
        consumed_amount numeric(18,2) NOT NULL DEFAULT 0.0,
        consumed_currency_code character varying(3) NOT NULL,
        "AllocationDate" timestamp without time zone NOT NULL,
        "FiscalPeriodIdentifier" character varying(50),
        "Description" text,
        "Status" character varying(50) NOT NULL,
        "CreatedAt" timestamp with time zone NOT NULL,
        "ModifiedAt" timestamp with time zone,
        CONSTRAINT "PK_budget_allocations" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_budget_allocations_budgets_BudgetId" FOREIGN KEY ("BudgetId") REFERENCES public.budgets ("Id") ON DELETE RESTRICT,
        CONSTRAINT "FK_budget_allocations_departments_DepartmentId" FOREIGN KEY ("DepartmentId") REFERENCES public.departments ("Id") ON DELETE RESTRICT
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE TABLE procurement_workflow_steps (
        "Id" uuid NOT NULL,
        "WorkflowId" uuid NOT NULL,
        "ProcurementWorkflowId" uuid NOT NULL,
        "StepName" character varying(150) NOT NULL,
        "SequenceOrder" integer NOT NULL,
        "StepOrder" integer NOT NULL,
        "Status" integer NOT NULL,
        "AssigneeUserId" character varying(450),
        "ApproverRoleId" uuid,
        "ApproverUserId" character varying(450),
        "AssigneeName" character varying(150),
        "AssignedDate" timestamp with time zone,
        "ActionDate" timestamp with time zone,
        "Comments" text,
        "ConditionExpression" text,
        "SlaDuration" interval,
        "CreatedAt" timestamp with time zone NOT NULL,
        "ModifiedAt" timestamp with time zone,
        CONSTRAINT "PK_procurement_workflow_steps" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_procurement_workflow_steps_AspNetUsers_ApproverUserId" FOREIGN KEY ("ApproverUserId") REFERENCES "AspNetUsers" ("Id") ON DELETE RESTRICT,
        CONSTRAINT "FK_procurement_workflow_steps_procurement_workflows_Procuremen~" FOREIGN KEY ("ProcurementWorkflowId") REFERENCES procurement_workflows ("Id") ON DELETE CASCADE,
        CONSTRAINT "FK_procurement_workflow_steps_procurement_workflows_WorkflowId" FOREIGN KEY ("WorkflowId") REFERENCES procurement_workflows ("Id") ON DELETE CASCADE
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE TABLE requests_for_information (
        "Id" uuid NOT NULL,
        "RfiNumber" character varying(100) NOT NULL,
        "Title" character varying(255) NOT NULL,
        "Description" text,
        "TenantId" uuid NOT NULL,
        "IssuedDate" timestamp with time zone,
        "IssueDate" timestamp without time zone NOT NULL,
        "ResponseDueDate" timestamp with time zone NOT NULL,
        "ResponseDeadline" timestamp without time zone NOT NULL,
        "ProjectId" uuid,
        "Status" character varying(50) NOT NULL,
        "IssuedByUserId" character varying(450),
        "IssuedByName" character varying(150),
        "TargetAudienceDescription" character varying(500),
        "Notes" text,
        "CreatedAt" timestamp with time zone NOT NULL,
        "ModifiedAt" timestamp with time zone,
        CONSTRAINT "PK_requests_for_information" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_requests_for_information_projects_ProjectId" FOREIGN KEY ("ProjectId") REFERENCES public.projects ("Id") ON DELETE SET NULL
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE TABLE sales_territory_representatives (
        "SalesTerritoryId" uuid NOT NULL,
        "RepresentativeId" text NOT NULL,
        CONSTRAINT "PK_sales_territory_representatives" PRIMARY KEY ("SalesTerritoryId", "RepresentativeId"),
        CONSTRAINT "FK_sales_territory_representatives_AspNetUsers_RepresentativeId" FOREIGN KEY ("RepresentativeId") REFERENCES "AspNetUsers" ("Id") ON DELETE CASCADE,
        CONSTRAINT "FK_sales_territory_representatives_sales_territories_SalesTerr~" FOREIGN KEY ("SalesTerritoryId") REFERENCES sales_territories ("Id") ON DELETE CASCADE
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE TABLE "Contracts" (
        "Id" uuid NOT NULL,
        "ContractNumber" character varying(100) NOT NULL,
        "Title" character varying(250) NOT NULL,
        "ContractType" character varying(100),
        "VendorId" uuid NOT NULL,
        "StartDate" timestamp with time zone NOT NULL,
        "EndDate" timestamp with time zone NOT NULL,
        "TotalContractValueAmount" numeric(18,4),
        "TotalContractValueCurrencyCode" character varying(3),
        "Status" character varying(50) NOT NULL,
        "PaymentTerms" character varying(200),
        "RenewalTerms" character varying(500),
        "IsAutoRenew" boolean NOT NULL DEFAULT FALSE,
        "TerminationPenaltyTerms" character varying(500),
        "TermsAndConditions" text,
        "MilestonesJson" jsonb,
        "SlaDetailsJson" jsonb,
        "ComplianceDocumentLinksJson" jsonb,
        "VendorPerformanceScoreSnapshot" numeric(5,2),
        "Version" integer NOT NULL DEFAULT 1,
        "IsDeleted" boolean NOT NULL DEFAULT FALSE,
        "TenantId" uuid,
        "CreatedAt" timestamp with time zone NOT NULL,
        "ModifiedAt" timestamp with time zone,
        CONSTRAINT "PK_Contracts" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_Contracts_Vendors_VendorId" FOREIGN KEY ("VendorId") REFERENCES "Vendors" ("Id") ON DELETE RESTRICT
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE TABLE "Suppliers" (
        "Id" uuid NOT NULL,
        "SupplierName" character varying(250) NOT NULL,
        "Status" character varying(50) NOT NULL DEFAULT 'Active',
        "RiskRating" character varying(50),
        "SustainabilityScore" numeric(5,2),
        "IsContractManufacturer" boolean NOT NULL DEFAULT FALSE,
        "AverageLeadTimeDays" integer,
        "IsCsrCompliant" boolean NOT NULL DEFAULT FALSE,
        "CapacityUtilizationPercent" numeric(5,2),
        "VendorId" uuid NOT NULL,
        "CreatedAt" timestamp with time zone NOT NULL,
        "ModifiedAt" timestamp with time zone,
        "EmergencyContacts" jsonb,
        CONSTRAINT "PK_Suppliers" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_Suppliers_Vendors_VendorId" FOREIGN KEY ("VendorId") REFERENCES "Vendors" ("Id") ON DELETE CASCADE
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE TABLE "VendorProducts" (
        "Id" uuid NOT NULL,
        "ProductDefinitionId" uuid NOT NULL,
        "VendorId" uuid NOT NULL,
        "VendorSku" character varying(100),
        "UnitPriceAmount" numeric(18,4) NOT NULL,
        "UnitPriceCurrencyCode" character varying(3) NOT NULL,
        "UnitOfMeasure" character varying(50) NOT NULL,
        "PackSize" numeric(18,4),
        "IsActive" boolean NOT NULL,
        "LeadTimeDays" integer,
        "CreatedAt" timestamp with time zone NOT NULL,
        "ModifiedAt" timestamp with time zone,
        CONSTRAINT "PK_VendorProducts" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_VendorProducts_ProductDefinitions_ProductDefinitionId" FOREIGN KEY ("ProductDefinitionId") REFERENCES "ProductDefinitions" ("Id") ON DELETE RESTRICT,
        CONSTRAINT "FK_VendorProducts_Vendors_VendorId" FOREIGN KEY ("VendorId") REFERENCES "Vendors" ("Id") ON DELETE RESTRICT
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE TABLE requests_for_proposal (
        "Id" uuid NOT NULL,
        "RfpNumber" character varying(100) NOT NULL,
        "Title" character varying(255) NOT NULL,
        "ScopeOfWork" text,
        "EvaluationCriteria" jsonb,
        "TenantId" uuid NOT NULL,
        "IssuedDate" timestamp with time zone,
        "IssueDate" timestamp without time zone NOT NULL,
        "DecisionDate" timestamp without time zone,
        "ProjectId" uuid,
        "QuestionDeadline" timestamp with time zone,
        "SubmissionDeadline" timestamp without time zone NOT NULL,
        "Status" character varying(50) NOT NULL,
        "IssuedByUserId" character varying(450),
        "IssuedByName" character varying(150),
        "Department" character varying(100),
        "ExpectedContractStartDate" timestamp with time zone,
        "ExpectedContractDuration" character varying(50),
        "AwardedVendorId" uuid,
        "AwardedContractId" uuid,
        "CompletedDate" timestamp with time zone,
        "CreatedAt" timestamp with time zone NOT NULL,
        "ModifiedAt" timestamp with time zone,
        CONSTRAINT "PK_requests_for_proposal" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_requests_for_proposal_Contracts_AwardedContractId" FOREIGN KEY ("AwardedContractId") REFERENCES "Contracts" ("Id"),
        CONSTRAINT "FK_requests_for_proposal_Vendors_AwardedVendorId" FOREIGN KEY ("AwardedVendorId") REFERENCES "Vendors" ("Id"),
        CONSTRAINT "FK_requests_for_proposal_projects_ProjectId" FOREIGN KEY ("ProjectId") REFERENCES public.projects ("Id") ON DELETE SET NULL
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE TABLE vendor_proposals (
        "Id" uuid NOT NULL,
        "TenantId" uuid NOT NULL,
        "VendorId" uuid NOT NULL,
        "RequestForProposalId" uuid NOT NULL,
        "SubmissionDate" timestamp without time zone NOT NULL,
        "Status" character varying(50) NOT NULL,
        "TotalProposedValue" numeric(18,2),
        "ValidityPeriodDays" integer NOT NULL,
        "ValidityEndDate" timestamp without time zone,
        "AlternatePaymentTerms" text,
        "ValueAddedServices" text,
        "SustainabilityCommitments" text,
        "RiskSharingClauses" text,
        "PerformanceGuarantees" text,
        "SubcontractorDisclosures" text,
        "ComplianceCertificationsJson" text,
        "Comments" text,
        "Version" integer NOT NULL,
        "IsDeleted" boolean NOT NULL,
        "CreatedAt" timestamp with time zone NOT NULL,
        "ModifiedAt" timestamp with time zone,
        CONSTRAINT "PK_vendor_proposals" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_vendor_proposals_Vendors_VendorId" FOREIGN KEY ("VendorId") REFERENCES "Vendors" ("Id") ON DELETE RESTRICT,
        CONSTRAINT "FK_vendor_proposals_requests_for_proposal_RequestForProposalId" FOREIGN KEY ("RequestForProposalId") REFERENCES requests_for_proposal ("Id") ON DELETE RESTRICT
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE TABLE delivery_note_lines (
        "DeliveryNoteId" uuid NOT NULL,
        "LineNumber" integer NOT NULL,
        "PurchaseOrderLineId" uuid,
        "ProductId" uuid NOT NULL,
        "SalesOrderId" uuid,
        "SalesOrderLineNumber" integer,
        "ProductSkuSnapshot" character varying(100) NOT NULL,
        "Description" character varying(500) NOT NULL,
        "QuantityShipped" numeric(18,4) NOT NULL,
        "UnitOfMeasure" character varying(20) NOT NULL,
        "BatchNumber" text,
        "SerialNumber" text,
        "Notes" text,
        "Id" uuid NOT NULL,
        "CreatedAt" timestamp with time zone NOT NULL,
        "ModifiedAt" timestamp with time zone,
        CONSTRAINT "PK_delivery_note_lines" PRIMARY KEY ("DeliveryNoteId", "LineNumber"),
        CONSTRAINT "FK_delivery_note_lines_ProductDefinitions_ProductId" FOREIGN KEY ("ProductId") REFERENCES "ProductDefinitions" ("Id") ON DELETE RESTRICT
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE TABLE delivery_notes (
        "Id" uuid NOT NULL,
        "TenantId" uuid NOT NULL,
        "DeliveryNoteNumber" character varying(100) NOT NULL,
        "Status" character varying(50) NOT NULL,
        "PurchaseOrderId" uuid,
        "VendorId" uuid NOT NULL,
        "SalesOrderId" uuid,
        "ShipmentDate" timestamp without time zone,
        "DeliveryDate" timestamp without time zone NOT NULL,
        "ReceivedBy" character varying(150),
        carrier_name character varying(100),
        tracking_number character varying(100),
        shipping_street character varying(200),
        shipping_city character varying(100),
        shipping_state character varying(100),
        shipping_country character varying(100),
        shipping_postal_code character varying(20),
        billing_street character varying(200),
        billing_city character varying(100),
        billing_state character varying(100),
        billing_country character varying(100),
        billing_postal_code character varying(20),
        "Notes" text,
        "CreatedAt" timestamp with time zone NOT NULL,
        "ModifiedAt" timestamp with time zone,
        CONSTRAINT "PK_delivery_notes" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_delivery_notes_Vendors_VendorId" FOREIGN KEY ("VendorId") REFERENCES "Vendors" ("Id") ON DELETE CASCADE
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE TABLE goods_receipt_note_lines (
        "LineNumber" integer NOT NULL,
        "GoodsReceiptNoteId" uuid NOT NULL,
        "PurchaseOrderLineId" uuid NOT NULL,
        "DeliveryNoteLineId" uuid,
        "DeliveryNoteLineDeliveryNoteId" uuid,
        "DeliveryNoteLineLineNumber" integer,
        "ProductId" uuid NOT NULL,
        "ProductSkuSnapshot" character varying(100) NOT NULL,
        "ProductDescriptionSnapshot" character varying(500) NOT NULL,
        "QuantityReceived" numeric(18,4) NOT NULL DEFAULT 0.0,
        "UnitOfMeasure" character varying(20) NOT NULL,
        "QualityControlStatus" character varying(50) NOT NULL,
        "PutAwayLocation" character varying(100),
        "BatchNumber" text,
        "LotNumber" text,
        "ExpiryDate" timestamp without time zone,
        "InspectionCompleted" boolean NOT NULL,
        "QuantityAccepted" numeric(18,4) NOT NULL DEFAULT 0.0,
        "QuantityRejected" numeric(18,4) NOT NULL DEFAULT 0.0,
        "RejectionReason" character varying(500),
        "Notes" text,
        "Id" uuid NOT NULL,
        "CreatedAt" timestamp with time zone NOT NULL,
        "ModifiedAt" timestamp with time zone,
        CONSTRAINT "PK_goods_receipt_note_lines" PRIMARY KEY ("GoodsReceiptNoteId", "LineNumber"),
        CONSTRAINT "FK_goods_receipt_note_lines_ProductDefinitions_ProductId" FOREIGN KEY ("ProductId") REFERENCES "ProductDefinitions" ("Id") ON DELETE RESTRICT,
        CONSTRAINT "FK_goods_receipt_note_lines_delivery_note_lines_DeliveryNoteLi~" FOREIGN KEY ("DeliveryNoteLineDeliveryNoteId", "DeliveryNoteLineLineNumber") REFERENCES delivery_note_lines ("DeliveryNoteId", "LineNumber")
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE TABLE goods_receipt_notes (
        "Id" uuid NOT NULL,
        "TenantId" uuid NOT NULL,
        "GoodsReceiptNoteNumber" character varying(100) NOT NULL,
        "GrnNumber" character varying(50) NOT NULL,
        "PurchaseOrderId" uuid NOT NULL,
        "DeliveryNoteId" uuid,
        "VendorId" uuid NOT NULL,
        "ReceiptDate" timestamp without time zone NOT NULL,
        "InspectionDate" timestamp without time zone,
        "ReceivingLocation" character varying(255),
        "ReceivedByUserId" character varying(450) NOT NULL,
        "ReceivedByName" character varying(150) NOT NULL,
        "Status" character varying(50) NOT NULL,
        "Notes" text,
        "CreatedAt" timestamp with time zone NOT NULL,
        "ModifiedAt" timestamp with time zone,
        CONSTRAINT "PK_goods_receipt_notes" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_goods_receipt_notes_AspNetUsers_ReceivedByUserId" FOREIGN KEY ("ReceivedByUserId") REFERENCES "AspNetUsers" ("Id") ON DELETE RESTRICT,
        CONSTRAINT "FK_goods_receipt_notes_Vendors_VendorId" FOREIGN KEY ("VendorId") REFERENCES "Vendors" ("Id") ON DELETE CASCADE,
        CONSTRAINT "FK_goods_receipt_notes_delivery_notes_DeliveryNoteId" FOREIGN KEY ("DeliveryNoteId") REFERENCES delivery_notes ("Id") ON DELETE SET NULL
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE TABLE invoice_lines (
        "LineNumber" integer NOT NULL,
        "InvoiceId" uuid NOT NULL,
        "PurchaseOrderLineId" uuid,
        "ProductId" uuid,
        "Description" text NOT NULL,
        "Quantity" numeric(18,4) NOT NULL,
        "UnitPrice" numeric(18,4) NOT NULL,
        "LineTotal" numeric(18,2) NOT NULL,
        "TaxAmount" numeric(18,2) DEFAULT 0.0,
        "TaxRate" numeric(5,4),
        "UnitOfMeasure" character varying(20) NOT NULL,
        "Notes" text,
        "Id" uuid NOT NULL,
        "CreatedAt" timestamp with time zone NOT NULL,
        "ModifiedAt" timestamp with time zone,
        CONSTRAINT "PK_invoice_lines" PRIMARY KEY ("InvoiceId", "LineNumber"),
        CONSTRAINT "FK_invoice_lines_ProductDefinitions_ProductId" FOREIGN KEY ("ProductId") REFERENCES "ProductDefinitions" ("Id") ON DELETE RESTRICT
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE TABLE invoices (
        "Id" uuid NOT NULL,
        "TenantId" uuid NOT NULL,
        "InvoiceNumber" character varying(100) NOT NULL,
        "VendorId" uuid NOT NULL,
        "CustomerId" uuid,
        "PurchaseOrderId" uuid,
        "InvoiceDate" timestamp without time zone NOT NULL,
        "DueDate" timestamp without time zone NOT NULL,
        "Status" character varying(50) NOT NULL,
        "CurrencyCode" character varying(3) NOT NULL,
        "SubTotal" numeric(18,2) NOT NULL,
        "Subtotal" numeric(18,2) NOT NULL,
        "TaxAmount" numeric(18,2) NOT NULL DEFAULT 0.0,
        "TotalAmount" numeric(18,2) NOT NULL,
        "AmountPaid" numeric(18,2) NOT NULL,
        "PaymentDate" timestamp without time zone,
        "Notes" text,
        "PaymentTerms" character varying(255),
        "CreatedAt" timestamp with time zone NOT NULL,
        "ModifiedAt" timestamp with time zone,
        CONSTRAINT "PK_invoices" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_invoices_Vendors_VendorId" FOREIGN KEY ("VendorId") REFERENCES "Vendors" ("Id") ON DELETE RESTRICT,
        CONSTRAINT "FK_invoices_customers_CustomerId" FOREIGN KEY ("CustomerId") REFERENCES public.customers ("Id") ON DELETE RESTRICT
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE TABLE payment_transactions (
        "Id" uuid NOT NULL,
        "TenantId" uuid NOT NULL,
        "TransactionReference" character varying(100),
        "PaymentDate" timestamp without time zone NOT NULL,
        "Amount" numeric(18,2) NOT NULL,
        "CurrencyCode" character varying(3) NOT NULL,
        "PaymentMethod" character varying(50) NOT NULL,
        "Status" character varying(50) NOT NULL,
        "VendorId" uuid NOT NULL,
        "InvoiceId" uuid,
        "Notes" text,
        "BankReference" character varying(100),
        "CreatedAt" timestamp with time zone NOT NULL,
        "ModifiedAt" timestamp with time zone,
        CONSTRAINT "PK_payment_transactions" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_payment_transactions_Vendors_VendorId" FOREIGN KEY ("VendorId") REFERENCES "Vendors" ("Id") ON DELETE RESTRICT,
        CONSTRAINT "FK_payment_transactions_invoices_InvoiceId" FOREIGN KEY ("InvoiceId") REFERENCES invoices ("Id") ON DELETE SET NULL
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE TABLE "PurchaseOrderLines" (
        "Id" uuid NOT NULL,
        "PurchaseOrderId" uuid NOT NULL,
        "VendorProductId" uuid NOT NULL,
        "SkuSnapshot" character varying(100) NOT NULL,
        "DescriptionSnapshot" character varying(2000) NOT NULL,
        "UnitPriceAmount" numeric(18,4) NOT NULL,
        "UnitPriceCurrencyCode" character varying(3) NOT NULL,
        "UnitOfMeasureSnapshot" character varying(50) NOT NULL,
        "Quantity" numeric(18,4) NOT NULL,
        "LineTotalAmount" numeric(18,4) NOT NULL,
        "LineTotalCurrencyCode" character varying(3) NOT NULL,
        "Notes" character varying(1000),
        "CreatedAt" timestamp with time zone NOT NULL,
        "ModifiedAt" timestamp with time zone,
        CONSTRAINT "PK_PurchaseOrderLines" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_PurchaseOrderLines_VendorProducts_VendorProductId" FOREIGN KEY ("VendorProductId") REFERENCES "VendorProducts" ("Id") ON DELETE RESTRICT
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE TABLE technical_submittals (
        "Id" uuid NOT NULL,
        "TenantId" uuid NOT NULL,
        "SubmittalNumber" character varying(100) NOT NULL,
        "Revision" character varying(20),
        "Title" character varying(255) NOT NULL,
        "Description" text,
        "SubmittalType" character varying(50) NOT NULL,
        "Status" character varying(50) NOT NULL,
        "CurrentReviewCycle" integer NOT NULL,
        "ReviewDueDate" timestamp with time zone,
        "RequiredDate" timestamp without time zone,
        "ReviewStartDate" timestamp with time zone,
        "ReviewCompletionDate" timestamp with time zone,
        "CurrentOverallDisposition" integer NOT NULL,
        "PurchaseOrderLineId" uuid,
        "ContractId" uuid,
        "ProjectId" uuid NOT NULL,
        "SpecificationSection" text,
        "SpecificationId" uuid,
        "RelatedITPReference" text,
        "TestPlanId" uuid,
        "RelatedNCRReference" text,
        "NonConformanceReportId" uuid,
        "VendorId" uuid,
        "SubmittedById" text NOT NULL,
        "SubmittedByUserId" character varying(450) NOT NULL,
        "CurrentReviewerId" uuid,
        "CycleCount" integer NOT NULL DEFAULT 1,
        "IsAsBuilt" boolean NOT NULL DEFAULT FALSE,
        "IsFinalDocumentation" boolean NOT NULL DEFAULT FALSE,
        "SubmittedDate" timestamp without time zone NOT NULL,
        "FinalSignOffById" text,
        "SignedOffById" uuid,
        "FinalSignOffDate" timestamp with time zone,
        "SignedOffDate" timestamp without time zone,
        "ResubmissionCount" integer NOT NULL DEFAULT 0,
        "MaxResubmissions" integer,
        "IsDeleted" boolean NOT NULL,
        "SubmittedDocuments" jsonb NOT NULL,
        "CreatedAt" timestamp with time zone NOT NULL,
        "ModifiedAt" timestamp with time zone,
        CONSTRAINT "PK_technical_submittals" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_technical_submittals_AspNetUsers_SubmittedByUserId" FOREIGN KEY ("SubmittedByUserId") REFERENCES "AspNetUsers" ("Id") ON DELETE RESTRICT,
        CONSTRAINT "FK_technical_submittals_Contracts_ContractId" FOREIGN KEY ("ContractId") REFERENCES "Contracts" ("Id"),
        CONSTRAINT "FK_technical_submittals_PurchaseOrderLines_PurchaseOrderLineId" FOREIGN KEY ("PurchaseOrderLineId") REFERENCES "PurchaseOrderLines" ("Id"),
        CONSTRAINT "FK_technical_submittals_Vendors_VendorId" FOREIGN KEY ("VendorId") REFERENCES "Vendors" ("Id") ON DELETE RESTRICT,
        CONSTRAINT "FK_technical_submittals_projects_ProjectId" FOREIGN KEY ("ProjectId") REFERENCES public.projects ("Id") ON DELETE RESTRICT
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE TABLE submittal_reviews (
        "Id" uuid NOT NULL,
        "TechnicalSubmittalId" uuid NOT NULL,
        "ReviewCycle" integer NOT NULL,
        "ReviewerId" text NOT NULL,
        "ReviewerGuid" uuid NOT NULL,
        "ReviewerName" text NOT NULL,
        "ReviewDate" timestamp without time zone NOT NULL DEFAULT (CURRENT_TIMESTAMP),
        "Disposition" character varying(50) NOT NULL,
        "Comments" text,
        "MarkupDocument" jsonb,
        "TechnicalSubmittalId1" uuid,
        "CreatedAt" timestamp with time zone NOT NULL,
        "ModifiedAt" timestamp with time zone,
        CONSTRAINT "PK_submittal_reviews" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_submittal_reviews_AspNetUsers_ReviewerId" FOREIGN KEY ("ReviewerId") REFERENCES "AspNetUsers" ("Id") ON DELETE RESTRICT,
        CONSTRAINT "FK_submittal_reviews_technical_submittals_TechnicalSubmittalId" FOREIGN KEY ("TechnicalSubmittalId") REFERENCES technical_submittals ("Id") ON DELETE CASCADE,
        CONSTRAINT "FK_submittal_reviews_technical_submittals_TechnicalSubmittalId1" FOREIGN KEY ("TechnicalSubmittalId1") REFERENCES technical_submittals ("Id")
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE TABLE "PurchaseOrders" (
        "Id" uuid NOT NULL,
        "OrderNumber" character varying(100) NOT NULL,
        "OrderDate" timestamp with time zone NOT NULL,
        "DeliveryDate" timestamp with time zone,
        "Status" character varying(50) NOT NULL,
        "PaymentTerms" character varying(255),
        "CurrencyCode" character varying(3) NOT NULL,
        "TotalAmount" numeric(18,4) NOT NULL,
        "VendorId" uuid NOT NULL,
        "ContractId" uuid,
        "RequisitionId" uuid,
        "Shipment_Street" character varying(200) NOT NULL,
        "Shipment_City" character varying(100) NOT NULL,
        "Shipment_State" character varying(100) NOT NULL,
        "Shipment_Country" character varying(100) NOT NULL,
        "Shipment_PostalCode" character varying(20) NOT NULL,
        "CreatedAt" timestamp with time zone NOT NULL,
        "ModifiedAt" timestamp with time zone,
        CONSTRAINT "PK_PurchaseOrders" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_PurchaseOrders_Contracts_ContractId" FOREIGN KEY ("ContractId") REFERENCES "Contracts" ("Id") ON DELETE SET NULL,
        CONSTRAINT "FK_PurchaseOrders_Vendors_VendorId" FOREIGN KEY ("VendorId") REFERENCES "Vendors" ("Id") ON DELETE RESTRICT
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE TABLE "PurchaseRequisitions" (
        "Id" uuid NOT NULL,
        "TenantId" uuid NOT NULL,
        "RequisitionNumber" character varying(50) NOT NULL,
        "RequestorName" character varying(150) NOT NULL,
        "RequestorEmail" character varying(254) NOT NULL,
        "RequestorUserId" character varying(450),
        "Department" character varying(100) NOT NULL,
        "RequestDate" timestamp with time zone NOT NULL,
        "DateNeeded" timestamp with time zone,
        "Justification" character varying(1000) NOT NULL,
        "Status" character varying(50) NOT NULL,
        "CurrencyCode" character varying(3) NOT NULL,
        "TotalEstimatedCostAmount" numeric(18,4) NOT NULL,
        "Shipping_Street" character varying(200),
        "Shipping_City" character varying(100),
        "Shipping_State" character varying(100),
        "Shipping_Country" character varying(100),
        "Shipping_PostalCode" character varying(20),
        "Notes" character varying(2000),
        "AssociatedPurchaseOrderId" uuid,
        "CreatedAt" timestamp with time zone NOT NULL,
        "ModifiedAt" timestamp with time zone,
        CONSTRAINT "PK_PurchaseRequisitions" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_PurchaseRequisitions_PurchaseOrders_AssociatedPurchaseOrder~" FOREIGN KEY ("AssociatedPurchaseOrderId") REFERENCES "PurchaseOrders" ("Id") ON DELETE SET NULL
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE TABLE "PurchaseRequisitionLines" (
        "Id" uuid NOT NULL,
        "PurchaseRequisitionId" uuid NOT NULL,
        "LineNumber" integer NOT NULL,
        "TenantId" uuid NOT NULL,
        "VendorProductId" uuid,
        "ProductDefinitionId" uuid,
        "Description" character varying(1000) NOT NULL,
        "Quantity" numeric(18,4) NOT NULL,
        "UnitOfMeasure" character varying(50) NOT NULL,
        "EstUnitPriceAmount" numeric(18,4),
        "EstUnitPriceCurrencyCode" character varying(3),
        "EstLineCostAmount" numeric(18,4),
        "EstLineCostCurrencyCode" character varying(3),
        "GLAccountCode" character varying(50),
        "DateNeeded" timestamp with time zone,
        "SuggestedVendorId" uuid,
        "Notes" character varying(1000),
        "CreatedAt" timestamp with time zone NOT NULL,
        "ModifiedAt" timestamp with time zone,
        CONSTRAINT "PK_PurchaseRequisitionLines" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_PurchaseRequisitionLines_ProductDefinitions_ProductDefiniti~" FOREIGN KEY ("ProductDefinitionId") REFERENCES "ProductDefinitions" ("Id") ON DELETE SET NULL,
        CONSTRAINT "FK_PurchaseRequisitionLines_PurchaseRequisitions_PurchaseRequi~" FOREIGN KEY ("PurchaseRequisitionId") REFERENCES "PurchaseRequisitions" ("Id") ON DELETE CASCADE,
        CONSTRAINT "FK_PurchaseRequisitionLines_VendorProducts_VendorProductId" FOREIGN KEY ("VendorProductId") REFERENCES "VendorProducts" ("Id") ON DELETE SET NULL,
        CONSTRAINT "FK_PurchaseRequisitionLines_Vendors_SuggestedVendorId" FOREIGN KEY ("SuggestedVendorId") REFERENCES "Vendors" ("Id") ON DELETE SET NULL
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE TABLE public.request_for_quotes (
        "Id" uuid NOT NULL,
        "TenantId" uuid NOT NULL,
        "RFQNumber" character varying(50) NOT NULL,
        "Title" character varying(250) NOT NULL,
        "Status" character varying(50) NOT NULL,
        "SubmissionDeadline" timestamp without time zone NOT NULL,
        "RequiredDeliveryDate" timestamp without time zone,
        "Description" text,
        "ScopeOfWork" text,
        "TermsAndConditions" text,
        "VendorReferenceInstructions" text,
        "CurrencyCode" character varying(3) NOT NULL,
        deliver_to_street character varying(200) NOT NULL,
        deliver_to_city character varying(100) NOT NULL,
        deliver_to_state character varying(100) NOT NULL,
        deliver_to_country character varying(100) NOT NULL,
        deliver_to_postal_code character varying(20) NOT NULL,
        "CreatedByUserId" character varying(450) NOT NULL,
        "OriginatingRequisitionId" uuid,
        "AwardedVendorId" uuid,
        "RelatedAgreementId" uuid,
        "ContactPersonEmail" character varying(254),
        "CommunicationMethod" character varying(50),
        "IsDeleted" boolean NOT NULL DEFAULT FALSE,
        "CreatedAt" timestamp with time zone NOT NULL,
        "ModifiedAt" timestamp with time zone,
        CONSTRAINT "PK_request_for_quotes" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_request_for_quotes_Contracts_RelatedAgreementId" FOREIGN KEY ("RelatedAgreementId") REFERENCES "Contracts" ("Id") ON DELETE SET NULL,
        CONSTRAINT "FK_request_for_quotes_PurchaseRequisitions_OriginatingRequisit~" FOREIGN KEY ("OriginatingRequisitionId") REFERENCES "PurchaseRequisitions" ("Id") ON DELETE SET NULL,
        CONSTRAINT "FK_request_for_quotes_Vendors_AwardedVendorId" FOREIGN KEY ("AwardedVendorId") REFERENCES "Vendors" ("Id") ON DELETE RESTRICT
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE TABLE public.request_for_quote_lines (
        "Id" uuid NOT NULL,
        "RequestForQuoteId" uuid NOT NULL,
        "LineNumber" integer NOT NULL,
        "TenantId" uuid NOT NULL,
        "ProductDefinitionId" uuid,
        "VendorProductId" uuid,
        "Description" text NOT NULL,
        "Quantity" numeric(18,4) NOT NULL,
        "UnitOfMeasure" character varying(50) NOT NULL,
        target_unit_price_amount numeric(18,4),
        target_unit_price_currency_code character varying(3),
        "AlternateItemProposal" text,
        est_tco_value_amount numeric(18,4),
        est_tco_value_currency_code character varying(3),
        "TechnicalSpecifications" text,
        "SampleRequired" boolean NOT NULL DEFAULT FALSE,
        "MinimumOrderQuantity" numeric(18,4),
        "PreferredIncoterm" character varying(10),
        "IsSubstituteAllowed" boolean NOT NULL DEFAULT FALSE,
        "Notes" text,
        "IsDeleted" boolean NOT NULL DEFAULT FALSE,
        "CreatedAt" timestamp with time zone NOT NULL,
        "ModifiedAt" timestamp with time zone,
        CONSTRAINT "PK_request_for_quote_lines" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_request_for_quote_lines_ProductDefinitions_ProductDefinitio~" FOREIGN KEY ("ProductDefinitionId") REFERENCES "ProductDefinitions" ("Id") ON DELETE SET NULL,
        CONSTRAINT "FK_request_for_quote_lines_VendorProducts_VendorProductId" FOREIGN KEY ("VendorProductId") REFERENCES "VendorProducts" ("Id") ON DELETE SET NULL,
        CONSTRAINT "FK_request_for_quote_lines_request_for_quotes_RequestForQuoteId" FOREIGN KEY ("RequestForQuoteId") REFERENCES public.request_for_quotes ("Id") ON DELETE CASCADE
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE TABLE return_authorization_lines (
        "LineNumber" integer NOT NULL,
        "ReturnAuthorizationId" uuid NOT NULL,
        "OriginalSalesOrderLineId" uuid,
        "SalesOrderId" uuid,
        "SalesOrderLineNumber" integer,
        "InvoiceId" uuid,
        "InvoiceLineNumber" integer,
        "ItemCondition" character varying(50),
        "ProductDefinitionId" uuid,
        "VendorProductId" uuid,
        "ProductId" uuid NOT NULL,
        "SkuSnapshot" text NOT NULL,
        "DescriptionSnapshot" text NOT NULL,
        "QuantityAuthorized" numeric(18,4) NOT NULL,
        "UnitOfMeasure" character varying(20) NOT NULL,
        "QuantityReceived" numeric NOT NULL,
        "ReasonForReturn" text NOT NULL,
        "RequestedAction" integer NOT NULL,
        "OriginalSalesOrderLineSalesOrderId" uuid,
        "OriginalSalesOrderLineLineNumber" integer,
        "Id" uuid NOT NULL,
        "CreatedAt" timestamp with time zone NOT NULL,
        "ModifiedAt" timestamp with time zone,
        CONSTRAINT "PK_return_authorization_lines" PRIMARY KEY ("ReturnAuthorizationId", "LineNumber"),
        CONSTRAINT "FK_return_authorization_lines_ProductDefinitions_ProductDefini~" FOREIGN KEY ("ProductDefinitionId") REFERENCES "ProductDefinitions" ("Id"),
        CONSTRAINT "FK_return_authorization_lines_VendorProducts_VendorProductId" FOREIGN KEY ("VendorProductId") REFERENCES "VendorProducts" ("Id"),
        CONSTRAINT "FK_return_authorization_lines_invoice_lines_InvoiceId_InvoiceL~" FOREIGN KEY ("InvoiceId", "InvoiceLineNumber") REFERENCES invoice_lines ("InvoiceId", "LineNumber") ON DELETE RESTRICT,
        CONSTRAINT "FK_return_authorization_lines_products_ProductId" FOREIGN KEY ("ProductId") REFERENCES public.products ("Id") ON DELETE RESTRICT
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE TABLE return_authorizations (
        "Id" uuid NOT NULL,
        "TenantId" uuid NOT NULL,
        "RmaNumber" character varying(100) NOT NULL,
        "RequestDate" timestamp without time zone NOT NULL,
        "Status" character varying(50) NOT NULL,
        "CustomerId" uuid NOT NULL,
        "OriginalSalesOrderId" uuid NOT NULL,
        "SalesOrderId" uuid,
        "InvoiceId" uuid,
        "AuthorizationDate" timestamp without time zone,
        "ExpiryDate" timestamp without time zone,
        "ReasonForReturn" text,
        "RequestedAction" integer NOT NULL,
        "ShippingInstructions" text,
        "Notes" text,
        "IsDeleted" boolean NOT NULL,
        "CreatedAt" timestamp with time zone NOT NULL,
        "ModifiedAt" timestamp with time zone,
        CONSTRAINT "PK_return_authorizations" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_return_authorizations_customers_CustomerId" FOREIGN KEY ("CustomerId") REFERENCES public.customers ("Id") ON DELETE RESTRICT,
        CONSTRAINT "FK_return_authorizations_invoices_InvoiceId" FOREIGN KEY ("InvoiceId") REFERENCES invoices ("Id") ON DELETE RESTRICT
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE TABLE public.sales_orders (
        "Id" uuid NOT NULL,
        "TenantId" uuid NOT NULL,
        "OrderNumber" character varying(50) NOT NULL,
        "OrderDate" timestamp without time zone NOT NULL,
        "CustomerId" uuid,
        "Status" character varying(50) NOT NULL,
        billing_street character varying(200) NOT NULL,
        billing_city character varying(100) NOT NULL,
        billing_state character varying(100) NOT NULL,
        billing_country character varying(100) NOT NULL,
        billing_postal_code character varying(20) NOT NULL,
        shipping_street character varying(200) NOT NULL,
        shipping_city character varying(100) NOT NULL,
        shipping_state character varying(100) NOT NULL,
        shipping_country character varying(100) NOT NULL,
        shipping_postal_code character varying(20) NOT NULL,
        total_amount numeric(18,4) NOT NULL,
        "CurrencyCode" character varying(3) NOT NULL,
        "IsCreditApproved" boolean NOT NULL DEFAULT FALSE,
        "AtpCheckDate" timestamp without time zone,
        "IsAtpConfirmed" boolean NOT NULL DEFAULT FALSE,
        "IsDropShipment" boolean NOT NULL DEFAULT FALSE,
        "SalespersonId" character varying(450),
        "CommissionRate" numeric(5,4),
        "RelatedReturnAuthorizationId" uuid,
        "SalesTerritoryId" uuid,
        "EdiTransactionReference" character varying(100),
        "IsDeleted" boolean NOT NULL DEFAULT FALSE,
        "SalesTerritoryId1" uuid,
        "CreatedAt" timestamp with time zone NOT NULL,
        "ModifiedAt" timestamp with time zone,
        CONSTRAINT "PK_sales_orders" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_sales_orders_customers_CustomerId" FOREIGN KEY ("CustomerId") REFERENCES public.customers ("Id") ON DELETE RESTRICT,
        CONSTRAINT "FK_sales_orders_return_authorizations_RelatedReturnAuthorizati~" FOREIGN KEY ("RelatedReturnAuthorizationId") REFERENCES return_authorizations ("Id") ON DELETE SET NULL,
        CONSTRAINT "FK_sales_orders_sales_territories_SalesTerritoryId" FOREIGN KEY ("SalesTerritoryId") REFERENCES sales_territories ("Id") ON DELETE SET NULL,
        CONSTRAINT "FK_sales_orders_sales_territories_SalesTerritoryId1" FOREIGN KEY ("SalesTerritoryId1") REFERENCES sales_territories ("Id")
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE TABLE sales_order_lines (
        "SalesOrderId" uuid NOT NULL,
        "LineNumber" integer NOT NULL,
        "TenantId" uuid NOT NULL,
        "VendorProductId" uuid NOT NULL,
        "SkuSnapshot" text NOT NULL,
        "DescriptionSnapshot" text NOT NULL,
        "Description" text NOT NULL,
        "RequestedDeliveryDate" timestamp without time zone,
        "ProductId" uuid NOT NULL,
        "Quantity" numeric(18,4) NOT NULL,
        "UnitOfMeasure" character varying(20) NOT NULL,
        "UnitPriceAmount" numeric(18,4) NOT NULL,
        "UnitPriceCurrency" character varying(3) NOT NULL,
        "AppliedDiscountDescription" text,
        "DiscountAmountValue" numeric(18,2),
        "DiscountAmountCurrency" character varying(3),
        "LineTotalAmount" numeric(18,2) NOT NULL,
        "LineTotalCurrency" character varying(3) NOT NULL,
        "QuantityBackordered" numeric NOT NULL,
        "ReservedSerialNumbersJson" text,
        "WarrantyEndDate" timestamp with time zone,
        "IsKitComponent" boolean NOT NULL,
        "ParentSalesOrderLineId" uuid,
        "ProjectId" uuid,
        "CostCode" text,
        "IsDeleted" boolean NOT NULL,
        "ParentSalesOrderLineSalesOrderId" uuid,
        "ParentSalesOrderLineLineNumber" integer,
        "ProjectId1" uuid,
        "Id" uuid NOT NULL,
        "CreatedAt" timestamp with time zone NOT NULL,
        "ModifiedAt" timestamp with time zone,
        CONSTRAINT "PK_sales_order_lines" PRIMARY KEY ("SalesOrderId", "LineNumber"),
        CONSTRAINT "FK_sales_order_lines_VendorProducts_VendorProductId" FOREIGN KEY ("VendorProductId") REFERENCES "VendorProducts" ("Id") ON DELETE CASCADE,
        CONSTRAINT "FK_sales_order_lines_products_ProductId" FOREIGN KEY ("ProductId") REFERENCES public.products ("Id") ON DELETE RESTRICT,
        CONSTRAINT "FK_sales_order_lines_projects_ProjectId" FOREIGN KEY ("ProjectId") REFERENCES public.projects ("Id") ON DELETE SET NULL,
        CONSTRAINT "FK_sales_order_lines_projects_ProjectId1" FOREIGN KEY ("ProjectId1") REFERENCES public.projects ("Id"),
        CONSTRAINT "FK_sales_order_lines_sales_order_lines_ParentSalesOrderLineSal~" FOREIGN KEY ("ParentSalesOrderLineSalesOrderId", "ParentSalesOrderLineLineNumber") REFERENCES sales_order_lines ("SalesOrderId", "LineNumber"),
        CONSTRAINT "FK_sales_order_lines_sales_orders_SalesOrderId" FOREIGN KEY ("SalesOrderId") REFERENCES public.sales_orders ("Id") ON DELETE CASCADE
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_AspNetRoleClaims_RoleId" ON "AspNetRoleClaims" ("RoleId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE UNIQUE INDEX "RoleNameIndex" ON "AspNetRoles" ("NormalizedName");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_AspNetUserClaims_UserId" ON "AspNetUserClaims" ("UserId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_AspNetUserLogins_UserId" ON "AspNetUserLogins" ("UserId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_AspNetUserRoles_RoleId" ON "AspNetUserRoles" ("RoleId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "EmailIndex" ON "AspNetUsers" ("NormalizedEmail");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE UNIQUE INDEX "UserNameIndex" ON "AspNetUsers" ("NormalizedUserName");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_budget_allocations_BudgetId" ON public.budget_allocations ("BudgetId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_budget_allocations_BudgetId_FiscalPeriodIdentifier" ON public.budget_allocations ("BudgetId", "FiscalPeriodIdentifier");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_budget_allocations_DepartmentId" ON public.budget_allocations ("DepartmentId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_budget_allocations_FiscalPeriodIdentifier" ON public.budget_allocations ("FiscalPeriodIdentifier");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_budget_allocations_Status" ON public.budget_allocations ("Status");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_budget_allocations_TenantId" ON public.budget_allocations ("TenantId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_budgets_ApprovedById" ON public.budgets ("ApprovedById");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_budgets_CreatedById" ON public.budgets ("CreatedById");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_budgets_EndDate" ON public.budgets ("EndDate");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_budgets_FiscalYear" ON public.budgets ("FiscalYear");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_budgets_IsDeleted" ON public.budgets ("IsDeleted");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_budgets_Name" ON public.budgets ("Name");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_budgets_StartDate" ON public.budgets ("StartDate");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_budgets_Status" ON public.budgets ("Status");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_budgets_TenantId" ON public.budgets ("TenantId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_budgets_WorkflowInstanceId" ON public.budgets ("WorkflowInstanceId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE UNIQUE INDEX "IX_Categories_Code" ON "Categories" ("Code") WHERE "Code" IS NOT NULL;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_Categories_ParentCategoryId" ON "Categories" ("ParentCategoryId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_Categories_UnspscCode" ON "Categories" ("UnspscCode");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE UNIQUE INDEX "IX_Contracts_ContractNumber" ON "Contracts" ("ContractNumber");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_Contracts_EndDate" ON "Contracts" ("EndDate");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_Contracts_IsDeleted" ON "Contracts" ("IsDeleted");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_Contracts_StartDate" ON "Contracts" ("StartDate");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_Contracts_Status" ON "Contracts" ("Status");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_Contracts_TenantId" ON "Contracts" ("TenantId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_Contracts_VendorId" ON "Contracts" ("VendorId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_customers_AssignedSalesRepId" ON public.customers ("AssignedSalesRepId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_customers_customer_type" ON public.customers (customer_type);
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_customers_is_active" ON public.customers (is_active);
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_customers_IsDeleted" ON public.customers ("IsDeleted");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_customers_name" ON public.customers (name);
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_customers_tax_identifier" ON public.customers (tax_identifier);
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_customers_TenantId" ON public.customers ("TenantId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE UNIQUE INDEX "IX_customers_TenantId_customer_code" ON public.customers ("TenantId", customer_code);
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_delivery_note_lines_ProductId" ON delivery_note_lines ("ProductId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_delivery_note_lines_PurchaseOrderLineId" ON delivery_note_lines ("PurchaseOrderLineId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_delivery_note_lines_SalesOrderId_SalesOrderLineNumber" ON delivery_note_lines ("SalesOrderId", "SalesOrderLineNumber");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_delivery_notes_DeliveryDate" ON delivery_notes ("DeliveryDate");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE UNIQUE INDEX "IX_delivery_notes_DeliveryNoteNumber" ON delivery_notes ("DeliveryNoteNumber");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_delivery_notes_PurchaseOrderId" ON delivery_notes ("PurchaseOrderId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_delivery_notes_SalesOrderId" ON delivery_notes ("SalesOrderId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_delivery_notes_Status" ON delivery_notes ("Status");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_delivery_notes_TenantId" ON delivery_notes ("TenantId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_delivery_notes_VendorId" ON delivery_notes ("VendorId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_departments_Name" ON public.departments ("Name");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_departments_TenantId" ON public.departments ("TenantId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE UNIQUE INDEX "IX_departments_TenantId_Code" ON public.departments ("TenantId", "Code") WHERE "Code" IS NOT NULL;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_goods_receipt_note_lines_DeliveryNoteLineDeliveryNoteId_Del~" ON goods_receipt_note_lines ("DeliveryNoteLineDeliveryNoteId", "DeliveryNoteLineLineNumber");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_goods_receipt_note_lines_ExpiryDate" ON goods_receipt_note_lines ("ExpiryDate");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_goods_receipt_note_lines_ProductId" ON goods_receipt_note_lines ("ProductId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_goods_receipt_note_lines_PurchaseOrderLineId" ON goods_receipt_note_lines ("PurchaseOrderLineId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_goods_receipt_note_lines_QualityControlStatus" ON goods_receipt_note_lines ("QualityControlStatus");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_goods_receipt_notes_DeliveryNoteId" ON goods_receipt_notes ("DeliveryNoteId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE UNIQUE INDEX "IX_goods_receipt_notes_GoodsReceiptNoteNumber" ON goods_receipt_notes ("GoodsReceiptNoteNumber");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_goods_receipt_notes_PurchaseOrderId" ON goods_receipt_notes ("PurchaseOrderId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_goods_receipt_notes_ReceiptDate" ON goods_receipt_notes ("ReceiptDate");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_goods_receipt_notes_ReceivedByUserId" ON goods_receipt_notes ("ReceivedByUserId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_goods_receipt_notes_Status" ON goods_receipt_notes ("Status");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_goods_receipt_notes_TenantId" ON goods_receipt_notes ("TenantId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_goods_receipt_notes_VendorId" ON goods_receipt_notes ("VendorId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_invoice_lines_ProductId" ON invoice_lines ("ProductId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_invoice_lines_PurchaseOrderLineId" ON invoice_lines ("PurchaseOrderLineId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_invoices_CustomerId" ON invoices ("CustomerId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_invoices_DueDate" ON invoices ("DueDate");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_invoices_InvoiceDate" ON invoices ("InvoiceDate");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_invoices_PurchaseOrderId" ON invoices ("PurchaseOrderId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_invoices_Status" ON invoices ("Status");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_invoices_TenantId" ON invoices ("TenantId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_invoices_VendorId" ON invoices ("VendorId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE UNIQUE INDEX "IX_invoices_VendorId_InvoiceNumber" ON invoices ("VendorId", "InvoiceNumber");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_payment_transactions_InvoiceId" ON payment_transactions ("InvoiceId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_payment_transactions_PaymentDate" ON payment_transactions ("PaymentDate");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_payment_transactions_Status" ON payment_transactions ("Status");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_payment_transactions_TenantId" ON payment_transactions ("TenantId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE UNIQUE INDEX "IX_payment_transactions_TransactionReference" ON payment_transactions ("TransactionReference") WHERE transaction_reference IS NOT NULL;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_payment_transactions_VendorId" ON payment_transactions ("VendorId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_procurement_workflow_steps_ApproverRoleId" ON procurement_workflow_steps ("ApproverRoleId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_procurement_workflow_steps_ApproverUserId" ON procurement_workflow_steps ("ApproverUserId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_procurement_workflow_steps_ProcurementWorkflowId" ON procurement_workflow_steps ("ProcurementWorkflowId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE UNIQUE INDEX "IX_procurement_workflow_steps_ProcurementWorkflowId_StepOrder" ON procurement_workflow_steps ("ProcurementWorkflowId", "StepOrder");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_procurement_workflow_steps_WorkflowId" ON procurement_workflow_steps ("WorkflowId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_procurement_workflows_IsActive" ON procurement_workflows ("IsActive");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_procurement_workflows_Name" ON procurement_workflows ("Name");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_procurement_workflows_TenantId" ON procurement_workflows ("TenantId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE UNIQUE INDEX "IX_procurement_workflows_TenantId_WorkflowType_Name" ON procurement_workflows ("TenantId", "WorkflowType", "Name");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_procurement_workflows_WorkflowType" ON procurement_workflows ("WorkflowType");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_ProductDefinitions_CategoryId" ON "ProductDefinitions" ("CategoryId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE UNIQUE INDEX "IX_ProductDefinitions_Ean" ON "ProductDefinitions" ("Ean") WHERE "Ean" IS NOT NULL;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE UNIQUE INDEX "IX_ProductDefinitions_Gtin" ON "ProductDefinitions" ("Gtin") WHERE "Gtin" IS NOT NULL;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_ProductDefinitions_IsDeleted" ON "ProductDefinitions" ("IsDeleted");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE UNIQUE INDEX "IX_ProductDefinitions_Sku" ON "ProductDefinitions" ("Sku");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE UNIQUE INDEX "IX_ProductDefinitions_Upc" ON "ProductDefinitions" ("Upc") WHERE "Upc" IS NOT NULL;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_products_CategoryId" ON public.products ("CategoryId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_products_IsActive" ON public.products ("IsActive");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_products_IsDeleted" ON public.products ("IsDeleted");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_products_Name" ON public.products ("Name");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_products_TenantId" ON public.products ("TenantId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE UNIQUE INDEX "IX_products_TenantId_ProductCode" ON public.products ("TenantId", "ProductCode");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_projects_EndDate" ON public.projects ("EndDate");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_projects_IsDeleted" ON public.projects ("IsDeleted");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_projects_Name" ON public.projects ("Name");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_projects_StartDate" ON public.projects ("StartDate");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_projects_Status" ON public.projects ("Status");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_projects_TenantId" ON public.projects ("TenantId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE UNIQUE INDEX "IX_projects_TenantId_ProjectCode" ON public.projects ("TenantId", "ProjectCode");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_PurchaseOrderLines_PurchaseOrderId" ON "PurchaseOrderLines" ("PurchaseOrderId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_PurchaseOrderLines_VendorProductId" ON "PurchaseOrderLines" ("VendorProductId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_PurchaseOrder_DeliveryDate" ON "PurchaseOrders" ("DeliveryDate");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_PurchaseOrders_ContractId" ON "PurchaseOrders" ("ContractId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_PurchaseOrders_OrderDate" ON "PurchaseOrders" ("OrderDate");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE UNIQUE INDEX "IX_PurchaseOrders_OrderNumber" ON "PurchaseOrders" ("OrderNumber");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_PurchaseOrders_RequisitionId" ON "PurchaseOrders" ("RequisitionId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_PurchaseOrders_Status" ON "PurchaseOrders" ("Status");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_PurchaseOrders_VendorId" ON "PurchaseOrders" ("VendorId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_PurchaseRequisitionLines_GLAccountCode" ON "PurchaseRequisitionLines" ("GLAccountCode");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_PurchaseRequisitionLines_ProductDefinitionId" ON "PurchaseRequisitionLines" ("ProductDefinitionId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE UNIQUE INDEX "IX_PurchaseRequisitionLines_PurchaseRequisitionId_LineNumber" ON "PurchaseRequisitionLines" ("PurchaseRequisitionId", "LineNumber");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_PurchaseRequisitionLines_SuggestedVendorId" ON "PurchaseRequisitionLines" ("SuggestedVendorId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_PurchaseRequisitionLines_TenantId" ON "PurchaseRequisitionLines" ("TenantId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_PurchaseRequisitionLines_VendorProductId" ON "PurchaseRequisitionLines" ("VendorProductId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_PurchaseRequisitions_AssociatedPurchaseOrderId" ON "PurchaseRequisitions" ("AssociatedPurchaseOrderId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_PurchaseRequisitions_RequestDate" ON "PurchaseRequisitions" ("RequestDate");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_PurchaseRequisitions_RequestorUserId" ON "PurchaseRequisitions" ("RequestorUserId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_PurchaseRequisitions_Status" ON "PurchaseRequisitions" ("Status");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_PurchaseRequisitions_TenantId" ON "PurchaseRequisitions" ("TenantId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE UNIQUE INDEX "IX_PurchaseRequisitions_TenantId_RequisitionNumber" ON "PurchaseRequisitions" ("TenantId", "RequisitionNumber");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_request_for_quote_lines_IsDeleted" ON public.request_for_quote_lines ("IsDeleted");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_request_for_quote_lines_ProductDefinitionId" ON public.request_for_quote_lines ("ProductDefinitionId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE UNIQUE INDEX "IX_request_for_quote_lines_RequestForQuoteId_LineNumber" ON public.request_for_quote_lines ("RequestForQuoteId", "LineNumber");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_request_for_quote_lines_TenantId" ON public.request_for_quote_lines ("TenantId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_request_for_quote_lines_VendorProductId" ON public.request_for_quote_lines ("VendorProductId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_request_for_quotes_AwardedVendorId" ON public.request_for_quotes ("AwardedVendorId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_request_for_quotes_CreatedByUserId" ON public.request_for_quotes ("CreatedByUserId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_request_for_quotes_IsDeleted" ON public.request_for_quotes ("IsDeleted");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_request_for_quotes_OriginatingRequisitionId" ON public.request_for_quotes ("OriginatingRequisitionId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_request_for_quotes_RelatedAgreementId" ON public.request_for_quotes ("RelatedAgreementId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_request_for_quotes_Status" ON public.request_for_quotes ("Status");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_request_for_quotes_SubmissionDeadline" ON public.request_for_quotes ("SubmissionDeadline");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_request_for_quotes_TenantId" ON public.request_for_quotes ("TenantId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE UNIQUE INDEX "IX_request_for_quotes_TenantId_RFQNumber" ON public.request_for_quotes ("TenantId", "RFQNumber");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_requests_for_information_ProjectId" ON requests_for_information ("ProjectId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_requests_for_information_ResponseDeadline" ON requests_for_information ("ResponseDeadline");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE UNIQUE INDEX "IX_requests_for_information_RfiNumber" ON requests_for_information ("RfiNumber");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_requests_for_information_Status" ON requests_for_information ("Status");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_requests_for_information_TenantId" ON requests_for_information ("TenantId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_requests_for_proposal_AwardedContractId" ON requests_for_proposal ("AwardedContractId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_requests_for_proposal_AwardedVendorId" ON requests_for_proposal ("AwardedVendorId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_requests_for_proposal_ProjectId" ON requests_for_proposal ("ProjectId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE UNIQUE INDEX "IX_requests_for_proposal_RfpNumber" ON requests_for_proposal ("RfpNumber");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_requests_for_proposal_Status" ON requests_for_proposal ("Status");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_requests_for_proposal_SubmissionDeadline" ON requests_for_proposal ("SubmissionDeadline");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_requests_for_proposal_TenantId" ON requests_for_proposal ("TenantId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_return_authorization_lines_InvoiceId_InvoiceLineNumber" ON return_authorization_lines ("InvoiceId", "InvoiceLineNumber");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_return_authorization_lines_OriginalSalesOrderLineSalesOrder~" ON return_authorization_lines ("OriginalSalesOrderLineSalesOrderId", "OriginalSalesOrderLineLineNumber");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_return_authorization_lines_ProductDefinitionId" ON return_authorization_lines ("ProductDefinitionId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_return_authorization_lines_ProductId" ON return_authorization_lines ("ProductId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_return_authorization_lines_SalesOrderId_SalesOrderLineNumber" ON return_authorization_lines ("SalesOrderId", "SalesOrderLineNumber");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_return_authorization_lines_VendorProductId" ON return_authorization_lines ("VendorProductId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_return_authorizations_CustomerId" ON return_authorizations ("CustomerId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_return_authorizations_InvoiceId" ON return_authorizations ("InvoiceId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_return_authorizations_RequestDate" ON return_authorizations ("RequestDate");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE UNIQUE INDEX "IX_return_authorizations_RmaNumber" ON return_authorizations ("RmaNumber");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_return_authorizations_SalesOrderId" ON return_authorizations ("SalesOrderId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_return_authorizations_Status" ON return_authorizations ("Status");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_return_authorizations_TenantId" ON return_authorizations ("TenantId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_sales_order_lines_ParentSalesOrderLineSalesOrderId_ParentSa~" ON sales_order_lines ("ParentSalesOrderLineSalesOrderId", "ParentSalesOrderLineLineNumber");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_sales_order_lines_ProductId" ON sales_order_lines ("ProductId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_sales_order_lines_ProjectId" ON sales_order_lines ("ProjectId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_sales_order_lines_ProjectId1" ON sales_order_lines ("ProjectId1");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_sales_order_lines_VendorProductId" ON sales_order_lines ("VendorProductId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_sales_orders_CustomerId" ON public.sales_orders ("CustomerId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_sales_orders_EdiTransactionReference" ON public.sales_orders ("EdiTransactionReference");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_sales_orders_IsDeleted" ON public.sales_orders ("IsDeleted");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_sales_orders_OrderDate" ON public.sales_orders ("OrderDate");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_sales_orders_RelatedReturnAuthorizationId" ON public.sales_orders ("RelatedReturnAuthorizationId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_sales_orders_SalespersonId" ON public.sales_orders ("SalespersonId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_sales_orders_SalesTerritoryId" ON public.sales_orders ("SalesTerritoryId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_sales_orders_SalesTerritoryId1" ON public.sales_orders ("SalesTerritoryId1");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_sales_orders_Status" ON public.sales_orders ("Status");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_sales_orders_TenantId" ON public.sales_orders ("TenantId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE UNIQUE INDEX "IX_sales_orders_TenantId_OrderNumber" ON public.sales_orders ("TenantId", "OrderNumber");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_sales_territories_Name" ON sales_territories ("Name");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_sales_territories_ParentTerritoryId" ON sales_territories ("ParentTerritoryId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_sales_territories_TenantId" ON sales_territories ("TenantId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE UNIQUE INDEX "IX_sales_territories_TerritoryCode" ON sales_territories ("TerritoryCode");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_sales_territory_representatives_RepresentativeId" ON sales_territory_representatives ("RepresentativeId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_submittal_reviews_Disposition" ON submittal_reviews ("Disposition");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_submittal_reviews_ReviewDate" ON submittal_reviews ("ReviewDate");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_submittal_reviews_ReviewerId" ON submittal_reviews ("ReviewerId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_submittal_reviews_TechnicalSubmittalId" ON submittal_reviews ("TechnicalSubmittalId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_submittal_reviews_TechnicalSubmittalId1" ON submittal_reviews ("TechnicalSubmittalId1");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_Suppliers_IsContractManufacturer" ON "Suppliers" ("IsContractManufacturer");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_Suppliers_RiskRating" ON "Suppliers" ("RiskRating");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE UNIQUE INDEX "IX_Suppliers_VendorId" ON "Suppliers" ("VendorId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_technical_submittals_ContractId" ON technical_submittals ("ContractId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_technical_submittals_ProjectId" ON technical_submittals ("ProjectId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE UNIQUE INDEX "IX_technical_submittals_ProjectId_SubmittalNumber_Revision" ON technical_submittals ("ProjectId", "SubmittalNumber", "Revision");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_technical_submittals_PurchaseOrderLineId" ON technical_submittals ("PurchaseOrderLineId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_technical_submittals_RequiredDate" ON technical_submittals ("RequiredDate");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_technical_submittals_SpecificationId" ON technical_submittals ("SpecificationId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_technical_submittals_Status" ON technical_submittals ("Status");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE UNIQUE INDEX "IX_technical_submittals_SubmittalNumber" ON technical_submittals ("SubmittalNumber");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_technical_submittals_SubmittedByUserId" ON technical_submittals ("SubmittedByUserId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_technical_submittals_SubmittedDate" ON technical_submittals ("SubmittedDate");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_technical_submittals_TenantId" ON technical_submittals ("TenantId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_technical_submittals_VendorId" ON technical_submittals ("VendorId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_tenant_products_TenantId" ON tenant_products ("TenantId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE UNIQUE INDEX "IX_Tenants_Identifier" ON "Tenants" ("Identifier");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_vendor_proposals_RequestForProposalId" ON vendor_proposals ("RequestForProposalId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE UNIQUE INDEX "IX_vendor_proposals_RequestForProposalId_VendorId" ON vendor_proposals ("RequestForProposalId", "VendorId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_vendor_proposals_Status" ON vendor_proposals ("Status");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_vendor_proposals_SubmissionDate" ON vendor_proposals ("SubmissionDate");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_vendor_proposals_TenantId" ON vendor_proposals ("TenantId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_vendor_proposals_VendorId" ON vendor_proposals ("VendorId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_VendorProducts_ProductDefinitionId" ON "VendorProducts" ("ProductDefinitionId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE UNIQUE INDEX "IX_VendorProducts_VendorId_ProductDefinitionId_UnitOfMeasure_P~" ON "VendorProducts" ("VendorId", "ProductDefinitionId", "UnitOfMeasure", "PackSize");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_VendorProducts_VendorId_VendorSku" ON "VendorProducts" ("VendorId", "VendorSku") WHERE "VendorSku" IS NOT NULL;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_Vendors_CommercialRegistrationNumber" ON "Vendors" ("CommercialRegistrationNumber");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_Vendors_Name" ON "Vendors" ("Name");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_Vendors_Status" ON "Vendors" ("Status");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_Vendors_TaxId" ON "Vendors" ("TaxId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE INDEX "IX_Vendors_VatNumber" ON "Vendors" ("VatNumber");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    CREATE UNIQUE INDEX "IX_Vendors_VendorCode" ON "Vendors" ("VendorCode") WHERE "VendorCode" IS NOT NULL;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    ALTER TABLE delivery_note_lines ADD CONSTRAINT "FK_delivery_note_lines_PurchaseOrderLines_PurchaseOrderLineId" FOREIGN KEY ("PurchaseOrderLineId") REFERENCES "PurchaseOrderLines" ("Id") ON DELETE RESTRICT;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    ALTER TABLE delivery_note_lines ADD CONSTRAINT "FK_delivery_note_lines_delivery_notes_DeliveryNoteId" FOREIGN KEY ("DeliveryNoteId") REFERENCES delivery_notes ("Id") ON DELETE CASCADE;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    ALTER TABLE delivery_note_lines ADD CONSTRAINT "FK_delivery_note_lines_sales_order_lines_SalesOrderId_SalesOrd~" FOREIGN KEY ("SalesOrderId", "SalesOrderLineNumber") REFERENCES sales_order_lines ("SalesOrderId", "LineNumber") ON DELETE RESTRICT;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    ALTER TABLE delivery_notes ADD CONSTRAINT "FK_delivery_notes_PurchaseOrders_PurchaseOrderId" FOREIGN KEY ("PurchaseOrderId") REFERENCES "PurchaseOrders" ("Id") ON DELETE RESTRICT;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    ALTER TABLE delivery_notes ADD CONSTRAINT "FK_delivery_notes_sales_orders_SalesOrderId" FOREIGN KEY ("SalesOrderId") REFERENCES public.sales_orders ("Id") ON DELETE RESTRICT;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    ALTER TABLE goods_receipt_note_lines ADD CONSTRAINT "FK_goods_receipt_note_lines_PurchaseOrderLines_PurchaseOrderLi~" FOREIGN KEY ("PurchaseOrderLineId") REFERENCES "PurchaseOrderLines" ("Id") ON DELETE RESTRICT;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    ALTER TABLE goods_receipt_note_lines ADD CONSTRAINT "FK_goods_receipt_note_lines_goods_receipt_notes_GoodsReceiptNo~" FOREIGN KEY ("GoodsReceiptNoteId") REFERENCES goods_receipt_notes ("Id") ON DELETE CASCADE;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    ALTER TABLE goods_receipt_notes ADD CONSTRAINT "FK_goods_receipt_notes_PurchaseOrders_PurchaseOrderId" FOREIGN KEY ("PurchaseOrderId") REFERENCES "PurchaseOrders" ("Id") ON DELETE RESTRICT;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    ALTER TABLE invoice_lines ADD CONSTRAINT "FK_invoice_lines_PurchaseOrderLines_PurchaseOrderLineId" FOREIGN KEY ("PurchaseOrderLineId") REFERENCES "PurchaseOrderLines" ("Id") ON DELETE RESTRICT;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    ALTER TABLE invoice_lines ADD CONSTRAINT "FK_invoice_lines_invoices_InvoiceId" FOREIGN KEY ("InvoiceId") REFERENCES invoices ("Id") ON DELETE CASCADE;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    ALTER TABLE invoices ADD CONSTRAINT "FK_invoices_PurchaseOrders_PurchaseOrderId" FOREIGN KEY ("PurchaseOrderId") REFERENCES "PurchaseOrders" ("Id") ON DELETE RESTRICT;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    ALTER TABLE "PurchaseOrderLines" ADD CONSTRAINT "FK_PurchaseOrderLines_PurchaseOrders_PurchaseOrderId" FOREIGN KEY ("PurchaseOrderId") REFERENCES "PurchaseOrders" ("Id") ON DELETE CASCADE;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    ALTER TABLE "PurchaseOrders" ADD CONSTRAINT "FK_PurchaseOrders_PurchaseRequisitions_RequisitionId" FOREIGN KEY ("RequisitionId") REFERENCES "PurchaseRequisitions" ("Id") ON DELETE SET NULL;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    ALTER TABLE return_authorization_lines ADD CONSTRAINT "FK_return_authorization_lines_return_authorizations_ReturnAuth~" FOREIGN KEY ("ReturnAuthorizationId") REFERENCES return_authorizations ("Id") ON DELETE CASCADE;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    ALTER TABLE return_authorization_lines ADD CONSTRAINT "FK_return_authorization_lines_sales_order_lines_OriginalSalesO~" FOREIGN KEY ("OriginalSalesOrderLineSalesOrderId", "OriginalSalesOrderLineLineNumber") REFERENCES sales_order_lines ("SalesOrderId", "LineNumber");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    ALTER TABLE return_authorization_lines ADD CONSTRAINT "FK_return_authorization_lines_sales_order_lines_SalesOrderId_S~" FOREIGN KEY ("SalesOrderId", "SalesOrderLineNumber") REFERENCES sales_order_lines ("SalesOrderId", "LineNumber") ON DELETE RESTRICT;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    ALTER TABLE return_authorizations ADD CONSTRAINT "FK_return_authorizations_sales_orders_SalesOrderId" FOREIGN KEY ("SalesOrderId") REFERENCES public.sales_orders ("Id") ON DELETE RESTRICT;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507024423_InitialSchema') THEN
    INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
    VALUES ('20250507024423_InitialSchema', '9.0.5');
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250507110827_CompleteSchemaReset') THEN
    INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
    VALUES ('20250507110827_CompleteSchemaReset', '9.0.5');
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250508080948_CleanInitialSchema') THEN
    DROP INDEX "IX_payment_transactions_TransactionReference";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250508080948_CleanInitialSchema') THEN
    CREATE UNIQUE INDEX "IX_payment_transactions_TransactionReference" ON payment_transactions ("TransactionReference") WHERE "TransactionReference" IS NOT NULL;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250508080948_CleanInitialSchema') THEN
    INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
    VALUES ('20250508080948_CleanInitialSchema', '9.0.5');
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523230135_InitialSchemaSetup') THEN
    INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
    VALUES ('20250523230135_InitialSchemaSetup', '9.0.5');
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523230241_CompleteInitialSchema') THEN
    INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
    VALUES ('20250523230241_CompleteInitialSchema', '9.0.5');
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetRoleClaims" DROP CONSTRAINT fk_asp_net_role_claims_asp_net_roles_role_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetUserClaims" DROP CONSTRAINT fk_asp_net_user_claims_asp_net_users_user_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetUserLogins" DROP CONSTRAINT fk_asp_net_user_logins_asp_net_users_user_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetUserRoles" DROP CONSTRAINT fk_asp_net_user_roles_asp_net_roles_role_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetUserRoles" DROP CONSTRAINT fk_asp_net_user_roles_asp_net_users_user_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetUserTokens" DROP CONSTRAINT fk_asp_net_user_tokens_asp_net_users_user_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.budget_allocations DROP CONSTRAINT fk_budget_allocations_budgets_budget_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.budget_allocations DROP CONSTRAINT fk_budget_allocations_departments_department_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.categories DROP CONSTRAINT fk_categories_categories_parent_category_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE contracts DROP CONSTRAINT fk_contracts_vendors_vendor_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE delivery_note_lines DROP CONSTRAINT fk_delivery_note_lines_delivery_notes_delivery_note_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE delivery_note_lines DROP CONSTRAINT fk_delivery_note_lines_product_definitions_product_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE delivery_note_lines DROP CONSTRAINT fk_delivery_note_lines_purchase_order_lines_purchase_order_lin;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE delivery_note_lines DROP CONSTRAINT fk_delivery_note_lines_sales_order_lines_sales_order_id_sales_;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE delivery_notes DROP CONSTRAINT fk_delivery_notes_purchase_orders_purchase_order_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE delivery_notes DROP CONSTRAINT fk_delivery_notes_sales_orders_sales_order_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE delivery_notes DROP CONSTRAINT fk_delivery_notes_vendors_vendor_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_note_lines DROP CONSTRAINT fk_goods_receipt_note_lines_delivery_note_lines_delivery_note_;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_note_lines DROP CONSTRAINT fk_goods_receipt_note_lines_goods_receipt_notes_goods_receipt_;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_note_lines DROP CONSTRAINT fk_goods_receipt_note_lines_product_definitions_product_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_note_lines DROP CONSTRAINT fk_goods_receipt_note_lines_purchase_order_lines_purchase_orde;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_notes DROP CONSTRAINT fk_goods_receipt_notes_asp_net_users_received_by_user_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_notes DROP CONSTRAINT fk_goods_receipt_notes_delivery_notes_delivery_note_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_notes DROP CONSTRAINT fk_goods_receipt_notes_purchase_orders_purchase_order_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_notes DROP CONSTRAINT fk_goods_receipt_notes_vendors_vendor_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE invoice_lines DROP CONSTRAINT fk_invoice_lines_invoices_invoice_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE invoice_lines DROP CONSTRAINT fk_invoice_lines_product_definitions_product_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE invoice_lines DROP CONSTRAINT fk_invoice_lines_purchase_order_lines_purchase_order_line_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE invoices DROP CONSTRAINT fk_invoices_customers_customer_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE invoices DROP CONSTRAINT fk_invoices_purchase_orders_purchase_order_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE invoices DROP CONSTRAINT fk_invoices_vendors_vendor_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE payment_transactions DROP CONSTRAINT fk_payment_transactions_invoices_invoice_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE payment_transactions DROP CONSTRAINT fk_payment_transactions_vendors_vendor_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE procurement_workflow_steps DROP CONSTRAINT fk_procurement_workflow_steps_asp_net_users_approver_user_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE procurement_workflow_steps DROP CONSTRAINT fk_procurement_workflow_steps_procurement_workflows_procuremen;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE procurement_workflow_steps DROP CONSTRAINT fk_procurement_workflow_steps_procurement_workflows_workflow_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE product_definitions DROP CONSTRAINT fk_product_definitions_categories_category_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.products DROP CONSTRAINT fk_products_categories_category_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE purchase_orders DROP CONSTRAINT fk_purchase_orders_contracts_contract_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE purchase_orders DROP CONSTRAINT fk_purchase_orders_purchase_requisitions_requisition_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE purchase_orders DROP CONSTRAINT fk_purchase_orders_vendors_vendor_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseOrderLines" DROP CONSTRAINT fk_purchase_order_lines_purchase_orders_purchase_order_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseOrderLines" DROP CONSTRAINT fk_purchase_order_lines_vendor_products_vendor_product_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseRequisitionLines" DROP CONSTRAINT fk_purchase_requisition_lines_product_definitions_product_defini;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseRequisitionLines" DROP CONSTRAINT fk_purchase_requisition_lines_purchase_requisitions_purchase_requ;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseRequisitionLines" DROP CONSTRAINT fk_purchase_requisition_lines_vendor_products_vendor_product_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseRequisitionLines" DROP CONSTRAINT fk_purchase_requisition_lines_vendors_suggested_vendor_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseRequisitions" DROP CONSTRAINT fk_purchase_requisitions_purchase_orders_associated_purchase_or;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quote_lines DROP CONSTRAINT fk_request_for_quote_lines_product_definitions_product_definit;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quote_lines DROP CONSTRAINT fk_request_for_quote_lines_requests_for_quote_request_for_quot;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quote_lines DROP CONSTRAINT fk_request_for_quote_lines_vendor_products_vendor_product_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quotes DROP CONSTRAINT fk_request_for_quotes_contracts_related_agreement_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quotes DROP CONSTRAINT fk_request_for_quotes_purchase_requisitions_originating_requisi;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quotes DROP CONSTRAINT fk_request_for_quotes_vendors_awarded_vendor_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE requests_for_information DROP CONSTRAINT fk_requests_for_information_projects_project_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE requests_for_proposal DROP CONSTRAINT fk_requests_for_proposal_contracts_awarded_contract_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE requests_for_proposal DROP CONSTRAINT fk_requests_for_proposal_projects_project_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE requests_for_proposal DROP CONSTRAINT fk_requests_for_proposal_vendors_awarded_vendor_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorization_lines DROP CONSTRAINT fk_return_authorization_lines_invoice_lines_invoice_id_invoice;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorization_lines DROP CONSTRAINT fk_return_authorization_lines_product_definitions_product_defi;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorization_lines DROP CONSTRAINT fk_return_authorization_lines_products_product_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorization_lines DROP CONSTRAINT fk_return_authorization_lines_return_authorizations_return_aut;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorization_lines DROP CONSTRAINT fk_return_authorization_lines_sales_order_lines_original_sales;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorization_lines DROP CONSTRAINT fk_return_authorization_lines_sales_order_lines_sales_order_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorization_lines DROP CONSTRAINT fk_return_authorization_lines_vendor_products_vendor_product_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorizations DROP CONSTRAINT fk_return_authorizations_customers_customer_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorizations DROP CONSTRAINT fk_return_authorizations_invoices_invoice_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorizations DROP CONSTRAINT fk_return_authorizations_sales_orders_sales_order_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_order_lines DROP CONSTRAINT fk_sales_order_lines_products_product_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_order_lines DROP CONSTRAINT fk_sales_order_lines_projects_project_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_order_lines DROP CONSTRAINT fk_sales_order_lines_projects_project_id1;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_order_lines DROP CONSTRAINT fk_sales_order_lines_sales_order_lines_parent_sales_order_line;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_order_lines DROP CONSTRAINT fk_sales_order_lines_sales_orders_sales_order_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_order_lines DROP CONSTRAINT fk_sales_order_lines_vendor_products_vendor_product_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.sales_orders DROP CONSTRAINT fk_sales_orders_customers_customer_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.sales_orders DROP CONSTRAINT fk_sales_orders_return_authorizations_related_return_authoriza;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.sales_orders DROP CONSTRAINT fk_sales_orders_sales_territories_sales_territory_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.sales_orders DROP CONSTRAINT fk_sales_orders_sales_territories_sales_territory_id1;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_territories DROP CONSTRAINT fk_sales_territories_sales_territories_parent_territory_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_territory_representatives DROP CONSTRAINT fk_sales_territory_representatives_asp_net_users_representative_;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_territory_representatives DROP CONSTRAINT fk_sales_territory_representatives_sales_territories_sales_ter;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE submittal_reviews DROP CONSTRAINT fk_submittal_reviews_asp_net_users_reviewer_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE submittal_reviews DROP CONSTRAINT fk_submittal_reviews_technical_submittals_technical_submittal_;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE submittal_reviews DROP CONSTRAINT fk_submittal_reviews_technical_submittals_technical_submittal_1;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE suppliers DROP CONSTRAINT fk_suppliers_vendors_vendor_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE technical_submittals DROP CONSTRAINT fk_technical_submittals_asp_net_users_submitted_by_user_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE technical_submittals DROP CONSTRAINT fk_technical_submittals_contracts_contract_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE technical_submittals DROP CONSTRAINT fk_technical_submittals_projects_project_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE technical_submittals DROP CONSTRAINT fk_technical_submittals_purchase_order_lines_purchase_order_li;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE technical_submittals DROP CONSTRAINT fk_technical_submittals_vendors_vendor_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendor_products DROP CONSTRAINT fk_vendor_products_product_definitions_product_definition_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendor_products DROP CONSTRAINT fk_vendor_products_vendors_vendor_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendor_proposals DROP CONSTRAINT fk_vendor_proposals_requests_for_proposal_request_for_proposal;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendor_proposals DROP CONSTRAINT fk_vendor_proposals_vendors_vendor_id;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendors DROP CONSTRAINT pk_vendors;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendor_proposals DROP CONSTRAINT pk_vendor_proposals;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendor_products DROP CONSTRAINT pk_vendor_products;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE test_entities DROP CONSTRAINT pk_test_entities;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.tenants DROP CONSTRAINT pk_tenants;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE tenant_products DROP CONSTRAINT pk_tenant_products;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE technical_submittals DROP CONSTRAINT pk_technical_submittals;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE suppliers DROP CONSTRAINT pk_suppliers;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE submittal_reviews DROP CONSTRAINT pk_submittal_reviews;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_territory_representatives DROP CONSTRAINT pk_sales_territory_representatives;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_territories DROP CONSTRAINT pk_sales_territories;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.sales_orders DROP CONSTRAINT pk_sales_orders;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_order_lines DROP CONSTRAINT pk_sales_order_lines;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorizations DROP CONSTRAINT pk_return_authorizations;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorization_lines DROP CONSTRAINT pk_return_authorization_lines;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE requests_for_proposal DROP CONSTRAINT pk_requests_for_proposal;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE requests_for_information DROP CONSTRAINT pk_requests_for_information;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quotes DROP CONSTRAINT pk_request_for_quotes;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quote_lines DROP CONSTRAINT pk_request_for_quote_lines;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseRequisitions" DROP CONSTRAINT pk_purchase_requisitions;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseRequisitionLines" DROP CONSTRAINT pk_purchase_requisition_lines;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseOrderLines" DROP CONSTRAINT pk_purchase_order_lines;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE purchase_orders DROP CONSTRAINT pk_purchase_orders;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.projects DROP CONSTRAINT pk_projects;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.products DROP CONSTRAINT pk_products;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE product_definitions DROP CONSTRAINT pk_product_definitions;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE procurement_workflows DROP CONSTRAINT pk_procurement_workflows;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE procurement_workflow_steps DROP CONSTRAINT pk_procurement_workflow_steps;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE payment_transactions DROP CONSTRAINT pk_payment_transactions;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE invoices DROP CONSTRAINT pk_invoices;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE invoice_lines DROP CONSTRAINT pk_invoice_lines;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_notes DROP CONSTRAINT pk_goods_receipt_notes;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_note_lines DROP CONSTRAINT pk_goods_receipt_note_lines;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.departments DROP CONSTRAINT pk_departments;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE delivery_notes DROP CONSTRAINT pk_delivery_notes;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE delivery_note_lines DROP CONSTRAINT pk_delivery_note_lines;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.customers DROP CONSTRAINT pk_customers;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE contracts DROP CONSTRAINT pk_contracts;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.categories DROP CONSTRAINT pk_categories;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.budgets DROP CONSTRAINT pk_budgets;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.budget_allocations DROP CONSTRAINT pk_budget_allocations;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetUserTokens" DROP CONSTRAINT pk_asp_net_user_tokens;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetUsers" DROP CONSTRAINT pk_asp_net_users;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetUserRoles" DROP CONSTRAINT pk_asp_net_user_roles;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetUserLogins" DROP CONSTRAINT pk_asp_net_user_logins;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetUserClaims" DROP CONSTRAINT pk_asp_net_user_claims;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetRoles" DROP CONSTRAINT pk_asp_net_roles;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetRoleClaims" DROP CONSTRAINT pk_asp_net_role_claims;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE document_link RENAME TO "DocumentLink";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendors RENAME COLUMN website TO "Website";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendors RENAME COLUMN status TO "Status";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendors RENAME COLUMN name TO "Name";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendors RENAME COLUMN id TO "Id";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendors RENAME COLUMN vendor_code TO "VendorCode";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendors RENAME COLUMN vat_number TO "VatNumber";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendors RENAME COLUMN tax_id TO "TaxId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendors RENAME COLUMN phone_number TO "PhoneNumber";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendors RENAME COLUMN modified_at TO "ModifiedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendors RENAME COLUMN created_at TO "CreatedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendors RENAME COLUMN contact_name TO "ContactName";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendors RENAME COLUMN contact_email TO "ContactEmail";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendors RENAME COLUMN commercial_registration_number TO "CommercialRegistrationNumber";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_vendors_status RENAME TO "IX_vendors_Status";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_vendors_name RENAME TO "IX_vendors_Name";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_vendors_vendor_code RENAME TO "IX_vendors_VendorCode";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_vendors_vat_number RENAME TO "IX_vendors_VatNumber";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_vendors_tax_id RENAME TO "IX_vendors_TaxId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_vendors_commercial_registration_number RENAME TO "IX_vendors_CommercialRegistrationNumber";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendor_proposals RENAME COLUMN version TO "Version";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendor_proposals RENAME COLUMN status TO "Status";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendor_proposals RENAME COLUMN comments TO "Comments";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendor_proposals RENAME COLUMN id TO "Id";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendor_proposals RENAME COLUMN vendor_id TO "VendorId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendor_proposals RENAME COLUMN value_added_services TO "ValueAddedServices";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendor_proposals RENAME COLUMN validity_period_days TO "ValidityPeriodDays";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendor_proposals RENAME COLUMN validity_end_date TO "ValidityEndDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendor_proposals RENAME COLUMN total_proposed_value TO "TotalProposedValue";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendor_proposals RENAME COLUMN tenant_id TO "TenantId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendor_proposals RENAME COLUMN sustainability_commitments TO "SustainabilityCommitments";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendor_proposals RENAME COLUMN submission_date TO "SubmissionDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendor_proposals RENAME COLUMN subcontractor_disclosures TO "SubcontractorDisclosures";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendor_proposals RENAME COLUMN risk_sharing_clauses TO "RiskSharingClauses";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendor_proposals RENAME COLUMN request_for_proposal_id TO "RequestForProposalId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendor_proposals RENAME COLUMN performance_guarantees TO "PerformanceGuarantees";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendor_proposals RENAME COLUMN modified_at TO "ModifiedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendor_proposals RENAME COLUMN is_deleted TO "IsDeleted";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendor_proposals RENAME COLUMN created_at TO "CreatedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendor_proposals RENAME COLUMN compliance_certifications_json TO "ComplianceCertificationsJson";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendor_proposals RENAME COLUMN alternate_payment_terms TO "AlternatePaymentTerms";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_vendor_proposals_status RENAME TO "IX_vendor_proposals_Status";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_vendor_proposals_vendor_id RENAME TO "IX_vendor_proposals_VendorId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_vendor_proposals_tenant_id RENAME TO "IX_vendor_proposals_TenantId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_vendor_proposals_submission_date RENAME TO "IX_vendor_proposals_SubmissionDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_vendor_proposals_request_for_proposal_id_vendor_id RENAME TO "IX_vendor_proposals_RequestForProposalId_VendorId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_vendor_proposals_request_for_proposal_id RENAME TO "IX_vendor_proposals_RequestForProposalId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendor_products RENAME COLUMN id TO "Id";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendor_products RENAME COLUMN vendor_sku TO "VendorSku";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendor_products RENAME COLUMN vendor_id TO "VendorId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendor_products RENAME COLUMN unit_of_measure TO "UnitOfMeasure";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendor_products RENAME COLUMN product_definition_id TO "ProductDefinitionId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendor_products RENAME COLUMN pack_size TO "PackSize";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendor_products RENAME COLUMN modified_at TO "ModifiedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendor_products RENAME COLUMN lead_time_days TO "LeadTimeDays";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendor_products RENAME COLUMN is_active TO "IsActive";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendor_products RENAME COLUMN created_at TO "CreatedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_vendor_products_vendor_id_vendor_sku RENAME TO "IX_vendor_products_VendorId_VendorSku";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_vendor_products_vendor_id_product_definition_id_unit_of_mea RENAME TO "IX_vendor_products_VendorId_ProductDefinitionId_UnitOfMeasure_~";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_vendor_products_product_definition_id RENAME TO "IX_vendor_products_ProductDefinitionId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE test_entities RENAME COLUMN name TO "Name";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE test_entities RENAME COLUMN id TO "Id";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE test_entities RENAME COLUMN tenant_id TO "TenantId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.tenants RENAME COLUMN settings TO "Settings";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.tenants RENAME COLUMN name TO "Name";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.tenants RENAME COLUMN identifier TO "Identifier";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.tenants RENAME COLUMN country TO "Country";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.tenants RENAME COLUMN city TO "City";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.tenants RENAME COLUMN id TO "Id";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.tenants RENAME COLUMN subscription_plan TO "SubscriptionPlan";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.tenants RENAME COLUMN postal_code TO "PostalCode";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.tenants RENAME COLUMN modified_at TO "ModifiedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.tenants RENAME COLUMN is_active TO "IsActive";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.tenants RENAME COLUMN created_at TO "CreatedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.tenants RENAME COLUMN contact_email TO "ContactEmail";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.tenants RENAME COLUMN address_line1 TO "AddressLine1";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE tenant_products RENAME COLUMN sku TO "SKU";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE tenant_products RENAME COLUMN price TO "Price";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE tenant_products RENAME COLUMN name TO "Name";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE tenant_products RENAME COLUMN description TO "Description";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE tenant_products RENAME COLUMN id TO "Id";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE tenant_products RENAME COLUMN tenant_id TO "TenantId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE tenant_products RENAME COLUMN modified_at TO "ModifiedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE tenant_products RENAME COLUMN created_at TO "CreatedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_tenant_products_tenant_id RENAME TO "IX_tenant_products_TenantId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE technical_submittals RENAME COLUMN title TO "Title";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE technical_submittals RENAME COLUMN status TO "Status";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE technical_submittals RENAME COLUMN revision TO "Revision";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE technical_submittals RENAME COLUMN description TO "Description";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE technical_submittals RENAME COLUMN id TO "Id";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE technical_submittals RENAME COLUMN vendor_id TO "VendorId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE technical_submittals RENAME COLUMN test_plan_id TO "TestPlanId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE technical_submittals RENAME COLUMN tenant_id TO "TenantId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE technical_submittals RENAME COLUMN submitted_documents TO "SubmittedDocuments";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE technical_submittals RENAME COLUMN submitted_date TO "SubmittedDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE technical_submittals RENAME COLUMN submitted_by_user_id TO "SubmittedByUserId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE technical_submittals RENAME COLUMN submitted_by_id TO "SubmittedById";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE technical_submittals RENAME COLUMN submittal_type TO "SubmittalType";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE technical_submittals RENAME COLUMN submittal_number TO "SubmittalNumber";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE technical_submittals RENAME COLUMN specification_section TO "SpecificationSection";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE technical_submittals RENAME COLUMN specification_id TO "SpecificationId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE technical_submittals RENAME COLUMN signed_off_date TO "SignedOffDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE technical_submittals RENAME COLUMN signed_off_by_id TO "SignedOffById";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE technical_submittals RENAME COLUMN review_start_date TO "ReviewStartDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE technical_submittals RENAME COLUMN review_due_date TO "ReviewDueDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE technical_submittals RENAME COLUMN review_completion_date TO "ReviewCompletionDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE technical_submittals RENAME COLUMN resubmission_count TO "ResubmissionCount";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE technical_submittals RENAME COLUMN required_date TO "RequiredDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE technical_submittals RENAME COLUMN related_ncr_reference TO "RelatedNCRReference";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE technical_submittals RENAME COLUMN related_itp_reference TO "RelatedITPReference";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE technical_submittals RENAME COLUMN purchase_order_line_id TO "PurchaseOrderLineId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE technical_submittals RENAME COLUMN project_id TO "ProjectId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE technical_submittals RENAME COLUMN non_conformance_report_id TO "NonConformanceReportId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE technical_submittals RENAME COLUMN modified_at TO "ModifiedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE technical_submittals RENAME COLUMN max_resubmissions TO "MaxResubmissions";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE technical_submittals RENAME COLUMN is_final_documentation TO "IsFinalDocumentation";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE technical_submittals RENAME COLUMN is_deleted TO "IsDeleted";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE technical_submittals RENAME COLUMN is_as_built TO "IsAsBuilt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE technical_submittals RENAME COLUMN final_sign_off_date TO "FinalSignOffDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE technical_submittals RENAME COLUMN final_sign_off_by_id TO "FinalSignOffById";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE technical_submittals RENAME COLUMN cycle_count TO "CycleCount";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE technical_submittals RENAME COLUMN current_reviewer_id TO "CurrentReviewerId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE technical_submittals RENAME COLUMN current_review_cycle TO "CurrentReviewCycle";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE technical_submittals RENAME COLUMN current_overall_disposition TO "CurrentOverallDisposition";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE technical_submittals RENAME COLUMN created_at TO "CreatedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE technical_submittals RENAME COLUMN contract_id TO "ContractId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_technical_submittals_status RENAME TO "IX_technical_submittals_Status";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_technical_submittals_vendor_id RENAME TO "IX_technical_submittals_VendorId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_technical_submittals_tenant_id RENAME TO "IX_technical_submittals_TenantId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_technical_submittals_submitted_date RENAME TO "IX_technical_submittals_SubmittedDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_technical_submittals_submitted_by_user_id RENAME TO "IX_technical_submittals_SubmittedByUserId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_technical_submittals_submittal_number RENAME TO "IX_technical_submittals_SubmittalNumber";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_technical_submittals_specification_id RENAME TO "IX_technical_submittals_SpecificationId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_technical_submittals_required_date RENAME TO "IX_technical_submittals_RequiredDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_technical_submittals_purchase_order_line_id RENAME TO "IX_technical_submittals_PurchaseOrderLineId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_technical_submittals_project_id_submittal_number_revision RENAME TO "IX_technical_submittals_ProjectId_SubmittalNumber_Revision";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_technical_submittals_project_id RENAME TO "IX_technical_submittals_ProjectId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_technical_submittals_contract_id RENAME TO "IX_technical_submittals_ContractId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE suppliers RENAME COLUMN status TO "Status";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE suppliers RENAME COLUMN id TO "Id";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE suppliers RENAME COLUMN vendor_id TO "VendorId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE suppliers RENAME COLUMN sustainability_score TO "SustainabilityScore";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE suppliers RENAME COLUMN supplier_name TO "SupplierName";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE suppliers RENAME COLUMN risk_rating TO "RiskRating";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE suppliers RENAME COLUMN modified_at TO "ModifiedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE suppliers RENAME COLUMN is_csr_compliant TO "IsCsrCompliant";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE suppliers RENAME COLUMN is_contract_manufacturer TO "IsContractManufacturer";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE suppliers RENAME COLUMN created_at TO "CreatedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE suppliers RENAME COLUMN capacity_utilization_percent TO "CapacityUtilizationPercent";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE suppliers RENAME COLUMN average_lead_time_days TO "AverageLeadTimeDays";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE suppliers RENAME COLUMN emergency_contacts TO "EmergencyContacts";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_suppliers_vendor_id RENAME TO "IX_suppliers_VendorId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_suppliers_risk_rating RENAME TO "IX_suppliers_RiskRating";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_suppliers_is_contract_manufacturer RENAME TO "IX_suppliers_IsContractManufacturer";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE submittal_reviews RENAME COLUMN disposition TO "Disposition";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE submittal_reviews RENAME COLUMN comments TO "Comments";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE submittal_reviews RENAME COLUMN id TO "Id";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE submittal_reviews RENAME COLUMN technical_submittal_id1 TO "TechnicalSubmittalId1";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE submittal_reviews RENAME COLUMN technical_submittal_id TO "TechnicalSubmittalId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE submittal_reviews RENAME COLUMN reviewer_name TO "ReviewerName";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE submittal_reviews RENAME COLUMN reviewer_id TO "ReviewerId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE submittal_reviews RENAME COLUMN reviewer_guid TO "ReviewerGuid";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE submittal_reviews RENAME COLUMN review_date TO "ReviewDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE submittal_reviews RENAME COLUMN review_cycle TO "ReviewCycle";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE submittal_reviews RENAME COLUMN modified_at TO "ModifiedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE submittal_reviews RENAME COLUMN markup_document TO "MarkupDocument";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE submittal_reviews RENAME COLUMN created_at TO "CreatedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_submittal_reviews_disposition RENAME TO "IX_submittal_reviews_Disposition";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_submittal_reviews_technical_submittal_id1 RENAME TO "IX_submittal_reviews_TechnicalSubmittalId1";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_submittal_reviews_technical_submittal_id RENAME TO "IX_submittal_reviews_TechnicalSubmittalId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_submittal_reviews_reviewer_id RENAME TO "IX_submittal_reviews_ReviewerId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_submittal_reviews_review_date RENAME TO "IX_submittal_reviews_ReviewDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_territory_representatives RENAME COLUMN representative_id TO "RepresentativeId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_territory_representatives RENAME COLUMN sales_territory_id TO "SalesTerritoryId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_sales_territory_representatives_representative_id RENAME TO "IX_sales_territory_representatives_RepresentativeId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_territories RENAME COLUMN name TO "Name";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_territories RENAME COLUMN description TO "Description";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_territories RENAME COLUMN code TO "Code";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_territories RENAME COLUMN id TO "Id";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_territories RENAME COLUMN territory_code TO "TerritoryCode";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_territories RENAME COLUMN tenant_id TO "TenantId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_territories RENAME COLUMN primary_salesperson_id TO "PrimarySalespersonId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_territories RENAME COLUMN parent_territory_id TO "ParentTerritoryId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_territories RENAME COLUMN modified_at TO "ModifiedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_territories RENAME COLUMN is_deleted TO "IsDeleted";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_territories RENAME COLUMN created_at TO "CreatedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_sales_territories_name RENAME TO "IX_sales_territories_Name";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_sales_territories_territory_code RENAME TO "IX_sales_territories_TerritoryCode";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_sales_territories_tenant_id RENAME TO "IX_sales_territories_TenantId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_sales_territories_parent_territory_id RENAME TO "IX_sales_territories_ParentTerritoryId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.sales_orders RENAME COLUMN status TO "Status";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.sales_orders RENAME COLUMN id TO "Id";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.sales_orders RENAME COLUMN tenant_id TO "TenantId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.sales_orders RENAME COLUMN salesperson_id TO "SalespersonId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.sales_orders RENAME COLUMN sales_territory_id1 TO "SalesTerritoryId1";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.sales_orders RENAME COLUMN sales_territory_id TO "SalesTerritoryId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.sales_orders RENAME COLUMN related_return_authorization_id TO "RelatedReturnAuthorizationId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.sales_orders RENAME COLUMN order_number TO "OrderNumber";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.sales_orders RENAME COLUMN order_date TO "OrderDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.sales_orders RENAME COLUMN modified_at TO "ModifiedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.sales_orders RENAME COLUMN is_drop_shipment TO "IsDropShipment";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.sales_orders RENAME COLUMN is_deleted TO "IsDeleted";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.sales_orders RENAME COLUMN is_credit_approved TO "IsCreditApproved";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.sales_orders RENAME COLUMN is_atp_confirmed TO "IsAtpConfirmed";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.sales_orders RENAME COLUMN edi_transaction_reference TO "EdiTransactionReference";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.sales_orders RENAME COLUMN customer_id TO "CustomerId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.sales_orders RENAME COLUMN currency_code TO "CurrencyCode";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.sales_orders RENAME COLUMN created_at TO "CreatedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.sales_orders RENAME COLUMN commission_rate TO "CommissionRate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.sales_orders RENAME COLUMN atp_check_date TO "AtpCheckDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_sales_orders_status RENAME TO "IX_sales_orders_Status";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_sales_orders_tenant_id_order_number RENAME TO "IX_sales_orders_TenantId_OrderNumber";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_sales_orders_tenant_id RENAME TO "IX_sales_orders_TenantId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_sales_orders_salesperson_id RENAME TO "IX_sales_orders_SalespersonId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_sales_orders_sales_territory_id1 RENAME TO "IX_sales_orders_SalesTerritoryId1";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_sales_orders_sales_territory_id RENAME TO "IX_sales_orders_SalesTerritoryId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_sales_orders_related_return_authorization_id RENAME TO "IX_sales_orders_RelatedReturnAuthorizationId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_sales_orders_order_date RENAME TO "IX_sales_orders_OrderDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_sales_orders_is_deleted RENAME TO "IX_sales_orders_IsDeleted";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_sales_orders_edi_transaction_reference RENAME TO "IX_sales_orders_EdiTransactionReference";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_sales_orders_customer_id RENAME TO "IX_sales_orders_CustomerId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_order_lines RENAME COLUMN quantity TO "Quantity";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_order_lines RENAME COLUMN id TO "Id";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_order_lines RENAME COLUMN description TO "Description";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_order_lines RENAME COLUMN warranty_end_date TO "WarrantyEndDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_order_lines RENAME COLUMN vendor_product_id TO "VendorProductId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_order_lines RENAME COLUMN unit_price_currency TO "UnitPriceCurrency";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_order_lines RENAME COLUMN unit_price_amount TO "UnitPriceAmount";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_order_lines RENAME COLUMN unit_of_measure TO "UnitOfMeasure";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_order_lines RENAME COLUMN tenant_id TO "TenantId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_order_lines RENAME COLUMN sku_snapshot TO "SkuSnapshot";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_order_lines RENAME COLUMN reserved_serial_numbers_json TO "ReservedSerialNumbersJson";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_order_lines RENAME COLUMN requested_delivery_date TO "RequestedDeliveryDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_order_lines RENAME COLUMN quantity_backordered TO "QuantityBackordered";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_order_lines RENAME COLUMN project_id1 TO "ProjectId1";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_order_lines RENAME COLUMN project_id TO "ProjectId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_order_lines RENAME COLUMN product_id TO "ProductId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_order_lines RENAME COLUMN parent_sales_order_line_sales_order_id TO "ParentSalesOrderLineSalesOrderId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_order_lines RENAME COLUMN parent_sales_order_line_line_number TO "ParentSalesOrderLineLineNumber";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_order_lines RENAME COLUMN parent_sales_order_line_id TO "ParentSalesOrderLineId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_order_lines RENAME COLUMN modified_at TO "ModifiedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_order_lines RENAME COLUMN line_total_currency TO "LineTotalCurrency";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_order_lines RENAME COLUMN line_total_amount TO "LineTotalAmount";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_order_lines RENAME COLUMN is_kit_component TO "IsKitComponent";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_order_lines RENAME COLUMN is_deleted TO "IsDeleted";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_order_lines RENAME COLUMN discount_amount_value TO "DiscountAmountValue";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_order_lines RENAME COLUMN discount_amount_currency TO "DiscountAmountCurrency";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_order_lines RENAME COLUMN description_snapshot TO "DescriptionSnapshot";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_order_lines RENAME COLUMN created_at TO "CreatedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_order_lines RENAME COLUMN cost_code TO "CostCode";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_order_lines RENAME COLUMN applied_discount_description TO "AppliedDiscountDescription";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_order_lines RENAME COLUMN line_number TO "LineNumber";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_order_lines RENAME COLUMN sales_order_id TO "SalesOrderId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_sales_order_lines_vendor_product_id RENAME TO "IX_sales_order_lines_VendorProductId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_sales_order_lines_project_id1 RENAME TO "IX_sales_order_lines_ProjectId1";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_sales_order_lines_project_id RENAME TO "IX_sales_order_lines_ProjectId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_sales_order_lines_product_id RENAME TO "IX_sales_order_lines_ProductId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_sales_order_lines_parent_sales_order_line_sales_order_id_pa RENAME TO "IX_sales_order_lines_ParentSalesOrderLineSalesOrderId_ParentSa~";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorizations RENAME COLUMN status TO "Status";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorizations RENAME COLUMN notes TO "Notes";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorizations RENAME COLUMN id TO "Id";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorizations RENAME COLUMN tenant_id TO "TenantId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorizations RENAME COLUMN shipping_instructions TO "ShippingInstructions";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorizations RENAME COLUMN sales_order_id TO "SalesOrderId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorizations RENAME COLUMN rma_number TO "RmaNumber";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorizations RENAME COLUMN requested_action TO "RequestedAction";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorizations RENAME COLUMN request_date TO "RequestDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorizations RENAME COLUMN reason_for_return TO "ReasonForReturn";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorizations RENAME COLUMN original_sales_order_id TO "OriginalSalesOrderId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorizations RENAME COLUMN modified_at TO "ModifiedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorizations RENAME COLUMN is_deleted TO "IsDeleted";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorizations RENAME COLUMN invoice_id TO "InvoiceId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorizations RENAME COLUMN expiry_date TO "ExpiryDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorizations RENAME COLUMN customer_id TO "CustomerId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorizations RENAME COLUMN created_at TO "CreatedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorizations RENAME COLUMN authorization_date TO "AuthorizationDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_return_authorizations_status RENAME TO "IX_return_authorizations_Status";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_return_authorizations_tenant_id RENAME TO "IX_return_authorizations_TenantId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_return_authorizations_sales_order_id RENAME TO "IX_return_authorizations_SalesOrderId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_return_authorizations_rma_number RENAME TO "IX_return_authorizations_RmaNumber";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_return_authorizations_request_date RENAME TO "IX_return_authorizations_RequestDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_return_authorizations_invoice_id RENAME TO "IX_return_authorizations_InvoiceId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_return_authorizations_customer_id RENAME TO "IX_return_authorizations_CustomerId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorization_lines RENAME COLUMN id TO "Id";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorization_lines RENAME COLUMN vendor_product_id TO "VendorProductId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorization_lines RENAME COLUMN unit_of_measure TO "UnitOfMeasure";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorization_lines RENAME COLUMN sku_snapshot TO "SkuSnapshot";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorization_lines RENAME COLUMN sales_order_line_number TO "SalesOrderLineNumber";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorization_lines RENAME COLUMN sales_order_id TO "SalesOrderId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorization_lines RENAME COLUMN requested_action TO "RequestedAction";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorization_lines RENAME COLUMN reason_for_return TO "ReasonForReturn";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorization_lines RENAME COLUMN quantity_received TO "QuantityReceived";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorization_lines RENAME COLUMN quantity_authorized TO "QuantityAuthorized";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorization_lines RENAME COLUMN product_id TO "ProductId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorization_lines RENAME COLUMN product_definition_id TO "ProductDefinitionId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorization_lines RENAME COLUMN original_sales_order_line_sales_order_id TO "OriginalSalesOrderLineSalesOrderId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorization_lines RENAME COLUMN original_sales_order_line_line_number TO "OriginalSalesOrderLineLineNumber";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorization_lines RENAME COLUMN original_sales_order_line_id TO "OriginalSalesOrderLineId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorization_lines RENAME COLUMN modified_at TO "ModifiedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorization_lines RENAME COLUMN item_condition TO "ItemCondition";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorization_lines RENAME COLUMN invoice_line_number TO "InvoiceLineNumber";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorization_lines RENAME COLUMN invoice_id TO "InvoiceId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorization_lines RENAME COLUMN description_snapshot TO "DescriptionSnapshot";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorization_lines RENAME COLUMN created_at TO "CreatedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorization_lines RENAME COLUMN line_number TO "LineNumber";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorization_lines RENAME COLUMN return_authorization_id TO "ReturnAuthorizationId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_return_authorization_lines_vendor_product_id RENAME TO "IX_return_authorization_lines_VendorProductId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_return_authorization_lines_sales_order_id_sales_order_line_ RENAME TO "IX_return_authorization_lines_SalesOrderId_SalesOrderLineNumber";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_return_authorization_lines_product_id RENAME TO "IX_return_authorization_lines_ProductId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_return_authorization_lines_product_definition_id RENAME TO "IX_return_authorization_lines_ProductDefinitionId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_return_authorization_lines_original_sales_order_line_sales_ RENAME TO "IX_return_authorization_lines_OriginalSalesOrderLineSalesOrder~";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_return_authorization_lines_invoice_id_invoice_line_number RENAME TO "IX_return_authorization_lines_InvoiceId_InvoiceLineNumber";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE requests_for_proposal RENAME COLUMN title TO "Title";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE requests_for_proposal RENAME COLUMN status TO "Status";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE requests_for_proposal RENAME COLUMN department TO "Department";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE requests_for_proposal RENAME COLUMN id TO "Id";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE requests_for_proposal RENAME COLUMN tenant_id TO "TenantId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE requests_for_proposal RENAME COLUMN submission_deadline TO "SubmissionDeadline";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE requests_for_proposal RENAME COLUMN scope_of_work TO "ScopeOfWork";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE requests_for_proposal RENAME COLUMN rfp_number TO "RfpNumber";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE requests_for_proposal RENAME COLUMN question_deadline TO "QuestionDeadline";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE requests_for_proposal RENAME COLUMN project_id TO "ProjectId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE requests_for_proposal RENAME COLUMN modified_at TO "ModifiedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE requests_for_proposal RENAME COLUMN issued_date TO "IssuedDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE requests_for_proposal RENAME COLUMN issued_by_user_id TO "IssuedByUserId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE requests_for_proposal RENAME COLUMN issued_by_name TO "IssuedByName";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE requests_for_proposal RENAME COLUMN issue_date TO "IssueDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE requests_for_proposal RENAME COLUMN expected_contract_start_date TO "ExpectedContractStartDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE requests_for_proposal RENAME COLUMN expected_contract_duration TO "ExpectedContractDuration";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE requests_for_proposal RENAME COLUMN evaluation_criteria TO "EvaluationCriteria";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE requests_for_proposal RENAME COLUMN decision_date TO "DecisionDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE requests_for_proposal RENAME COLUMN created_at TO "CreatedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE requests_for_proposal RENAME COLUMN completed_date TO "CompletedDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE requests_for_proposal RENAME COLUMN awarded_vendor_id TO "AwardedVendorId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE requests_for_proposal RENAME COLUMN awarded_contract_id TO "AwardedContractId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_requests_for_proposal_status RENAME TO "IX_requests_for_proposal_Status";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_requests_for_proposal_tenant_id RENAME TO "IX_requests_for_proposal_TenantId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_requests_for_proposal_submission_deadline RENAME TO "IX_requests_for_proposal_SubmissionDeadline";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_requests_for_proposal_rfp_number RENAME TO "IX_requests_for_proposal_RfpNumber";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_requests_for_proposal_project_id RENAME TO "IX_requests_for_proposal_ProjectId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_requests_for_proposal_awarded_vendor_id RENAME TO "IX_requests_for_proposal_AwardedVendorId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_requests_for_proposal_awarded_contract_id RENAME TO "IX_requests_for_proposal_AwardedContractId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE requests_for_information RENAME COLUMN title TO "Title";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE requests_for_information RENAME COLUMN status TO "Status";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE requests_for_information RENAME COLUMN notes TO "Notes";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE requests_for_information RENAME COLUMN description TO "Description";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE requests_for_information RENAME COLUMN id TO "Id";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE requests_for_information RENAME COLUMN tenant_id TO "TenantId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE requests_for_information RENAME COLUMN target_audience_description TO "TargetAudienceDescription";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE requests_for_information RENAME COLUMN rfi_number TO "RfiNumber";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE requests_for_information RENAME COLUMN response_due_date TO "ResponseDueDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE requests_for_information RENAME COLUMN response_deadline TO "ResponseDeadline";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE requests_for_information RENAME COLUMN project_id TO "ProjectId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE requests_for_information RENAME COLUMN modified_at TO "ModifiedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE requests_for_information RENAME COLUMN issued_date TO "IssuedDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE requests_for_information RENAME COLUMN issued_by_user_id TO "IssuedByUserId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE requests_for_information RENAME COLUMN issued_by_name TO "IssuedByName";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE requests_for_information RENAME COLUMN issue_date TO "IssueDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE requests_for_information RENAME COLUMN created_at TO "CreatedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_requests_for_information_status RENAME TO "IX_requests_for_information_Status";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_requests_for_information_tenant_id RENAME TO "IX_requests_for_information_TenantId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_requests_for_information_rfi_number RENAME TO "IX_requests_for_information_RfiNumber";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_requests_for_information_response_deadline RENAME TO "IX_requests_for_information_ResponseDeadline";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_requests_for_information_project_id RENAME TO "IX_requests_for_information_ProjectId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quotes RENAME COLUMN title TO "Title";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quotes RENAME COLUMN status TO "Status";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quotes RENAME COLUMN description TO "Description";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quotes RENAME COLUMN id TO "Id";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quotes RENAME COLUMN vendor_reference_instructions TO "VendorReferenceInstructions";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quotes RENAME COLUMN terms_and_conditions TO "TermsAndConditions";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quotes RENAME COLUMN tenant_id TO "TenantId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quotes RENAME COLUMN submission_deadline TO "SubmissionDeadline";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quotes RENAME COLUMN scope_of_work TO "ScopeOfWork";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quotes RENAME COLUMN rfq_number TO "RFQNumber";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quotes RENAME COLUMN required_delivery_date TO "RequiredDeliveryDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quotes RENAME COLUMN related_agreement_id TO "RelatedAgreementId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quotes RENAME COLUMN originating_requisition_id TO "OriginatingRequisitionId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quotes RENAME COLUMN modified_at TO "ModifiedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quotes RENAME COLUMN is_deleted TO "IsDeleted";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quotes RENAME COLUMN currency_code TO "CurrencyCode";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quotes RENAME COLUMN created_by_user_id TO "CreatedByUserId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quotes RENAME COLUMN created_at TO "CreatedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quotes RENAME COLUMN contact_person_email TO "ContactPersonEmail";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quotes RENAME COLUMN communication_method TO "CommunicationMethod";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quotes RENAME COLUMN awarded_vendor_id TO "AwardedVendorId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_request_for_quotes_status RENAME TO "IX_request_for_quotes_Status";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_request_for_quotes_tenant_id_rfq_number RENAME TO "IX_request_for_quotes_TenantId_RFQNumber";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_request_for_quotes_tenant_id RENAME TO "IX_request_for_quotes_TenantId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_request_for_quotes_submission_deadline RENAME TO "IX_request_for_quotes_SubmissionDeadline";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_request_for_quotes_related_agreement_id RENAME TO "IX_request_for_quotes_RelatedAgreementId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_request_for_quotes_originating_requisition_id RENAME TO "IX_request_for_quotes_OriginatingRequisitionId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_request_for_quotes_is_deleted RENAME TO "IX_request_for_quotes_IsDeleted";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_request_for_quotes_created_by_user_id RENAME TO "IX_request_for_quotes_CreatedByUserId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_request_for_quotes_awarded_vendor_id RENAME TO "IX_request_for_quotes_AwardedVendorId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quote_lines RENAME COLUMN quantity TO "Quantity";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quote_lines RENAME COLUMN notes TO "Notes";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quote_lines RENAME COLUMN description TO "Description";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quote_lines RENAME COLUMN id TO "Id";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quote_lines RENAME COLUMN vendor_product_id TO "VendorProductId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quote_lines RENAME COLUMN unit_of_measure TO "UnitOfMeasure";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quote_lines RENAME COLUMN tenant_id TO "TenantId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quote_lines RENAME COLUMN technical_specifications TO "TechnicalSpecifications";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quote_lines RENAME COLUMN sample_required TO "SampleRequired";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quote_lines RENAME COLUMN request_for_quote_id TO "RequestForQuoteId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quote_lines RENAME COLUMN product_definition_id TO "ProductDefinitionId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quote_lines RENAME COLUMN preferred_incoterm TO "PreferredIncoterm";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quote_lines RENAME COLUMN modified_at TO "ModifiedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quote_lines RENAME COLUMN minimum_order_quantity TO "MinimumOrderQuantity";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quote_lines RENAME COLUMN line_number TO "LineNumber";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quote_lines RENAME COLUMN is_substitute_allowed TO "IsSubstituteAllowed";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quote_lines RENAME COLUMN is_deleted TO "IsDeleted";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quote_lines RENAME COLUMN created_at TO "CreatedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quote_lines RENAME COLUMN alternate_item_proposal TO "AlternateItemProposal";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_request_for_quote_lines_vendor_product_id RENAME TO "IX_request_for_quote_lines_VendorProductId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_request_for_quote_lines_tenant_id RENAME TO "IX_request_for_quote_lines_TenantId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_request_for_quote_lines_request_for_quote_id_line_number RENAME TO "IX_request_for_quote_lines_RequestForQuoteId_LineNumber";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_request_for_quote_lines_product_definition_id RENAME TO "IX_request_for_quote_lines_ProductDefinitionId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_request_for_quote_lines_is_deleted RENAME TO "IX_request_for_quote_lines_IsDeleted";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseRequisitions" RENAME COLUMN status TO "Status";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseRequisitions" RENAME COLUMN notes TO "Notes";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseRequisitions" RENAME COLUMN justification TO "Justification";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseRequisitions" RENAME COLUMN department TO "Department";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseRequisitions" RENAME COLUMN id TO "Id";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseRequisitions" RENAME COLUMN tenant_id TO "TenantId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseRequisitions" RENAME COLUMN requisition_number TO "RequisitionNumber";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseRequisitions" RENAME COLUMN requestor_user_id TO "RequestorUserId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseRequisitions" RENAME COLUMN requestor_name TO "RequestorName";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseRequisitions" RENAME COLUMN requestor_email TO "RequestorEmail";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseRequisitions" RENAME COLUMN request_date TO "RequestDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseRequisitions" RENAME COLUMN modified_at TO "ModifiedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseRequisitions" RENAME COLUMN date_needed TO "DateNeeded";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseRequisitions" RENAME COLUMN currency_code TO "CurrencyCode";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseRequisitions" RENAME COLUMN created_at TO "CreatedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseRequisitions" RENAME COLUMN associated_purchase_order_id TO "AssociatedPurchaseOrderId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_purchase_requisitions_tenant_id_requisition_number RENAME TO "IX_PurchaseRequisitions_TenantId_RequisitionNumber";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_purchase_requisitions_tenant_id RENAME TO "IX_PurchaseRequisitions_TenantId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_purchase_requisitions_status RENAME TO "IX_PurchaseRequisitions_Status";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_purchase_requisitions_requestor_user_id RENAME TO "IX_PurchaseRequisitions_RequestorUserId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_purchase_requisitions_request_date RENAME TO "IX_PurchaseRequisitions_RequestDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_purchase_requisitions_associated_purchase_order_id RENAME TO "IX_PurchaseRequisitions_AssociatedPurchaseOrderId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseRequisitionLines" RENAME COLUMN quantity TO "Quantity";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseRequisitionLines" RENAME COLUMN notes TO "Notes";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseRequisitionLines" RENAME COLUMN description TO "Description";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseRequisitionLines" RENAME COLUMN id TO "Id";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseRequisitionLines" RENAME COLUMN vendor_product_id TO "VendorProductId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseRequisitionLines" RENAME COLUMN unit_of_measure TO "UnitOfMeasure";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseRequisitionLines" RENAME COLUMN tenant_id TO "TenantId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseRequisitionLines" RENAME COLUMN suggested_vendor_id TO "SuggestedVendorId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseRequisitionLines" RENAME COLUMN purchase_requisition_id TO "PurchaseRequisitionId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseRequisitionLines" RENAME COLUMN product_definition_id TO "ProductDefinitionId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseRequisitionLines" RENAME COLUMN modified_at TO "ModifiedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseRequisitionLines" RENAME COLUMN line_number TO "LineNumber";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseRequisitionLines" RENAME COLUMN gl_account_code TO "GLAccountCode";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseRequisitionLines" RENAME COLUMN date_needed TO "DateNeeded";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseRequisitionLines" RENAME COLUMN created_at TO "CreatedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_purchase_requisition_lines_vendor_product_id RENAME TO "IX_PurchaseRequisitionLines_VendorProductId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_purchase_requisition_lines_tenant_id RENAME TO "IX_PurchaseRequisitionLines_TenantId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_purchase_requisition_lines_suggested_vendor_id RENAME TO "IX_PurchaseRequisitionLines_SuggestedVendorId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_purchase_requisition_lines_purchase_requisition_id_line_number RENAME TO "IX_PurchaseRequisitionLines_PurchaseRequisitionId_LineNumber";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_purchase_requisition_lines_product_definition_id RENAME TO "IX_PurchaseRequisitionLines_ProductDefinitionId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_purchase_requisition_lines_gl_account_code RENAME TO "IX_PurchaseRequisitionLines_GLAccountCode";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseOrderLines" RENAME COLUMN quantity TO "Quantity";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseOrderLines" RENAME COLUMN notes TO "Notes";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseOrderLines" RENAME COLUMN id TO "Id";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseOrderLines" RENAME COLUMN vendor_product_id TO "VendorProductId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseOrderLines" RENAME COLUMN unit_of_measure_snapshot TO "UnitOfMeasureSnapshot";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseOrderLines" RENAME COLUMN sku_snapshot TO "SkuSnapshot";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseOrderLines" RENAME COLUMN purchase_order_id TO "PurchaseOrderId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseOrderLines" RENAME COLUMN modified_at TO "ModifiedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseOrderLines" RENAME COLUMN description_snapshot TO "DescriptionSnapshot";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseOrderLines" RENAME COLUMN created_at TO "CreatedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_purchase_order_lines_vendor_product_id RENAME TO "IX_PurchaseOrderLines_VendorProductId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_purchase_order_lines_purchase_order_id RENAME TO "IX_PurchaseOrderLines_PurchaseOrderId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE purchase_orders RENAME COLUMN status TO "Status";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE purchase_orders RENAME COLUMN id TO "Id";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE purchase_orders RENAME COLUMN vendor_id TO "VendorId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE purchase_orders RENAME COLUMN requisition_id TO "RequisitionId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE purchase_orders RENAME COLUMN payment_terms TO "PaymentTerms";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE purchase_orders RENAME COLUMN order_number TO "OrderNumber";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE purchase_orders RENAME COLUMN order_date TO "OrderDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE purchase_orders RENAME COLUMN modified_at TO "ModifiedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE purchase_orders RENAME COLUMN delivery_date TO "DeliveryDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE purchase_orders RENAME COLUMN currency_code TO "CurrencyCode";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE purchase_orders RENAME COLUMN created_at TO "CreatedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE purchase_orders RENAME COLUMN contract_id TO "ContractId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_purchase_orders_status RENAME TO "IX_purchase_orders_Status";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_purchase_orders_vendor_id RENAME TO "IX_purchase_orders_VendorId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_purchase_orders_requisition_id RENAME TO "IX_purchase_orders_RequisitionId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_purchase_orders_order_number RENAME TO "IX_purchase_orders_OrderNumber";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_purchase_orders_order_date RENAME TO "IX_purchase_orders_OrderDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_purchase_orders_contract_id RENAME TO "IX_purchase_orders_ContractId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.projects RENAME COLUMN status TO "Status";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.projects RENAME COLUMN name TO "Name";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.projects RENAME COLUMN description TO "Description";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.projects RENAME COLUMN id TO "Id";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.projects RENAME COLUMN tenant_id TO "TenantId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.projects RENAME COLUMN start_date TO "StartDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.projects RENAME COLUMN project_code TO "ProjectCode";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.projects RENAME COLUMN modified_at TO "ModifiedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.projects RENAME COLUMN is_deleted TO "IsDeleted";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.projects RENAME COLUMN end_date TO "EndDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.projects RENAME COLUMN created_at TO "CreatedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_projects_status RENAME TO "IX_projects_Status";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_projects_name RENAME TO "IX_projects_Name";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_projects_tenant_id_project_code RENAME TO "IX_projects_TenantId_ProjectCode";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_projects_tenant_id RENAME TO "IX_projects_TenantId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_projects_start_date RENAME TO "IX_projects_StartDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_projects_is_deleted RENAME TO "IX_projects_IsDeleted";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_projects_end_date RENAME TO "IX_projects_EndDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.products RENAME COLUMN name TO "Name";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.products RENAME COLUMN description TO "Description";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.products RENAME COLUMN id TO "Id";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.products RENAME COLUMN unit_of_measure TO "UnitOfMeasure";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.products RENAME COLUMN tenant_id TO "TenantId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.products RENAME COLUMN product_code TO "ProductCode";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.products RENAME COLUMN modified_at TO "ModifiedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.products RENAME COLUMN is_deleted TO "IsDeleted";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.products RENAME COLUMN is_active TO "IsActive";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.products RENAME COLUMN created_at TO "CreatedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.products RENAME COLUMN category_id TO "CategoryId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE product_definitions RENAME COLUMN version TO "Version";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE product_definitions RENAME COLUMN upc TO "Upc";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE product_definitions RENAME COLUMN sku TO "Sku";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE product_definitions RENAME COLUMN name TO "Name";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE product_definitions RENAME COLUMN gtin TO "Gtin";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE product_definitions RENAME COLUMN ean TO "Ean";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE product_definitions RENAME COLUMN description TO "Description";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE product_definitions RENAME COLUMN id TO "Id";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE product_definitions RENAME COLUMN tenant_id TO "TenantId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE product_definitions RENAME COLUMN modified_at TO "ModifiedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE product_definitions RENAME COLUMN lifecycle_state TO "LifecycleState";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE product_definitions RENAME COLUMN is_deleted TO "IsDeleted";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE product_definitions RENAME COLUMN created_at TO "CreatedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE product_definitions RENAME COLUMN category_id TO "CategoryId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE product_definitions RENAME COLUMN attributes_json TO "AttributesJson";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_product_definitions_upc RENAME TO "IX_product_definitions_Upc";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_product_definitions_sku RENAME TO "IX_product_definitions_Sku";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_product_definitions_gtin RENAME TO "IX_product_definitions_Gtin";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_product_definitions_ean RENAME TO "IX_product_definitions_Ean";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_product_definitions_is_deleted RENAME TO "IX_product_definitions_IsDeleted";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_product_definitions_category_id RENAME TO "IX_product_definitions_CategoryId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE procurement_workflows RENAME COLUMN version TO "Version";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE procurement_workflows RENAME COLUMN name TO "Name";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE procurement_workflows RENAME COLUMN description TO "Description";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE procurement_workflows RENAME COLUMN id TO "Id";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE procurement_workflows RENAME COLUMN workflow_type TO "WorkflowType";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE procurement_workflows RENAME COLUMN workflow_name TO "WorkflowName";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE procurement_workflows RENAME COLUMN tenant_id TO "TenantId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE procurement_workflows RENAME COLUMN subject_document_type TO "SubjectDocumentType";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE procurement_workflows RENAME COLUMN subject_document_id TO "SubjectDocumentId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE procurement_workflows RENAME COLUMN started_date TO "StartedDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE procurement_workflows RENAME COLUMN modified_at TO "ModifiedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE procurement_workflows RENAME COLUMN is_active TO "IsActive";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE procurement_workflows RENAME COLUMN initiated_by_user_id TO "InitiatedByUserId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE procurement_workflows RENAME COLUMN initiated_by_name TO "InitiatedByName";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE procurement_workflows RENAME COLUMN current_status TO "CurrentStatus";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE procurement_workflows RENAME COLUMN created_at TO "CreatedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE procurement_workflows RENAME COLUMN completed_date TO "CompletedDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_procurement_workflows_name RENAME TO "IX_procurement_workflows_Name";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_procurement_workflows_workflow_type RENAME TO "IX_procurement_workflows_WorkflowType";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_procurement_workflows_tenant_id_workflow_type_name RENAME TO "IX_procurement_workflows_TenantId_WorkflowType_Name";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_procurement_workflows_tenant_id RENAME TO "IX_procurement_workflows_TenantId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_procurement_workflows_is_active RENAME TO "IX_procurement_workflows_IsActive";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE procurement_workflow_steps RENAME COLUMN status TO "Status";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE procurement_workflow_steps RENAME COLUMN comments TO "Comments";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE procurement_workflow_steps RENAME COLUMN id TO "Id";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE procurement_workflow_steps RENAME COLUMN workflow_id TO "WorkflowId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE procurement_workflow_steps RENAME COLUMN step_order TO "StepOrder";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE procurement_workflow_steps RENAME COLUMN step_name TO "StepName";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE procurement_workflow_steps RENAME COLUMN sla_duration TO "SlaDuration";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE procurement_workflow_steps RENAME COLUMN sequence_order TO "SequenceOrder";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE procurement_workflow_steps RENAME COLUMN procurement_workflow_id TO "ProcurementWorkflowId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE procurement_workflow_steps RENAME COLUMN modified_at TO "ModifiedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE procurement_workflow_steps RENAME COLUMN created_at TO "CreatedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE procurement_workflow_steps RENAME COLUMN condition_expression TO "ConditionExpression";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE procurement_workflow_steps RENAME COLUMN assignee_user_id TO "AssigneeUserId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE procurement_workflow_steps RENAME COLUMN assignee_name TO "AssigneeName";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE procurement_workflow_steps RENAME COLUMN assigned_date TO "AssignedDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE procurement_workflow_steps RENAME COLUMN approver_user_id TO "ApproverUserId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE procurement_workflow_steps RENAME COLUMN approver_role_id TO "ApproverRoleId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE procurement_workflow_steps RENAME COLUMN action_date TO "ActionDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_procurement_workflow_steps_workflow_id RENAME TO "IX_procurement_workflow_steps_WorkflowId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_procurement_workflow_steps_procurement_workflow_id_step_ord RENAME TO "IX_procurement_workflow_steps_ProcurementWorkflowId_StepOrder";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_procurement_workflow_steps_procurement_workflow_id RENAME TO "IX_procurement_workflow_steps_ProcurementWorkflowId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_procurement_workflow_steps_approver_user_id RENAME TO "IX_procurement_workflow_steps_ApproverUserId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_procurement_workflow_steps_approver_role_id RENAME TO "IX_procurement_workflow_steps_ApproverRoleId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE payment_transactions RENAME COLUMN status TO "Status";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE payment_transactions RENAME COLUMN notes TO "Notes";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE payment_transactions RENAME COLUMN amount TO "Amount";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE payment_transactions RENAME COLUMN id TO "Id";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE payment_transactions RENAME COLUMN vendor_id TO "VendorId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE payment_transactions RENAME COLUMN transaction_reference TO "TransactionReference";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE payment_transactions RENAME COLUMN tenant_id TO "TenantId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE payment_transactions RENAME COLUMN payment_method TO "PaymentMethod";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE payment_transactions RENAME COLUMN payment_date TO "PaymentDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE payment_transactions RENAME COLUMN modified_at TO "ModifiedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE payment_transactions RENAME COLUMN invoice_id TO "InvoiceId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE payment_transactions RENAME COLUMN currency_code TO "CurrencyCode";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE payment_transactions RENAME COLUMN created_at TO "CreatedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE payment_transactions RENAME COLUMN bank_reference TO "BankReference";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_payment_transactions_status RENAME TO "IX_payment_transactions_Status";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_payment_transactions_vendor_id RENAME TO "IX_payment_transactions_VendorId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_payment_transactions_tenant_id RENAME TO "IX_payment_transactions_TenantId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_payment_transactions_payment_date RENAME TO "IX_payment_transactions_PaymentDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_payment_transactions_invoice_id RENAME TO "IX_payment_transactions_InvoiceId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE invoices RENAME COLUMN subtotal TO "Subtotal";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE invoices RENAME COLUMN status TO "Status";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE invoices RENAME COLUMN notes TO "Notes";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE invoices RENAME COLUMN id TO "Id";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE invoices RENAME COLUMN vendor_id TO "VendorId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE invoices RENAME COLUMN total_amount TO "TotalAmount";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE invoices RENAME COLUMN tenant_id TO "TenantId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE invoices RENAME COLUMN tax_amount TO "TaxAmount";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE invoices RENAME COLUMN sub_total TO "SubTotal";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE invoices RENAME COLUMN purchase_order_id TO "PurchaseOrderId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE invoices RENAME COLUMN payment_terms TO "PaymentTerms";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE invoices RENAME COLUMN payment_date TO "PaymentDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE invoices RENAME COLUMN modified_at TO "ModifiedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE invoices RENAME COLUMN invoice_number TO "InvoiceNumber";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE invoices RENAME COLUMN invoice_date TO "InvoiceDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE invoices RENAME COLUMN due_date TO "DueDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE invoices RENAME COLUMN customer_id TO "CustomerId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE invoices RENAME COLUMN currency_code TO "CurrencyCode";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE invoices RENAME COLUMN created_at TO "CreatedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE invoices RENAME COLUMN amount_paid TO "AmountPaid";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_invoices_status RENAME TO "IX_invoices_Status";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_invoices_vendor_id_invoice_number RENAME TO "IX_invoices_VendorId_InvoiceNumber";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_invoices_vendor_id RENAME TO "IX_invoices_VendorId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_invoices_tenant_id RENAME TO "IX_invoices_TenantId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_invoices_purchase_order_id RENAME TO "IX_invoices_PurchaseOrderId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_invoices_invoice_date RENAME TO "IX_invoices_InvoiceDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_invoices_due_date RENAME TO "IX_invoices_DueDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_invoices_customer_id RENAME TO "IX_invoices_CustomerId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE invoice_lines RENAME COLUMN quantity TO "Quantity";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE invoice_lines RENAME COLUMN notes TO "Notes";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE invoice_lines RENAME COLUMN id TO "Id";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE invoice_lines RENAME COLUMN description TO "Description";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE invoice_lines RENAME COLUMN unit_price TO "UnitPrice";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE invoice_lines RENAME COLUMN unit_of_measure TO "UnitOfMeasure";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE invoice_lines RENAME COLUMN tax_rate TO "TaxRate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE invoice_lines RENAME COLUMN tax_amount TO "TaxAmount";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE invoice_lines RENAME COLUMN purchase_order_line_id TO "PurchaseOrderLineId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE invoice_lines RENAME COLUMN product_id TO "ProductId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE invoice_lines RENAME COLUMN modified_at TO "ModifiedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE invoice_lines RENAME COLUMN line_total TO "LineTotal";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE invoice_lines RENAME COLUMN created_at TO "CreatedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE invoice_lines RENAME COLUMN line_number TO "LineNumber";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE invoice_lines RENAME COLUMN invoice_id TO "InvoiceId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_invoice_lines_purchase_order_line_id RENAME TO "IX_invoice_lines_PurchaseOrderLineId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_invoice_lines_product_id RENAME TO "IX_invoice_lines_ProductId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_notes RENAME COLUMN status TO "Status";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_notes RENAME COLUMN notes TO "Notes";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_notes RENAME COLUMN id TO "Id";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_notes RENAME COLUMN vendor_id TO "VendorId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_notes RENAME COLUMN tenant_id TO "TenantId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_notes RENAME COLUMN receiving_location TO "ReceivingLocation";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_notes RENAME COLUMN received_by_user_id TO "ReceivedByUserId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_notes RENAME COLUMN received_by_name TO "ReceivedByName";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_notes RENAME COLUMN receipt_date TO "ReceiptDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_notes RENAME COLUMN purchase_order_id TO "PurchaseOrderId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_notes RENAME COLUMN modified_at TO "ModifiedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_notes RENAME COLUMN inspection_date TO "InspectionDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_notes RENAME COLUMN grn_number TO "GrnNumber";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_notes RENAME COLUMN goods_receipt_note_number TO "GoodsReceiptNoteNumber";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_notes RENAME COLUMN delivery_note_id TO "DeliveryNoteId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_notes RENAME COLUMN created_at TO "CreatedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_goods_receipt_notes_status RENAME TO "IX_goods_receipt_notes_Status";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_goods_receipt_notes_vendor_id RENAME TO "IX_goods_receipt_notes_VendorId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_goods_receipt_notes_tenant_id RENAME TO "IX_goods_receipt_notes_TenantId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_goods_receipt_notes_received_by_user_id RENAME TO "IX_goods_receipt_notes_ReceivedByUserId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_goods_receipt_notes_receipt_date RENAME TO "IX_goods_receipt_notes_ReceiptDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_goods_receipt_notes_purchase_order_id RENAME TO "IX_goods_receipt_notes_PurchaseOrderId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_goods_receipt_notes_goods_receipt_note_number RENAME TO "IX_goods_receipt_notes_GoodsReceiptNoteNumber";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_goods_receipt_notes_delivery_note_id RENAME TO "IX_goods_receipt_notes_DeliveryNoteId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_note_lines RENAME COLUMN notes TO "Notes";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_note_lines RENAME COLUMN id TO "Id";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_note_lines RENAME COLUMN unit_of_measure TO "UnitOfMeasure";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_note_lines RENAME COLUMN rejection_reason TO "RejectionReason";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_note_lines RENAME COLUMN quantity_rejected TO "QuantityRejected";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_note_lines RENAME COLUMN quantity_received TO "QuantityReceived";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_note_lines RENAME COLUMN quantity_accepted TO "QuantityAccepted";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_note_lines RENAME COLUMN quality_control_status TO "QualityControlStatus";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_note_lines RENAME COLUMN put_away_location TO "PutAwayLocation";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_note_lines RENAME COLUMN purchase_order_line_id TO "PurchaseOrderLineId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_note_lines RENAME COLUMN product_sku_snapshot TO "ProductSkuSnapshot";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_note_lines RENAME COLUMN product_id TO "ProductId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_note_lines RENAME COLUMN product_description_snapshot TO "ProductDescriptionSnapshot";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_note_lines RENAME COLUMN modified_at TO "ModifiedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_note_lines RENAME COLUMN lot_number TO "LotNumber";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_note_lines RENAME COLUMN inspection_completed TO "InspectionCompleted";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_note_lines RENAME COLUMN expiry_date TO "ExpiryDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_note_lines RENAME COLUMN delivery_note_line_line_number TO "DeliveryNoteLineLineNumber";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_note_lines RENAME COLUMN delivery_note_line_id TO "DeliveryNoteLineId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_note_lines RENAME COLUMN delivery_note_line_delivery_note_id TO "DeliveryNoteLineDeliveryNoteId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_note_lines RENAME COLUMN created_at TO "CreatedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_note_lines RENAME COLUMN batch_number TO "BatchNumber";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_note_lines RENAME COLUMN line_number TO "LineNumber";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_note_lines RENAME COLUMN goods_receipt_note_id TO "GoodsReceiptNoteId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_goods_receipt_note_lines_quality_control_status RENAME TO "IX_goods_receipt_note_lines_QualityControlStatus";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_goods_receipt_note_lines_purchase_order_line_id RENAME TO "IX_goods_receipt_note_lines_PurchaseOrderLineId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_goods_receipt_note_lines_product_id RENAME TO "IX_goods_receipt_note_lines_ProductId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_goods_receipt_note_lines_expiry_date RENAME TO "IX_goods_receipt_note_lines_ExpiryDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_goods_receipt_note_lines_delivery_note_line_delivery_note_i RENAME TO "IX_goods_receipt_note_lines_DeliveryNoteLineDeliveryNoteId_Del~";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.departments RENAME COLUMN name TO "Name";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.departments RENAME COLUMN description TO "Description";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.departments RENAME COLUMN code TO "Code";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.departments RENAME COLUMN id TO "Id";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.departments RENAME COLUMN tenant_id TO "TenantId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.departments RENAME COLUMN modified_at TO "ModifiedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.departments RENAME COLUMN created_at TO "CreatedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_departments_name RENAME TO "IX_departments_Name";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_departments_tenant_id_code RENAME TO "IX_departments_TenantId_Code";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_departments_tenant_id RENAME TO "IX_departments_TenantId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE delivery_notes RENAME COLUMN status TO "Status";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE delivery_notes RENAME COLUMN notes TO "Notes";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE delivery_notes RENAME COLUMN id TO "Id";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE delivery_notes RENAME COLUMN vendor_id TO "VendorId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE delivery_notes RENAME COLUMN tenant_id TO "TenantId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE delivery_notes RENAME COLUMN shipment_date TO "ShipmentDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE delivery_notes RENAME COLUMN sales_order_id TO "SalesOrderId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE delivery_notes RENAME COLUMN received_by TO "ReceivedBy";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE delivery_notes RENAME COLUMN purchase_order_id TO "PurchaseOrderId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE delivery_notes RENAME COLUMN modified_at TO "ModifiedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE delivery_notes RENAME COLUMN delivery_note_number TO "DeliveryNoteNumber";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE delivery_notes RENAME COLUMN delivery_date TO "DeliveryDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE delivery_notes RENAME COLUMN created_at TO "CreatedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_delivery_notes_status RENAME TO "IX_delivery_notes_Status";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_delivery_notes_vendor_id RENAME TO "IX_delivery_notes_VendorId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_delivery_notes_tenant_id RENAME TO "IX_delivery_notes_TenantId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_delivery_notes_sales_order_id RENAME TO "IX_delivery_notes_SalesOrderId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_delivery_notes_purchase_order_id RENAME TO "IX_delivery_notes_PurchaseOrderId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_delivery_notes_delivery_note_number RENAME TO "IX_delivery_notes_DeliveryNoteNumber";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_delivery_notes_delivery_date RENAME TO "IX_delivery_notes_DeliveryDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE delivery_note_lines RENAME COLUMN notes TO "Notes";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE delivery_note_lines RENAME COLUMN id TO "Id";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE delivery_note_lines RENAME COLUMN description TO "Description";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE delivery_note_lines RENAME COLUMN unit_of_measure TO "UnitOfMeasure";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE delivery_note_lines RENAME COLUMN serial_number TO "SerialNumber";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE delivery_note_lines RENAME COLUMN sales_order_line_number TO "SalesOrderLineNumber";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE delivery_note_lines RENAME COLUMN sales_order_id TO "SalesOrderId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE delivery_note_lines RENAME COLUMN quantity_shipped TO "QuantityShipped";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE delivery_note_lines RENAME COLUMN purchase_order_line_id TO "PurchaseOrderLineId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE delivery_note_lines RENAME COLUMN product_sku_snapshot TO "ProductSkuSnapshot";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE delivery_note_lines RENAME COLUMN product_id TO "ProductId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE delivery_note_lines RENAME COLUMN modified_at TO "ModifiedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE delivery_note_lines RENAME COLUMN created_at TO "CreatedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE delivery_note_lines RENAME COLUMN batch_number TO "BatchNumber";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE delivery_note_lines RENAME COLUMN line_number TO "LineNumber";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE delivery_note_lines RENAME COLUMN delivery_note_id TO "DeliveryNoteId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_delivery_note_lines_sales_order_id_sales_order_line_number RENAME TO "IX_delivery_note_lines_SalesOrderId_SalesOrderLineNumber";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_delivery_note_lines_purchase_order_line_id RENAME TO "IX_delivery_note_lines_PurchaseOrderLineId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_delivery_note_lines_product_id RENAME TO "IX_delivery_note_lines_ProductId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.customers RENAME COLUMN id TO "Id";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.customers RENAME COLUMN tenant_id TO "TenantId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.customers RENAME COLUMN modified_at TO "ModifiedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.customers RENAME COLUMN is_deleted TO "IsDeleted";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.customers RENAME COLUMN created_at TO "CreatedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.customers RENAME COLUMN assigned_sales_rep_id TO "AssignedSalesRepId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_customers_tax_identifier RENAME TO "IX_customers_tax_identifier";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_customers_name RENAME TO "IX_customers_name";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_customers_is_active RENAME TO "IX_customers_is_active";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_customers_customer_type RENAME TO "IX_customers_customer_type";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_customers_tenant_id_customer_code RENAME TO "IX_customers_TenantId_customer_code";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_customers_tenant_id RENAME TO "IX_customers_TenantId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_customers_is_deleted RENAME TO "IX_customers_IsDeleted";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_customers_assigned_sales_rep_id RENAME TO "IX_customers_AssignedSalesRepId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE contracts RENAME COLUMN version TO "Version";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE contracts RENAME COLUMN title TO "Title";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE contracts RENAME COLUMN status TO "Status";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE contracts RENAME COLUMN id TO "Id";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE contracts RENAME COLUMN vendor_performance_score_snapshot TO "VendorPerformanceScoreSnapshot";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE contracts RENAME COLUMN vendor_id TO "VendorId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE contracts RENAME COLUMN terms_and_conditions TO "TermsAndConditions";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE contracts RENAME COLUMN termination_penalty_terms TO "TerminationPenaltyTerms";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE contracts RENAME COLUMN tenant_id TO "TenantId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE contracts RENAME COLUMN start_date TO "StartDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE contracts RENAME COLUMN sla_details_json TO "SlaDetailsJson";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE contracts RENAME COLUMN renewal_terms TO "RenewalTerms";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE contracts RENAME COLUMN payment_terms TO "PaymentTerms";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE contracts RENAME COLUMN modified_at TO "ModifiedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE contracts RENAME COLUMN milestones_json TO "MilestonesJson";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE contracts RENAME COLUMN is_deleted TO "IsDeleted";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE contracts RENAME COLUMN is_auto_renew TO "IsAutoRenew";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE contracts RENAME COLUMN end_date TO "EndDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE contracts RENAME COLUMN created_at TO "CreatedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE contracts RENAME COLUMN contract_type TO "ContractType";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE contracts RENAME COLUMN contract_number TO "ContractNumber";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE contracts RENAME COLUMN compliance_document_links_json TO "ComplianceDocumentLinksJson";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_contracts_status RENAME TO "IX_contracts_Status";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_contracts_vendor_id RENAME TO "IX_contracts_VendorId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_contracts_tenant_id RENAME TO "IX_contracts_TenantId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_contracts_start_date RENAME TO "IX_contracts_StartDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_contracts_is_deleted RENAME TO "IX_contracts_IsDeleted";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_contracts_end_date RENAME TO "IX_contracts_EndDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_contracts_contract_number RENAME TO "IX_contracts_ContractNumber";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.categories RENAME COLUMN name TO "Name";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.categories RENAME COLUMN description TO "Description";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.categories RENAME COLUMN code TO "Code";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.categories RENAME COLUMN id TO "Id";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.categories RENAME COLUMN unspsc_code TO "UnspscCode";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.categories RENAME COLUMN tenant_id TO "TenantId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.categories RENAME COLUMN parent_category_id TO "ParentCategoryId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.categories RENAME COLUMN modified_at TO "ModifiedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.categories RENAME COLUMN created_at TO "CreatedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_categories_tenant_id RENAME TO "IX_categories_TenantId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_categories_parent_category_id RENAME TO "IX_categories_ParentCategoryId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.budgets RENAME COLUMN version TO "Version";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.budgets RENAME COLUMN status TO "Status";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.budgets RENAME COLUMN name TO "Name";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.budgets RENAME COLUMN description TO "Description";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.budgets RENAME COLUMN id TO "Id";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.budgets RENAME COLUMN workflow_instance_id TO "WorkflowInstanceId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.budgets RENAME COLUMN tenant_id TO "TenantId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.budgets RENAME COLUMN start_date TO "StartDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.budgets RENAME COLUMN modified_at TO "ModifiedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.budgets RENAME COLUMN is_rolling_forecast TO "IsRollingForecast";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.budgets RENAME COLUMN is_deleted TO "IsDeleted";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.budgets RENAME COLUMN forecast_periods_json TO "ForecastPeriodsJson";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.budgets RENAME COLUMN fiscal_year TO "FiscalYear";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.budgets RENAME COLUMN end_date TO "EndDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.budgets RENAME COLUMN currency_code TO "CurrencyCode";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.budgets RENAME COLUMN created_by_id TO "CreatedById";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.budgets RENAME COLUMN created_at TO "CreatedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.budgets RENAME COLUMN approved_by_id TO "ApprovedById";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.budgets RENAME COLUMN allocation_rules_json TO "AllocationRulesJson";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_budgets_status RENAME TO "IX_budgets_Status";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_budgets_name RENAME TO "IX_budgets_Name";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_budgets_workflow_instance_id RENAME TO "IX_budgets_WorkflowInstanceId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_budgets_tenant_id RENAME TO "IX_budgets_TenantId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_budgets_start_date RENAME TO "IX_budgets_StartDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_budgets_is_deleted RENAME TO "IX_budgets_IsDeleted";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_budgets_fiscal_year RENAME TO "IX_budgets_FiscalYear";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_budgets_end_date RENAME TO "IX_budgets_EndDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_budgets_created_by_id RENAME TO "IX_budgets_CreatedById";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_budgets_approved_by_id RENAME TO "IX_budgets_ApprovedById";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.budget_allocations RENAME COLUMN status TO "Status";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.budget_allocations RENAME COLUMN description TO "Description";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.budget_allocations RENAME COLUMN id TO "Id";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.budget_allocations RENAME COLUMN tenant_id TO "TenantId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.budget_allocations RENAME COLUMN modified_at TO "ModifiedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.budget_allocations RENAME COLUMN fiscal_period_identifier TO "FiscalPeriodIdentifier";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.budget_allocations RENAME COLUMN department_id TO "DepartmentId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.budget_allocations RENAME COLUMN created_at TO "CreatedAt";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.budget_allocations RENAME COLUMN budget_id TO "BudgetId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.budget_allocations RENAME COLUMN allocation_date TO "AllocationDate";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_budget_allocations_status RENAME TO "IX_budget_allocations_Status";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_budget_allocations_tenant_id RENAME TO "IX_budget_allocations_TenantId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_budget_allocations_fiscal_period_identifier RENAME TO "IX_budget_allocations_FiscalPeriodIdentifier";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_budget_allocations_department_id RENAME TO "IX_budget_allocations_DepartmentId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_budget_allocations_budget_id_fiscal_period_identifier RENAME TO "IX_budget_allocations_BudgetId_FiscalPeriodIdentifier";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX public.ix_budget_allocations_budget_id RENAME TO "IX_budget_allocations_BudgetId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetUserTokens" RENAME COLUMN value TO "Value";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetUserTokens" RENAME COLUMN name TO "Name";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetUserTokens" RENAME COLUMN login_provider TO "LoginProvider";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetUserTokens" RENAME COLUMN user_id TO "UserId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetUsers" RENAME COLUMN email TO "Email";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetUsers" RENAME COLUMN id TO "Id";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetUsers" RENAME COLUMN user_name TO "UserName";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetUsers" RENAME COLUMN two_factor_enabled TO "TwoFactorEnabled";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetUsers" RENAME COLUMN security_stamp TO "SecurityStamp";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetUsers" RENAME COLUMN phone_number_confirmed TO "PhoneNumberConfirmed";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetUsers" RENAME COLUMN phone_number TO "PhoneNumber";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetUsers" RENAME COLUMN password_hash TO "PasswordHash";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetUsers" RENAME COLUMN normalized_user_name TO "NormalizedUserName";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetUsers" RENAME COLUMN normalized_email TO "NormalizedEmail";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetUsers" RENAME COLUMN lockout_end TO "LockoutEnd";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetUsers" RENAME COLUMN lockout_enabled TO "LockoutEnabled";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetUsers" RENAME COLUMN email_confirmed TO "EmailConfirmed";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetUsers" RENAME COLUMN concurrency_stamp TO "ConcurrencyStamp";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetUsers" RENAME COLUMN access_failed_count TO "AccessFailedCount";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetUserRoles" RENAME COLUMN role_id TO "RoleId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetUserRoles" RENAME COLUMN user_id TO "UserId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_asp_net_user_roles_role_id RENAME TO "IX_AspNetUserRoles_RoleId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetUserLogins" RENAME COLUMN user_id TO "UserId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetUserLogins" RENAME COLUMN provider_display_name TO "ProviderDisplayName";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetUserLogins" RENAME COLUMN provider_key TO "ProviderKey";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetUserLogins" RENAME COLUMN login_provider TO "LoginProvider";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_asp_net_user_logins_user_id RENAME TO "IX_AspNetUserLogins_UserId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetUserClaims" RENAME COLUMN id TO "Id";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetUserClaims" RENAME COLUMN user_id TO "UserId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetUserClaims" RENAME COLUMN claim_value TO "ClaimValue";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetUserClaims" RENAME COLUMN claim_type TO "ClaimType";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_asp_net_user_claims_user_id RENAME TO "IX_AspNetUserClaims_UserId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetRoles" RENAME COLUMN name TO "Name";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetRoles" RENAME COLUMN id TO "Id";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetRoles" RENAME COLUMN normalized_name TO "NormalizedName";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetRoles" RENAME COLUMN concurrency_stamp TO "ConcurrencyStamp";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetRoleClaims" RENAME COLUMN id TO "Id";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetRoleClaims" RENAME COLUMN role_id TO "RoleId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetRoleClaims" RENAME COLUMN claim_value TO "ClaimValue";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetRoleClaims" RENAME COLUMN claim_type TO "ClaimType";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER INDEX ix_asp_net_role_claims_role_id RENAME TO "IX_AspNetRoleClaims_RoleId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "DocumentLink" RENAME COLUMN url TO "Url";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "DocumentLink" RENAME COLUMN name TO "Name";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "DocumentLink" RENAME COLUMN description TO "Description";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "DocumentLink" RENAME COLUMN document_type TO "DocumentType";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE test_entities ADD "CreatedDate" timestamp with time zone NOT NULL DEFAULT TIMESTAMPTZ '-infinity';
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE test_entities ADD "Description" text NOT NULL DEFAULT '';
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE test_entities ADD "TestNumber" integer NOT NULL DEFAULT 0;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendors ADD CONSTRAINT "PK_vendors" PRIMARY KEY ("Id");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendor_proposals ADD CONSTRAINT "PK_vendor_proposals" PRIMARY KEY ("Id");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendor_products ADD CONSTRAINT "PK_vendor_products" PRIMARY KEY ("Id");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE test_entities ADD CONSTRAINT "PK_test_entities" PRIMARY KEY ("Id");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.tenants ADD CONSTRAINT "PK_tenants" PRIMARY KEY ("Id");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE tenant_products ADD CONSTRAINT "PK_tenant_products" PRIMARY KEY ("Id");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE technical_submittals ADD CONSTRAINT "PK_technical_submittals" PRIMARY KEY ("Id");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE suppliers ADD CONSTRAINT "PK_suppliers" PRIMARY KEY ("Id");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE submittal_reviews ADD CONSTRAINT "PK_submittal_reviews" PRIMARY KEY ("Id");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_territory_representatives ADD CONSTRAINT "PK_sales_territory_representatives" PRIMARY KEY ("SalesTerritoryId", "RepresentativeId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_territories ADD CONSTRAINT "PK_sales_territories" PRIMARY KEY ("Id");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.sales_orders ADD CONSTRAINT "PK_sales_orders" PRIMARY KEY ("Id");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_order_lines ADD CONSTRAINT "PK_sales_order_lines" PRIMARY KEY ("SalesOrderId", "LineNumber");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorizations ADD CONSTRAINT "PK_return_authorizations" PRIMARY KEY ("Id");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorization_lines ADD CONSTRAINT "PK_return_authorization_lines" PRIMARY KEY ("ReturnAuthorizationId", "LineNumber");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE requests_for_proposal ADD CONSTRAINT "PK_requests_for_proposal" PRIMARY KEY ("Id");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE requests_for_information ADD CONSTRAINT "PK_requests_for_information" PRIMARY KEY ("Id");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quotes ADD CONSTRAINT "PK_request_for_quotes" PRIMARY KEY ("Id");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quote_lines ADD CONSTRAINT "PK_request_for_quote_lines" PRIMARY KEY ("Id");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseRequisitions" ADD CONSTRAINT "PK_PurchaseRequisitions" PRIMARY KEY ("Id");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseRequisitionLines" ADD CONSTRAINT "PK_PurchaseRequisitionLines" PRIMARY KEY ("Id");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseOrderLines" ADD CONSTRAINT "PK_PurchaseOrderLines" PRIMARY KEY ("Id");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE purchase_orders ADD CONSTRAINT "PK_purchase_orders" PRIMARY KEY ("Id");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.projects ADD CONSTRAINT "PK_projects" PRIMARY KEY ("Id");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.products ADD CONSTRAINT "PK_products" PRIMARY KEY ("Id");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE product_definitions ADD CONSTRAINT "PK_product_definitions" PRIMARY KEY ("Id");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE procurement_workflows ADD CONSTRAINT "PK_procurement_workflows" PRIMARY KEY ("Id");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE procurement_workflow_steps ADD CONSTRAINT "PK_procurement_workflow_steps" PRIMARY KEY ("Id");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE payment_transactions ADD CONSTRAINT "PK_payment_transactions" PRIMARY KEY ("Id");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE invoices ADD CONSTRAINT "PK_invoices" PRIMARY KEY ("Id");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE invoice_lines ADD CONSTRAINT "PK_invoice_lines" PRIMARY KEY ("InvoiceId", "LineNumber");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_notes ADD CONSTRAINT "PK_goods_receipt_notes" PRIMARY KEY ("Id");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_note_lines ADD CONSTRAINT "PK_goods_receipt_note_lines" PRIMARY KEY ("GoodsReceiptNoteId", "LineNumber");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.departments ADD CONSTRAINT "PK_departments" PRIMARY KEY ("Id");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE delivery_notes ADD CONSTRAINT "PK_delivery_notes" PRIMARY KEY ("Id");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE delivery_note_lines ADD CONSTRAINT "PK_delivery_note_lines" PRIMARY KEY ("DeliveryNoteId", "LineNumber");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.customers ADD CONSTRAINT "PK_customers" PRIMARY KEY ("Id");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE contracts ADD CONSTRAINT "PK_contracts" PRIMARY KEY ("Id");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.categories ADD CONSTRAINT "PK_categories" PRIMARY KEY ("Id");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.budgets ADD CONSTRAINT "PK_budgets" PRIMARY KEY ("Id");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.budget_allocations ADD CONSTRAINT "PK_budget_allocations" PRIMARY KEY ("Id");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetUserTokens" ADD CONSTRAINT "PK_AspNetUserTokens" PRIMARY KEY ("UserId", "LoginProvider", "Name");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetUsers" ADD CONSTRAINT "PK_AspNetUsers" PRIMARY KEY ("Id");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetUserRoles" ADD CONSTRAINT "PK_AspNetUserRoles" PRIMARY KEY ("UserId", "RoleId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetUserLogins" ADD CONSTRAINT "PK_AspNetUserLogins" PRIMARY KEY ("LoginProvider", "ProviderKey");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetUserClaims" ADD CONSTRAINT "PK_AspNetUserClaims" PRIMARY KEY ("Id");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetRoles" ADD CONSTRAINT "PK_AspNetRoles" PRIMARY KEY ("Id");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetRoleClaims" ADD CONSTRAINT "PK_AspNetRoleClaims" PRIMARY KEY ("Id");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetRoleClaims" ADD CONSTRAINT "FK_AspNetRoleClaims_AspNetRoles_RoleId" FOREIGN KEY ("RoleId") REFERENCES "AspNetRoles" ("Id") ON DELETE CASCADE;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetUserClaims" ADD CONSTRAINT "FK_AspNetUserClaims_AspNetUsers_UserId" FOREIGN KEY ("UserId") REFERENCES "AspNetUsers" ("Id") ON DELETE CASCADE;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetUserLogins" ADD CONSTRAINT "FK_AspNetUserLogins_AspNetUsers_UserId" FOREIGN KEY ("UserId") REFERENCES "AspNetUsers" ("Id") ON DELETE CASCADE;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetUserRoles" ADD CONSTRAINT "FK_AspNetUserRoles_AspNetRoles_RoleId" FOREIGN KEY ("RoleId") REFERENCES "AspNetRoles" ("Id") ON DELETE CASCADE;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetUserRoles" ADD CONSTRAINT "FK_AspNetUserRoles_AspNetUsers_UserId" FOREIGN KEY ("UserId") REFERENCES "AspNetUsers" ("Id") ON DELETE CASCADE;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "AspNetUserTokens" ADD CONSTRAINT "FK_AspNetUserTokens_AspNetUsers_UserId" FOREIGN KEY ("UserId") REFERENCES "AspNetUsers" ("Id") ON DELETE CASCADE;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.budget_allocations ADD CONSTRAINT "FK_budget_allocations_budgets_BudgetId" FOREIGN KEY ("BudgetId") REFERENCES public.budgets ("Id") ON DELETE RESTRICT;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.budget_allocations ADD CONSTRAINT "FK_budget_allocations_departments_DepartmentId" FOREIGN KEY ("DepartmentId") REFERENCES public.departments ("Id") ON DELETE RESTRICT;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.categories ADD CONSTRAINT "FK_categories_categories_ParentCategoryId" FOREIGN KEY ("ParentCategoryId") REFERENCES public.categories ("Id") ON DELETE RESTRICT;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE contracts ADD CONSTRAINT "FK_contracts_vendors_VendorId" FOREIGN KEY ("VendorId") REFERENCES vendors ("Id") ON DELETE RESTRICT;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE delivery_note_lines ADD CONSTRAINT "FK_delivery_note_lines_PurchaseOrderLines_PurchaseOrderLineId" FOREIGN KEY ("PurchaseOrderLineId") REFERENCES "PurchaseOrderLines" ("Id") ON DELETE RESTRICT;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE delivery_note_lines ADD CONSTRAINT "FK_delivery_note_lines_delivery_notes_DeliveryNoteId" FOREIGN KEY ("DeliveryNoteId") REFERENCES delivery_notes ("Id") ON DELETE CASCADE;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE delivery_note_lines ADD CONSTRAINT "FK_delivery_note_lines_product_definitions_ProductId" FOREIGN KEY ("ProductId") REFERENCES product_definitions ("Id") ON DELETE RESTRICT;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE delivery_note_lines ADD CONSTRAINT "FK_delivery_note_lines_sales_order_lines_SalesOrderId_SalesOrd~" FOREIGN KEY ("SalesOrderId", "SalesOrderLineNumber") REFERENCES sales_order_lines ("SalesOrderId", "LineNumber") ON DELETE RESTRICT;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE delivery_notes ADD CONSTRAINT "FK_delivery_notes_purchase_orders_PurchaseOrderId" FOREIGN KEY ("PurchaseOrderId") REFERENCES purchase_orders ("Id") ON DELETE RESTRICT;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE delivery_notes ADD CONSTRAINT "FK_delivery_notes_sales_orders_SalesOrderId" FOREIGN KEY ("SalesOrderId") REFERENCES public.sales_orders ("Id") ON DELETE RESTRICT;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE delivery_notes ADD CONSTRAINT "FK_delivery_notes_vendors_VendorId" FOREIGN KEY ("VendorId") REFERENCES vendors ("Id") ON DELETE CASCADE;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_note_lines ADD CONSTRAINT "FK_goods_receipt_note_lines_PurchaseOrderLines_PurchaseOrderLi~" FOREIGN KEY ("PurchaseOrderLineId") REFERENCES "PurchaseOrderLines" ("Id") ON DELETE RESTRICT;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_note_lines ADD CONSTRAINT "FK_goods_receipt_note_lines_delivery_note_lines_DeliveryNoteLi~" FOREIGN KEY ("DeliveryNoteLineDeliveryNoteId", "DeliveryNoteLineLineNumber") REFERENCES delivery_note_lines ("DeliveryNoteId", "LineNumber");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_note_lines ADD CONSTRAINT "FK_goods_receipt_note_lines_goods_receipt_notes_GoodsReceiptNo~" FOREIGN KEY ("GoodsReceiptNoteId") REFERENCES goods_receipt_notes ("Id") ON DELETE CASCADE;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_note_lines ADD CONSTRAINT "FK_goods_receipt_note_lines_product_definitions_ProductId" FOREIGN KEY ("ProductId") REFERENCES product_definitions ("Id") ON DELETE RESTRICT;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_notes ADD CONSTRAINT "FK_goods_receipt_notes_AspNetUsers_ReceivedByUserId" FOREIGN KEY ("ReceivedByUserId") REFERENCES "AspNetUsers" ("Id") ON DELETE RESTRICT;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_notes ADD CONSTRAINT "FK_goods_receipt_notes_delivery_notes_DeliveryNoteId" FOREIGN KEY ("DeliveryNoteId") REFERENCES delivery_notes ("Id") ON DELETE SET NULL;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_notes ADD CONSTRAINT "FK_goods_receipt_notes_purchase_orders_PurchaseOrderId" FOREIGN KEY ("PurchaseOrderId") REFERENCES purchase_orders ("Id") ON DELETE RESTRICT;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE goods_receipt_notes ADD CONSTRAINT "FK_goods_receipt_notes_vendors_VendorId" FOREIGN KEY ("VendorId") REFERENCES vendors ("Id") ON DELETE CASCADE;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE invoice_lines ADD CONSTRAINT "FK_invoice_lines_PurchaseOrderLines_PurchaseOrderLineId" FOREIGN KEY ("PurchaseOrderLineId") REFERENCES "PurchaseOrderLines" ("Id") ON DELETE RESTRICT;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE invoice_lines ADD CONSTRAINT "FK_invoice_lines_invoices_InvoiceId" FOREIGN KEY ("InvoiceId") REFERENCES invoices ("Id") ON DELETE CASCADE;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE invoice_lines ADD CONSTRAINT "FK_invoice_lines_product_definitions_ProductId" FOREIGN KEY ("ProductId") REFERENCES product_definitions ("Id") ON DELETE RESTRICT;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE invoices ADD CONSTRAINT "FK_invoices_customers_CustomerId" FOREIGN KEY ("CustomerId") REFERENCES public.customers ("Id") ON DELETE RESTRICT;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE invoices ADD CONSTRAINT "FK_invoices_purchase_orders_PurchaseOrderId" FOREIGN KEY ("PurchaseOrderId") REFERENCES purchase_orders ("Id") ON DELETE RESTRICT;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE invoices ADD CONSTRAINT "FK_invoices_vendors_VendorId" FOREIGN KEY ("VendorId") REFERENCES vendors ("Id") ON DELETE RESTRICT;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE payment_transactions ADD CONSTRAINT "FK_payment_transactions_invoices_InvoiceId" FOREIGN KEY ("InvoiceId") REFERENCES invoices ("Id") ON DELETE SET NULL;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE payment_transactions ADD CONSTRAINT "FK_payment_transactions_vendors_VendorId" FOREIGN KEY ("VendorId") REFERENCES vendors ("Id") ON DELETE RESTRICT;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE procurement_workflow_steps ADD CONSTRAINT "FK_procurement_workflow_steps_AspNetUsers_ApproverUserId" FOREIGN KEY ("ApproverUserId") REFERENCES "AspNetUsers" ("Id") ON DELETE RESTRICT;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE procurement_workflow_steps ADD CONSTRAINT "FK_procurement_workflow_steps_procurement_workflows_Procuremen~" FOREIGN KEY ("ProcurementWorkflowId") REFERENCES procurement_workflows ("Id") ON DELETE CASCADE;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE procurement_workflow_steps ADD CONSTRAINT "FK_procurement_workflow_steps_procurement_workflows_WorkflowId" FOREIGN KEY ("WorkflowId") REFERENCES procurement_workflows ("Id") ON DELETE CASCADE;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE product_definitions ADD CONSTRAINT "FK_product_definitions_categories_CategoryId" FOREIGN KEY ("CategoryId") REFERENCES public.categories ("Id") ON DELETE SET NULL;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.products ADD CONSTRAINT "FK_products_categories_CategoryId" FOREIGN KEY ("CategoryId") REFERENCES public.categories ("Id") ON DELETE SET NULL;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE purchase_orders ADD CONSTRAINT "FK_purchase_orders_PurchaseRequisitions_RequisitionId" FOREIGN KEY ("RequisitionId") REFERENCES "PurchaseRequisitions" ("Id") ON DELETE SET NULL;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE purchase_orders ADD CONSTRAINT "FK_purchase_orders_contracts_ContractId" FOREIGN KEY ("ContractId") REFERENCES contracts ("Id") ON DELETE SET NULL;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE purchase_orders ADD CONSTRAINT "FK_purchase_orders_vendors_VendorId" FOREIGN KEY ("VendorId") REFERENCES vendors ("Id") ON DELETE RESTRICT;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseOrderLines" ADD CONSTRAINT "FK_PurchaseOrderLines_purchase_orders_PurchaseOrderId" FOREIGN KEY ("PurchaseOrderId") REFERENCES purchase_orders ("Id") ON DELETE CASCADE;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseOrderLines" ADD CONSTRAINT "FK_PurchaseOrderLines_vendor_products_VendorProductId" FOREIGN KEY ("VendorProductId") REFERENCES vendor_products ("Id") ON DELETE RESTRICT;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseRequisitionLines" ADD CONSTRAINT "FK_PurchaseRequisitionLines_PurchaseRequisitions_PurchaseRequi~" FOREIGN KEY ("PurchaseRequisitionId") REFERENCES "PurchaseRequisitions" ("Id") ON DELETE CASCADE;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseRequisitionLines" ADD CONSTRAINT "FK_PurchaseRequisitionLines_product_definitions_ProductDefinit~" FOREIGN KEY ("ProductDefinitionId") REFERENCES product_definitions ("Id") ON DELETE SET NULL;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseRequisitionLines" ADD CONSTRAINT "FK_PurchaseRequisitionLines_vendor_products_VendorProductId" FOREIGN KEY ("VendorProductId") REFERENCES vendor_products ("Id") ON DELETE SET NULL;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseRequisitionLines" ADD CONSTRAINT "FK_PurchaseRequisitionLines_vendors_SuggestedVendorId" FOREIGN KEY ("SuggestedVendorId") REFERENCES vendors ("Id") ON DELETE SET NULL;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE "PurchaseRequisitions" ADD CONSTRAINT "FK_PurchaseRequisitions_purchase_orders_AssociatedPurchaseOrde~" FOREIGN KEY ("AssociatedPurchaseOrderId") REFERENCES purchase_orders ("Id") ON DELETE SET NULL;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quote_lines ADD CONSTRAINT "FK_request_for_quote_lines_product_definitions_ProductDefiniti~" FOREIGN KEY ("ProductDefinitionId") REFERENCES product_definitions ("Id") ON DELETE SET NULL;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quote_lines ADD CONSTRAINT "FK_request_for_quote_lines_request_for_quotes_RequestForQuoteId" FOREIGN KEY ("RequestForQuoteId") REFERENCES public.request_for_quotes ("Id") ON DELETE CASCADE;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quote_lines ADD CONSTRAINT "FK_request_for_quote_lines_vendor_products_VendorProductId" FOREIGN KEY ("VendorProductId") REFERENCES vendor_products ("Id") ON DELETE SET NULL;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quotes ADD CONSTRAINT "FK_request_for_quotes_PurchaseRequisitions_OriginatingRequisit~" FOREIGN KEY ("OriginatingRequisitionId") REFERENCES "PurchaseRequisitions" ("Id") ON DELETE SET NULL;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quotes ADD CONSTRAINT "FK_request_for_quotes_contracts_RelatedAgreementId" FOREIGN KEY ("RelatedAgreementId") REFERENCES contracts ("Id") ON DELETE SET NULL;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.request_for_quotes ADD CONSTRAINT "FK_request_for_quotes_vendors_AwardedVendorId" FOREIGN KEY ("AwardedVendorId") REFERENCES vendors ("Id") ON DELETE RESTRICT;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE requests_for_information ADD CONSTRAINT "FK_requests_for_information_projects_ProjectId" FOREIGN KEY ("ProjectId") REFERENCES public.projects ("Id") ON DELETE SET NULL;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE requests_for_proposal ADD CONSTRAINT "FK_requests_for_proposal_contracts_AwardedContractId" FOREIGN KEY ("AwardedContractId") REFERENCES contracts ("Id");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE requests_for_proposal ADD CONSTRAINT "FK_requests_for_proposal_projects_ProjectId" FOREIGN KEY ("ProjectId") REFERENCES public.projects ("Id") ON DELETE SET NULL;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE requests_for_proposal ADD CONSTRAINT "FK_requests_for_proposal_vendors_AwardedVendorId" FOREIGN KEY ("AwardedVendorId") REFERENCES vendors ("Id");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorization_lines ADD CONSTRAINT "FK_return_authorization_lines_invoice_lines_InvoiceId_InvoiceL~" FOREIGN KEY ("InvoiceId", "InvoiceLineNumber") REFERENCES invoice_lines ("InvoiceId", "LineNumber") ON DELETE RESTRICT;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorization_lines ADD CONSTRAINT "FK_return_authorization_lines_product_definitions_ProductDefin~" FOREIGN KEY ("ProductDefinitionId") REFERENCES product_definitions ("Id");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorization_lines ADD CONSTRAINT "FK_return_authorization_lines_products_ProductId" FOREIGN KEY ("ProductId") REFERENCES public.products ("Id") ON DELETE RESTRICT;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorization_lines ADD CONSTRAINT "FK_return_authorization_lines_return_authorizations_ReturnAuth~" FOREIGN KEY ("ReturnAuthorizationId") REFERENCES return_authorizations ("Id") ON DELETE CASCADE;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorization_lines ADD CONSTRAINT "FK_return_authorization_lines_sales_order_lines_OriginalSalesO~" FOREIGN KEY ("OriginalSalesOrderLineSalesOrderId", "OriginalSalesOrderLineLineNumber") REFERENCES sales_order_lines ("SalesOrderId", "LineNumber");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorization_lines ADD CONSTRAINT "FK_return_authorization_lines_sales_order_lines_SalesOrderId_S~" FOREIGN KEY ("SalesOrderId", "SalesOrderLineNumber") REFERENCES sales_order_lines ("SalesOrderId", "LineNumber") ON DELETE RESTRICT;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorization_lines ADD CONSTRAINT "FK_return_authorization_lines_vendor_products_VendorProductId" FOREIGN KEY ("VendorProductId") REFERENCES vendor_products ("Id");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorizations ADD CONSTRAINT "FK_return_authorizations_customers_CustomerId" FOREIGN KEY ("CustomerId") REFERENCES public.customers ("Id") ON DELETE RESTRICT;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorizations ADD CONSTRAINT "FK_return_authorizations_invoices_InvoiceId" FOREIGN KEY ("InvoiceId") REFERENCES invoices ("Id") ON DELETE RESTRICT;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE return_authorizations ADD CONSTRAINT "FK_return_authorizations_sales_orders_SalesOrderId" FOREIGN KEY ("SalesOrderId") REFERENCES public.sales_orders ("Id") ON DELETE RESTRICT;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_order_lines ADD CONSTRAINT "FK_sales_order_lines_products_ProductId" FOREIGN KEY ("ProductId") REFERENCES public.products ("Id") ON DELETE RESTRICT;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_order_lines ADD CONSTRAINT "FK_sales_order_lines_projects_ProjectId" FOREIGN KEY ("ProjectId") REFERENCES public.projects ("Id") ON DELETE SET NULL;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_order_lines ADD CONSTRAINT "FK_sales_order_lines_projects_ProjectId1" FOREIGN KEY ("ProjectId1") REFERENCES public.projects ("Id");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_order_lines ADD CONSTRAINT "FK_sales_order_lines_sales_order_lines_ParentSalesOrderLineSal~" FOREIGN KEY ("ParentSalesOrderLineSalesOrderId", "ParentSalesOrderLineLineNumber") REFERENCES sales_order_lines ("SalesOrderId", "LineNumber");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_order_lines ADD CONSTRAINT "FK_sales_order_lines_sales_orders_SalesOrderId" FOREIGN KEY ("SalesOrderId") REFERENCES public.sales_orders ("Id") ON DELETE CASCADE;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_order_lines ADD CONSTRAINT "FK_sales_order_lines_vendor_products_VendorProductId" FOREIGN KEY ("VendorProductId") REFERENCES vendor_products ("Id") ON DELETE CASCADE;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.sales_orders ADD CONSTRAINT "FK_sales_orders_customers_CustomerId" FOREIGN KEY ("CustomerId") REFERENCES public.customers ("Id") ON DELETE RESTRICT;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.sales_orders ADD CONSTRAINT "FK_sales_orders_return_authorizations_RelatedReturnAuthorizati~" FOREIGN KEY ("RelatedReturnAuthorizationId") REFERENCES return_authorizations ("Id") ON DELETE SET NULL;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.sales_orders ADD CONSTRAINT "FK_sales_orders_sales_territories_SalesTerritoryId" FOREIGN KEY ("SalesTerritoryId") REFERENCES sales_territories ("Id") ON DELETE SET NULL;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE public.sales_orders ADD CONSTRAINT "FK_sales_orders_sales_territories_SalesTerritoryId1" FOREIGN KEY ("SalesTerritoryId1") REFERENCES sales_territories ("Id");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_territories ADD CONSTRAINT "FK_sales_territories_sales_territories_ParentTerritoryId" FOREIGN KEY ("ParentTerritoryId") REFERENCES sales_territories ("Id") ON DELETE RESTRICT;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_territory_representatives ADD CONSTRAINT "FK_sales_territory_representatives_AspNetUsers_RepresentativeId" FOREIGN KEY ("RepresentativeId") REFERENCES "AspNetUsers" ("Id") ON DELETE CASCADE;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE sales_territory_representatives ADD CONSTRAINT "FK_sales_territory_representatives_sales_territories_SalesTerr~" FOREIGN KEY ("SalesTerritoryId") REFERENCES sales_territories ("Id") ON DELETE CASCADE;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE submittal_reviews ADD CONSTRAINT "FK_submittal_reviews_AspNetUsers_ReviewerId" FOREIGN KEY ("ReviewerId") REFERENCES "AspNetUsers" ("Id") ON DELETE RESTRICT;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE submittal_reviews ADD CONSTRAINT "FK_submittal_reviews_technical_submittals_TechnicalSubmittalId" FOREIGN KEY ("TechnicalSubmittalId") REFERENCES technical_submittals ("Id") ON DELETE CASCADE;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE submittal_reviews ADD CONSTRAINT "FK_submittal_reviews_technical_submittals_TechnicalSubmittalId1" FOREIGN KEY ("TechnicalSubmittalId1") REFERENCES technical_submittals ("Id");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE suppliers ADD CONSTRAINT "FK_suppliers_vendors_VendorId" FOREIGN KEY ("VendorId") REFERENCES vendors ("Id") ON DELETE CASCADE;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE technical_submittals ADD CONSTRAINT "FK_technical_submittals_AspNetUsers_SubmittedByUserId" FOREIGN KEY ("SubmittedByUserId") REFERENCES "AspNetUsers" ("Id") ON DELETE RESTRICT;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE technical_submittals ADD CONSTRAINT "FK_technical_submittals_PurchaseOrderLines_PurchaseOrderLineId" FOREIGN KEY ("PurchaseOrderLineId") REFERENCES "PurchaseOrderLines" ("Id");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE technical_submittals ADD CONSTRAINT "FK_technical_submittals_contracts_ContractId" FOREIGN KEY ("ContractId") REFERENCES contracts ("Id");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE technical_submittals ADD CONSTRAINT "FK_technical_submittals_projects_ProjectId" FOREIGN KEY ("ProjectId") REFERENCES public.projects ("Id") ON DELETE RESTRICT;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE technical_submittals ADD CONSTRAINT "FK_technical_submittals_vendors_VendorId" FOREIGN KEY ("VendorId") REFERENCES vendors ("Id") ON DELETE RESTRICT;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendor_products ADD CONSTRAINT "FK_vendor_products_product_definitions_ProductDefinitionId" FOREIGN KEY ("ProductDefinitionId") REFERENCES product_definitions ("Id") ON DELETE RESTRICT;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendor_products ADD CONSTRAINT "FK_vendor_products_vendors_VendorId" FOREIGN KEY ("VendorId") REFERENCES vendors ("Id") ON DELETE RESTRICT;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendor_proposals ADD CONSTRAINT "FK_vendor_proposals_requests_for_proposal_RequestForProposalId" FOREIGN KEY ("RequestForProposalId") REFERENCES requests_for_proposal ("Id") ON DELETE RESTRICT;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    ALTER TABLE vendor_proposals ADD CONSTRAINT "FK_vendor_proposals_vendors_VendorId" FOREIGN KEY ("VendorId") REFERENCES vendors ("Id") ON DELETE RESTRICT;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232237_CompleteSchema') THEN
    INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
    VALUES ('20250523232237_CompleteSchema', '9.0.5');
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250523232332_FullDatabaseSchema') THEN
    INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
    VALUES ('20250523232332_FullDatabaseSchema', '9.0.5');
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250524102057_EnhancedSnakeCaseImplementation') THEN
    ALTER TABLE tenant_products RENAME COLUMN s_k_u TO sku;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250524102057_EnhancedSnakeCaseImplementation') THEN
    ALTER TABLE technical_submittals RENAME COLUMN related_n_c_r_reference TO related_ncr_reference;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250524102057_EnhancedSnakeCaseImplementation') THEN
    ALTER TABLE technical_submittals RENAME COLUMN related_i_t_p_reference TO related_itp_reference;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250524102057_EnhancedSnakeCaseImplementation') THEN
    ALTER TABLE public.request_for_quotes RENAME COLUMN r_f_q_number TO rfq_number;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250524102057_EnhancedSnakeCaseImplementation') THEN
    ALTER INDEX public."IX_request_for_quotes_tenant_id_r_f_q_number" RENAME TO "IX_request_for_quotes_tenant_id_rfq_number";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250524102057_EnhancedSnakeCaseImplementation') THEN
    ALTER TABLE purchase_requisition_lines RENAME COLUMN g_l_account_code TO gl_account_code;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250524102057_EnhancedSnakeCaseImplementation') THEN
    ALTER INDEX "IX_purchase_requisition_lines_g_l_account_code" RENAME TO "IX_purchase_requisition_lines_gl_account_code";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250524102057_EnhancedSnakeCaseImplementation') THEN
    INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
    VALUES ('20250524102057_EnhancedSnakeCaseImplementation', '9.0.5');
    END IF;
END $EF$;
COMMIT;

