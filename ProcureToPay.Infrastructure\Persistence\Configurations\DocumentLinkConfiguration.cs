using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProcureToPay.Domain.ValueObjects;

namespace ProcureToPay.Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// Configuration for the DocumentLink value object for EF Core and PostgreSQL.
    /// </summary>
    public class DocumentLinkConfiguration : IEntityTypeConfiguration<DocumentLink>
    {
        public void Configure(EntityTypeBuilder<DocumentLink> builder)
        {
            // Mark as owned type (not a separate table)
            builder.HasNoKey();
            
            // Configure properties
            builder.Property(d => d.Name)
                .IsRequired()
                .HasMaxLength(255);
                
            builder.Property(d => d.Url)
                .IsRequired()
                .HasMaxLength(1000);
                
            builder.Property(d => d.DocumentType)
                .IsRequired()
                .HasMaxLength(100);
                
            builder.Property(d => d.Description)
                .HasMaxLength(500)
                .IsRequired(false);
        }
    }
}

