﻿using System;
using System.Collections.Generic;
// Assuming ProductDefinition entity exists in ProcureToPay.Domain.Entities
using ProcureToPay.Domain.Entities;
using ProcureToPay.Domain.Events; // Assuming events namespace exists
using ProcureToPay.Domain.Exceptions; // Assuming DomainStateException exists
using ProcureToPay.Domain.Interfaces; // For ITenantEntity

namespace ProcureToPay.Domain.Entities
{
    /// <summary>
    /// Represents a category for organizing products or services.
    /// Can be structured hierarchically. Includes methods for state changes.
    /// </summary>
    public class Category : BaseEntity<Guid>, ITenantEntity // Implementing ITenantEntity for multi-tenancy
    {
        /// <summary>
        /// The display name of the category.
        /// </summary>
        public string Name { get; private set; } = null!;

        /// <summary>
        /// A detailed description of the category.
        /// </summary>
        public string? Description { get; private set; }

        /// <summary>
        /// An optional short code or identifier for the category.
        /// </summary>
        public string? Code { get; private set; }

        /// <summary>
        /// Optional United Nations Standard Products and Services Code (UNSPSC).
        /// </summary>
        public string? UnspscCode { get; private set; }

        /// <summary>
        /// Foreign key for the parent category in the hierarchy. Null for root categories.
        /// </summary>
        public Guid? ParentCategoryId { get; private set; }

        /// <summary>
        /// Gets or sets the tenant ID for multi-tenancy support.
        /// </summary>
        public Guid TenantId { get; set; }

        // Navigation Properties
        /// <summary>
        /// Navigation property to the parent category. Virtual for lazy loading.
        /// </summary>
        public virtual Category? ParentCategory { get; private set; }

        /// <summary>
        /// Navigation property to the child categories. Virtual for lazy loading.
        /// </summary>
        public virtual ICollection<Category> ChildCategories { get; private set; } = new List<Category>();

        /// <summary>
        /// Navigation property to the product definitions belonging to this category. Virtual for lazy loading.
        /// </summary>
        public virtual ICollection<ProductDefinition> ProductDefinitions { get; private set; } = new List<ProductDefinition>();

        /// <summary>
        /// Navigation property to the products belonging to this category. Virtual for lazy loading.
        /// </summary>
        public virtual ICollection<Product> Products { get; private set; } = new List<Product>();

        /// <summary>
        /// Private constructor for EF Core.
        /// </summary>
        private Category() : base(Guid.NewGuid()) { }

        /// <summary>
        /// Creates a new Category instance.
        /// </summary>
        /// <param name="id">Category identifier.</param>
        /// <param name="tenantId">Tenant identifier for multi-tenancy support.</param>
        /// <param name="name">Name of the category.</param>
        /// <param name="description">Optional description.</param>
        /// <param name="code">Optional category code.</param>
        /// <param name="unspscCode">Optional UNSPSC code.</param>
        /// <param name="parentCategoryId">Optional ID of the parent category.</param>
        public Category(Guid id, Guid tenantId, string name, string? description = null, string? code = null, string? unspscCode = null, Guid? parentCategoryId = null) : base(id)
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new ArgumentNullException(nameof(name));
            if (tenantId == Guid.Empty)
                throw new ArgumentException("TenantId cannot be empty", nameof(tenantId));

            TenantId = tenantId;
            Name = name;
            Description = description;
            Code = code;
            UnspscCode = unspscCode;
            ParentCategoryId = parentCategoryId; // Basic assignment, cycle checks done externally

            AddDomainEvent(new CategoryCreatedEvent(this.Id, name)); // Raise event on creation
        }

        // --- Domain Methods ---

        /// <summary>
        /// Updates the descriptive details of the category.
        /// Complex format validation for Code/UnspscCode should happen before calling this.
        /// </summary>
        /// <param name="name">The new name for the category.</param>
        /// <param name="description">The new optional description.</param>
        /// <param name="code">The new optional code.</param>
        /// <param name="unspscCode">The new optional UNSPSC code.</param>
        /// <exception cref="ArgumentNullException">Thrown if name is null or whitespace.</exception>
        public void UpdateDetails(string name, string? description, string? code, string? unspscCode)
        {
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));

            bool changed = false;
            if (Name != name) { Name = name; changed = true; }
            if (Description != description) { Description = description; changed = true; }
            if (Code != code) { Code = code; changed = true; }
            if (UnspscCode != unspscCode) { UnspscCode = unspscCode; changed = true; }

            if (changed)
            {
                // Raise event only if something actually changed
                AddDomainEvent(new CategoryDetailsUpdatedEvent(this.Id));
            }
        }

        /// <summary>
        /// Assigns a new parent category or makes this a root category.
        /// Complex hierarchy validation (e.g., preventing cycles) should be performed
        /// by the Application Service or Domain Service before calling this method.
        /// </summary>
        /// <param name="newParentCategoryId">The ID of the new parent category, or null to make it a root category.</param>
        /// <exception cref="DomainStateException">Thrown if attempting to assign itself as parent.</exception>
        public void AssignParent(Guid? newParentCategoryId)
        {
            // Prevent assigning itself as parent
            if (newParentCategoryId.HasValue && newParentCategoryId.Value == this.Id)
            {
                throw new DomainStateException("Category cannot be its own parent.");
            }

            if (ParentCategoryId == newParentCategoryId) return; // No change

            ParentCategoryId = newParentCategoryId;

            // Raise event indicating the category might have moved in the hierarchy
            AddDomainEvent(new CategoryParentChangedEvent(this.Id, newParentCategoryId));
        }

        // Note: AddChildCategory/RemoveChildCategory methods are omitted intentionally.
        // Hierarchy is managed by setting the ParentCategoryId on the child.
    }

    // --- Placeholder Domain Event Definitions (Place in Domain/Events) ---
    /*
    namespace ProcureToPay.Domain.Events
    {
        // Base class or marker interface for domain events might exist
        public record CategoryCreatedEvent(Guid CategoryId, string Name);
        public record CategoryDetailsUpdatedEvent(Guid CategoryId);
        public record CategoryParentChangedEvent(Guid CategoryId, Guid? NewParentId);
        // Add other events as needed (e.g., CategoryDeletedEvent)
    }
    */
}
