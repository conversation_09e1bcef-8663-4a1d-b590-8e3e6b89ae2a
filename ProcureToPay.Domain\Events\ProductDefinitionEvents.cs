using System;
using ProcureToPay.Domain.Enums; // For ProductLifecycleState

namespace ProcureToPay.Domain.Events
{
    // Base marker interface (optional)
    // public interface IDomainEvent { }

    // --- ProductDefinition Specific Events ---
    public record ProductDefinitionCreatedEvent(Guid ProductDefinitionId, Guid? TenantId);
    public record ProductDefinitionDescriptionUpdatedEvent(Guid ProductDefinitionId);
    public record ProductDefinitionIdentifiersUpdatedEvent(Guid ProductDefinitionId);
    public record ProductDefinitionLifecycleStateChangedEvent(Guid ProductDefinitionId, ProductLifecycleState NewState);
    public record ProductDefinitionCategoryAssignedEvent(Guid ProductDefinitionId, Guid? CategoryId);
    public record ProductDefinitionAttributesUpdatedEvent(Guid ProductDefinitionId);
    public record ProductDefinitionDeletedEvent(Guid ProductDefinitionId);
    public record ProductDefinitionRestoredEvent(Guid ProductDefinitionId);
    public record ProductDefinitionVersionIncrementedEvent(Guid ProductDefinitionId, int NewVersion);
}
