using System;

namespace ProcureToPay.Domain.Events
{
    /// <summary>
    /// Extension methods for domain events to maintain backward compatibility.
    /// </summary>
    public static class DomainEventExtensions
    {
        /// <summary>
        /// Converts an object to an IDomainEvent.
        /// </summary>
        /// <param name="obj">The object to convert.</param>
        /// <returns>An IDomainEvent.</returns>
        public static IDomainEvent ToIDomainEvent(this object obj)
        {
            if (obj is IDomainEvent domainEvent)
            {
                return domainEvent;
            }

            return new LegacyDomainEvent(obj);
        }

        /// <summary>
        /// Wrapper for legacy domain events.
        /// </summary>
        private class LegacyDomainEvent : IDomainEvent
        {
            public DateTime OccurredOn { get; }
            public Guid EventId { get; }
            public object OriginalEvent { get; }

            public LegacyDomainEvent(object originalEvent)
            {
                OriginalEvent = originalEvent ?? throw new ArgumentNullException(nameof(originalEvent));
                EventId = Guid.NewGuid();
                OccurredOn = DateTime.UtcNow;
            }
        }
    }
}
