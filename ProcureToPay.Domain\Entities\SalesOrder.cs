﻿using System;
using System.Collections.Generic;
using System.Linq;
using ProcureToPay.Domain.Enums;       // Required for SalesOrderStatus
using ProcureToPay.Domain.ValueObjects; // Required for Address, Money
using ProcureToPay.Domain.Exceptions;   // Assuming DomainStateException exists
using ProcureToPay.Domain.Events;       // Assuming SalesOrder domain events namespace exists

// Assuming BaseEntity<Guid>, SalesOrderLine, Customer, ReturnAuthorization, SalesTerritory entities exist
namespace ProcureToPay.Domain.Entities
{
    /// <summary>
    /// Represents a Sales Order placed by a customer.
    /// Acts as the Aggregate Root for SalesOrderLines.
    /// </summary>
    public class SalesOrder : BaseEntity<Guid>
    {
        /// <summary>
        /// Foreign Key for multi-tenancy.
        /// </summary>
        public Guid TenantId { get; private set; }

        /// <summary>
        /// Unique, human-readable identifier for the sales order.
        /// </summary>
        public string OrderNumber { get; private set; } = null!;

        /// <summary>
        /// Date the order was placed or created.
        /// </summary>
        public DateTime OrderDate { get; private set; }

        /// <summary>
        /// Optional Foreign Key linking to the Customer who placed the order.
        /// </summary>
        public Guid? CustomerId { get; private set; } // Nullable if orders can be placed without registered customer

        /// <summary>
        /// Current status of the sales order (e.g., Draft, PendingApproval, Confirmed, Shipped).
        /// </summary>
        public SalesOrderStatus Status { get; private set; }

        /// <summary>
        /// Billing address for the order. Uses Address VO.
        /// </summary>
        public Address BillingAddress { get; private set; } = null!;

        /// <summary>
        /// Shipping address for the order. Uses Address VO.
        /// </summary>
        public Address ShippingAddress { get; private set; } = null!;

        /// <summary>
        /// Total amount for the order. Uses Money VO.
        /// </summary>
        public Money TotalAmount { get; private set; } = null!;

        /// <summary>
        /// Currency code for the order.
        /// </summary>
        public string CurrencyCode { get; private set; } = null!;

        // --- Flags and Indicators ---
        /// <summary>
        /// Indicates if the customer's credit limit check passed for this order.
        /// Logic for check resides externally (Application/Domain Service).
        /// </summary>
        public bool IsCreditApproved { get; private set; }

        /// <summary>
        /// Date when the Available-To-Promise check was performed.
        /// Logic for ATP check resides externally.
        /// </summary>
        public DateTime? AtpCheckDate { get; private set; }

        /// <summary>
        /// Indicates if the ATP check confirmed availability for the order lines.
        /// </summary>
        public bool IsAtpConfirmed { get; private set; }

        /// <summary>
        /// Flag indicating if this is a drop-shipment order (shipped directly from supplier to customer).
        /// </summary>
        public bool IsDropShipment { get; private set; }

        // --- Sales & Commission ---
        /// <summary>
        /// Optional Foreign Key (string representation) of the salesperson associated with the order. Links to ApplicationUser Id.
        /// </summary>
        public string? SalespersonId { get; private set; }

        /// <summary>
        /// Optional commission rate applicable to this order.
        /// Calculation logic resides externally.
        /// </summary>
        public decimal? CommissionRate { get; private set; }

        // --- Related Documents ---
        /// <summary>
        /// Optional Foreign Key linking to a related Return Merchandise Authorization (RMA).
        /// </summary>
        public Guid? RelatedReturnAuthorizationId { get; private set; }

        /// <summary>
        /// Optional Foreign Key linking to the Sales Territory.
        /// </summary>
        public Guid? SalesTerritoryId { get; private set; }

        /// <summary>
        /// Optional reference number for EDI (Electronic Data Interchange) transactions related to this order.
        /// </summary>
        public string? EdiTransactionReference { get; private set; }

        // --- Lifecycle ---
        /// <summary>
        /// Flag indicating if the order has been soft-deleted.
        /// </summary>
        public bool IsDeleted { get; private set; }


        // --- Navigation Properties ---
        private readonly List<SalesOrderLine> _lines = new();
        /// <summary>
        /// Line items included in this sales order.
        /// </summary>
        public virtual IReadOnlyCollection<SalesOrderLine> Lines => _lines.AsReadOnly();

        /// <summary>
        /// Navigation property to the Customer.
        /// </summary>
        public virtual Customer? Customer { get; private set; } // Nullable if CustomerId is nullable

        /// <summary>
        /// Navigation property to the related Return Authorization.
        /// </summary>
        public virtual ReturnAuthorization? ReturnAuthorization { get; private set; }

        /// <summary>
        /// Navigation property to the Sales Territory.
        /// </summary>
        public virtual SalesTerritory? SalesTerritory { get; private set; }
        // No direct navigation to Salesperson (ApplicationUser)


        /// <summary>
        /// Private constructor for EF Core.
        /// </summary>
        private SalesOrder() : base(Guid.NewGuid())
        {
            TotalAmount = new Money(0, "XXX"); // Placeholder
            CurrencyCode = "XXX"; // Placeholder
        }

        /// <summary>
        /// Creates a new Sales Order, typically in a Draft or Pending status.
        /// </summary>
        public SalesOrder(
            Guid id,
            Guid tenantId,
            string orderNumber,
            DateTime orderDate,
            Address billingAddress,
            Address shippingAddress,
            string currencyCode,
            Guid? customerId = null, // Optional customer link
            SalesOrderStatus initialStatus = SalesOrderStatus.Draft, // Example initial status
            string? salespersonId = null,
            decimal? commissionRate = null,
            Guid? salesTerritoryId = null,
            bool isDropShipment = false
            ) : base(id)
        {
            // Validation
            if (tenantId == Guid.Empty) throw new ArgumentException("TenantId cannot be empty.", nameof(tenantId));
            ArgumentException.ThrowIfNullOrWhiteSpace(orderNumber);
            if (orderDate == default) throw new ArgumentException("Order date must be specified.", nameof(orderDate));
            ArgumentNullException.ThrowIfNull(billingAddress);
            ArgumentNullException.ThrowIfNull(shippingAddress);
            ArgumentException.ThrowIfNullOrWhiteSpace(currencyCode);
            if (currencyCode.Length != 3) throw new ArgumentException("Currency code must be 3 characters.", nameof(currencyCode));
            if (commissionRate.HasValue && (commissionRate < 0 || commissionRate > 1)) throw new ArgumentOutOfRangeException(nameof(commissionRate), "Commission rate must be between 0 and 1.");

            TenantId = tenantId;
            OrderNumber = orderNumber;
            OrderDate = orderDate.ToUniversalTime(); // Store as UTC
            CustomerId = customerId;
            Status = initialStatus;
            BillingAddress = billingAddress;
            ShippingAddress = shippingAddress;
            CurrencyCode = currencyCode.ToUpperInvariant();
            TotalAmount = new Money(0m, this.CurrencyCode); // Initialize total
            IsCreditApproved = false; // Requires explicit check
            AtpCheckDate = null;
            IsAtpConfirmed = false; // Requires explicit check
            IsDropShipment = isDropShipment;
            SalespersonId = salespersonId;
            CommissionRate = commissionRate;
            SalesTerritoryId = salesTerritoryId;
            IsDeleted = false;

            AddDomainEvent(new SalesOrderCreatedEvent(this.Id, this.TenantId, this.CustomerId));
        }

        // --- Domain Methods ---

        public void AddLine(SalesOrderLine line)
        {
            // Check status - can lines be added?
            if (Status != SalesOrderStatus.Draft && Status != SalesOrderStatus.PendingApproval) // Example statuses
                throw new DomainStateException($"Cannot add lines when Sales Order status is '{Status}'.");

            ArgumentNullException.ThrowIfNull(line);
            if (line.SalesOrderId != this.Id)
                throw new ArgumentException("Line does not belong to this Sales Order.", nameof(line));
            // Check currency consistency if line has monetary values defined independently
            if (line.UnitPrice?.CurrencyCode != this.CurrencyCode || line.LineTotal?.CurrencyCode != this.CurrencyCode)
                throw new ArgumentException("Line item currency does not match Sales Order currency.", nameof(line));

            _lines.Add(line);
            RecalculateTotalAmount();
            AddDomainEvent(new SalesOrderLineAddedEvent(this.Id, line.Id));
        }

        public void RemoveLine(Guid lineId)
        {
            // Check status
            if (Status != SalesOrderStatus.Draft && Status != SalesOrderStatus.PendingApproval) // Example statuses
                throw new DomainStateException($"Cannot remove lines when Sales Order status is '{Status}'.");

            var line = _lines.FirstOrDefault(l => l.Id == lineId);
            if (line != null)
            {
                _lines.Remove(line);
                RecalculateTotalAmount();
                AddDomainEvent(new SalesOrderLineRemovedEvent(this.Id, lineId));
            }
        }

        // --- Methods related to Configuration Requirements ---

        public void MarkCreditApproved(bool isApproved)
        {
            // Check status? Only check if PendingApproval?
            IsCreditApproved = isApproved;
            // If not approved, maybe change status to OnHold?
            // if (!isApproved) SetStatus(SalesOrderStatus.OnHold);
            AddDomainEvent(new SalesOrderCreditCheckedEvent(this.Id, isApproved));
        }

        public void MarkAtpConfirmed(bool isConfirmed, DateTime checkDate)
        {
            // Check status?
            IsAtpConfirmed = isConfirmed;
            AtpCheckDate = checkDate.ToUniversalTime();
            // If not confirmed, maybe change status to Backordered or OnHold?
            AddDomainEvent(new SalesOrderAtpConfirmedEvent(this.Id, isConfirmed));
        }

        public void MarkAsDropShipment(bool isDropShip)
        {
            if (Status != SalesOrderStatus.Draft && Status != SalesOrderStatus.PendingApproval) // Example statuses
                throw new DomainStateException($"Cannot change drop-ship status when Sales Order status is '{Status}'.");
            if (IsDropShipment == isDropShip) return;

            IsDropShipment = isDropShip;
            // AddDomainEvent(...)
        }

        public void AssignSalesperson(string? salespersonId, decimal? commissionRate)
        {
            // Check status?
            if (commissionRate.HasValue && (commissionRate < 0 || commissionRate > 1)) throw new ArgumentOutOfRangeException(nameof(commissionRate), "Commission rate must be between 0 and 1.");

            SalespersonId = salespersonId;
            CommissionRate = commissionRate;
            // AddDomainEvent(...)
        }

        public void LinkReturnAuthorization(Guid? returnAuthorizationId)
        {
            // Check status? Should only link if order is Completed/Shipped?
            RelatedReturnAuthorizationId = returnAuthorizationId;
            // AddDomainEvent(...)
        }

        public void AssignTerritory(Guid? salesTerritoryId)
        {
            // Check status?
            SalesTerritoryId = salesTerritoryId;
            // AddDomainEvent(...)
        }

        public void SetEdiReference(string? ediReference)
        {
            // Check status?
            EdiTransactionReference = ediReference;
            // AddDomainEvent(...)
        }


        // --- Status Transitions ---
        public void ConfirmOrder()
        {
            // Example: Move from Draft/Pending to Confirmed after checks
            if (Status != SalesOrderStatus.Draft && Status != SalesOrderStatus.PendingApproval)
                throw new DomainStateException($"Cannot confirm Sales Order with status '{Status}'.");
            // Add checks: Credit Approved? ATP Confirmed? Lines exist?
            if (!IsCreditApproved) throw new DomainStateException("Cannot confirm Sales Order: Credit check not approved.");
            if (!IsAtpConfirmed) throw new DomainStateException("Cannot confirm Sales Order: ATP check not confirmed.");
            if (!_lines.Any()) throw new DomainStateException("Cannot confirm Sales Order with no lines.");

            SetStatus(SalesOrderStatus.Confirmed);
            AddDomainEvent(new SalesOrderConfirmedEvent(this.Id));
        }

        public void ReleaseToFulfillment()
        {
            if (Status != SalesOrderStatus.Confirmed)
                throw new DomainStateException($"Cannot release Sales Order with status '{Status}'. Must be '{SalesOrderStatus.Confirmed}'.");

            SetStatus(SalesOrderStatus.ReleasedToFulfillment); // Example status
                                                               // AddDomainEvent(...)
        }

        public void Ship(DateTime shipDate /*, tracking info */)
        {
            if (Status != SalesOrderStatus.ReleasedToFulfillment) // Example prerequisite
                throw new DomainStateException($"Cannot ship Sales Order with status '{Status}'.");

            SetStatus(SalesOrderStatus.Shipped);
            // Store shipDate, tracking info?
            // AddDomainEvent(new SalesOrderShippedEvent(this.Id, shipDate));
        }

        // Other transitions: Invoice, Complete, Cancel...


        // --- Soft Delete ---
        public void MarkAsDeleted()
        {
            if (IsDeleted) return;
            // Add checks? Cannot delete if Shipped/Invoiced/Completed?
            IsDeleted = true;
            AddDomainEvent(new SalesOrderDeletedEvent(this.Id));
        }

        public void Restore()
        {
            if (!IsDeleted) return;
            IsDeleted = false;
            AddDomainEvent(new SalesOrderRestoredEvent(this.Id));
        }

        // --- Helpers ---
        private void RecalculateTotalAmount()
        {
            if (_lines.Any(l => l.LineTotal?.CurrencyCode != this.CurrencyCode))
            {
                throw new DomainStateException("Cannot calculate total amount due to currency mismatch in order lines.");
            }
            decimal total = _lines.Sum(line => line.LineTotal?.Amount ?? 0m);
            TotalAmount = new Money(total, this.CurrencyCode);
            // AddDomainEvent(new SalesOrderTotalAmountChangedEvent(this.Id, TotalAmount));
        }

        private void SetStatus(SalesOrderStatus newStatus)
        {
            if (Status == newStatus) return;
            var oldStatus = Status;
            Status = newStatus;
            AddDomainEvent(new SalesOrderStatusChangedEvent(this.Id, oldStatus, newStatus));
        }
    }

    // --- Placeholder Domain Event Definitions (Place in Domain/Events) ---
    /*
    namespace ProcureToPay.Domain.Events
    {
        public record SalesOrderCreatedEvent(Guid SalesOrderId, Guid TenantId, Guid? CustomerId);
        public record SalesOrderLineAddedEvent(Guid SalesOrderId, Guid LineId);
        public record SalesOrderLineRemovedEvent(Guid SalesOrderId, Guid LineId);
        public record SalesOrderCreditCheckedEvent(Guid SalesOrderId, bool IsApproved);
        public record SalesOrderAtpConfirmedEvent(Guid SalesOrderId, bool IsConfirmed);
        public record SalesOrderConfirmedEvent(Guid SalesOrderId);
        public record SalesOrderStatusChangedEvent(Guid SalesOrderId, SalesOrderStatus OldStatus, SalesOrderStatus NewStatus);
        public record SalesOrderDeletedEvent(Guid SalesOrderId);
        public record SalesOrderRestoredEvent(Guid SalesOrderId);
        // Add events for Shipped, Invoiced, Completed, Cancelled etc.
    }
    */

    // --- Placeholder Related Entities (Define elsewhere) ---
    // public class Customer : BaseEntity<Guid> { /* ... */ }
    // public class ReturnAuthorization : BaseEntity<Guid> { /* ... */ }
    // public class SalesTerritory : BaseEntity<Guid> { /* ... */ }
    // public class SalesOrderLine : BaseEntity<Guid> { public Guid SalesOrderId { get; set; } public Money? UnitPrice {get; set;} public Money? LineTotal {get; set;} /* ... */ }

}
