using System;
using System.Collections.Generic;
using ProcureToPay.Domain.Enums;
using ProcureToPay.Domain.ValueObjects; // Assuming Address VO exists here
using ProcureToPay.Domain.Exceptions; // Assuming DomainStateException exists here
using ProcureToPay.Domain.Events;     // Assuming Vendor domain events namespace exists

namespace ProcureToPay.Domain.Entities
{
    /// <summary>
    /// Represents a Vendor entity, providing goods or services.
    /// Contains identification, contact, address, and status information.
    /// </summary>
    public class Vendor : BaseEntity<Guid> // Assuming inherits from enhanced BaseEntity<Guid>
    {
        // --- Core Identification ---
        public string Name { get; private set; } = null!;
        public string? VendorCode { get; private set; }
        public VendorStatus Status { get; private set; }

        // --- Identification Numbers ---
        /// <summary>
        /// Value Added Tax (VAT) registration number. Format may vary by region.
        /// </summary>
        public string? VatNumber { get; private set; }
        /// <summary>
        /// Commercial Registration Number (CRN). Format may vary by region.
        /// </summary>
        public string? CommercialRegistrationNumber { get; private set; }
        /// <summary>
        /// General Tax Identification Number, if different from VAT/CRN.
        /// </summary>
        public string? TaxId { get; private set; } // Could be combined depending on region

        // --- Contact Information ---
        /// <summary>
        /// Primary contact person's name at the vendor.
        /// </summary>
        public string? ContactName { get; private set; }
        /// <summary>
        /// Primary contact person's email address.
        /// </summary>
        public string? ContactEmail { get; private set; }
        /// <summary>
        /// Primary contact phone number.
        /// </summary>
        public string? PhoneNumber { get; private set; }
        /// <summary>
        /// Vendor's primary website URL.
        /// </summary>
        public string? Website { get; private set; }


        // --- Address ---
        /// <summary>
        /// Primary physical or mailing address of the vendor.
        /// </summary>
        public virtual Address PrimaryAddress { get; private set; } = null!;

        // --- Navigation Properties ---

        /// <summary>
        /// Optional link to a more detailed Supplier profile (if using separate Supplier entity).
        /// Inverse navigation for a 1-to-1 relationship defined on Supplier.
        /// </summary>
        public virtual Supplier? SupplierProfile { get; private set; }

        /// <summary>
        /// Purchase Orders issued to this Vendor.
        /// </summary>
        public virtual ICollection<PurchaseOrder> PurchaseOrders { get; private set; } = new List<PurchaseOrder>();

        /// <summary>
        /// Specific product/service offerings from this Vendor.
        /// </summary>
        public virtual ICollection<VendorProduct> VendorProducts { get; private set; } = new List<VendorProduct>();

        /// <summary>
        /// Contracts associated with this Vendor (optional, if relationship exists).
        /// </summary>
        public virtual ICollection<Contract> Contracts { get; private set; } = new List<Contract>();

        /// <summary>
        /// Invoices associated with this Vendor.
        /// </summary>
        public virtual ICollection<Invoice> Invoices { get; private set; } = new List<Invoice>();

        /// <summary>
        /// Payment transactions associated with this Vendor.
        /// </summary>
        public virtual ICollection<PaymentTransaction> PaymentTransactions { get; private set; } = new List<PaymentTransaction>();

        /// <summary>
        /// Proposals submitted by this Vendor.
        /// </summary>
        public virtual ICollection<VendorProposal> VendorProposals { get; private set; } = new List<VendorProposal>();


        // --- Constructor ---
        private Vendor() : base(Guid.NewGuid()) { } // For EF Core

        /// <summary>
        /// Creates a new Vendor instance.
        /// </summary>
        /// <param name="id">Vendor identifier.</param>
        /// <param name="name">Vendor name.</param>
        /// <param name="primaryAddress">Primary address (Address VO).</param>
        /// <param name="vendorCode">Optional unique vendor code.</param>
        /// <param name="status">Initial status (defaults to Pending).</param>
        /// <param name="contactName">Optional primary contact name.</param>
        /// <param name="contactEmail">Optional primary contact email.</param>
        /// <param name="phoneNumber">Optional primary phone number.</param>
        /// <param name="website">Optional website.</param>
        /// <param name="vatNumber">Optional VAT number.</param>
        /// <param name="crn">Optional Commercial Registration Number.</param>
        /// <param name="taxId">Optional Tax ID.</param>
        public Vendor(
            Guid id,
            string name,
            Address primaryAddress,
            string? vendorCode = null,
            VendorStatus status = VendorStatus.Pending,
            string? contactName = null,
            string? contactEmail = null,
            string? phoneNumber = null,
            string? website = null,
            string? vatNumber = null,
            string? crn = null,
            string? taxId = null
            ) : base(id)
        {
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
            ArgumentNullException.ThrowIfNull(primaryAddress);
            // Basic validation for other fields if needed (e.g., email format - though maybe better externally)

            Name = name;
            PrimaryAddress = primaryAddress;
            VendorCode = vendorCode;
            Status = status;
            ContactName = contactName;
            ContactEmail = contactEmail;
            PhoneNumber = phoneNumber;
            Website = website;
            VatNumber = vatNumber;
            CommercialRegistrationNumber = crn;
            TaxId = taxId;

            AddDomainEvent(new VendorCreatedEvent(this.Id)); // Raise event
        }

        // --- Domain Methods ---

        /// <summary>
        /// Updates the primary contact information for the vendor.
        /// </summary>
        public void UpdateContactInfo(string? contactName, string? contactEmail, string? phoneNumber, string? website)
        {
            // Basic validation if needed (e.g., email format - maybe better externally)
            bool changed = (ContactName != contactName || ContactEmail != contactEmail || PhoneNumber != phoneNumber || Website != website);
            ContactName = contactName;
            ContactEmail = contactEmail;
            PhoneNumber = phoneNumber;
            Website = website;

            if (changed) AddDomainEvent(new VendorContactInfoUpdatedEvent(this.Id)); // Raise event
        }

        /// <summary>
        /// Updates the identification numbers (VAT, CRN, Tax ID).
        /// Complex/country-specific format validation should be done *before* calling this method.
        /// </summary>
        public void UpdateIdentificationNumbers(string? vatNumber, string? commercialRegistrationNumber, string? taxId)
        {
            bool changed = (VatNumber != vatNumber || CommercialRegistrationNumber != commercialRegistrationNumber || TaxId != taxId);
            VatNumber = vatNumber;
            CommercialRegistrationNumber = commercialRegistrationNumber;
            TaxId = taxId;

            if (changed) AddDomainEvent(new VendorIdentificationUpdatedEvent(this.Id)); // Raise event
        }

        /// <summary>
        /// Updates the primary address.
        /// </summary>
        public void UpdatePrimaryAddress(Address newAddress)
        {
            ArgumentNullException.ThrowIfNull(newAddress);
            if (PrimaryAddress == newAddress) return; // Assuming Address VO has equality implemented

            PrimaryAddress = newAddress;
            AddDomainEvent(new VendorAddressUpdatedEvent(this.Id, newAddress)); // Raise event
        }

        /// <summary>
        /// Activates the vendor if prerequisites are met.
        /// </summary>
        /// <exception cref="DomainStateException">Thrown if the vendor cannot be activated due to missing information or current status.</exception>
        public void Activate()
        {
            if (Status == VendorStatus.Active)
            {
                return; // Already active
            }
            // Use Terminated instead of Blacklisted
            if (Status == VendorStatus.Terminated || Status == VendorStatus.Inactive)
            {
                throw new DomainStateException($"Cannot activate a vendor with status '{Status}'.");
            }

            // Enforce internal prerequisites for activation (based on entity's own state)
            // Example: Require address and at least one Tax ID to be present
            if (PrimaryAddress == null) // Should ideally check specific address fields if VO allows partial construction
            {
                throw new DomainStateException("Cannot activate vendor without a primary address.");
            }
            if (string.IsNullOrWhiteSpace(VatNumber) && string.IsNullOrWhiteSpace(CommercialRegistrationNumber) && string.IsNullOrWhiteSpace(TaxId))
            {
                throw new DomainStateException("Cannot activate vendor without at least one identification number (VAT, CRN, or Tax ID).");
            }

            Status = VendorStatus.Active;
            AddDomainEvent(new VendorActivatedEvent(this.Id)); // Raise event
        }

        /// <summary>
        /// Deactivates the vendor (marks as Inactive).
        /// </summary>
        /// <exception cref="DomainStateException">Thrown if the vendor is already inactive or terminated.</exception>
        public void Deactivate()
        {
            // Use Terminated instead of Blacklisted
            if (Status == VendorStatus.Inactive || Status == VendorStatus.Terminated)
            {
                return; // Already inactive or terminated
            }

            Status = VendorStatus.Inactive; // Use Inactive from VendorStatus enum
            AddDomainEvent(new VendorDeactivatedEvent(this.Id)); // Raise event
        }

        /// <summary>
        /// Terminates the relationship with the vendor, preventing future business.
        /// </summary>
        /// <param name="reason">Reason for termination.</param>
        /// <exception cref="ArgumentNullException">Thrown if reason is empty.</exception>
        public void Terminate(string reason) // Renamed from Blacklist
        {
            if (string.IsNullOrWhiteSpace(reason)) throw new ArgumentNullException(nameof(reason));
            if (Status == VendorStatus.Terminated) return; // Already terminated

            // Use Terminated instead of Blacklisted
            Status = VendorStatus.Terminated;
            AddDomainEvent(new VendorTerminatedEvent(this.Id, reason)); // Raise event
        }

        // Other methods like updating VendorCode, etc.
    }
}
