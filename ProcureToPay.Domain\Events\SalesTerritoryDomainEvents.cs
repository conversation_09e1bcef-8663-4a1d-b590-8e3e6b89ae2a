﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ProcureToPay.Domain.Events
{
    public record SalesTerritoryCreatedEvent(Guid SalesTerritoryId, Guid TenantId, string Name);
    public record SalesTerritoryUpdatedEvent(Guid SalesTerritoryId, string OldName, string NewName); // Or more detailed
    public record SalesTerritoryDeletedEvent(Guid SalesTerritoryId);
    public record SalesTerritoryRestoredEvent(Guid SalesTerritoryId);
    // public record SalesTerritorySalespersonAssignedEvent(Guid SalesTerritoryId, string? SalespersonId);
}