using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProcureToPay.Domain.Entities;
using ProcureToPay.Domain.Enums; // Assuming ReturnAuthorizationStatus enum exists
using ProcureToPay.Infrastructure.Persistence.Extensions;

namespace ProcureToPay.Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// Configuration for the ReturnAuthorization entity for EF Core and PostgreSQL.
    /// </summary>
    public class ReturnAuthorizationConfiguration : IEntityTypeConfiguration<ReturnAuthorization>
    {
        public void Configure(EntityTypeBuilder<ReturnAuthorization> builder)
        {
            // Table Mapping
            builder.ToTable("return_authorizations");

            // Primary Key
            builder.HasKey(ra => ra.Id);
            builder.Property(ra => ra.Id).ValueGeneratedOnAdd();

            // Properties
            builder.Property(ra => ra.RmaNumber)
                .IsRequired()
                .HasMaxLength(100); // Adjust length as needed

            builder.Property(ra => ra.RequestDate)
                .IsRequired()
                .HasColumnType("timestamp without time zone"); // Or "date"

            builder.Property(ra => ra.AuthorizationDate)
                .HasColumnType("timestamp without time zone") // Or "date"
                .IsRequired(false); // Null until authorized

            builder.Property(ra => ra.ExpiryDate)
                .HasColumnType("timestamp without time zone") // Or "date"
                .IsRequired(false); // RMA might not expire

            builder.Property(ra => ra.Status)
                .IsRequired()
                .HasConversion<string>() // Or int
                .HasMaxLength(50);

            builder.Property(ra => ra.ReasonForReturn)
                .HasColumnType("text") // Allow detailed reasons
                .IsRequired(false);

            // Foreign Keys (Assuming Guid or appropriate types)
            builder.Property(ra => ra.CustomerId).IsRequired();
            builder.Property(ra => ra.SalesOrderId).IsRequired(false); // Link to original SO or Invoice
            builder.Property(ra => ra.InvoiceId).IsRequired(false); // Link to original SO or Invoice

            builder.Property(ra => ra.TenantId).IsRequired();


            // --- Concurrency Token ---
            builder.UseXminAsConcurrencyToken();


            // --- Relationships ---
            // Header-Lines (One-to-Many)
            builder.HasMany(ra => ra.Lines)
                   .WithOne(l => l.ReturnAuthorization) // Assuming Line has RA nav prop
                   .HasForeignKey(l => l.ReturnAuthorizationId)
                   .IsRequired()
                   .OnDelete(DeleteBehavior.Cascade); // Deleting header deletes lines

            // Link to Customer (Many-to-One)
            builder.HasOne(ra => ra.Customer)
                   .WithMany(c => c.ReturnAuthorizations) // Assuming Customer has collection
                   .HasForeignKey(ra => ra.CustomerId)
                   .IsRequired()
                   .OnDelete(DeleteBehavior.Restrict); // Prevent deleting customer if RMAs exist

            // Link to Origin (Many-to-One, Optional)
            builder.HasOne(ra => ra.SalesOrder)
                   .WithMany() // Assuming SO doesn't have direct nav back
                   .HasForeignKey(ra => ra.SalesOrderId)
                   .IsRequired(false)
                   .OnDelete(DeleteBehavior.Restrict); // Or SetNull?

            // Link to Origin Invoice (Many-to-One, Optional)
            builder.HasOne(ra => ra.Invoice)
                   .WithMany() // Assuming Invoice doesn't have direct nav back
                   .HasForeignKey(ra => ra.InvoiceId)
                   .IsRequired(false)
                   .OnDelete(DeleteBehavior.Restrict); // Or SetNull?


            // --- Indexes ---
            builder.HasIndex(ra => ra.RmaNumber).IsUnique();
            builder.HasIndex(ra => ra.Status);
            builder.HasIndex(ra => ra.RequestDate);
            builder.HasIndex(ra => ra.CustomerId);
            builder.HasIndex(ra => ra.SalesOrderId);
            builder.HasIndex(ra => ra.InvoiceId);
            builder.HasIndex(ra => ra.TenantId);


            // --- TODO ---
            // TODO: Verify FK types (CustomerId, SalesOrderId, InvoiceId) match related entities.
            // TODO: Define ReturnAuthorizationStatus enum.
            // TODO: Decide if link is to SalesOrder OR Invoice, or both allowed.
        }
    }
}

