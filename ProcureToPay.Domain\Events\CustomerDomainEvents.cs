﻿using System;
using ProcureToPay.Domain.ValueObjects; // For Money, Address, ContactPerson if needed

namespace ProcureToPay.Domain.Events
{
    // --- Placeholder: Define a base marker interface or class if desired ---
    // public interface IDomainEvent { }

    // --- Customer Specific Events ---

    /// <summary>
    /// Raised when a new Customer is created.
    /// </summary>
    /// <param name="CustomerId">The ID of the newly created customer.</param>
    /// <param name="TenantId">The Tenant ID associated with the customer.</param>
    public record CustomerCreatedEvent(Guid CustomerId, Guid TenantId);

    /// <summary>
    /// Raised when core details of a Customer are updated (e.g., Name, LegalName, TaxId, Website, Type).
    /// </summary>
    /// <param name="CustomerId">The ID of the updated customer.</param>
    public record CustomerDetailsUpdatedEvent(Guid CustomerId);

    /// <summary>
    /// Raised when the Billing or Shipping Address of a Customer is updated.
    /// Consider separate events or include which address changed if needed.
    /// </summary>
    /// <param name="CustomerId">The ID of the customer whose address changed.</param>
    public record CustomerAddressUpdatedEvent(Guid CustomerId);

    /// <summary>
    /// Raised when the Primary Contact details of a Customer are updated.
    /// </summary>
    /// <param name="CustomerId">The ID of the customer whose contact changed.</param>
    public record CustomerContactUpdatedEvent(Guid CustomerId);

    /// <summary>
    /// Raised when the default payment terms or currency code for a Customer are updated.
    /// </summary>
    /// <param name="CustomerId">The ID of the updated customer.</param>
    public record CustomerDefaultsUpdatedEvent(Guid CustomerId);

    /// <summary>
    /// Raised when the Credit Limit for a Customer is set or updated.
    /// </summary>
    /// <param name="CustomerId">The ID of the customer.</param>
    /// <param name="NewLimit">The new credit limit (nullable Money VO).</param>
    public record CustomerCreditLimitSetEvent(Guid CustomerId, Money? NewLimit);

    /// <summary>
    /// Raised when a Sales Representative is assigned to a Customer.
    /// </summary>
    /// <param name="CustomerId">The ID of the customer.</param>
    /// <param name="SalesRepId">The ID of the assigned sales representative (nullable string).</param>
    public record CustomerSalesRepAssignedEvent(Guid CustomerId, string? SalesRepId);

    /// <summary>
    /// Raised when a Customer is activated.
    /// </summary>
    /// <param name="CustomerId">The ID of the activated customer.</param>
    public record CustomerActivatedEvent(Guid CustomerId);

    /// <summary>
    /// Raised when a Customer is deactivated.
    /// </summary>
    /// <param name="CustomerId">The ID of the deactivated customer.</param>
    public record CustomerDeactivatedEvent(Guid CustomerId);

    /// <summary>
    /// Raised when a Customer is soft-deleted.
    /// </summary>
    /// <param name="CustomerId">The ID of the deleted customer.</param>
    public record CustomerDeletedEvent(Guid CustomerId);

    /// <summary>
    /// Raised when a soft-deleted Customer is restored.
    /// </summary>
    /// <param name="CustomerId">The ID of the restored customer.</param>
    public record CustomerRestoredEvent(Guid CustomerId);

}
