using ProcureToPay.Domain.Interfaces;

namespace ProcureToPay.Infrastructure.Persistence
{
    /// <summary>
    /// Implementation of ITenantProvider for design-time operations like migrations.
    /// Always returns null for the tenant ID, which means no tenant filtering during migrations.
    /// </summary>
    public class DesignTimeTenantProvider : ITenantProvider
    {
        /// <summary>
        /// Gets the current tenant ID, which is always null for design-time operations.
        /// </summary>
        /// <returns>Always returns null.</returns>
        public Guid? GetCurrentTenantId()
        {
            return null;
        }
    }
}
