using Microsoft.EntityFrameworkCore;
using ProcureToPay.Application.Interfaces;
using System.Threading;
using System.Threading.Tasks;

namespace ProcureToPay.Infrastructure.Persistence
{
    /// <summary>
    /// Adapter that implements ICategoryDbContextFactory by wrapping
    /// an IDbContextFactory&lt;ApplicationDbContext&gt;.
    /// </summary>
    public class ApplicationDbContextFactoryAdapter : ICategoryDbContextFactory
    {
        private readonly IDbContextFactory<ApplicationDbContext> _factory;

        /// <summary>
        /// Initializes a new instance of the <see cref="ApplicationDbContextFactoryAdapter"/> class.
        /// </summary>
        /// <param name="factory">The underlying factory for ApplicationDbContext.</param>
        public ApplicationDbContextFactoryAdapter(IDbContextFactory<ApplicationDbContext> factory)
        {
            _factory = factory;
        }

        /// <summary>
        /// Creates a new instance of an IApplicationDbContext.
        /// </summary>
        /// <returns>A new instance of an IApplicationDbContext.</returns>
        public IApplicationDbContext CreateDbContext()
        {
            return _factory.CreateDbContext();
        }

        /// <summary>
        /// Creates a new instance of an IApplicationDbContext asynchronously.
        /// </summary>
        /// <param name="cancellationToken">A token to cancel the operation.</param>
        /// <returns>A task representing the asynchronous operation. The task result contains a new instance of an IApplicationDbContext.</returns>
        public async Task<IApplicationDbContext> CreateDbContextAsync(CancellationToken cancellationToken = default)
        {
            return await _factory.CreateDbContextAsync(cancellationToken);
        }
    }
}
