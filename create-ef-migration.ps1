# PowerShell script for creating new EF Core migrations in a .NET Aspire environment
# This script simplifies the process of creating new migrations with proper naming and configuration

param (
    [Parameter(Mandatory=$true)]
    [string]$MigrationName,
    
    [Parameter(Mandatory=$false)]
    [switch]$NoCompile,
    
    [Parameter(Mandatory=$false)]
    [string]$OutputDir = "Persistence\Migrations"
)

# Set script to stop on error
$ErrorActionPreference = "Stop"

# Define colors for output
$colorInfo = "Cyan"
$colorSuccess = "Green"
$colorWarning = "Yellow"
$colorError = "Red"

# Define paths
$infrastructureProjectPath = "ProcureToPay.Infrastructure"
$webAppProjectPath = "ProcureToPay.WebApp\ProcureToPay.WebApp"

# Display banner
Write-Host "=======================================================" -ForegroundColor $colorInfo
Write-Host "   EF Core Migration Creator for .NET Aspire Projects  " -ForegroundColor $colorInfo
Write-Host "=======================================================" -ForegroundColor $colorInfo
Write-Host ""

# Step 1: Validate migration name
Write-Host "Step 1: Validating migration name..." -ForegroundColor $colorInfo

if ([string]::IsNullOrWhiteSpace($MigrationName)) {
    Write-Error "  Migration name cannot be empty."
    exit 1
}

# Remove any invalid characters from migration name
$sanitizedName = $MigrationName -replace '[^a-zA-Z0-9_]', ''

if ($sanitizedName -ne $MigrationName) {
    Write-Host "  Migration name contains invalid characters. Sanitized to: $sanitizedName" -ForegroundColor $colorWarning
    $MigrationName = $sanitizedName
}

Write-Host "  Creating migration: $MigrationName" -ForegroundColor $colorSuccess

# Step 2: Build the projects
if (-not $NoCompile) {
    Write-Host "Step 2: Building projects..." -ForegroundColor $colorInfo
    
    try {
        # Build the infrastructure project
        Write-Host "  Building $infrastructureProjectPath..." -ForegroundColor $colorInfo
        dotnet build $infrastructureProjectPath
        
        if ($LASTEXITCODE -ne 0) {
            Write-Error "  Failed to build $infrastructureProjectPath"
            exit 1
        }
        
        # Build the web app project
        Write-Host "  Building $webAppProjectPath..." -ForegroundColor $colorInfo
        dotnet build $webAppProjectPath
        
        if ($LASTEXITCODE -ne 0) {
            Write-Error "  Failed to build $webAppProjectPath"
            exit 1
        }
        
        Write-Host "  Projects built successfully" -ForegroundColor $colorSuccess
    }
    catch {
        Write-Error "  An error occurred while building projects: $_"
        exit 1
    }
}
else {
    Write-Host "Step 2: Skipping build (NoCompile flag set)" -ForegroundColor $colorInfo
}

# Step 3: Create the migration
Write-Host "Step 3: Creating migration..." -ForegroundColor $colorInfo

try {
    # Construct the EF Core command
    $efCommand = "dotnet ef migrations add $MigrationName --project $infrastructureProjectPath --startup-project $webAppProjectPath --context ApplicationDbContext --output-dir $OutputDir"
    
    Write-Host "  Running: $efCommand" -ForegroundColor $colorInfo
    
    # Execute the command
    Invoke-Expression $efCommand
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "  Migration '$MigrationName' created successfully!" -ForegroundColor $colorSuccess
    }
    else {
        Write-Error "  Failed to create migration. See error details above."
        exit 1
    }
}
catch {
    Write-Error "  An error occurred while creating the migration: $_"
    exit 1
}

# Step 4: List created migration files
Write-Host "Step 4: Listing created migration files..." -ForegroundColor $colorInfo

$migrationFiles = Get-ChildItem -Path "$infrastructureProjectPath\$OutputDir" -Filter "*$MigrationName*"

if ($migrationFiles.Count -gt 0) {
    Write-Host "  Created migration files:" -ForegroundColor $colorSuccess
    foreach ($file in $migrationFiles) {
        Write-Host "    $($file.Name)" -ForegroundColor $colorInfo
    }
}
else {
    Write-Host "  Warning: No migration files found matching '$MigrationName'" -ForegroundColor $colorWarning
}

Write-Host ""
Write-Host "Migration creation process completed!" -ForegroundColor $colorSuccess
Write-Host "To apply this migration, run: .\apply-ef-migrations.ps1" -ForegroundColor $colorInfo
Write-Host ""
