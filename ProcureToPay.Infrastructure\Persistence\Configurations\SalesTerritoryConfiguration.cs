using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProcureToPay.Domain.Entities;
using ProcureToPay.Infrastructure.Identity; // Assuming ApplicationUser exists

namespace ProcureToPay.Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// Configuration for the SalesTerritory entity for EF Core and PostgreSQL.
    /// </summary>
    public class SalesTerritoryConfiguration : IEntityTypeConfiguration<SalesTerritory>
    {
        public void Configure(EntityTypeBuilder<SalesTerritory> builder)
        {
            // Table Mapping
            builder.ToTable("sales_territories");

            // Primary Key
            builder.HasKey(st => st.Id);
            builder.Property(st => st.Id).ValueGeneratedOnAdd();

            // Properties
            builder.Property(st => st.TerritoryCode)
                .IsRequired()
                .HasMaxLength(50); // Adjust length as needed

            builder.Property(st => st.Name)
                .IsRequired()
                .HasMaxLength(150);

            builder.Property(st => st.Description)
                .HasColumnType("text")
                .IsRequired(false);

            // Foreign Key for Hierarchy
            builder.Property(st => st.ParentTerritoryId).IsRequired(false); // Null for root territories

            builder.Property(st => st.TenantId).IsRequired();


            // --- Relationships ---
            // Hierarchy (Self-referencing Many-to-One/One-to-Many)
            builder.HasOne(st => st.ParentTerritory)
                   .WithMany(st => st.ChildTerritories) // Assumes ChildTerritories collection exists
                   .HasForeignKey(st => st.ParentTerritoryId)
                   .IsRequired(false) // Root territories have no parent
                   .OnDelete(DeleteBehavior.Restrict); // Prevent deleting territory if children exist (or SetNull?)

            // Link to Reps (Many-to-Many)
            // Requires a joining entity (e.g., SalesTerritoryRep) or UsingEntity()
            // Example using UsingEntity assuming ApplicationUser represents reps:
            builder.HasMany<ProcureToPay.Infrastructure.Identity.ApplicationUser>() // Assuming SalesRepresentatives collection on SalesTerritory
                   .WithMany(u => u.SalesTerritories) // Assuming SalesTerritories collection on ApplicationUser
                   .UsingEntity<Dictionary<string, object>>( // Simple join table
                       "sales_territory_representatives", // Join table name
                       j => j.HasOne<ProcureToPay.Infrastructure.Identity.ApplicationUser>().WithMany().HasForeignKey("RepresentativeId"), // FK to User
                       j => j.HasOne<SalesTerritory>().WithMany().HasForeignKey("SalesTerritoryId"), // FK to Territory
                       j =>
                       {
                           j.HasKey("SalesTerritoryId", "RepresentativeId"); // Composite PK for join table
                           j.ToTable("sales_territory_representatives"); // Ensure table name
                       });
            // TODO: Confirm navigation property names (SalesRepresentatives, SalesTerritories) and User entity type. Configure join entity explicitly if more properties are needed on the relationship.


            // --- Indexes ---
            builder.HasIndex(st => st.TerritoryCode).IsUnique();
            builder.HasIndex(st => st.Name);
            builder.HasIndex(st => st.ParentTerritoryId);
            builder.HasIndex(st => st.TenantId);


            // --- TODO ---
            // TODO: Define ChildTerritories and SalesRepresentatives collections on SalesTerritory entity.
            // TODO: Define SalesTerritories collection on ApplicationUser entity (or relevant User entity).
            // TODO: Verify FK type for ParentTerritoryId.
        }
    }
}

