using System;
using System.Collections.Generic;
using System.Linq;

namespace ProcureToPay.Domain.ValueObjects
{
    /// <summary>
    /// Base class for Value Objects in Domain-Driven Design.
    /// Value Objects are immutable and compared by their values, not their reference.
    /// </summary>
    public abstract class ValueObject
    {
        /// <summary>
        /// When overridden in a derived class, returns all components of the value object
        /// that should be used for equality comparison.
        /// </summary>
        /// <returns>An enumerable of objects representing the value components.</returns>
        protected abstract IEnumerable<object> GetEqualityComponents();

        /// <summary>
        /// Determines whether this value object is equal to another object.
        /// </summary>
        /// <param name="obj">The object to compare with.</param>
        /// <returns>True if the objects are equal, false otherwise.</returns>
        public override bool Equals(object? obj)
        {
            if (obj == null || obj.GetType() != GetType())
            {
                return false;
            }

            var other = (ValueObject)obj;
            return GetEqualityComponents().SequenceEqual(other.GetEqualityComponents());
        }

        /// <summary>
        /// Serves as the default hash function.
        /// </summary>
        /// <returns>A hash code for the current value object.</returns>
        public override int GetHashCode()
        {
            return GetEqualityComponents()
                .Select(x => x != null ? x.GetHashCode() : 0)
                .Aggregate((x, y) => x ^ y);
        }

        /// <summary>
        /// Equality operator.
        /// </summary>
        /// <param name="left">The left value object.</param>
        /// <param name="right">The right value object.</param>
        /// <returns>True if the value objects are equal, false otherwise.</returns>
        public static bool operator ==(ValueObject? left, ValueObject? right)
        {
            if (left is null && right is null)
                return true;
            if (left is null || right is null)
                return false;
            return left.Equals(right);
        }

        /// <summary>
        /// Inequality operator.
        /// </summary>
        /// <param name="left">The left value object.</param>
        /// <param name="right">The right value object.</param>
        /// <returns>True if the value objects are not equal, false otherwise.</returns>
        public static bool operator !=(ValueObject? left, ValueObject? right)
        {
            return !(left == right);
        }
    }
}