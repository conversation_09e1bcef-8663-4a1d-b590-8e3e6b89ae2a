﻿using System;
using System.Collections.Generic;
using ProcureToPay.Domain.Events;       // Assuming SalesTerritory domain events namespace exists
using ProcureToPay.Domain.Exceptions;   // Assuming DomainStateException exists

// Assuming BaseEntity<Guid> and SalesOrder entities exist
namespace ProcureToPay.Domain.Entities
{
    /// <summary>
    /// Represents a geographical or logical sales territory used for organizing sales efforts and reporting.
    /// </summary>
    public class SalesTerritory : BaseEntity<Guid>
    {
        /// <summary>
        /// Foreign Key for multi-tenancy.
        /// </summary>
        public Guid TenantId { get; private set; }

        /// <summary>
        /// The human-readable name of the sales territory (e.g., "Northwest Region", "California - Bay Area").
        /// </summary>
        public string Name { get; private set; } = null!;

        /// <summary>
        /// An optional unique code or identifier for the territory (e.g., "NW", "CA-BAY").
        /// Useful for integrations or shorter references.
        /// </summary>
        public string? Code { get; private set; }

        /// <summary>
        /// The territory code used for reporting and integration.
        /// </summary>
        public string TerritoryCode { get; private set; } = string.Empty;

        /// <summary>
        /// Optional Foreign Key to the parent territory in a hierarchical structure.
        /// </summary>
        public Guid? ParentTerritoryId { get; private set; }

        /// <summary>
        /// An optional description providing more details about the territory's scope or definition.
        /// </summary>
        public string? Description { get; private set; }

        /// <summary>
        /// Optional Foreign Key (string representation) of the primary salesperson or manager
        /// responsible for this territory. Links to ApplicationUser Id.
        /// </summary>
        public string? PrimarySalespersonId { get; private set; }

        /// <summary>
        /// Flag indicating if the territory has been soft-deleted.
        /// </summary>
        public bool IsDeleted { get; private set; }

        // --- Navigation Properties ---

        /// <summary>
        /// Navigation property to the parent territory.
        /// </summary>
        public virtual SalesTerritory? ParentTerritory { get; private set; }

        /// <summary>
        /// Navigation property to the child territories.
        /// </summary>
        public virtual ICollection<SalesTerritory> ChildTerritories { get; private set; } = new HashSet<SalesTerritory>();

        /// <summary>
        /// Navigation property to the sales representatives assigned to this territory.
        /// </summary>
        // SalesRepresentatives collection removed to avoid dependency on Infrastructure layer

        // One-to-Many relationship: A territory can have multiple sales orders.
        private readonly List<SalesOrder> _salesOrders = new();
        /// <summary>
        /// Sales Orders associated with this territory.
        /// </summary>
        public virtual IReadOnlyCollection<SalesOrder> SalesOrders => _salesOrders.AsReadOnly();

        // Potential future relationships (examples):
        // public virtual ICollection<SalespersonTerritoryAssignment> SalespersonAssignments { get; private set; } = new List<SalespersonTerritoryAssignment>();
        // public virtual GeoBoundaryDefinition? Boundary { get; private set; } // Could be a Value Object or separate entity


        /// <summary>
        /// Private constructor for EF Core.
        /// </summary>
        private SalesTerritory() : base(Guid.NewGuid()) { }

        /// <summary>
        /// Creates a new Sales Territory.
        /// </summary>
        /// <param name="id">The unique identifier for the territory.</param>
        /// <param name="tenantId">The tenant identifier.</param>
        /// <param name="name">The name of the territory.</param>
        /// <param name="territoryCode">The territory code for reporting and integration.</param>
        /// <param name="code">Optional unique code for the territory.</param>
        /// <param name="parentTerritoryId">Optional ID of the parent territory.</param>
        /// <param name="description">Optional description.</param>
        /// <param name="primarySalespersonId">Optional ID of the primary salesperson.</param>
        public SalesTerritory(
            Guid id,
            Guid tenantId,
            string name,
            string territoryCode,
            string? code = null,
            Guid? parentTerritoryId = null,
            string? description = null,
            string? primarySalespersonId = null
            ) : base(id)
        {
            // Validation
            if (tenantId == Guid.Empty) throw new ArgumentException("TenantId cannot be empty.", nameof(tenantId));
            ArgumentException.ThrowIfNullOrWhiteSpace(name);
            ArgumentException.ThrowIfNullOrWhiteSpace(territoryCode);
            // Optional: Add validation for Code uniqueness if required by business rules (checked in Application layer or via DB constraint)

            TenantId = tenantId;
            Name = name;
            TerritoryCode = territoryCode;
            Code = code;
            ParentTerritoryId = parentTerritoryId;
            Description = description;
            PrimarySalespersonId = primarySalespersonId;
            IsDeleted = false;

            AddDomainEvent(new SalesTerritoryCreatedEvent(this.Id, this.TenantId, this.Name));
        }

        // --- Domain Methods ---

        /// <summary>
        /// Updates the details of the Sales Territory.
        /// </summary>
        public void UpdateDetails(string name, string territoryCode, string? code, Guid? parentTerritoryId, string? description, string? primarySalespersonId)
        {
            // Validation
            ArgumentException.ThrowIfNullOrWhiteSpace(name);
            ArgumentException.ThrowIfNullOrWhiteSpace(territoryCode);
            // Optional: Add validation for Code uniqueness if required

            var oldName = Name; // Capture old value for event if needed

            Name = name;
            TerritoryCode = territoryCode;
            Code = code;
            ParentTerritoryId = parentTerritoryId;
            Description = description;
            PrimarySalespersonId = primarySalespersonId;

            // Consider raising a more detailed event if specific field changes are important
            AddDomainEvent(new SalesTerritoryUpdatedEvent(this.Id, oldName, this.Name));
        }

        /// <summary>
        /// Assigns or updates the primary salesperson for the territory.
        /// </summary>
        /// <param name="salespersonId">The ID of the salesperson (or null to remove).</param>
        public void AssignPrimarySalesperson(string? salespersonId)
        {
            if (PrimarySalespersonId == salespersonId) return; // No change

            PrimarySalespersonId = salespersonId;
            // AddDomainEvent(new SalesTerritorySalespersonAssignedEvent(this.Id, salespersonId));
        }


        // --- Soft Delete ---

        /// <summary>
        /// Marks the sales territory as deleted.
        /// </summary>
        public void MarkAsDeleted()
        {
            if (IsDeleted) return;

            // Business Rule Check: Can we delete a territory if it has active Sales Orders?
            // This check might belong in an Application Service or Domain Service
            // if (_salesOrders.Any(so => !so.IsDeleted && so.Status != SalesOrderStatus.Completed && so.Status != SalesOrderStatus.Cancelled)) // Example check
            // {
            //     throw new DomainStateException("Cannot delete a territory with active sales orders.");
            // }

            IsDeleted = true;
            AddDomainEvent(new SalesTerritoryDeletedEvent(this.Id));
        }

        /// <summary>
        /// Restores a soft-deleted sales territory.
        /// </summary>
        public void Restore()
        {
            if (!IsDeleted) return;

            IsDeleted = false;
            AddDomainEvent(new SalesTerritoryRestoredEvent(this.Id));
        }
    }

    // --- Placeholder Domain Event Definitions (Place in Domain/Events/SalesTerritoryEvents.cs) ---
    /*
    namespace ProcureToPay.Domain.Events
    {
        public record SalesTerritoryCreatedEvent(Guid SalesTerritoryId, Guid TenantId, string Name);
        public record SalesTerritoryUpdatedEvent(Guid SalesTerritoryId, string OldName, string NewName); // Or more detailed
        public record SalesTerritoryDeletedEvent(Guid SalesTerritoryId);
        public record SalesTerritoryRestoredEvent(Guid SalesTerritoryId);
        // public record SalesTerritorySalespersonAssignedEvent(Guid SalesTerritoryId, string? SalespersonId);
    }
    */

    // --- Placeholder Related Entities (Define elsewhere if needed) ---
    // public class SalesOrder : BaseEntity<Guid> { public Guid? SalesTerritoryId { get; set; } /* ... */ }


}
