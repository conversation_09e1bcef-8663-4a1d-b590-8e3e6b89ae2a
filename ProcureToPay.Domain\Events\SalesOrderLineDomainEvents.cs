﻿using ProcureToPay.Domain.ValueObjects;
using ProcureToPay.Domain.Enums; // For UnitOfMeasure
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ProcureToPay.Domain.Events
{
    // Note: Line item events are often raised by the Aggregate Root (SalesOrder)
    public record SalesOrderLineCreatedEvent(Guid SalesOrderId, Guid LineId);
    public record SalesOrderLineQuantityUpdatedEvent(Guid LineId, decimal NewQuantity);
    public record SalesOrderLineDiscountAppliedEvent(Guid LineId, string DiscountDescription, Money DiscountAmount);
    public record SalesOrderLineBackorderedEvent(Guid LineId, decimal BackorderedQuantity);
    public record SalesOrderLineBackorderFulfilledEvent(Guid LineId, decimal FulfilledQuantity);
    public record SalesOrderLineSerialsReservedEvent(Guid LineId, List<string> SerialNumbers);
    public record SalesOrderLineWarrantySetEvent(Guid LineId, DateTime? WarrantyEndDate);
    public record SalesOrderLineProjectAssignedEvent(Guid LineId, Guid? ProjectId, string? CostCode);
    public record SalesOrderLineDeletedEvent(Guid SalesOrderId, Guid LineId);
    public record SalesOrderLineRestoredEvent(Guid SalesOrderId, Guid LineId);
}