﻿using ProcureToPay.Domain.Enums;
using ProcureToPay.Domain.ValueObjects;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ProcureToPay.Domain.Events
{
    public record ProjectCreatedEvent(Guid ProjectId, Guid TenantId, string ProjectCode, string Name);
    public record ProjectDetailsUpdatedEvent(Guid ProjectId, string Name, string? Description);
    public record ProjectDatesUpdatedEvent(Guid ProjectId, DateTime? StartDate, DateTime? EndDate);
    public record ProjectStatusChangedEvent(Guid ProjectId, ProjectStatus NewStatus);
    public record ProjectBudgetUpdatedEvent(Guid ProjectId, Money? NewBudgetAmount);
    public record ProjectDeletedEvent(Guid ProjectId);
    public record ProjectRestoredEvent(Guid ProjectId);
}