using ProcureToPay.Domain.Enums;
using ProcureToPay.Domain.ValueObjects; // For Address, Money
using ProcureToPay.Domain.Exceptions; // Assuming DomainStateException exists
using ProcureToPay.Domain.Events;     // For Domain Events
using System;
using System.Collections.Generic;
using System.Linq;

namespace ProcureToPay.Domain.Entities
{
    /// <summary>
    /// Represents a Purchase Order (PO) within the Procure-to-Pay system.
    /// Acts as the Aggregate Root for PurchaseOrderLines.
    /// Uses Value Objects and raises Domain Events.
    /// </summary>
    public class PurchaseOrder : BaseEntity<Guid> // Assuming BaseEntity provides Id and Domain Event handling
    {
        private readonly List<PurchaseOrderLine> _lines = new();

        /// <summary>
        /// Unique identifier number for the Purchase Order.
        /// </summary>
        public string OrderNumber { get; private set; } = null!;

        /// <summary>
        /// Date the Purchase Order was created or issued.
        /// </summary>
        public DateTime OrderDate { get; private set; }

        /// <summary>
        /// Requested or expected delivery date for the items/services.
        /// </summary>
        public DateTime? DeliveryDate { get; private set; }

        /// <summary>
        /// Current status of the Purchase Order.
        /// </summary>
        public PurchaseOrderStatus Status { get; private set; }

        /// <summary>
        /// Payment terms agreed upon with the vendor for this specific order.
        /// </summary>
        public string? PaymentTerms { get; private set; }

        /// <summary>
        /// 3-letter ISO currency code for the amounts in this order.
        /// </summary>
        public string CurrencyCode { get; private set; } = null!;

        /// <summary>
        /// Total calculated amount for the Purchase Order based on its lines.
        /// Stored as Money VO for type safety.
        /// </summary>
        public Money TotalAmount { get; private set; } = null!; // Corrected: Use Money VO

        // Optional: Store exchange rate if conversion is done before saving
        // public decimal? ExchangeRate { get; private set; }

        // --- Foreign Keys ---
        public Guid VendorId { get; private set; }
        public Guid? ContractId { get; private set; }
        public Guid? RequisitionId { get; private set; }

        // --- Navigation Properties ---
        public virtual Vendor Vendor { get; private set; } = null!;
        public virtual Contract? Contract { get; private set; }
        public virtual PurchaseRequisition? Requisition { get; private set; }
        public virtual IReadOnlyCollection<PurchaseOrderLine> Lines => _lines.AsReadOnly();
        public virtual ICollection<DeliveryNote> DeliveryNotes { get; private set; } = new HashSet<DeliveryNote>();
        public virtual ICollection<GoodsReceiptNote> GoodsReceiptNotes { get; private set; } = new HashSet<GoodsReceiptNote>();
        public virtual ICollection<Invoice> Invoices { get; private set; } = new HashSet<Invoice>();

        // --- Value Objects ---
        public virtual Address ShipmentAddress { get; private set; } = null!;

        // --- Concurrency Control ---
        // Using xmin from PostgreSQL via configuration - no property needed here
        // Removed: public byte[] RowVersion { get; private set; } = null!;

        // --- Constructors ---
        private PurchaseOrder() : base(Guid.NewGuid())
        {
            // Required for EF Core materialization
            // Initialize Money VO to prevent null reference issues before lines are added
            TotalAmount = new Money(0, "XXX"); // Use a default or placeholder currency initially
        }

        /// <summary>
        /// Creates a new instance of a Purchase Order.
        /// </summary>
        /// <param name="id">The unique identifier for the PO.</param>
        /// <param name="orderNumber">The PO number.</param>
        /// <param name="orderDate">The date of the order.</param>
        /// <param name="vendorId">The ID of the associated vendor.</param>
        /// <param name="shipmentAddress">The shipment address.</param>
        /// <param name="currencyCode">The 3-letter ISO currency code for the order.</param>
        /// <param name="paymentTerms">Optional payment terms.</param>
        /// <param name="deliveryDate">Optional delivery date.</param>
        /// <param name="contractId">Optional associated contract ID.</param>
        /// <param name="requisitionId">Optional originating requisition ID.</param>
        public PurchaseOrder(
            Guid id,
            string orderNumber,
            DateTime orderDate,
            Guid vendorId,
            Address shipmentAddress,
            string currencyCode, // Expect currency code for the order
            string? paymentTerms = null,
            DateTime? deliveryDate = null,
            Guid? contractId = null,
            Guid? requisitionId = null) : base(id)
        {
            if (string.IsNullOrWhiteSpace(orderNumber))
                throw new ArgumentNullException(nameof(orderNumber), "Order number cannot be empty.");
            if (vendorId == Guid.Empty)
                throw new ArgumentException("Vendor ID cannot be empty.", nameof(vendorId));
            ArgumentNullException.ThrowIfNull(shipmentAddress);
            if (string.IsNullOrWhiteSpace(currencyCode) || currencyCode.Length != 3)
                throw new ArgumentException("A valid 3-letter ISO currency code is required.", nameof(currencyCode));
            if (orderDate == default)
                throw new ArgumentException("Order date must be specified.", nameof(orderDate));

            OrderNumber = orderNumber;
            OrderDate = orderDate;
            VendorId = vendorId;
            ShipmentAddress = shipmentAddress;
            CurrencyCode = currencyCode.ToUpperInvariant(); // Ensure consistency
            PaymentTerms = paymentTerms;
            DeliveryDate = deliveryDate;
            ContractId = contractId;
            RequisitionId = requisitionId;

            Status = PurchaseOrderStatus.Draft; // Use correct initial status from updated enum
            TotalAmount = new Money(0m, this.CurrencyCode); // Initialize with correct currency

            AddDomainEvent(new PurchaseOrderCreatedEvent(this.Id)); // Raise event
        }

        // --- Domain Logic Methods ---

        /// <summary>
        /// Adds a line item to the Purchase Order using a specific VendorProduct offering.
        /// Validates status and currency consistency.
        /// </summary>
        /// <param name="vendorProduct">The specific vendor product offering being ordered (ensure ProductDefinition is loaded if needed by POLine constructor).</param>
        /// <param name="quantity">The quantity ordered.</param>
        /// <exception cref="DomainStateException">Thrown if the PO is not in Draft status.</exception>
        /// <exception cref="ArgumentException">Thrown for invalid input parameters or currency mismatch.</exception>
        /// <exception cref="ArgumentNullException">Thrown if vendorProduct is null.</exception>
        /// <exception cref="InvalidOperationException">Thrown if vendorProduct is missing required data (like Price or loaded ProductDefinition).</exception>
        public void AddLineItem(VendorProduct vendorProduct, decimal quantity)
        {
            // Check PO Status
            if (Status != PurchaseOrderStatus.Draft)
            {
                throw new DomainStateException($"Cannot add lines to a Purchase Order with status '{Status}'. Must be '{PurchaseOrderStatus.Draft}'.");
            }

            // Validate Input
            ArgumentNullException.ThrowIfNull(vendorProduct);
            if (vendorProduct.UnitPrice == null) // Check Money VO is not null
                throw new InvalidOperationException("VendorProduct must have a UnitPrice defined.");

            // Validate Currency
            if (vendorProduct.UnitPrice.CurrencyCode != this.CurrencyCode)
            {
                throw new ArgumentException($"Cannot add line item with currency '{vendorProduct.UnitPrice.CurrencyCode}' to a Purchase Order with currency '{this.CurrencyCode}'.");
            }

            // Optional: Check VendorProduct status (might also be done in Application Service)
            // if (!vendorProduct.IsActive)
            // {
            //     throw new DomainStateException($"Cannot add inactive VendorProduct '{vendorProduct.Id}' to Purchase Order.");
            // }

            // Create the line (constructor now takes VendorProduct)
            var newLine = new PurchaseOrderLine(
                purchaseOrderId: this.Id,
                vendorProduct: vendorProduct,
                quantity: quantity
            );

            _lines.Add(newLine);
            RecalculateTotalAmount();
            AddDomainEvent(new PurchaseOrderLineAddedEvent(this.Id, newLine.Id)); // Raise event
        }

        /// <summary>
        /// Removes a line item from the Purchase Order.
        /// </summary>
        /// <param name="lineId">The ID of the line item to remove.</param>
        /// <exception cref="DomainStateException">Thrown if the PO is not in Draft status.</exception>
        /// <exception cref="ArgumentException">Thrown if the line item is not found.</exception>
        public void RemoveLineItem(Guid lineId)
        {
            if (Status != PurchaseOrderStatus.Draft)
            {
                throw new DomainStateException($"Cannot remove lines from a Purchase Order with status '{Status}'. Must be '{PurchaseOrderStatus.Draft}'.");
            }

            var lineToRemove = _lines.FirstOrDefault(l => l.Id == lineId);
            if (lineToRemove == null)
            {
                throw new ArgumentException($"Line item with ID '{lineId}' not found on this Purchase Order.", nameof(lineId));
            }

            _lines.Remove(lineToRemove);
            RecalculateTotalAmount();
            AddDomainEvent(new PurchaseOrderLineRemovedEvent(this.Id, lineId)); // Raise event
        }

        /// <summary>
        /// Updates the shipment address.
        /// </summary>
        /// <param name="newAddress">The new shipment address.</param>
        /// <exception cref="DomainStateException">Thrown if the PO status prevents address changes.</exception>
        /// <exception cref="ArgumentNullException">Thrown if newAddress is null.</exception>
        public void UpdateShipmentAddress(Address newAddress)
        {
            if (Status == PurchaseOrderStatus.Completed || Status == PurchaseOrderStatus.Cancelled)
            {
                throw new DomainStateException($"Cannot update shipment address for a Purchase Order with status '{Status}'.");
            }
            ArgumentNullException.ThrowIfNull(newAddress);
            if (ShipmentAddress == newAddress) return; // Assuming Address VO has equality

            ShipmentAddress = newAddress;
            // AddDomainEvent(new PurchaseOrderShipmentAddressUpdatedEvent(this.Id)); // Example event
        }

        /// <summary>
        /// Updates the expected delivery date.
        /// </summary>
        /// <param name="newDeliveryDate">The new delivery date.</param>
        /// <exception cref="DomainStateException">Thrown if the PO status prevents date changes.</exception>
        public void UpdateDeliveryDate(DateTime? newDeliveryDate)
        {
            if (Status == PurchaseOrderStatus.Completed || Status == PurchaseOrderStatus.Cancelled)
            {
                throw new DomainStateException($"Cannot update delivery date for a Purchase Order with status '{Status}'.");
            }
            if (DeliveryDate == newDeliveryDate) return;

            DeliveryDate = newDeliveryDate;
            // AddDomainEvent(new PurchaseOrderDeliveryDateUpdatedEvent(this.Id)); // Example event
        }


        // --- Status Transition Methods ---

        /// <summary>
        /// Submits the Purchase Order for approval.
        /// </summary>
        /// <exception cref="DomainStateException">Thrown if the PO is not in Draft status or has no lines.</exception>
        public void SubmitForApproval()
        {
            if (Status != PurchaseOrderStatus.Draft) // Use correct enum member
            {
                throw new DomainStateException($"Purchase Order must be in '{PurchaseOrderStatus.Draft}' status to be submitted. Current status: '{Status}'.");
            }
            if (!_lines.Any())
            {
                throw new DomainStateException("Purchase Order cannot be submitted without any line items.");
            }

            SetStatus(PurchaseOrderStatus.PendingApproval); // Use correct enum member
            AddDomainEvent(new PurchaseOrderSubmittedEvent(this.Id)); // Raise event
        }

        /// <summary>
        /// Approves the Purchase Order.
        /// </summary>
        /// <exception cref="DomainStateException">Thrown if the PO is not in PendingApproval status.</exception>
        public void Approve()
        {
            if (Status != PurchaseOrderStatus.PendingApproval) // Use correct enum member
            {
                throw new DomainStateException($"Purchase Order must be in '{PurchaseOrderStatus.PendingApproval}' status to be approved. Current status: '{Status}'.");
            }
            SetStatus(PurchaseOrderStatus.Approved); // Use correct enum member
            AddDomainEvent(new PurchaseOrderApprovedEvent(this.Id)); // Raise event
        }

        /// <summary>
        /// Rejects the Purchase Order.
        /// </summary>
        /// <param name="reason">Optional reason for rejection.</param>
        /// <exception cref="DomainStateException">Thrown if the PO is not in PendingApproval status.</exception>
        public void Reject(string? reason = null)
        {
            if (Status != PurchaseOrderStatus.PendingApproval) // Use correct enum member
            {
                throw new DomainStateException($"Purchase Order must be in '{PurchaseOrderStatus.PendingApproval}' status to be rejected. Current status: '{Status}'.");
            }
            SetStatus(PurchaseOrderStatus.Rejected); // Use correct enum member
            AddDomainEvent(new PurchaseOrderRejectedEvent(this.Id, reason)); // Raise event
        }

        /// <summary>
        /// Marks the Purchase Order as completed.
        /// </summary>
        /// <exception cref="DomainStateException">Thrown if the PO is not in an appropriate status (e.g., Approved).</exception>
        public void Complete()
        {
            // Corrected: Check against Approved or PartiallyReceived (if enum exists)
            if (Status != PurchaseOrderStatus.Approved && Status != PurchaseOrderStatus.PartiallyReceived)
            {
                throw new DomainStateException($"Purchase Order cannot be marked as completed from status '{Status}'. Must be '{PurchaseOrderStatus.Approved}' or '{PurchaseOrderStatus.PartiallyReceived}'.");
            }

            SetStatus(PurchaseOrderStatus.Completed); // Use correct enum member
            AddDomainEvent(new PurchaseOrderCompletedEvent(this.Id)); // Raise event
        }

        /// <summary>
        /// Cancels the Purchase Order.
        /// </summary>
        /// <param name="reason">Optional reason for cancellation.</param>
        /// <exception cref="DomainStateException">Thrown if the PO is already completed or cancelled.</exception>
        public void Cancel(string? reason = null)
        {
            if (Status == PurchaseOrderStatus.Completed || Status == PurchaseOrderStatus.Cancelled) // Use correct enum members
            {
                throw new DomainStateException($"Cannot cancel a Purchase Order with status '{Status}'.");
            }
            SetStatus(PurchaseOrderStatus.Cancelled); // Use correct enum member
            AddDomainEvent(new PurchaseOrderCancelledEvent(this.Id, reason)); // Raise event
        }


        // --- Private Helper Methods ---

        /// <summary>
        /// Recalculates the total amount based on current line items using Money VO.
        /// </summary>
        private void RecalculateTotalAmount()
        {
            // Ensure all lines have the same currency as the order before summing
            if (_lines.Any(l => l.LineTotal.CurrencyCode != this.CurrencyCode))
            {
                // This should ideally not happen if AddLineItem validates currency
                throw new DomainStateException("Cannot calculate total amount due to currency mismatch in order lines.");
            }

            // Sum the amounts from the Money VO in each line
            decimal total = _lines.Sum(line => line.LineTotal.Amount);
            // Create a new Money VO for the total amount
            TotalAmount = new Money(total, this.CurrencyCode);
            // Optional: Raise event if total changes significantly?
            // AddDomainEvent(new PurchaseOrderTotalAmountChangedEvent(this.Id, TotalAmount));
        }

        /// <summary>
        /// Sets the new status and raises a domain event if changed.
        /// </summary>
        /// <param name="newStatus">The new status to set.</param>
        private void SetStatus(PurchaseOrderStatus newStatus)
        {
            if (Status == newStatus) return; // No change

            var oldStatus = Status;
            Status = newStatus;

            AddDomainEvent(new PurchaseOrderStatusChangedEvent(this.Id, oldStatus, newStatus)); // Raise event
        }
    }
}