﻿using ProcureToPay.Domain.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ProcureToPay.Domain.Events
{
    public record ReturnAuthorizationCreatedEvent(Guid RmaId, Guid TenantId, Guid CustomerId, Guid OriginalSalesOrderId);
    public record ReturnAuthorizationStatusChangedEvent(Guid RmaId, ReturnAuthorizationStatus OldStatus, ReturnAuthorizationStatus NewStatus);
    public record ReturnAuthorizationApprovedEvent(Guid RmaId);
    public record ReturnAuthorizationRejectedEvent(Guid RmaId, string Reason);
    public record ReturnAuthorizationCompletedEvent(Guid RmaId);
    public record ReturnAuthorizationCancelledEvent(Guid RmaId, string Reason);
    public record ReturnAuthorizationDeletedEvent(Guid RmaId);
    public record ReturnAuthorizationRestoredEvent(Guid RmaId);
    // public record ReturnAuthorizationLineAddedEvent(Guid RmaId, Guid LineId);
    // public record ReturnAuthorizationLineRemovedEvent(Guid RmaId, Guid LineId);
}