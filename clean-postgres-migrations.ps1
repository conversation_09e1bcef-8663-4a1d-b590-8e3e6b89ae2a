# Script to create clean PostgreSQL-compatible migrations
# This script:
# 1. Archives existing migrations
# 2. Creates a new initial migration
# 3. Verifies the migration for PostgreSQL syntax
# 4. Generates a SQL script for applying the migration

param(
    [Parameter(Mandatory=$false)]
    [string]$NewMigrationName = "CleanPostgreSQLSchema",
    
    [Parameter(Mandatory=$false)]
    [string]$InfrastructureProject = "ProcureToPay.Infrastructure",
    
    [Parameter(Mandatory=$false)]
    [string]$WebAppProject = "ProcureToPay.WebApp\ProcureToPay.WebApp",
    
    [Parameter(Mandatory=$false)]
    [string]$MigrationsOutputDir = "Persistence\Migrations",
    
    [Parameter(Mandatory=$false)]
    [switch]$SkipVerification = $false
)

# Define colors for output
$colorInfo = "Cyan"
$colorSuccess = "Green"
$colorWarning = "Yellow"
$colorError = "Red"

Write-Host "PostgreSQL-Compatible Migration Generator" -ForegroundColor $colorInfo
Write-Host "=======================================" -ForegroundColor $colorInfo

$infrastructureProjectPath = Join-Path $PSScriptRoot $InfrastructureProject
$webAppProjectPath = Join-Path $PSScriptRoot $WebAppProject
$migrationsFolder = Join-Path $infrastructureProjectPath $MigrationsOutputDir
$migrationsArchiveFolder = Join-Path $infrastructureProjectPath "Migrations_Archive"
$sqlScriptPath = Join-Path $PSScriptRoot "clean_migration_script.sql"

# Step 1: Archive existing migration files
Write-Host "Step 1: Archiving existing migrations..." -ForegroundColor $colorInfo

# Create migrations folder if it doesn't exist
if (-not (Test-Path $migrationsFolder)) {
    Write-Host "  Creating migrations folder: $migrationsFolder" -ForegroundColor $colorInfo
    New-Item -Path $migrationsFolder -ItemType Directory -Force | Out-Null
}

# Check if there are any existing migration files
$existingMigrations = Get-ChildItem -Path $migrationsFolder -Filter "*.cs" -Recurse | Measure-Object
if ($existingMigrations.Count -gt 0) {
    Write-Host "  Found $($existingMigrations.Count) existing migration files" -ForegroundColor $colorInfo
    
    # Create archive folder if it doesn't exist
    if (-not (Test-Path $migrationsArchiveFolder)) {
        Write-Host "  Creating migrations archive folder: $migrationsArchiveFolder" -ForegroundColor $colorInfo
        New-Item -Path $migrationsArchiveFolder -ItemType Directory -Force | Out-Null
    }
    
    # Move existing migration files to archive
    Write-Host "  Moving existing migration files to archive..." -ForegroundColor $colorInfo
    Get-ChildItem -Path $migrationsFolder -Filter "*.cs" -Recurse | ForEach-Object {
        $destinationPath = Join-Path $migrationsArchiveFolder $_.Name
        if (Test-Path $destinationPath) {
            # If file already exists in archive, append timestamp to avoid conflicts
            $timestamp = Get-Date -Format "yyyyMMddHHmmss"
            $destinationPath = Join-Path $migrationsArchiveFolder "$($_.BaseName)_$timestamp$($_.Extension)"
        }
        Move-Item -Path $_.FullName -Destination $destinationPath -Force
        Write-Host "    Moved $($_.Name) to archive" -ForegroundColor $colorInfo
    }
} else {
    Write-Host "  No existing migration files found" -ForegroundColor $colorInfo
}

# Step 2: Create a new initial migration
Write-Host "Step 2: Creating new initial migration..." -ForegroundColor $colorInfo

try {
    $migrationCmd = "dotnet ef migrations add $NewMigrationName --project $infrastructureProjectPath --startup-project $webAppProjectPath --context ApplicationDbContext --output-dir $MigrationsOutputDir"
    Write-Host "  Running: $migrationCmd" -ForegroundColor $colorInfo
    
    $migrationOutput = Invoke-Expression $migrationCmd
    
    # Display the output
    $migrationOutput | ForEach-Object { Write-Host "    $_" }
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "  New migration '$NewMigrationName' created successfully!" -ForegroundColor $colorSuccess
    } else {
        Write-Error "  Failed to create new migration. See output above for details."
        exit 1
    }
} catch {
    Write-Error "  An error occurred while creating the migration: $_"
    exit 1
}

# Step 3: Verify the migration for PostgreSQL syntax
if (-not $SkipVerification) {
    Write-Host "Step 3: Verifying migration for PostgreSQL syntax..." -ForegroundColor $colorInfo
    
    # Find the migration files
    $migrationFiles = Get-ChildItem -Path $migrationsFolder -Filter "*$NewMigrationName*.cs" -Recurse
    
    if ($migrationFiles.Count -eq 0) {
        Write-Warning "  No migration files found matching '$NewMigrationName'"
    } else {
        $hasErrors = $false
        
        foreach ($file in $migrationFiles) {
            Write-Host "  Checking file: $($file.Name)" -ForegroundColor $colorInfo
            $content = Get-Content -Path $file.FullName -Raw
            
            # Check for SQL Server syntax in HasFilter
            if ($content -match "HasFilter\(\s*\[\w+\]\s+IS\s+NOT\s+NULL\s*\)") {
                Write-Host "    ERROR: Found SQL Server syntax in HasFilter (square brackets)" -ForegroundColor $colorError
                Write-Host "    Please use PostgreSQL syntax with double quotes: HasFilter(\"\`"ColumnName\`\" IS NOT NULL\")" -ForegroundColor $colorError
                $hasErrors = $true
            }
            
            # Check for other SQL Server syntax
            if ($content -match "\[dbo\]\.") {
                Write-Host "    ERROR: Found SQL Server schema reference [dbo]" -ForegroundColor $colorError
                Write-Host "    Please use PostgreSQL schema reference: \"public\"." -ForegroundColor $colorError
                $hasErrors = $true
            }
        }
        
        if ($hasErrors) {
            Write-Warning "  Migration files contain SQL Server syntax. Please fix the issues before proceeding."
        } else {
            Write-Host "  Migration files look good for PostgreSQL!" -ForegroundColor $colorSuccess
        }
    }
}

# Step 4: Generate SQL script
Write-Host "Step 4: Generating SQL script..." -ForegroundColor $colorInfo

try {
    $scriptCmd = "dotnet ef migrations script --project $infrastructureProjectPath --startup-project $webAppProjectPath --output $sqlScriptPath --idempotent"
    Write-Host "  Running: $scriptCmd" -ForegroundColor $colorInfo
    
    $scriptOutput = Invoke-Expression $scriptCmd
    
    # Display the output
    $scriptOutput | ForEach-Object { Write-Host "    $_" }
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "  SQL script generated successfully at: $sqlScriptPath" -ForegroundColor $colorSuccess
        
        # Check the SQL script for PostgreSQL compatibility
        $sqlContent = Get-Content -Path $sqlScriptPath -Raw
        
        if ($sqlContent -match "\[dbo\]\.") {
            Write-Warning "  The generated SQL script contains SQL Server schema references [dbo]."
            Write-Warning "  This may cause issues when applying to PostgreSQL."
        }
        
        if ($sqlContent -match "\[\w+\]\s+IS\s+NOT\s+NULL") {
            Write-Warning "  The generated SQL script contains SQL Server syntax for filtered indexes."
            Write-Warning "  This may cause issues when applying to PostgreSQL."
        }
    } else {
        Write-Error "  Failed to generate SQL script. See output above for details."
        exit 1
    }
} catch {
    Write-Error "  An error occurred while generating the SQL script: $_"
    exit 1
}

# Step 5: Provide next steps
Write-Host "Migration process completed successfully!" -ForegroundColor $colorSuccess
Write-Host "Next steps:" -ForegroundColor $colorInfo
Write-Host "1. Review the migration files in $migrationsFolder" -ForegroundColor $colorInfo
Write-Host "2. Review the SQL script at $sqlScriptPath" -ForegroundColor $colorInfo
Write-Host "3. Apply the migration using one of the following methods:" -ForegroundColor $colorInfo
Write-Host "   a. Run .\execute-migration-sql.ps1 to apply the SQL script directly" -ForegroundColor $colorInfo
Write-Host "   b. Run .\apply-aspire-migrations.ps1 to apply the migration programmatically" -ForegroundColor $colorInfo
