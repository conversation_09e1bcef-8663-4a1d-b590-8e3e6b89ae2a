using System;
using System.ComponentModel.DataAnnotations;

namespace ProcureToPay.Application.Features.Categories.DTOs
{
    /// <summary>
    /// Data Transfer Object for updating an existing category.
    /// </summary>
    public class UpdateCategoryRequest
    {
        /// <summary>
        /// The unique identifier of the category to update.
        /// </summary>
        [Required(ErrorMessage = "Id is required")]
        public Guid Id { get; set; }

        /// <summary>
        /// The name of the category.
        /// </summary>
        [Required(ErrorMessage = "Name is required")]
        [StringLength(200, ErrorMessage = "Name cannot be longer than 200 characters")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// The optional description of the category.
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// The optional code of the category.
        /// </summary>
        [StringLength(50, ErrorMessage = "Code cannot be longer than 50 characters")]
        public string? Code { get; set; }

        /// <summary>
        /// The optional UNSPSC code of the category.
        /// </summary>
        [StringLength(20, ErrorMessage = "UNSPSC code cannot be longer than 20 characters")]
        public string? UnspscCode { get; set; }

        /// <summary>
        /// The optional parent category ID.
        /// </summary>
        public Guid? ParentCategoryId { get; set; }
    }
}
