# Using DbContext in Singleton Contexts

This document explains how to properly use the `ApplicationDbContext` in singleton contexts, such as hosted services or singleton services.

## The Problem

In ASP.NET Core, the `ApplicationDbContext` is registered as a scoped service, which means it is created once per request. The `ApplicationDbContext` depends on `ITenantProvider`, which is also registered as a scoped service.

When a singleton service (like a hosted service) tries to use the `ApplicationDbContext` directly, it will encounter the following error:

```
InvalidOperationException: Cannot resolve scoped service 'ProcureToPay.Domain.Interfaces.ITenantProvider' from root provider.
```

This happens because a singleton service is created once when the application starts and lives for the entire application lifetime. It cannot directly use scoped services because scoped services are designed to be created and disposed within a specific scope (like a request).

## The Solution

To use the `ApplicationDbContext` in a singleton context, you need to create a new scope and resolve the `ApplicationDbContext` within that scope. The `DbContextHelper` class provides several methods to help with this:

### 1. Using the Factory Function

```csharp
// In your singleton service constructor
private readonly Func<ApplicationDbContext> _dbContextFactory;

public MySingletonService(Func<ApplicationDbContext> dbContextFactory)
{
    _dbContextFactory = dbContextFactory;
}

// In your singleton service method
public void DoSomething()
{
    using var dbContext = _dbContextFactory();
    // Use dbContext here
}
```

### 2. Using the DbContextHelper

```csharp
// In your singleton service constructor
private readonly IServiceProvider _serviceProvider;

public MySingletonService(IServiceProvider serviceProvider)
{
    _serviceProvider = serviceProvider;
}

// In your singleton service method
public void DoSomething()
{
    DbContextHelper.ExecuteWithDbContext(_serviceProvider, dbContext =>
    {
        // Use dbContext here
    });
}

// Or with a return value
public int GetCount()
{
    return DbContextHelper.ExecuteWithDbContext(_serviceProvider, dbContext =>
    {
        return dbContext.MyEntities.Count();
    });
}

// Or with async operations
public async Task<int> GetCountAsync()
{
    return await DbContextHelper.ExecuteWithDbContextAsync(_serviceProvider, async dbContext =>
    {
        return await dbContext.MyEntities.CountAsync();
    });
}
```

## Best Practices

1. Always use the `DbContextHelper` or the factory function to create a new `ApplicationDbContext` instance in singleton contexts.
2. Dispose the `ApplicationDbContext` when you're done with it.
3. Keep the scope of the `ApplicationDbContext` as narrow as possible.
4. Consider using the `ExecuteWithDbContext` methods to ensure proper disposal of the `ApplicationDbContext`.
5. If you need to perform multiple operations with the same `ApplicationDbContext`, use a single scope to avoid creating multiple scopes.

## Example: Hosted Service

```csharp
public class MyHostedService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;

    public MyHostedService(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            await DbContextHelper.ExecuteWithDbContextAsync(_serviceProvider, async dbContext =>
            {
                // Use dbContext here
                var entities = await dbContext.MyEntities.ToListAsync();
                // Process entities
            });

            await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken);
        }
    }
}
```
