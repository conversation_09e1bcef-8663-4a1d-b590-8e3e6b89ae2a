namespace ProcureToPay.Domain.Enums;

/// <summary>
/// Represents the status of a Purchase Requisition.
/// </summary>
public enum RequisitionStatus
{
    /// <summary>
    /// Requisition is being created or modified.
    /// </summary>
    Draft = 0,
    /// <summary>
    /// Requisition submitted for approval.
    /// </summary>
    PendingApproval = 1,
    /// <summary>
    /// Requisition approved, ready to be converted to PO.
    /// </summary>
    Approved = 2,
    /// <summary>
    /// Requisition rejected.
    /// </summary>
    Rejected = 3,
    /// <summary>
    /// Some lines have been ordered (PO created).
    /// </summary>
    PartiallyOrdered = 4,
    /// <summary>
    /// All lines have been ordered (PO created).
    /// </summary>
    FullyOrdered = 5,
    /// <summary>
    /// Requisition was cancelled before being fully ordered.
    /// </summary>
    Cancelled = 6
}
