param(
    [Parameter(Mandatory=$true)]
    [string]$ConnectionString
)

Write-Host "Checking database state..." -ForegroundColor Cyan

# Mask password in output for security
$maskedConnectionString = if ($ConnectionString -match "Password=([^;]+)") {
    $ConnectionString -replace "Password=$($matches[1])", "Password=***"
} else {
    $ConnectionString
}
Write-Host "Using Connection String: $maskedConnectionString" -ForegroundColor Cyan

# Extract connection parameters
$connectionParams = @{}
$ConnectionString -split ';' | ForEach-Object {
    $keyValue = $_ -split '='
    if ($keyValue.Length -eq 2) {
        $connectionParams[$keyValue[0].Trim()] = $keyValue[1].Trim()
    }
}

# Verify we have all required parameters
$requiredParams = @('Host', 'Port', 'Database', 'Username', 'Password')
$missingParams = $requiredParams | Where-Object { -not $connectionParams.ContainsKey($_) }
if ($missingParams) {
    Write-Error "Missing required connection parameters: $($missingParams -join ', ')"
    exit 1
}

# Set environment variables for psql
$env:PGPASSWORD = $connectionParams['Password']
$pgHost = $connectionParams['Host']
$pgPort = $connectionParams['Port']
$pgDatabase = $connectionParams['Database']
$pgUser = $connectionParams['Username']

Write-Host "Step 1: Checking if database exists..." -ForegroundColor Cyan

# SQL to check if the database exists
$checkDbSql = @"
SELECT 1;
"@

# Execute the SQL using psql directly
try {
    $result = $checkDbSql | psql -h $pgHost -p $pgPort -U $pgUser -d $pgDatabase -t
    Write-Host "  Database '$pgDatabase' exists and is accessible." -ForegroundColor Green
} catch {
    Write-Error "  Cannot connect to database '$pgDatabase': $_"
    exit 1
}

Write-Host "Step 2: Checking for __EFMigrationsHistory table..." -ForegroundColor Cyan

# SQL to check if the migrations table exists
$checkMigrationsTableSql = @"
SELECT EXISTS (
    SELECT FROM information_schema.tables
    WHERE table_schema = 'public'
    AND table_name = '__EFMigrationsHistory'
);
"@

# Execute the SQL using psql directly
try {
    $result = $checkMigrationsTableSql | psql -h $pgHost -p $pgPort -U $pgUser -d $pgDatabase -t
    $tableExists = $result.Trim() -eq 't'

    if ($tableExists) {
        Write-Host "  __EFMigrationsHistory table exists." -ForegroundColor Green

        # SQL to get migrations
        $getMigrationsSql = @"
SELECT "MigrationId", "ProductVersion" FROM "__EFMigrationsHistory" ORDER BY "MigrationId";
"@

        $migrations = $getMigrationsSql | psql -h $pgHost -p $pgPort -U $pgUser -d $pgDatabase -t

        Write-Host "  Applied migrations:" -ForegroundColor Green
        $migrations -split "`n" | Where-Object { $_.Trim() } | ForEach-Object {
            Write-Host "    - $($_.Trim())" -ForegroundColor Green
        }
    } else {
        Write-Warning "  __EFMigrationsHistory table does not exist. No migrations have been applied."
    }
} catch {
    Write-Error "  Error checking for __EFMigrationsHistory table: $_"
    exit 1
}

Write-Host "Step 3: Listing all tables in the database..." -ForegroundColor Cyan

# SQL to list all tables
$listTablesSql = @"
SELECT table_schema, table_name
FROM information_schema.tables
WHERE table_schema NOT IN ('pg_catalog', 'information_schema')
ORDER BY table_schema, table_name;
"@

# Execute the SQL using psql directly
try {
    $tables = $listTablesSql | psql -h $pgHost -p $pgPort -U $pgUser -d $pgDatabase -t

    Write-Host "  Tables in database:" -ForegroundColor Green
    $tables -split "`n" | Where-Object { $_.Trim() } | ForEach-Object {
        Write-Host "    - $($_.Trim())" -ForegroundColor Green
    }
} catch {
    Write-Error "  Error listing tables: $_"
    exit 1
}

Write-Host "Step 4: Checking for specific tables of interest..." -ForegroundColor Cyan

# List of tables to check
$tablesToCheck = @(
    "Categories",
    "products",
    "AspNetUsers",
    "AspNetRoles",
    "Tenants"
)

foreach ($table in $tablesToCheck) {
    # SQL to check if the table exists
    $checkTableSql = @"
SELECT EXISTS (
    SELECT FROM information_schema.tables
    WHERE table_schema = 'public'
    AND table_name = '$table'
);
"@

    # Execute the SQL using psql directly
    try {
        $result = $checkTableSql | psql -h $pgHost -p $pgPort -U $pgUser -d $pgDatabase -t
        $tableExists = $result.Trim() -eq 't'

        if ($tableExists) {
            Write-Host "  Table '$table' exists." -ForegroundColor Green

            # SQL to count rows
            $countRowsSql = @"
SELECT COUNT(*) FROM "$table";
"@

            $rowCount = $countRowsSql | psql -h $pgHost -p $pgPort -U $pgUser -d $pgDatabase -t
            Write-Host "    Row count: $($rowCount.Trim())" -ForegroundColor Green

            # SQL to get table structure
            $tableStructureSql = @"
\d "$table"
"@

            Write-Host "    Table structure:" -ForegroundColor Green
            $tableStructure = $tableStructureSql | psql -h $pgHost -p $pgPort -U $pgUser -d $pgDatabase
            $tableStructure | ForEach-Object { Write-Host "      $_" -ForegroundColor Gray }
        } else {
            Write-Warning "  Table '$table' does not exist."
        }
    } catch {
        Write-Warning "  Error checking table '$table': $_"
    }
}

Write-Host "Step 5: Checking for Product-Category relationship..." -ForegroundColor Cyan

# SQL to check for the foreign key constraint
$checkFkSql = @"
SELECT
    tc.constraint_name,
    tc.table_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
FROM
    information_schema.table_constraints AS tc
    JOIN information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
      AND tc.table_schema = kcu.table_schema
    JOIN information_schema.constraint_column_usage AS ccu
      ON ccu.constraint_name = tc.constraint_name
      AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = 'products'
    AND ccu.table_name = 'Categories';
"@

# Execute the SQL using psql directly
try {
    $fkResult = $checkFkSql | psql -h $pgHost -p $pgPort -U $pgUser -d $pgDatabase -t

    if ($fkResult.Trim()) {
        Write-Host "  Product-Category relationship exists:" -ForegroundColor Green
        $fkResult -split "`n" | Where-Object { $_.Trim() } | ForEach-Object {
            Write-Host "    $($_.Trim())" -ForegroundColor Green
        }
    } else {
        Write-Warning "  No Product-Category relationship found."
    }
} catch {
    Write-Warning "  Error checking Product-Category relationship: $_"
}

Write-Host "Database state check completed." -ForegroundColor Cyan
