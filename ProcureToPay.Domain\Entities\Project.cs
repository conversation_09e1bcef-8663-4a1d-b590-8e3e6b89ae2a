﻿using System;
using System.Collections.Generic;
using ProcureToPay.Domain.Enums;       // For ProjectStatus
using ProcureToPay.Domain.ValueObjects; // For Money (if using for budget)
using ProcureToPay.Domain.Events;       // Assuming Project domain events namespace exists
using ProcureToPay.Domain.Exceptions;   // Assuming DomainStateException exists

namespace ProcureToPay.Domain.Entities
{
    /// <summary>
    /// Represents a project for tracking costs, associating orders, etc.
    /// </summary>
    public class Project : BaseEntity<Guid>
    {
        /// <summary>
        /// Foreign Key to the Tenant.
        /// </summary>
        public Guid TenantId { get; private set; }

        /// <summary>
        /// A unique code or number identifying the project within the tenant.
        /// </summary>
        public string ProjectCode { get; private set; } = null!;

        /// <summary>
        /// The name of the project. Required.
        /// </summary>
        public string Name { get; private set; } = null!;

        /// <summary>
        /// Optional detailed description of the project.
        /// </summary>
        public string? Description { get; private set; }

        /// <summary>
        /// Current status of the project.
        /// </summary>
        public ProjectStatus Status { get; private set; }

        /// <summary>
        /// Planned or actual start date of the project.
        /// </summary>
        public DateTime? StartDate { get; private set; }

        /// <summary>
        /// Planned or actual end date of the project.
        /// </summary>
        public DateTime? EndDate { get; private set; }

        /// <summary>
        /// Optional budget allocated to the project.
        /// Consider linking to a full Budget entity if more complexity is needed.
        /// </summary>
        public Money? BudgetAmount { get; private set; }

        // --- Potential Future Enhancements ---
        // public Guid? ProjectManagerId { get; private set; }
        // public virtual User? ProjectManager { get; private set; }

        /// <summary>
        /// Flag indicating if the project has been soft-deleted.
        /// </summary>
        public bool IsDeleted { get; private set; }


        // --- Navigation Properties ---

        /// <summary>
        /// Collection of Sales Order Lines associated with this project.
        /// </summary>
        public virtual ICollection<SalesOrderLine> SalesOrderLines { get; private set; } = new List<SalesOrderLine>();

        /// <summary>
        /// Collection of Technical Submittals associated with this project.
        /// </summary>
        public virtual ICollection<TechnicalSubmittal> TechnicalSubmittals { get; private set; } = new List<TechnicalSubmittal>();

        /// <summary>
        /// Collection of Requests for Information associated with this project.
        /// </summary>
        public virtual ICollection<RequestForInformation> RequestsForInformation { get; private set; } = new List<RequestForInformation>();

        /// <summary>
        /// Collection of Requests for Proposal associated with this project.
        /// </summary>
        public virtual ICollection<RequestForProposal> RequestsForProposal { get; private set; } = new List<RequestForProposal>();

        // Add other related collections as needed (e.g., Purchase Orders, Invoices, Tasks)


        /// <summary>
        /// Private constructor for EF Core.
        /// </summary>
        private Project() : base(Guid.NewGuid())
        {
            // Required for EF Core initialization
            SalesOrderLines = new List<SalesOrderLine>();
        }

        /// <summary>
        /// Creates a new Project.
        /// </summary>
        public Project(
            Guid id,
            Guid tenantId,
            string projectCode,
            string name,
            string? description = null,
            DateTime? startDate = null,
            DateTime? endDate = null,
            Money? budgetAmount = null) : base(id)
        {
            // Basic Validation
            if (tenantId == Guid.Empty) throw new ArgumentException("TenantId cannot be empty.", nameof(tenantId));
            if (string.IsNullOrWhiteSpace(projectCode)) throw new ArgumentException("Project Code cannot be empty.", nameof(projectCode));
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentException("Project Name cannot be empty.", nameof(name));
            if (startDate.HasValue && endDate.HasValue && endDate.Value < startDate.Value)
                throw new ArgumentException("End Date cannot be earlier than Start Date.", nameof(endDate));

            TenantId = tenantId;
            ProjectCode = projectCode;
            Name = name;
            Description = description;
            Status = ProjectStatus.NotStarted; // Default status
            StartDate = startDate?.ToUniversalTime();
            EndDate = endDate?.ToUniversalTime();
            BudgetAmount = budgetAmount; // Assumes Money VO handles currency validation if needed
            IsDeleted = false;
            SalesOrderLines = new List<SalesOrderLine>();

            AddDomainEvent(new ProjectCreatedEvent(this.Id, this.TenantId, this.ProjectCode, this.Name));
        }

        // --- Domain Methods ---

        public void UpdateDetails(string name, string? description)
        {
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentException("Project Name cannot be empty.", nameof(name));

            bool changed = false;
            if (Name != name) { Name = name; changed = true; }
            if (Description != description) { Description = description; changed = true; }

            if (changed)
            {
                AddDomainEvent(new ProjectDetailsUpdatedEvent(this.Id, this.Name, this.Description));
            }
        }

        public void SetDates(DateTime? startDate, DateTime? endDate)
        {
            if (startDate.HasValue && endDate.HasValue && endDate.Value < startDate.Value)
                throw new ArgumentException("End Date cannot be earlier than Start Date.", nameof(endDate));

            bool changed = false;
            var utcStartDate = startDate?.ToUniversalTime();
            var utcEndDate = endDate?.ToUniversalTime();

            if (StartDate != utcStartDate) { StartDate = utcStartDate; changed = true; }
            if (EndDate != utcEndDate) { EndDate = utcEndDate; changed = true; }

            if (changed)
            {
                AddDomainEvent(new ProjectDatesUpdatedEvent(this.Id, this.StartDate, this.EndDate));
            }
        }

        public void UpdateStatus(ProjectStatus newStatus)
        {
            // Add potential validation logic here (e.g., cannot move from Completed back to InProgress without specific action)
            if (Status == newStatus) return;

            Status = newStatus;
            AddDomainEvent(new ProjectStatusChangedEvent(this.Id, this.Status));
        }

        public void UpdateBudget(Money? newBudgetAmount)
        {
            // Add validation if needed (e.g., budget cannot be negative)
            if (BudgetAmount == newBudgetAmount) return; // Use Value Object equality

            BudgetAmount = newBudgetAmount;
            AddDomainEvent(new ProjectBudgetUpdatedEvent(this.Id, this.BudgetAmount));
        }


        // --- Soft Delete ---
        public void MarkAsDeleted()
        {
            if (IsDeleted) return;
            IsDeleted = true;
            AddDomainEvent(new ProjectDeletedEvent(this.Id));
        }

        public void Restore()
        {
            if (!IsDeleted) return;
            IsDeleted = false;
            AddDomainEvent(new ProjectRestoredEvent(this.Id));
        }
    }

    // --- Placeholder Domain Event Definitions (Place in Domain/Events/ProjectEvents.cs or similar) ---
    /*
    namespace ProcureToPay.Domain.Events
    {
        public record ProjectCreatedEvent(Guid ProjectId, Guid TenantId, string ProjectCode, string Name);
        public record ProjectDetailsUpdatedEvent(Guid ProjectId, string Name, string? Description);
        public record ProjectDatesUpdatedEvent(Guid ProjectId, DateTime? StartDate, DateTime? EndDate);
        public record ProjectStatusChangedEvent(Guid ProjectId, ProjectStatus NewStatus);
        public record ProjectBudgetUpdatedEvent(Guid ProjectId, Money? NewBudgetAmount);
        public record ProjectDeletedEvent(Guid ProjectId);
        public record ProjectRestoredEvent(Guid ProjectId);
    }
    */
}
