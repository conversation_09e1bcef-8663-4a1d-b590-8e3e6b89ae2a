using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using ProcureToPay.Domain.Enums; // Required for InvoiceStatus
using ProcureToPay.Domain.Entities; // Required for BaseEntity and related entities

namespace ProcureToPay.Domain.Entities;

/// <summary>
/// Represents a Vendor Invoice requesting payment for goods or services.
/// Typically matched against Purchase Orders and Goods Receipt Notes.
/// Often an Aggregate Root.
/// </summary>
public class Invoice : BaseEntity<Guid>
{
    /// <summary>
    /// Tenant identifier for multi-tenancy.
    /// </summary>
    [Required]
    public Guid TenantId { get; private set; }

    /// <summary>
    /// The unique invoice number provided by the Vendor. Should be unique per Vendor.
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string InvoiceNumber { get; private set; } = string.Empty;

    /// <summary>
    /// Foreign Key to the Vendor submitting the invoice.
    /// </summary>
    [Required]
    public Guid VendorId { get; private set; }
    /// <summary>
    /// Navigation property to the Vendor. Virtual for lazy loading.
    /// </summary>
    public virtual Vendor Vendor { get; private set; } = null!;

    /// <summary>
    /// Optional Foreign Key to the Customer associated with this invoice.
    /// </summary>
    public Guid? CustomerId { get; private set; }
    /// <summary>
    /// Optional navigation property to the associated Customer. Virtual for lazy loading.
    /// </summary>
    public virtual Customer? Customer { get; private set; }

    /// <summary>
    /// Optional Foreign Key to the primary Purchase Order related to this invoice.
    /// </summary>
    public Guid? PurchaseOrderId { get; private set; }
    /// <summary>
    /// Optional navigation property to the related Purchase Order. Virtual for lazy loading.
    /// </summary>
    public virtual PurchaseOrder? PurchaseOrder { get; private set; }

    /// <summary>
    /// Date the invoice was issued by the vendor.
    /// </summary>
    [Required]
    public DateTime InvoiceDate { get; private set; }

    /// <summary>
    /// Date the payment for the invoice is due, based on payment terms.
    /// </summary>
    [Required]
    public DateTime DueDate { get; private set; }

    /// <summary>
    /// Current status of the invoice within the AP process.
    /// </summary>
    [Required]
    public InvoiceStatus Status { get; private set; }

    /// <summary>
    /// Currency code for the invoice amounts (e.g., SAR, AED, QAR).
    /// </summary>
    [Required]
    [MaxLength(3)]
    public string CurrencyCode { get; private set; } = string.Empty;

    /// <summary>
    /// Total amount of all line items before tax and other charges.
    /// TODO: Consider using Money Value Object.
    /// </summary>
    [Required]
    [Column(TypeName = "decimal(18, 2)")]
    public decimal SubTotal { get; private set; }

    /// <summary>
    /// Alias for SubTotal to match configuration expectations.
    /// </summary>
    [Required]
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Subtotal { get => SubTotal; private set => SubTotal = value; }

    /// <summary>
    /// Total tax amount (e.g., VAT) applied to the invoice.
    /// TODO: Implement region-specific tax calculation logic. Consider Money Value Object.
    /// </summary>
    [Required]
    [Column(TypeName = "decimal(18, 2)")]
    public decimal TaxAmount { get; private set; }

    /// <summary>
    /// Total amount of the invoice (SubTotal + TaxAmount + Other Charges).
    /// TODO: Consider using Money Value Object.
    /// </summary>
    [Required]
    [Column(TypeName = "decimal(18, 2)")]
    public decimal TotalAmount { get; private set; }

    /// <summary>
    /// The amount that has been paid towards this invoice so far.
    /// TODO: Consider using Money Value Object.
    /// </summary>
    [Required]
    [Column(TypeName = "decimal(18, 2)")]
    public decimal AmountPaid { get; private set; } = 0m;

    /// <summary>
    /// The date when the invoice was fully paid. Null if not fully paid.
    /// </summary>
    public DateTime? PaymentDate { get; private set; }

    /// <summary>
    /// Optional notes related to the invoice or its processing.
    /// </summary>
    public string? Notes { get; private set; }

    /// <summary>
    /// Payment terms for this invoice (e.g., "Net 30", "2/10 Net 30").
    /// </summary>
    public string? PaymentTerms { get; private set; }

    // TODO: Add reference to attachments or scanned invoice document? (e.g., string FilePath or separate Document entity)
    // public string? AttachmentReference { get; private set; }

    // Collection of line items detailing invoiced products/services. Encapsulated list.
    private readonly List<InvoiceLine> _lines = new();
    public virtual IReadOnlyCollection<InvoiceLine> Lines => _lines.AsReadOnly();

    /// <summary>
    /// Navigation property to track payments made against this invoice.
    /// </summary>
    public virtual ICollection<PaymentTransaction> PaymentTransactions { get; private set; } = new HashSet<PaymentTransaction>();

    /// <summary>
    /// Private constructor for EF Core hydration.
    /// </summary>
    private Invoice() : base(Guid.Empty) { }

    /// <summary>
    /// Creates a new Invoice, typically in Received status.
    /// </summary>
    public Invoice(
        Guid tenantId,
        string invoiceNumber,
        Guid vendorId,
        DateTime invoiceDate,
        DateTime dueDate,
        string currencyCode,
        Guid? purchaseOrderId = null, // Optional PO link
        Guid? customerId = null, // Optional Customer link
        string? paymentTerms = null,
        string? notes = null
        // Lines are added separately via AddLine method
        ) : base(Guid.NewGuid())
    {
        if (tenantId == Guid.Empty) throw new ArgumentException("TenantId cannot be empty.", nameof(tenantId));
        ArgumentException.ThrowIfNullOrWhiteSpace(invoiceNumber);
        if (vendorId == Guid.Empty) throw new ArgumentException("VendorId cannot be empty.", nameof(vendorId));
        if (dueDate < invoiceDate) throw new ArgumentException("Due date cannot be before invoice date.", nameof(dueDate));
        ArgumentException.ThrowIfNullOrWhiteSpace(currencyCode);
        if (currencyCode.Length != 3) throw new ArgumentException("Currency code must be 3 characters.", nameof(currencyCode));

        TenantId = tenantId;
        InvoiceNumber = invoiceNumber;
        VendorId = vendorId;
        PurchaseOrderId = purchaseOrderId;
        CustomerId = customerId;
        InvoiceDate = invoiceDate.Date;
        DueDate = dueDate.Date;
        CurrencyCode = currencyCode.ToUpperInvariant();
        PaymentTerms = paymentTerms;
        Notes = notes;
        Status = InvoiceStatus.Received; // Initial status
        AmountPaid = 0m;
        PaymentDate = null;
        RecalculateTotals(); // Initial totals are zero
    }

    // --- Methods for Managing Lines ---

    /// <summary>
    /// Adds a line item to the invoice. Only allowed when Received or Pending Approval?
    /// </summary>
    /// <param name="description">Description of the invoiced item/service.</param>
    /// <param name="quantity">Quantity invoiced.</param>
    /// <param name="unitPrice">Unit price.</param>
    /// <param name="purchaseOrderLineId">Optional corresponding PO line ID.</param>
    /// <param name="productId">Optional corresponding Product ID.</param>
    /// <param name="taxAmount">Optional line-level tax amount.</param>
    public void AddLine(string description, decimal quantity, decimal unitPrice, Guid? purchaseOrderLineId = null, Guid? productId = null, decimal? taxAmount = null)
    {
        if (Status != InvoiceStatus.Received && Status != InvoiceStatus.PendingApproval) // Adjust allowed statuses as needed
            throw new InvalidOperationException($"Cannot add lines to an invoice with status '{Status}'.");

        // Get the next line number
        int lineNumber = _lines.Count > 0 ? _lines.Max(l => l.LineNumber) + 1 : 1;

        var newLine = new InvoiceLine(
            invoiceId: this.Id,
            lineNumber: lineNumber,
            description: description,
            quantity: quantity,
            unitPrice: unitPrice,
            purchaseOrderLineId: purchaseOrderLineId,
            productId: productId,
            taxAmount: taxAmount);

        _lines.Add(newLine);
        RecalculateTotals();
    }

    // Methods to remove/update lines might be needed, respecting status checks

    // --- Methods for Status Transitions ---

    public void SubmitForApproval()
    {
        if (Status != InvoiceStatus.Received)
            throw new InvalidOperationException($"Invoice in status {Status} cannot be submitted for approval.");
        if (!Lines.Any())
            throw new InvalidOperationException("Invoice cannot be submitted with no line items.");

        RecalculateTotals(); // Ensure totals are up-to-date
        Status = InvoiceStatus.PendingApproval;
        // TODO: Raise Domain Event? InvoiceSubmittedForApprovalEvent(this.Id)
    }

    public void ApproveForPayment()
    {
        if (Status != InvoiceStatus.PendingApproval)
            throw new InvalidOperationException($"Invoice in status {Status} cannot be approved for payment.");

        // TODO: Add approval logic checks (e.g., matching results) if done within domain
        Status = InvoiceStatus.ApprovedForPayment;
        // TODO: Raise Domain Event? InvoiceApprovedForPaymentEvent(this.Id)
    }

    public void Reject(string reason)
    {
        if (Status != InvoiceStatus.PendingApproval) // Can only reject if pending approval?
            throw new InvalidOperationException($"Invoice in status {Status} cannot be rejected.");

        ArgumentException.ThrowIfNullOrWhiteSpace(reason);
        Status = InvoiceStatus.Rejected;
        Notes = $"Rejected: {reason}\n---\n{Notes}";
        // TODO: Raise Domain Event? InvoiceRejectedEvent(this.Id, reason)
    }

    /// <summary>
    /// Records a payment made against this invoice.
    /// </summary>
    /// <param name="paymentAmount">The amount paid.</param>
    /// <param name="paymentDate">The date the payment was made.</param>
    /// <param name="transactionReference">Optional reference for the payment transaction.</param>
    public void RecordPayment(decimal paymentAmount, DateTime paymentDate, string? transactionReference = null)
    {
        if (Status != InvoiceStatus.ApprovedForPayment && Status != InvoiceStatus.PartiallyPaid)
            throw new InvalidOperationException($"Cannot record payment for invoice with status '{Status}'.");
        if (paymentAmount <= 0)
            throw new ArgumentOutOfRangeException(nameof(paymentAmount), "Payment amount must be positive.");
        if (paymentAmount > (TotalAmount - AmountPaid))
            throw new InvalidOperationException($"Payment amount {paymentAmount} exceeds remaining balance {TotalAmount - AmountPaid}.");

        AmountPaid += paymentAmount;

        // Check if fully paid (use a small tolerance for decimal comparisons)
        if (Math.Abs(AmountPaid - TotalAmount) < 0.001m)
        {
            Status = InvoiceStatus.Paid;
            PaymentDate = paymentDate;
            // TODO: Raise Domain Event? InvoicePaidEvent(this.Id)
        }
        else
        {
            Status = InvoiceStatus.PartiallyPaid;
            // TODO: Raise Domain Event? InvoicePartiallyPaidEvent(this.Id, paymentAmount)
        }
        // Optionally link to PaymentTransaction entity here if needed
    }

    public void Cancel(string reason)
    {
        // Define cancellation conditions (e.g., only if Received or PendingApproval?)
        if (Status == InvoiceStatus.Paid || Status == InvoiceStatus.PartiallyPaid || Status == InvoiceStatus.Cancelled)
            throw new InvalidOperationException($"Invoice in status {Status} cannot be cancelled.");

        ArgumentException.ThrowIfNullOrWhiteSpace(reason);
        Status = InvoiceStatus.Cancelled;
        Notes = $"Cancelled: {reason}\n---\n{Notes}";
        // TODO: Raise Domain Event? InvoiceCancelledEvent(this.Id, reason)
    }

    /// <summary>
    /// Associates this invoice with a customer.
    /// </summary>
    /// <param name="customerId">The ID of the customer to associate with this invoice.</param>
    public void AssociateWithCustomer(Guid? customerId)
    {
        // Only allow changing customer in certain statuses
        if (Status != InvoiceStatus.Received && Status != InvoiceStatus.PendingApproval)
            throw new InvalidOperationException($"Cannot change customer association for invoice with status '{Status}'.");

        CustomerId = customerId;
        // TODO: Raise Domain Event? InvoiceCustomerChangedEvent(this.Id, customerId)
    }

    // --- Private Helper Methods ---

    private void RecalculateTotals()
    {
        SubTotal = Lines?.Sum(l => l.LineTotal) ?? 0m;
        // TODO: Implement actual Tax calculation logic based on lines, vendor location, buyer location, tax codes etc.
        // This might involve summing line taxes or applying a header-level tax rate.
        TaxAmount = Lines?.Sum(l => l.TaxAmount ?? 0m) ?? 0m; // Example: Summing line taxes
        // Add other charges if applicable (e.g., Shipping, Handling - might come from PO or be separate lines)
        TotalAmount = SubTotal + TaxAmount; // Simplified total
    }
}
