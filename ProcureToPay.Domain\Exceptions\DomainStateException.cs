using System;

namespace ProcureToPay.Domain.Exceptions
{
    /// <summary>
    /// Custom exception type for representing errors related to invalid domain state transitions or operations.
    /// </summary>
    public class DomainStateException : Exception
    {
        public DomainStateException()
        { }

        public DomainStateException(string message)
            : base(message)
        { }

        public DomainStateException(string message, Exception innerException)
            : base(message, innerException)
        { }
    }
}
