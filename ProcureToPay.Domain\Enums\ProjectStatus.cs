﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ProcureToPay.Domain.Enums
{
    /// <summary>
    /// Represents the lifecycle status of a Project.
    /// </summary>
    public enum ProjectStatus
    {
        /// <summary>
        /// Project has been defined but not yet started.
        /// </summary>
        NotStarted = 0,

        /// <summary>
        /// Project planning phase is underway.
        /// </summary>
        Planning = 1,

        /// <summary>
        /// Project execution is in progress.
        /// </summary>
        InProgress = 2,

        /// <summary>
        /// Project work is temporarily suspended.
        /// </summary>
        OnHold = 3,

        /// <summary>
        /// Project has been successfully finished.
        /// </summary>
        Completed = 4,

        /// <summary>
        /// Project has been terminated before completion.
        /// </summary>
        Cancelled = 5
    }
}
