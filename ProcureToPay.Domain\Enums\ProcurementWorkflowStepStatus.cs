namespace ProcureToPay.Domain.Enums;

/// <summary>
/// Represents the status of an individual step within a Procurement Workflow.
/// </summary>
public enum ProcurementWorkflowStepStatus
{
    /// <summary>
    /// Step has not yet been started or assigned.
    /// </summary>
    Pending = 0,
    /// <summary>
    /// Step is assigned and waiting for action.
    /// </summary>
    InProgress = 1,
    /// <summary>
    /// Step action completed successfully (e.g., approved).
    /// </summary>
    Completed = 2,
    /// <summary>
    /// Step action resulted in rejection.
    /// </summary>
    Rejected = 3,
    /// <summary>
    /// Step was skipped (e.g., due to conditions).
    /// </summary>
    Skipped = 4,
    /// <summary>
    /// Step was cancelled as part of workflow cancellation.
    /// </summary>
    Cancelled = 5
}
