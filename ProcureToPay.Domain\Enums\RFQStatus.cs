namespace ProcureToPay.Domain.Enums
{
    /// <summary>
    /// Represents the lifecycle status of a Request for Quote (RFQ).
    /// </summary>
    public enum RFQStatus
    {
        /// <summary>
        /// RFQ is being created or modified, not yet sent to vendors.
        /// </summary>
        Draft = 0,

        /// <summary>
        /// RFQ has been sent to vendors and is open for submissions.
        /// (Note: The RFQ entity code currently uses 'Sent' - recommend standardizing)
        /// </summary>
        Open = 1, // Corresponds to 'Sent' used in RFQ entity logic

        /// <summary>
        /// Submission deadline has passed, and submitted quotes are being reviewed/evaluated.
        /// (Note: The RFQ entity code currently uses 'Evaluating' - recommend standardizing)
        /// </summary>
        UnderReview = 2, // Corresponds to 'Evaluating' used in RFQ entity logic

        /// <summary>
        /// A vendor has been selected and awarded based on the RFQ responses.
        /// </summary>
        Awarded = 3,

        /// <summary>
        /// The RFQ process was cancelled before an award was made.
        /// </summary>
        Cancelled = 4,

        /// <summary>
        /// The RFQ process is complete (e.g., awarded and PO issued, or cancelled and no further action).
        /// </summary>
        Closed = 5 // Note: Not explicitly used in current RFQ entity methods, but defined in enum.
    }
}
