﻿namespace ProcureToPay.Domain.Enums;

/// <summary>
/// Represents the status of a Payment Transaction.
/// </summary>
public enum PaymentStatus
{
    /// <summary>
    /// Payment initiated but not yet confirmed by the payment system/bank.
    /// </summary>
    Pending = 0,
    /// <summary>
    /// Payment is currently being processed by the payment system/bank.
    /// </summary>
    Processing = 1,
    /// <summary>
    /// Payment completed successfully.
    /// </summary>
    Succeeded = 2,
    /// <summary>
    /// Payment failed.
    /// </summary>
    Failed = 3,
    /// <summary>
    /// Payment was cancelled before completion.
    /// </summary>
    Cancelled = 4,
    /// <summary>
    /// Payment requires further action or review.
    /// </summary>
    RequiresAction = 5
}
