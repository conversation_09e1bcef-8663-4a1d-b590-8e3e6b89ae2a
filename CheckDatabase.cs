using System;
using System.Data;
using Npgsql;

class Program
{
    static void Main(string[] args)
    {
        if (args.Length < 1)
        {
            Console.WriteLine("Usage: dotnet run -- \"ConnectionString\"");
            return;
        }

        string connectionString = args[0];
        // Mask password for display
        string maskedConnectionString = connectionString;
        if (connectionString.Contains("Password="))
        {
            int startIndex = connectionString.IndexOf("Password=") + 9;
            int endIndex = connectionString.IndexOf(';', startIndex);
            if (endIndex == -1) endIndex = connectionString.Length;
            maskedConnectionString = connectionString.Substring(0, startIndex) + "***" + 
                (endIndex < connectionString.Length ? connectionString.Substring(endIndex) : "");
        }

        Console.WriteLine($"Checking database with connection: {maskedConnectionString}");

        try
        {
            using (var connection = new NpgsqlConnection(connectionString))
            {
                connection.Open();
                Console.WriteLine("Successfully connected to the database.");

                // Check for __EFMigrationsHistory table
                Console.WriteLine("\nChecking for __EFMigrationsHistory table...");
                bool migrationsTableExists = CheckTableExists(connection, "__EFMigrationsHistory");
                
                if (migrationsTableExists)
                {
                    Console.WriteLine("  __EFMigrationsHistory table exists.");
                    
                    // Get migrations
                    Console.WriteLine("  Applied migrations:");
                    using (var command = new NpgsqlCommand("SELECT \"MigrationId\", \"ProductVersion\" FROM \"__EFMigrationsHistory\" ORDER BY \"MigrationId\"", connection))
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            Console.WriteLine($"    - {reader["MigrationId"]} ({reader["ProductVersion"]})");
                        }
                    }
                }
                else
                {
                    Console.WriteLine("  WARNING: __EFMigrationsHistory table does not exist. No migrations have been applied.");
                }

                // List all tables
                Console.WriteLine("\nListing all tables in the database...");
                using (var command = new NpgsqlCommand(@"
                    SELECT table_schema, table_name 
                    FROM information_schema.tables 
                    WHERE table_schema NOT IN ('pg_catalog', 'information_schema')
                    ORDER BY table_schema, table_name", connection))
                using (var reader = command.ExecuteReader())
                {
                    int tableCount = 0;
                    Console.WriteLine("  Tables in database:");
                    while (reader.Read())
                    {
                        tableCount++;
                        Console.WriteLine($"    - {reader["table_schema"]}.{reader["table_name"]}");
                    }
                    
                    if (tableCount == 0)
                    {
                        Console.WriteLine("  WARNING: No user tables found in the database!");
                    }
                }

                // Check for specific tables
                Console.WriteLine("\nChecking for specific tables of interest...");
                string[] tablesToCheck = { "Categories", "products", "AspNetUsers", "AspNetRoles", "Tenants" };
                
                foreach (var table in tablesToCheck)
                {
                    bool tableExists = CheckTableExists(connection, table);
                    
                    if (tableExists)
                    {
                        Console.WriteLine($"  Table '{table}' exists.");
                        
                        // Count rows
                        try
                        {
                            using (var command = new NpgsqlCommand($"SELECT COUNT(*) FROM \"{table}\"", connection))
                            {
                                long rowCount = (long)command.ExecuteScalar();
                                Console.WriteLine($"    Row count: {rowCount}");
                            }
                            
                            // Get column info
                            using (var command = new NpgsqlCommand(@$"
                                SELECT column_name, data_type, is_nullable
                                FROM information_schema.columns
                                WHERE table_schema = 'public'
                                AND table_name = '{table}'
                                ORDER BY ordinal_position", connection))
                            using (var reader = command.ExecuteReader())
                            {
                                Console.WriteLine("    Columns:");
                                while (reader.Read())
                                {
                                    string nullable = reader["is_nullable"].ToString() == "YES" ? "NULL" : "NOT NULL";
                                    Console.WriteLine($"      {reader["column_name"]} - {reader["data_type"]} {nullable}");
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"    Error getting details for table '{table}': {ex.Message}");
                        }
                    }
                    else
                    {
                        Console.WriteLine($"  WARNING: Table '{table}' does not exist.");
                    }
                }

                // Check for Product-Category relationship
                Console.WriteLine("\nChecking for Product-Category relationship...");
                using (var command = new NpgsqlCommand(@"
                    SELECT
                        tc.constraint_name,
                        kcu.column_name,
                        ccu.table_name AS foreign_table_name,
                        ccu.column_name AS foreign_column_name
                    FROM
                        information_schema.table_constraints AS tc
                        JOIN information_schema.key_column_usage AS kcu
                          ON tc.constraint_name = kcu.constraint_name
                        JOIN information_schema.constraint_column_usage AS ccu
                          ON ccu.constraint_name = tc.constraint_name
                    WHERE constraint_type = 'FOREIGN KEY'
                        AND tc.table_name = 'products'
                        AND ccu.table_name = 'Categories'", connection))
                using (var reader = command.ExecuteReader())
                {
                    bool hasRelationship = false;
                    Console.WriteLine("  Product-Category relationships:");
                    while (reader.Read())
                    {
                        hasRelationship = true;
                        Console.WriteLine($"    {reader["constraint_name"]}: products.{reader["column_name"]} -> {reader["foreign_table_name"]}.{reader["foreign_column_name"]}");
                    }
                    
                    if (!hasRelationship)
                    {
                        Console.WriteLine("  WARNING: No Product-Category relationship found.");
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"ERROR: {ex.Message}");
            Console.WriteLine(ex.StackTrace);
        }

        Console.WriteLine("\nDatabase check completed.");
    }

    static bool CheckTableExists(NpgsqlConnection connection, string tableName)
    {
        using (var command = new NpgsqlCommand(@$"
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = '{tableName}'
            )", connection))
        {
            return (bool)command.ExecuteScalar();
        }
    }
}
