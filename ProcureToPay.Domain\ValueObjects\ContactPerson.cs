using System;

namespace ProcureToPay.Domain.ValueObjects
{
    /// <summary>
    /// Represents contact person details, potentially stored as JSON within entities.
    /// </summary>
    public class <PERSON><PERSON><PERSON>
    {
        /// <summary>
        /// Name of the contact person.
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Role or position of the contact person.
        /// </summary>
        public string? Role { get; set; }

        /// <summary>
        /// Email address of the contact person.
        /// </summary>
        public string? Email { get; set; }

        /// <summary>
        /// Phone number of the contact person.
        /// </summary>
        public string? Phone { get; set; }

        /// <summary>
        /// Optional hierarchy level for the contact person (e.g., 1 = primary).
        /// </summary>
        public int? HierarchyLevel { get; set; }

        /// <summary>
        /// Default constructor for EF Core.
        /// </summary>
        public ContactPerson() { }
    }
}
