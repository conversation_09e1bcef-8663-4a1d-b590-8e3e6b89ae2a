﻿namespace ProcureToPay.Domain.Enums;

/// <summary>
/// Represents the status of a Supplier relationship.
/// </summary>
public enum SupplierStatus
{
    /// <summary>
    /// Supplier is being evaluated or onboarded.
    /// </summary>
    Prospective = 0,

    /// <summary>
    /// Supplier is approved and active for sourcing.
    /// </summary>
    Active = 1,

    /// <summary>
    /// Supplier is currently inactive or suspended.
    /// </summary>
    Inactive = 2,

    /// <summary>
    /// Relationship with the supplier has been terminated.
    /// </summary>
    Terminated = 3
}
