using Microsoft.EntityFrameworkCore;
using Moq;
using ProcureToPay.Domain.Interfaces;

namespace ProcureToPay.Tests
{
    /// <summary>
    /// Helper class for creating and configuring test database contexts.
    /// </summary>
    public static class TestDbContextHelper
    {
        /// <summary>
        /// Creates a new DbContextOptions for TestApplicationDbContext with a unique database name.
        /// </summary>
        /// <param name="testName">The name of the test, used as part of the database name.</param>
        /// <returns>DbContextOptions for TestApplicationDbContext.</returns>
        public static DbContextOptions<TestApplicationDbContext> CreateNewContextOptions(string testName)
        {
            // Create a unique database name for this test
            string databaseName = $"TestDb_{testName}_{Guid.NewGuid()}";

            // Create and return options
            return new DbContextOptionsBuilder<TestApplicationDbContext>()
                .UseInMemoryDatabase(databaseName: databaseName)
                .Options;
        }

        /// <summary>
        /// Creates a mock ITenantProvider that returns the specified tenant ID.
        /// </summary>
        /// <param name="tenantId">The tenant ID to return, or null for migration/design-time context.</param>
        /// <returns>A mock ITenantProvider.</returns>
        public static ITenantProvider CreateTenantProvider(Guid? tenantId)
        {
            var tenantProviderMock = new Mock<ITenantProvider>();
            tenantProviderMock.Setup(tp => tp.GetCurrentTenantId()).Returns(tenantId);
            return tenantProviderMock.Object;
        }
    }
}
