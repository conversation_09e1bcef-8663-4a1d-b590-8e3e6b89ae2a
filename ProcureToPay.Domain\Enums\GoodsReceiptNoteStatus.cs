﻿namespace ProcureToPay.Domain.Enums;

/// <summary>
/// Represents the status of a Goods Receipt Note.
/// </summary>
public enum GoodsReceiptNoteStatus
{
    /// <summary>
    /// Goods received but pending inspection and final acceptance/rejection.
    /// </summary>
    PendingInspection = 0,

    /// <summary>
    /// Inspection complete, goods accepted (fully or partially).
    /// </summary>
    Completed = 1,

    /// <summary>
    /// GRN process was cancelled or voided.
    /// </summary>
    Cancelled = 2
}
