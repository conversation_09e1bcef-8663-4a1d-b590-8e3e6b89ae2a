﻿namespace ProcureToPay.Domain.Enums;

/// <summary>
/// Represents the status of a Vendor.
/// </summary>
public enum VendorStatus
{
    /// <summary>
    /// Vendor is pending approval or onboarding.
    /// </summary>
    Pending = 0,

    /// <summary>
    /// Vendor is approved and active for business.
    /// </summary>
    Active = 1,

    /// <summary>
    /// Vendor is currently inactive or suspended.
    /// </summary>
    Inactive = 2,

    /// <summary>
    /// Vendor relationship has been terminated or blacklisted.
    /// </summary>
    Terminated = 3,

}

