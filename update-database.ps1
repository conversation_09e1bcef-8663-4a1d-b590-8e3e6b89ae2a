param(
    [Parameter(Mandatory=$true)]
    [string]$ConnectionString
)

Write-Host "Updating database to latest migration..."
Write-Host "Using Connection String: $($ConnectionString.Substring(0, $ConnectionString.IndexOf('Password=')) + 'Password=***')"

# First, add the initial migration to the __EFMigrationsHistory table
$sqlScript = @"
INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
SELECT '20250404014913_InitialIdentitySchema', '9.0.3'
WHERE NOT EXISTS (
    SELECT 1 FROM "__EFMigrationsHistory" 
    WHERE "MigrationId" = '20250404014913_InitialIdentitySchema'
);
"@

# Use psql to execute the SQL script
$env:PGPASSWORD = $ConnectionString -replace '.*Password=([^;]*).*', '$1'
$host = $ConnectionString -replace '.*Host=([^;]*).*', '$1'
$port = $ConnectionString -replace '.*Port=([^;]*).*', '$1'
$database = $ConnectionString -replace '.*Database=([^;]*).*', '$1'
$username = $ConnectionString -replace '.*Username=([^;]*).*', '$1'

Write-Host "Executing SQL script to add initial migration to __EFMigrationsHistory table..."
$sqlScript | docker exec -i postgresdb-thgxdhqu psql -h localhost -U $username -d $database

# Now apply the latest migration
Write-Host "Applying latest migration..."
dotnet ef database update --project ProcureToPay.Infrastructure --startup-project ProcureToPay.WebApp\ProcureToPay.WebApp --connection $ConnectionString --verbose
