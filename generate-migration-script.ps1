# Script to generate a SQL migration script from the current model
# This is useful when you want to create a clean database schema

param(
    [Parameter(Mandatory=$false)]
    [string]$OutputFile = "clean_migration_script.sql",
    
    [Parameter(Mandatory=$false)]
    [string]$InfrastructureProject = "ProcureToPay.Infrastructure",
    
    [Parameter(Mandatory=$false)]
    [string]$WebAppProject = "ProcureToPay.WebApp\ProcureToPay.WebApp"
)

Write-Host "Migration Script Generator" -ForegroundColor Cyan
Write-Host "=========================" -ForegroundColor Cyan

$infrastructureProjectPath = Join-Path $PSScriptRoot $InfrastructureProject
$webAppProjectPath = Join-Path $PSScriptRoot $WebAppProject
$outputFilePath = Join-Path $PSScriptRoot $OutputFile

# Generate the SQL script
Write-Host "Generating SQL script..." -ForegroundColor Cyan

try {
    $scriptCmd = "dotnet ef migrations script --project $infrastructureProjectPath --startup-project $webAppProjectPath --output $outputFilePath --idempotent"
    Write-Host "  Running: $scriptCmd" -ForegroundColor Cyan
    
    $scriptOutput = Invoke-Expression $scriptCmd
    
    # Display the output
    $scriptOutput | ForEach-Object { Write-Host "    $_" }
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "  SQL script generated successfully at: $outputFilePath" -ForegroundColor Green
    } else {
        Write-Error "  Failed to generate SQL script. See output above for details."
        exit 1
    }
}
catch {
    Write-Error "  An error occurred while generating the SQL script: $_"
    exit 1
}

# Provide next steps
Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "1. Review the generated SQL script: $outputFilePath" -ForegroundColor Cyan
Write-Host "2. Apply the SQL script to your database using a tool like psql or pgAdmin" -ForegroundColor Cyan
Write-Host "3. Or use the apply-aspire-migrations.ps1 script to apply migrations programmatically" -ForegroundColor Cyan
