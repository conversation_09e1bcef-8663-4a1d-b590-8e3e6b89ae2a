namespace ProcureToPay.Domain.Enums;

/// <summary>
/// Represents the overall status of a Procurement Workflow instance.
/// </summary>
public enum ProcurementWorkflowStatus
{
    /// <summary>
    /// Workflow has not started yet.
    /// </summary>
    NotStarted = 0,
    /// <summary>
    /// Workflow is currently active and progressing through steps.
    /// </summary>
    InProgress = 1,
    /// <summary>
    /// All steps in the workflow have been successfully completed.
    /// </summary>
    Completed = 2,
    /// <summary>
    /// The workflow was cancelled before completion.
    /// </summary>
    Cancelled = 3,
    /// <summary>
    /// The workflow was rejected at one of the steps.
    /// </summary>
    Rejected = 4
}
