param(
    [Parameter(Mandatory=$true)]
    [string]$ConnectionString,
    
    [Parameter(Mandatory=$true)]
    [string]$MigrationId,
    
    [Parameter(Mandatory=$true)]
    [string]$ProductVersion
)

Write-Host "Adding migration history record for $MigrationId..."
Write-Host "Using Connection String: $($ConnectionString.Substring(0, $ConnectionString.IndexOf('Password=')) + 'Password=***')"

# Install the Npgsql package if not already installed
if (-not (Get-Module -ListAvailable -Name Npgsql)) {
    Write-Host "Installing Npgsql module..."
    Install-Module -Name Npgsql -Scope CurrentUser -Force
}

try {
    # Import the Npgsql module
    Import-Module Npgsql

    # Create a connection to the PostgreSQL database
    $connection = New-Object Npgsql.NpgsqlConnection($ConnectionString)
    $connection.Open()

    # Create a command to insert the migration record
    $command = $connection.CreateCommand()
    $command.CommandText = "INSERT INTO `"__EFMigrationsHistory`" (`"MigrationId`", `"ProductVersion`") VALUES (@MigrationId, @ProductVersion)"
    $command.Parameters.AddWithValue("@MigrationId", $MigrationId)
    $command.Parameters.AddWithValue("@ProductVersion", $ProductVersion)

    # Execute the command
    $rowsAffected = $command.ExecuteNonQuery()
    
    if ($rowsAffected -gt 0) {
        Write-Host "Migration history record added successfully." -ForegroundColor Green
    } else {
        Write-Error "Failed to add migration history record."
    }
} catch {
    Write-Error "An error occurred: $_"
} finally {
    # Close the connection
    if ($connection -and $connection.State -eq 'Open') {
        $connection.Close()
        $connection.Dispose()
    }
}
