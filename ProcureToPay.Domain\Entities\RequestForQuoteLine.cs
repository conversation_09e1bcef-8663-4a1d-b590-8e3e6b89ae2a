﻿using System;
using ProcureToPay.Domain.Enums;       // Required for UnitOfMeasure
using ProcureToPay.Domain.ValueObjects; // Required for Money
using ProcureToPay.Domain.Exceptions;   // Assuming DomainStateException exists
using ProcureToPay.Domain.Events;       // Assuming RFQ domain events namespace exists

// Assuming BaseEntity<Guid>, RequestForQuote, ProductDefinition, VendorProduct entities exist
namespace ProcureToPay.Domain.Entities
{
    /// <summary>
    /// Represents a single line item on a Request for Quote (RFQ), detailing a specific item or service required.
    /// </summary>
    public class RequestForQuoteLine : BaseEntity<Guid> // Assuming BaseEntity<Guid>
    {
        /// <summary>
        /// Foreign Key to the parent Request for Quote header.
        /// </summary>
        public Guid RequestForQuoteId { get; private set; }

        /// <summary>
        /// Line number for ordering within the RFQ.
        /// </summary>
        public int LineNumber { get; private set; }

        /// <summary>
        /// Foreign Key to the Tenant, inherited from the header.
        /// </summary>
        public Guid TenantId { get; private set; }

        /// <summary>
        /// Optional Foreign Key to a master ProductDefinition being requested.
        /// </summary>
        public Guid? ProductDefinitionId { get; private set; }

        /// <summary>
        /// Optional Foreign Key to a specific VendorProduct being requested (less common on RFQ line itself).
        /// </summary>
        public Guid? VendorProductId { get; private set; }

        /// <summary>
        /// Detailed description of the item or service required. Required.
        /// </summary>
        public string Description { get; private set; } = null!;

        /// <summary>
        /// The quantity required. Required.
        /// </summary>
        public decimal Quantity { get; private set; }

        /// <summary>
        /// The unit of measure for the quantity. Required.
        /// </summary>
        public UnitOfMeasure UnitOfMeasure { get; private set; }

        /// <summary>
        /// Optional target unit price (Money VO) to guide vendor quotes.
        /// </summary>
        public Money? TargetUnitPrice { get; private set; }

        // --- Properties based on Configuration Requirements ---

        /// <summary>
        /// Description of acceptable alternate items or proposals vendors can offer.
        /// </summary>
        public string? AlternateItemProposal { get; private set; }

        /// <summary>
        /// Optional estimated Total Cost of Ownership (TCO) value for evaluation.
        /// </summary>
        public Money? EstimatedTcoValue { get; private set; }

        /// <summary>
        /// Required technical specifications for the item or service. Can be text or link to document.
        /// </summary>
        public string? TechnicalSpecifications { get; private set; }

        /// <summary>
        /// Flag indicating if submission of a sample is required from the vendor.
        /// </summary>
        public bool SampleRequired { get; private set; }

        /// <summary>
        /// Minimum Order Quantity (MOQ) required or acceptable for this item.
        /// </summary>
        public decimal? MinimumOrderQuantity { get; private set; }

        /// <summary>
        /// Preferred Incoterm for delivery (e.g., "FOB", "CIF", "DDP").
        /// </summary>
        public string? PreferredIncoterm { get; private set; }

        /// <summary>
        /// Flag indicating if substitute items are allowed for this line.
        /// </summary>
        public bool IsSubstituteAllowed { get; private set; }

        // --- Standard Properties ---
        /// <summary>
        /// Optional notes specific to this line item.
        /// </summary>
        public string? Notes { get; private set; }

        /// <summary>
        /// Flag indicating if the line item has been soft-deleted.
        /// </summary>
        public bool IsDeleted { get; private set; }


        // --- Navigation Properties ---

        /// <summary>
        /// Navigation property to the parent Request for Quote. Virtual for lazy loading.
        /// </summary>
        public virtual RequestForQuote RequestForQuote { get; private set; } = null!;

        /// <summary>
        /// Optional navigation property to the master ProductDefinition. Virtual for lazy loading.
        /// </summary>
        public virtual ProductDefinition? ProductDefinition { get; private set; }

        /// <summary>
        /// Optional navigation property to the specific VendorProduct. Virtual for lazy loading.
        /// </summary>
        public virtual VendorProduct? VendorProduct { get; private set; }


        /// <summary>
        /// Private constructor for EF Core hydration.
        /// </summary>
        private RequestForQuoteLine() : base(Guid.NewGuid()) { }

        /// <summary>
        /// Internal constructor for creating a new RFQ Line. Called by RequestForQuote aggregate root.
        /// </summary>
        internal RequestForQuoteLine(
            Guid id,
            Guid requestForQuoteId,
            int lineNumber,
            Guid tenantId,
            string description,
            decimal quantity,
            UnitOfMeasure unitOfMeasure,
            Guid? productDefinitionId = null,
            Guid? vendorProductId = null,
            Money? targetUnitPrice = null,
            string? alternateItemProposal = null,
            Money? estimatedTcoValue = null,
            string? technicalSpecifications = null,
            bool sampleRequired = false,
            decimal? minimumOrderQuantity = null,
            string? preferredIncoterm = null,
            bool isSubstituteAllowed = false,
            string? notes = null
            ) : base(id)
        {
            // Basic Validation
            if (requestForQuoteId == Guid.Empty) throw new ArgumentException("RequestForQuoteId cannot be empty.", nameof(requestForQuoteId));
            if (tenantId == Guid.Empty) throw new ArgumentException("TenantId cannot be empty.", nameof(tenantId));
            if (lineNumber <= 0) throw new ArgumentOutOfRangeException(nameof(lineNumber), "Line number must be positive.");
            ArgumentException.ThrowIfNullOrWhiteSpace(description);
            if (quantity <= 0) throw new ArgumentOutOfRangeException(nameof(quantity), "Quantity must be positive.");
            if (vendorProductId.HasValue && productDefinitionId.HasValue) throw new ArgumentException("Cannot specify both VendorProductId and ProductDefinitionId.");
            if (targetUnitPrice?.Amount < 0) throw new ArgumentOutOfRangeException(nameof(targetUnitPrice), "Target unit price cannot be negative.");
            if (estimatedTcoValue?.Amount < 0) throw new ArgumentOutOfRangeException(nameof(estimatedTcoValue), "Estimated TCO value cannot be negative.");
            if (minimumOrderQuantity.HasValue && minimumOrderQuantity <= 0) throw new ArgumentOutOfRangeException(nameof(minimumOrderQuantity), "Minimum order quantity must be positive if specified.");
            // TODO: Add currency consistency check if TargetUnitPrice/EstimatedTcoValue provided vs RFQ header currency

            RequestForQuoteId = requestForQuoteId;
            LineNumber = lineNumber;
            TenantId = tenantId;
            ProductDefinitionId = productDefinitionId;
            VendorProductId = vendorProductId;
            Description = description;
            Quantity = quantity;
            UnitOfMeasure = unitOfMeasure;
            TargetUnitPrice = targetUnitPrice;
            AlternateItemProposal = alternateItemProposal;
            EstimatedTcoValue = estimatedTcoValue;
            TechnicalSpecifications = technicalSpecifications;
            SampleRequired = sampleRequired;
            MinimumOrderQuantity = minimumOrderQuantity;
            PreferredIncoterm = preferredIncoterm;
            IsSubstituteAllowed = isSubstituteAllowed;
            Notes = notes;
            IsDeleted = false;

            // AddDomainEvent(new RfqLineCreatedEvent(this.RequestForQuoteId, this.Id)); // Event raised by RFQ aggregate
        }


        // --- Domain Methods ---

        // Example method to update details - called via RFQ aggregate root
        internal void UpdateDetails(
            string description,
            decimal quantity,
            UnitOfMeasure unitOfMeasure,
            Money? targetUnitPrice,
            string? notes
            // Add other updatable fields as needed
            )
        {
            ArgumentException.ThrowIfNullOrWhiteSpace(description);
            if (quantity <= 0) throw new ArgumentOutOfRangeException(nameof(quantity), "Quantity must be positive.");
            if (targetUnitPrice?.Amount < 0) throw new ArgumentOutOfRangeException(nameof(targetUnitPrice), "Target unit price cannot be negative.");
            // TODO: Add currency consistency check if TargetUnitPrice provided vs RFQ header currency

            // Update properties...
            Description = description;
            Quantity = quantity;
            UnitOfMeasure = unitOfMeasure;
            TargetUnitPrice = targetUnitPrice;
            Notes = notes;

            // AddDomainEvent(new RfqLineDetailsUpdatedEvent(this.RequestForQuoteId, this.Id)); // Event raised by RFQ aggregate
        }

        // Example method to update sourcing details - called via RFQ aggregate root
        internal void UpdateSourcingDetails(
            string? alternateItemProposal,
            Money? estimatedTcoValue,
            string? technicalSpecifications,
            bool sampleRequired,
            decimal? minimumOrderQuantity,
            string? preferredIncoterm,
            bool isSubstituteAllowed)
        {
            if (estimatedTcoValue?.Amount < 0) throw new ArgumentOutOfRangeException(nameof(estimatedTcoValue), "Estimated TCO value cannot be negative.");
            if (minimumOrderQuantity.HasValue && minimumOrderQuantity <= 0) throw new ArgumentOutOfRangeException(nameof(minimumOrderQuantity), "Minimum order quantity must be positive if specified.");
            // TODO: Add currency consistency check for TCO value

            AlternateItemProposal = alternateItemProposal;
            EstimatedTcoValue = estimatedTcoValue;
            TechnicalSpecifications = technicalSpecifications;
            SampleRequired = sampleRequired;
            MinimumOrderQuantity = minimumOrderQuantity;
            PreferredIncoterm = preferredIncoterm;
            IsSubstituteAllowed = isSubstituteAllowed;

            // AddDomainEvent(new RfqLineSourcingDetailsUpdatedEvent(this.RequestForQuoteId, this.Id)); // Example event
        }


        // Soft Delete methods - potentially controlled by RFQ aggregate
        internal void MarkAsDeleted()
        {
            if (IsDeleted) return;
            IsDeleted = true;
            // AddDomainEvent(new RfqLineDeletedEvent(this.RequestForQuoteId, this.Id)); // Event raised by RFQ aggregate
        }

        internal void Restore()
        {
            if (!IsDeleted) return;
            IsDeleted = false;
            // AddDomainEvent(new RfqLineRestoredEvent(this.RequestForQuoteId, this.Id)); // Event raised by RFQ aggregate
        }
    }

    // --- Placeholder Domain Event Definitions (Place in Domain/Events) ---
    /*
    namespace ProcureToPay.Domain.Events
    {
        // Note: Line item events are often raised by the Aggregate Root (RequestForQuote)
        // after calling methods on the line item.
        public record RfqLineCreatedEvent(Guid RfqId, Guid LineId);
        public record RfqLineDetailsUpdatedEvent(Guid RfqId, Guid LineId);
        public record RfqLineSourcingDetailsUpdatedEvent(Guid RfqId, Guid LineId);
        public record RfqLineDeletedEvent(Guid RfqId, Guid LineId);
        public record RfqLineRestoredEvent(Guid RfqId, Guid LineId);
    }
    */
}
