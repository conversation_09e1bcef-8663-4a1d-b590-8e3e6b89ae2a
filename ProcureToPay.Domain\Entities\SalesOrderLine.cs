﻿using System;
using System.Collections.Generic;
using System.Linq;
using ProcureToPay.Domain.Enums;       // Required for UnitOfMeasure
using ProcureToPay.Domain.ValueObjects; // Required for Money
using ProcureToPay.Domain.Exceptions;   // Assuming DomainStateException exists
using ProcureToPay.Domain.Events;       // Assuming SalesOrderLine domain events namespace exists

// Assuming BaseEntity<Guid>, SalesOrder, VendorProduct, Project entities exist
namespace ProcureToPay.Domain.Entities
{
    /// <summary>
    /// Represents a single line item on a Sales Order.
    /// Includes details for pricing, discounts, backorders, kits, warranty, and project linking.
    /// </summary>
    public class SalesOrderLine : BaseEntity<Guid>
    {
        /// <summary>
        /// Foreign Key to the parent Sales Order header.
        /// </summary>
        public Guid SalesOrderId { get; private set; }

        /// <summary>
        /// Line number for ordering within the Sales Order.
        /// </summary>
        public int LineNumber { get; private set; }

        /// <summary>
        /// Foreign Key to the Tenant, inherited from the header.
        /// </summary>
        public Guid TenantId { get; private set; }

        /// <summary>
        /// Foreign Key to the specific VendorProduct being sold. Required.
        /// </summary>
        public Guid VendorProductId { get; private set; }

        // --- Snapshots & Core Details ---
        /// <summary>
        /// Snapshot of the SKU (Vendor specific or master) at the time of order.
        /// </summary>
        public string SkuSnapshot { get; private set; } = null!;

        /// <summary>
        /// Snapshot of the product description at the time of order.
        /// </summary>
        public string DescriptionSnapshot { get; private set; } = null!;

        /// <summary>
        /// Description of the line item.
        /// </summary>
        public string Description { get; private set; } = string.Empty;

        /// <summary>
        /// The requested delivery date for this line item.
        /// </summary>
        public DateTime? RequestedDeliveryDate { get; private set; }

        /// <summary>
        /// Foreign Key to the Product.
        /// </summary>
        public Guid ProductId { get; private set; }

        /// <summary>
        /// The quantity ordered by the customer.
        /// </summary>
        public decimal Quantity { get; private set; }

        /// <summary>
        /// The unit of measure for the quantity (snapshotted).
        /// </summary>
        public UnitOfMeasure UnitOfMeasure { get; private set; }

        /// <summary>
        /// The price per unit for this customer/order (snapshotted). Uses Money VO.
        /// Captures customer-specific pricing or applied contract pricing.
        /// </summary>
        [System.ComponentModel.DataAnnotations.Schema.NotMapped]
        public Money UnitPrice { get; private set; } = null!;

        /// <summary>
        /// The amount component of the UnitPrice.
        /// </summary>
        public decimal UnitPriceAmount { get; private set; }

        /// <summary>
        /// The currency code component of the UnitPrice.
        /// </summary>
        public string UnitPriceCurrency { get; private set; } = string.Empty;

        /// <summary>
        /// Description of any volume or special discount applied (e.g., "10% Volume Tier 2").
        /// </summary>
        public string? AppliedDiscountDescription { get; private set; }

        /// <summary>
        /// The monetary amount of the discount applied to this line (optional). Uses Money VO.
        /// </summary>
        [System.ComponentModel.DataAnnotations.Schema.NotMapped]
        public Money? DiscountAmount { get; private set; }

        /// <summary>
        /// The amount component of the DiscountAmount.
        /// </summary>
        public decimal? DiscountAmountValue { get; private set; }

        /// <summary>
        /// The currency code component of the DiscountAmount.
        /// </summary>
        public string? DiscountAmountCurrency { get; private set; }

        /// <summary>
        /// The calculated total for this line ((Quantity * UnitPrice) - DiscountAmount). Uses Money VO.
        /// </summary>
        [System.ComponentModel.DataAnnotations.Schema.NotMapped]
        public Money LineTotal { get; private set; } = null!;

        /// <summary>
        /// The amount component of the LineTotal.
        /// </summary>
        public decimal LineTotalAmount { get; private set; }

        /// <summary>
        /// The currency code component of the LineTotal.
        /// </summary>
        public string LineTotalCurrency { get; private set; } = string.Empty;


        // --- Fulfillment & Tracking ---
        /// <summary>
        /// Quantity currently on backorder.
        /// </summary>
        public decimal QuantityBackordered { get; private set; }

        /// <summary>
        /// Stores reserved serial numbers as JSON or delimited string. Requires external logic.
        /// </summary>
        public string? ReservedSerialNumbersJson { get; private set; }

        /// <summary>
        /// Optional end date for warranty coverage related to this line item.
        /// </summary>
        public DateTime? WarrantyEndDate { get; private set; }


        // --- Kit/Assembly Information ---
        /// <summary>
        /// Flag indicating if this line item is a component of a kit/assembly defined on another line.
        /// </summary>
        public bool IsKitComponent { get; private set; }

        /// <summary>
        /// Optional Foreign Key linking this component line to its parent kit/assembly line within the same Sales Order.
        /// </summary>
        public Guid? ParentSalesOrderLineId { get; private set; }


        // --- Project Accounting ---
        /// <summary>
        /// Optional Foreign Key linking this line item to a specific Project.
        /// </summary>
        public Guid? ProjectId { get; private set; }

        /// <summary>
        /// Optional cost code associated with this line item for project accounting.
        /// </summary>
        public string? CostCode { get; private set; }


        // --- Lifecycle ---
        /// <summary>
        /// Flag indicating if the line item has been soft-deleted.
        /// </summary>
        public bool IsDeleted { get; private set; }


        // --- Navigation Properties ---
        public virtual SalesOrder SalesOrder { get; private set; } = null!;
        public virtual VendorProduct VendorProduct { get; private set; } = null!;
        public virtual Product Product { get; private set; } = null!;
        public virtual SalesOrderLine? ParentSalesOrderLine { get; private set; }
        public virtual ICollection<SalesOrderLine> ChildSalesOrderLines { get; private set; } = new List<SalesOrderLine>(); // For kit components
        public virtual Project? Project { get; private set; } // Assuming Project entity exists


        /// <summary>
        /// Private constructor for EF Core.
        /// </summary>
        private SalesOrderLine() : base(Guid.NewGuid())
        {
            UnitPrice = new Money(0, "XXX"); // Placeholder
            LineTotal = new Money(0, "XXX"); // Placeholder
            UnitPriceAmount = 0;
            UnitPriceCurrency = "XXX";
            LineTotalAmount = 0;
            LineTotalCurrency = "XXX";
        }

        /// <summary>
        /// Internal constructor for creating a new Sales Order Line. Called by SalesOrder aggregate.
        /// </summary>
        internal SalesOrderLine(
            Guid id,
            Guid salesOrderId,
            int lineNumber,
            Guid tenantId,
            VendorProduct vendorProduct, // Pass VendorProduct to get details & snapshots
            decimal quantity,
            string description,
            DateTime? requestedDeliveryDate = null,
            Money? specificUnitPrice = null, // Allow overriding VP price for customer/contract specific
            string? appliedDiscountDescription = null,
            Money? discountAmount = null,
            Guid? projectId = null,
            string? costCode = null,
            bool isKitComponent = false,
            Guid? parentSalesOrderLineId = null
            ) : base(id)
        {
            // Validation
            if (salesOrderId == Guid.Empty) throw new ArgumentException("SalesOrderId cannot be empty.", nameof(salesOrderId));
            if (tenantId == Guid.Empty) throw new ArgumentException("TenantId cannot be empty.", nameof(tenantId));
            if (lineNumber <= 0) throw new ArgumentOutOfRangeException(nameof(lineNumber), "Line number must be positive.");
            ArgumentNullException.ThrowIfNull(vendorProduct);
            if (quantity <= 0) throw new ArgumentOutOfRangeException(nameof(quantity), "Quantity must be positive.");
            ArgumentException.ThrowIfNullOrWhiteSpace(description);
            if (vendorProduct.ProductDefinition == null) throw new InvalidOperationException("VendorProduct must have its ProductDefinition loaded.");
            if (vendorProduct.UnitPrice == null) throw new InvalidOperationException("VendorProduct must have a UnitPrice.");
            if (specificUnitPrice != null && specificUnitPrice.Amount < 0) throw new ArgumentOutOfRangeException(nameof(specificUnitPrice), "Unit price cannot be negative.");
            if (discountAmount != null && discountAmount.Amount < 0) throw new ArgumentOutOfRangeException(nameof(discountAmount), "Discount amount cannot be negative.");
            if (discountAmount != null && specificUnitPrice != null && discountAmount.CurrencyCode != specificUnitPrice.CurrencyCode) throw new ArgumentException("Discount currency must match unit price currency.");
            if (discountAmount != null && specificUnitPrice == null && discountAmount.CurrencyCode != vendorProduct.UnitPrice.CurrencyCode) throw new ArgumentException("Discount currency must match vendor product unit price currency.");
            if (isKitComponent && !parentSalesOrderLineId.HasValue) throw new ArgumentException("Kit component must have a ParentSalesOrderLineId.", nameof(parentSalesOrderLineId));
            if (!isKitComponent && parentSalesOrderLineId.HasValue) throw new ArgumentException("Non-kit component cannot have a ParentSalesOrderLineId.", nameof(parentSalesOrderLineId));


            SalesOrderId = salesOrderId;
            LineNumber = lineNumber;
            TenantId = tenantId;
            VendorProductId = vendorProduct.Id;
            ProductId = vendorProduct.ProductDefinitionId;
            Quantity = quantity;
            Description = description;
            RequestedDeliveryDate = requestedDeliveryDate?.ToUniversalTime();
            UnitOfMeasure = vendorProduct.UnitOfMeasure; // Snapshot UoM

            // Use specific price if provided, otherwise default to VendorProduct price
            UnitPrice = specificUnitPrice ?? vendorProduct.UnitPrice;
            UnitPriceAmount = UnitPrice.Amount;
            UnitPriceCurrency = UnitPrice.CurrencyCode;

            // Snapshots
            SkuSnapshot = !string.IsNullOrWhiteSpace(vendorProduct.VendorSku) ? vendorProduct.VendorSku : vendorProduct.ProductDefinition.Sku;
            DescriptionSnapshot = vendorProduct.ProductDefinition.Description ?? string.Empty;

            // Discount & Total
            AppliedDiscountDescription = appliedDiscountDescription;
            DiscountAmount = discountAmount;
            if (discountAmount != null)
            {
                DiscountAmountValue = discountAmount.Amount;
                DiscountAmountCurrency = discountAmount.CurrencyCode;
            }
            RecalculateLineTotal(); // Calculate initial total

            // Fulfillment & Tracking
            QuantityBackordered = 0; // Initially nothing is backordered
            ReservedSerialNumbersJson = null;
            WarrantyEndDate = null;

            // Kit/Assembly
            IsKitComponent = isKitComponent;
            ParentSalesOrderLineId = parentSalesOrderLineId;

            // Project Accounting
            ProjectId = projectId;
            CostCode = costCode;

            IsDeleted = false;

            // Event raised by SalesOrder aggregate after adding line
        }


        // --- Domain Methods ---

        internal void UpdateQuantity(decimal newQuantity)
        {
            if (newQuantity <= 0) throw new ArgumentOutOfRangeException(nameof(newQuantity), "Quantity must be positive.");
            if (Quantity == newQuantity) return;

            // Add logic here to potentially adjust QuantityBackordered based on fulfillment status?
            // This might belong in an Application Service or a dedicated Fulfillment service.
            Quantity = newQuantity;
            RecalculateLineTotal();
            AddDomainEvent(new SalesOrderLineQuantityUpdatedEvent(this.Id, newQuantity));
        }

        internal void ApplyDiscount(string description, Money discount)
        {
            ArgumentException.ThrowIfNullOrWhiteSpace(description);
            ArgumentNullException.ThrowIfNull(discount);
            if (discount.CurrencyCode != this.UnitPrice.CurrencyCode) throw new ArgumentException("Discount currency must match line item currency.", nameof(discount));
            if (discount.Amount < 0) throw new ArgumentOutOfRangeException(nameof(discount), "Discount amount cannot be negative.");

            AppliedDiscountDescription = description;
            DiscountAmount = discount;
            DiscountAmountValue = discount.Amount;
            DiscountAmountCurrency = discount.CurrencyCode;
            RecalculateLineTotal();
            AddDomainEvent(new SalesOrderLineDiscountAppliedEvent(this.Id, description, discount));
        }

        internal void MarkAsBackordered(decimal backorderedQuantity)
        {
            if (backorderedQuantity < 0) throw new ArgumentOutOfRangeException(nameof(backorderedQuantity), "Backordered quantity cannot be negative.");
            // Add validation? Cannot backorder more than ordered?
            // if (backorderedQuantity > Quantity) ...

            QuantityBackordered = backorderedQuantity;
            AddDomainEvent(new SalesOrderLineBackorderedEvent(this.Id, backorderedQuantity));
        }

        internal void FulfillBackorder(decimal fulfilledQuantity)
        {
            if (fulfilledQuantity <= 0) throw new ArgumentOutOfRangeException(nameof(fulfilledQuantity), "Fulfilled quantity must be positive.");
            if (fulfilledQuantity > QuantityBackordered) throw new InvalidOperationException($"Cannot fulfill {fulfilledQuantity} when only {QuantityBackordered} are backordered.");

            QuantityBackordered -= fulfilledQuantity;
            // AddDomainEvent(new SalesOrderLineBackorderFulfilledEvent(this.Id, fulfilledQuantity));
        }

        internal void ReserveSerialNumbers(List<string> serialNumbers)
        {
            ArgumentNullException.ThrowIfNull(serialNumbers);
            // Add validation? Check against quantity? Prevent duplicates?
            // Serialize to JSON - requires System.Text.Json or Newtonsoft.Json
            ReservedSerialNumbersJson = System.Text.Json.JsonSerializer.Serialize(serialNumbers);
            AddDomainEvent(new SalesOrderLineSerialsReservedEvent(this.Id, serialNumbers));
        }

        internal void SetWarrantyEndDate(DateTime? endDate)
        {
            // Add validation? End date must be after order/ship date?
            WarrantyEndDate = endDate?.ToUniversalTime();
            AddDomainEvent(new SalesOrderLineWarrantySetEvent(this.Id, endDate));
        }

        internal void AssignToProject(Guid? projectId, string? costCode)
        {
            // Add validation? Check if project exists (App Service)?
            ProjectId = projectId;
            CostCode = costCode;
            AddDomainEvent(new SalesOrderLineProjectAssignedEvent(this.Id, projectId, costCode));
        }


        // --- Soft Delete ---
        internal void MarkAsDeleted() // Internal as deletion should be controlled by SalesOrder aggregate
        {
            if (IsDeleted) return;
            IsDeleted = true;
            // AddDomainEvent(new SalesOrderLineDeletedEvent(this.SalesOrderId, this.Id)); // Event raised by SalesOrder aggregate
        }

        internal void Restore() // Internal as restoration should be controlled by SalesOrder aggregate
        {
            if (!IsDeleted) return;
            IsDeleted = false;
            // AddDomainEvent(new SalesOrderLineRestoredEvent(this.SalesOrderId, this.Id)); // Event raised by SalesOrder aggregate
        }

        // --- Helper ---
        private void RecalculateLineTotal()
        {
            if (UnitPrice == null)
            {
                throw new InvalidOperationException("UnitPrice cannot be null for line total calculation.");
            }
            var grossAmount = UnitPrice * Quantity;
            LineTotal = grossAmount - (DiscountAmount ?? new Money(0, UnitPrice.CurrencyCode)); // Subtract discount if present

            // Update the scalar properties for EF Core
            LineTotalAmount = LineTotal.Amount;
            LineTotalCurrency = LineTotal.CurrencyCode;

            if (LineTotal.Amount < 0)
            {
                // This might happen if discount exceeds gross amount - handle based on business rules
                // Option 1: Throw exception
                throw new DomainStateException("Line total cannot be negative after discount.");
                // Option 2: Set total to zero
                // LineTotal = new Money(0, UnitPrice.CurrencyCode);
                // LineTotalAmount = 0;
            }
        }
    }

    // --- Placeholder Domain Event Definitions (Place in Domain/Events) ---
    /*
    namespace ProcureToPay.Domain.Events
    {
        // Note: Line item events are often raised by the Aggregate Root (SalesOrder)
        public record SalesOrderLineCreatedEvent(Guid SalesOrderId, Guid LineId);
        public record SalesOrderLineQuantityUpdatedEvent(Guid LineId, decimal NewQuantity);
        public record SalesOrderLineDiscountAppliedEvent(Guid LineId, string DiscountDescription, Money DiscountAmount);
        public record SalesOrderLineBackorderedEvent(Guid LineId, decimal BackorderedQuantity);
        public record SalesOrderLineBackorderFulfilledEvent(Guid LineId, decimal FulfilledQuantity);
        public record SalesOrderLineSerialsReservedEvent(Guid LineId, List<string> SerialNumbers);
        public record SalesOrderLineWarrantySetEvent(Guid LineId, DateTime? WarrantyEndDate);
        public record SalesOrderLineProjectAssignedEvent(Guid LineId, Guid? ProjectId, string? CostCode);
        public record SalesOrderLineDeletedEvent(Guid SalesOrderId, Guid LineId);
        public record SalesOrderLineRestoredEvent(Guid SalesOrderId, Guid LineId);
    }
    */

    // --- Placeholder Related Entities (Define elsewhere) ---
    // public class Project : BaseEntity<Guid> { /* ... */ }
}
