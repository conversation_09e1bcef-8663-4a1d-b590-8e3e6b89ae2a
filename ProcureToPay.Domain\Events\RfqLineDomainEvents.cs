﻿using System;
using ProcureToPay.Domain.Enums;       // For UnitOfMeasure
using ProcureToPay.Domain.ValueObjects; // For Money

namespace ProcureToPay.Domain.Events
{
    // --- Placeholder: Define a base marker interface or class if desired ---
    // public interface IDomainEvent { }

    // --- RequestForQuoteLine Specific Events ---
    // Note: These events are often raised via the RequestForQuote Aggregate Root

    /// <summary>
    /// Raised when a new line item is added to an RFQ.
    /// Typically raised by the RequestForQuote aggregate after adding the line.
    /// </summary>
    /// <param name="RfqId">The ID of the parent RFQ.</param>
    /// <param name="LineId">The ID of the newly added line item.</param>
    public record RfqLineCreatedEvent(Guid RfqId, Guid LineId);

    /// <summary>
    /// Raised when core details of an RFQ line item are updated.
    /// Typically raised by the RequestForQuote aggregate after updating the line.
    /// </summary>
    /// <param name="RfqId">The ID of the parent RFQ.</param>
    /// <param name="LineId">The ID of the updated line item.</param>
    public record RfqLineDetailsUpdatedEvent(Guid RfqId, Guid LineId);

    /// <summary>
    /// Raised when sourcing-specific details of an RFQ line item are updated.
    /// Typically raised by the RequestForQuote aggregate after updating the line.
    /// </summary>
    /// <param name="RfqId">The ID of the parent RFQ.</param>
    /// <param name="LineId">The ID of the updated line item.</param>
    public record RfqLineSourcingDetailsUpdatedEvent(Guid RfqId, Guid LineId);

    /// <summary>
    /// Raised when an RFQ line item is soft-deleted.
    /// Typically raised by the RequestForQuote aggregate.
    /// </summary>
    /// <param name="RfqId">The ID of the parent RFQ.</param>
    /// <param name="LineId">The ID of the deleted line item.</param>
    public record RfqLineDeletedEvent(Guid RfqId, Guid LineId);

    /// <summary>
    /// Raised when a soft-deleted RFQ line item is restored.
    /// Typically raised by the RequestForQuote aggregate.
    /// </summary>
    /// <param name="RfqId">The ID of the parent RFQ.</param>
    /// <param name="LineId">The ID of the restored line item.</param>
    public record RfqLineRestoredEvent(Guid RfqId, Guid LineId);

}
