using ProcureToPay.Domain.Interfaces;
using ProcureToPay.Infrastructure.Persistence;
using System;

namespace ProcureToPay.Infrastructure.Services
{
    /// <summary>
    /// Implementation of ITenantProvider that works in all application contexts.
    /// </summary>
    public class TenantProvider : ITenantProvider
    {
        private readonly TenantResolutionStrategy _tenantResolutionStrategy;

        /// <summary>
        /// Initializes a new instance of the <see cref="TenantProvider"/> class.
        /// </summary>
        /// <param name="tenantResolutionStrategy">The tenant resolution strategy.</param>
        public TenantProvider(TenantResolutionStrategy tenantResolutionStrategy)
        {
            _tenantResolutionStrategy = tenantResolutionStrategy;
        }

        /// <summary>
        /// Gets the current tenant ID.
        /// </summary>
        /// <returns>The current tenant ID.</returns>
        public Guid? GetCurrentTenantId()
        {
            return _tenantResolutionStrategy.GetCurrentTenantId();
        }
    }
}
