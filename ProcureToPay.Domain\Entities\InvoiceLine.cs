﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using ProcureToPay.Domain.Entities; // Required for BaseEntity and related entities

namespace ProcureToPay.Domain.Entities;

/// <summary>
/// Represents a single line item on a Vendor Invoice.
/// </summary>
public class InvoiceLine : BaseEntity<Guid>
{
    /// <summary>
    /// Line number for ordering within the invoice.
    /// </summary>
    [Required]
    public int LineNumber { get; private set; }

    /// <summary>
    /// Foreign Key to the parent Invoice.
    /// </summary>
    [Required]
    public Guid InvoiceId { get; private set; }
    /// <summary>
    /// Navigation property to the parent Invoice. Virtual for lazy loading.
    /// </summary>
    public virtual Invoice Invoice { get; private set; } = null!;

    /// <summary>
    /// Optional Foreign Key to the Purchase Order Line this invoice line relates to (for matching).
    /// </summary>
    public Guid? PurchaseOrderLineId { get; private set; }
    /// <summary>
    /// Optional navigation property to the related Purchase Order Line. Virtual for lazy loading.
    /// </summary>
    public virtual PurchaseOrderLine? PurchaseOrderLine { get; private set; }

    /// <summary>
    /// Optional Foreign Key to the Product being invoiced.
    /// </summary>
    public Guid? ProductId { get; private set; }
    /// <summary>
    /// Optional navigation property to the Product. Virtual for lazy loading.
    /// </summary>
    public virtual ProductDefinition? Product { get; private set; }

    /// <summary>
    /// Required description of the invoiced item or service.
    /// Can be copied from PO line or entered manually.
    /// </summary>
    [Required]
    [MaxLength(1000)]
    public string Description { get; private set; } = string.Empty;

    /// <summary>
    /// The quantity being invoiced. Using decimal for flexibility.
    /// </summary>
    [Required]
    [Column(TypeName = "decimal(18, 4)")]
    public decimal Quantity { get; private set; }

    /// <summary>
    /// The price per unit being invoiced (should match PO or contract if applicable).
    /// TODO: Consider using Money Value Object.
    /// </summary>
    [Required]
    [Column(TypeName = "decimal(18, 4)")]
    public decimal UnitPrice { get; private set; }

    /// <summary>
    /// The calculated total for this line before tax (Quantity * UnitPrice).
    /// TODO: Consider using Money Value Object.
    /// </summary>
    [Required]
    [Column(TypeName = "decimal(18, 2)")]
    public decimal LineTotal { get; private set; }

    /// <summary>
    /// Optional tax amount applied specifically to this line item.
    /// TODO: Implement tax calculation logic. Consider using Money Value Object.
    /// </summary>
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? TaxAmount { get; private set; }

    /// <summary>
    /// Optional tax rate applied to this line item.
    /// </summary>
    [Column(TypeName = "decimal(5, 2)")]
    public decimal? TaxRate { get; private set; }

    /// <summary>
    /// The unit of measure for the quantity.
    /// </summary>
    [MaxLength(20)]
    public string? UnitOfMeasure { get; private set; }

    /// <summary>
    /// Optional notes specific to this invoice line.
    /// </summary>
    public string? Notes { get; private set; }

    /// <summary>
    /// Private constructor for EF Core hydration.
    /// </summary>
    private InvoiceLine() : base(Guid.Empty) { }

    /// <summary>
    /// Internal constructor, called from Invoice.AddLine.
    /// </summary>
    internal InvoiceLine(
        Guid invoiceId,
        int lineNumber,
        string description,
        decimal quantity,
        decimal unitPrice,
        Guid? purchaseOrderLineId = null,
        Guid? productId = null,
        decimal? taxAmount = null, // Allow specifying line tax
        decimal? taxRate = null,
        string? unitOfMeasure = null,
        string? notes = null) : base(Guid.NewGuid())
    {
        // Validation
        if (invoiceId == Guid.Empty) throw new ArgumentException("InvoiceId cannot be empty.", nameof(invoiceId));
        if (lineNumber <= 0) throw new ArgumentOutOfRangeException(nameof(lineNumber), "Line number must be positive.");
        ArgumentException.ThrowIfNullOrWhiteSpace(description);
        if (quantity <= 0) throw new ArgumentOutOfRangeException(nameof(quantity), "Quantity must be positive.");
        if (unitPrice < 0) throw new ArgumentOutOfRangeException(nameof(unitPrice), "Unit price cannot be negative.");
        if (taxAmount.HasValue && taxAmount < 0) throw new ArgumentOutOfRangeException(nameof(taxAmount), "Tax amount cannot be negative.");
        if (taxRate.HasValue && (taxRate < 0 || taxRate > 100)) throw new ArgumentOutOfRangeException(nameof(taxRate), "Tax rate must be between 0 and 100.");

        InvoiceId = invoiceId;
        LineNumber = lineNumber;
        PurchaseOrderLineId = purchaseOrderLineId; // Link to PO line for matching
        ProductId = productId;
        Description = description;
        Quantity = quantity;
        UnitPrice = unitPrice;
        TaxAmount = taxAmount; // Store line tax if provided
        TaxRate = taxRate;
        UnitOfMeasure = unitOfMeasure;
        Notes = notes;

        RecalculateLineTotal(); // Calculate initial total
    }

    /// <summary>
    /// Recalculates the line total based on Quantity and UnitPrice.
    /// </summary>
    private void RecalculateLineTotal()
    {
        LineTotal = Quantity * UnitPrice;
        // Note: This doesn't include tax here. Header recalculation sums line totals and line taxes (or applies header tax).
    }

    // Invoice lines are typically immutable once the invoice is received/processed.
    // Updates usually involve credit notes or adjustments rather than modifying the original line.
    // internal void UpdateQuantity(decimal newQuantity) { ... } // Add with caution if needed
    // internal void UpdatePrice(decimal newPrice) { ... } // Add with caution if needed
}
