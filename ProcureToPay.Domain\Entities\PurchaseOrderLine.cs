using System;
using ProcureToPay.Domain.Entities;
using ProcureToPay.Domain.Events;
using ProcureToPay.Domain.ValueObjects; // For Money VO
using ProcureToPay.Domain.Enums; // For UnitOfMeasure enum
using ProcureToPay.Domain.Exceptions; // For DomainStateException (optional)

namespace ProcureToPay.Domain.Entities
{
    /// <summary>
    /// Represents a line item within a Purchase Order.
    /// References a specific VendorProduct offering and snapshots relevant details at the time of order creation.
    /// Modifications like quantity updates should be controlled via the PurchaseOrder aggregate root.
    /// </summary>
    public class PurchaseOrderLine : BaseEntity<Guid> // Use generic BaseEntity
    {
        // Foreign Keys
        /// <summary>
        /// Foreign key to the parent Purchase Order.
        /// </summary>
        public Guid PurchaseOrderId { get; private set; }

        /// <summary>
        /// Foreign key to the specific VendorProduct being ordered.
        /// </summary>
        public Guid VendorProductId { get; private set; } // Corrected: Changed from ProductId

        // Snapshots (captured at time of adding line to PO)
        /// <summary>
        /// Snapshot of the VendorProduct's Vendor-specific SKU (if available) or the master ProductDefinition SKU.
        /// </summary>
        public string SkuSnapshot { get; private set; } = string.Empty;

        /// <summary>
        /// Snapshot of the master ProductDefinition's description.
        /// </summary>
        public string DescriptionSnapshot { get; private set; } = string.Empty;

        /// <summary>
        /// The price per unit (Money VO) snapshotted from the VendorProduct at the time of order.
        /// </summary>
        public Money UnitPriceSnapshot { get; private set; } = null!; // Added Snapshot Property

        /// <summary>
        /// The unit of measure snapshotted from the VendorProduct.
        /// </summary>
        public UnitOfMeasure UnitOfMeasureSnapshot { get; private set; } // Added Snapshot Property


        // Properties
        /// <summary>
        /// The quantity ordered. Can be fractional.
        /// </summary>
        public decimal Quantity { get; private set; }

        /// <summary>
        /// The calculated total for this line (Quantity * UnitPriceSnapshot) as a Money Value Object.
        /// </summary>
        public Money LineTotal { get; private set; } = null!; // Uses Money VO

        /// <summary>
        /// Optional notes specific to this line item.
        /// </summary>
        public string? Notes { get; private set; }

        // Navigation Properties
        /// <summary>
        /// Navigation property to the parent Purchase Order. Virtual for lazy loading.
        /// </summary>
        public virtual PurchaseOrder PurchaseOrder { get; private set; } = null!;

        /// <summary>
        /// Navigation property to the specific VendorProduct being ordered. Virtual for lazy loading.
        /// </summary>
        public virtual VendorProduct VendorProduct { get; private set; } = null!; // Corrected: Changed from Product/ProductDefinition

        /// <summary>
        /// Collection of delivery note lines associated with this purchase order line.
        /// </summary>
        public virtual ICollection<DeliveryNoteLine> DeliveryNoteLines { get; private set; } = new HashSet<DeliveryNoteLine>();

        /// <summary>
        /// Collection of goods receipt note lines associated with this purchase order line.
        /// </summary>
        public virtual ICollection<GoodsReceiptNoteLine> GoodsReceiptNoteLines { get; private set; } = new HashSet<GoodsReceiptNoteLine>();

        /// <summary>
        /// Collection of invoice lines associated with this purchase order line.
        /// </summary>
        public virtual ICollection<InvoiceLine> InvoiceLines { get; private set; } = new HashSet<InvoiceLine>();


        /// <summary>
        /// Private parameterless constructor for EF Core hydration.
        /// Requires owned type configuration for Money Value Objects in Fluent API.
        /// </summary>
        private PurchaseOrderLine() : base(Guid.NewGuid()) // Generate ID
        { }

        /// <summary>
        /// Internal constructor to create a new line item.
        /// Should only be called from within the PurchaseOrder aggregate root.
        /// Takes details from the VendorProduct and its associated ProductDefinition for snapshotting.
        /// </summary>
        /// <param name="purchaseOrderId">ID of the parent Purchase Order.</param>
        /// <param name="vendorProduct">The specific VendorProduct being ordered (ensure ProductDefinition is loaded if needed for snapshot).</param>
        /// <param name="quantity">The quantity ordered.</param>
        /// <exception cref="ArgumentException">Thrown for invalid parameters.</exception>
        /// <exception cref="ArgumentNullException">Thrown if vendorProduct is null.</exception>
        /// <exception cref="InvalidOperationException">Thrown if required related data (ProductDefinition) is not loaded on vendorProduct.</exception>
        internal PurchaseOrderLine(
            Guid purchaseOrderId,
            VendorProduct vendorProduct, // Corrected: Parameter is VendorProduct
            decimal quantity
            ) : base(Guid.NewGuid()) // Generate ID
        {
            // Validation
            if (purchaseOrderId == Guid.Empty) throw new ArgumentException("Purchase Order ID cannot be empty.", nameof(purchaseOrderId));
            ArgumentNullException.ThrowIfNull(vendorProduct);
            if (quantity <= 0) throw new ArgumentOutOfRangeException(nameof(quantity), "Quantity must be positive.");

            // Ensure ProductDefinition is available for snapshotting (crucial!)
            // The caller (PurchaseOrder.AddLineItem) should ensure this is loaded.
            if (vendorProduct.ProductDefinition == null)
            {
                throw new InvalidOperationException("VendorProduct must have its ProductDefinition loaded to create a Purchase Order Line.");
            }
            if (vendorProduct.UnitPrice == null) // Money VO shouldn't be null
            {
                throw new InvalidOperationException("VendorProduct must have a UnitPrice defined.");
            }


            // TODO: Consider checks here or in PurchaseOrder:
            // - Is vendorProduct.IsActive?
            // - Does vendorProduct.UnitPrice.CurrencyCode match PurchaseOrder.CurrencyCode?

            PurchaseOrderId = purchaseOrderId;
            VendorProductId = vendorProduct.Id; // Corrected: Assign VendorProductId
            Quantity = quantity;

            // Create Snapshots from VendorProduct and its ProductDefinition
            SkuSnapshot = !string.IsNullOrWhiteSpace(vendorProduct.VendorSku)
                            ? vendorProduct.VendorSku // Use vendor SKU if available
                            : vendorProduct.ProductDefinition.Sku; // Fallback to master SKU
            DescriptionSnapshot = vendorProduct.ProductDefinition.Description ?? string.Empty; // Use master description
            UnitPriceSnapshot = vendorProduct.UnitPrice; // Snapshot the Money VO from VendorProduct
            UnitOfMeasureSnapshot = vendorProduct.UnitOfMeasure; // Snapshot the Enum from VendorProduct

            RecalculateLineTotal(); // Calculate initial total based on snapshots
        }

        /// <summary>
        /// Internal method to update the quantity. Ensures the line total is recalculated.
        /// Should only be called from within the PurchaseOrder aggregate root (checking PO status).
        /// </summary>
        /// <param name="newQuantity">The new quantity (must be positive).</param>
        internal void UpdateQuantity(decimal newQuantity)
        {
            if (newQuantity <= 0) throw new ArgumentOutOfRangeException(nameof(newQuantity), "Quantity must be positive.");
            if (Quantity == newQuantity) return; // No change

            Quantity = newQuantity;
            RecalculateLineTotal();
            AddDomainEvent(new PurchaseOrderLineQuantityUpdatedEvent(this.Id, newQuantity));
        }

        /// <summary>
        /// Adds or updates notes for this specific line item.
        /// </summary>
        /// <param name="note">The note text. Pass null or empty string to clear notes.</param>
        public void AddOrUpdateNote(string? note)
        {
            Notes = note;
            AddDomainEvent(new PurchaseOrderLineNoteUpdatedEvent(this.Id));
        }

        /// <summary>
        /// Recalculates the line total based on Quantity and the snapshotted UnitPrice.
        /// Sets the LineTotal property.
        /// </summary>
        private void RecalculateLineTotal()
        {
            // Ensure UnitPriceSnapshot is not null before calculation (should be guaranteed by constructor)
            if (UnitPriceSnapshot == null)
            {
                throw new InvalidOperationException("UnitPriceSnapshot cannot be null for line total calculation.");
            }
            // Use the Money operator overload for multiplication
            LineTotal = UnitPriceSnapshot * Quantity;
        }

        // No public CalculateTotal() method needed, use LineTotal property.
    }
}
