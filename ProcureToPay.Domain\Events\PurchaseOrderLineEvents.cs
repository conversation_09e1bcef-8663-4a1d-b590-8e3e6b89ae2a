using System;

namespace ProcureToPay.Domain.Events
{
    // --- PurchaseOrderLine Specific Events ---
    // Often, events are raised by the Aggregate Root (PurchaseOrder) when lines change.
    // However, if direct line changes trigger events, define them here.
    public record PurchaseOrderLineQuantityUpdatedEvent(Guid PurchaseOrderLineId, decimal NewQuantity);
    public record PurchaseOrderLineNoteUpdatedEvent(Guid PurchaseOrderLineId);
}
