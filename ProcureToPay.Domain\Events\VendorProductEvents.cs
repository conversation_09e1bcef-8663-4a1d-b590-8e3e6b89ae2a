using System;
using ProcureToPay.Domain.ValueObjects; // For Money

namespace ProcureToPay.Domain.Events
{
    // --- VendorProduct Specific Events ---
    public record VendorProductCreatedEvent(Guid VendorProductId);
    public record VendorProductPriceChangedEvent(Guid VendorProductId, Money NewPrice);
    public record VendorProductActivatedEvent(Guid VendorProductId);
    public record VendorProductDeactivatedEvent(Guid VendorProductId);
    // Add other events like VendorProductSkuUpdatedEvent, VendorProductPackagingUpdatedEvent if needed
}
