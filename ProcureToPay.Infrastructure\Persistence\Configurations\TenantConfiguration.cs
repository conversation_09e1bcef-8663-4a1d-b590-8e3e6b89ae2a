using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProcureToPay.Domain.Entities;
using System;

namespace ProcureToPay.Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// EF Core Configuration for the Tenant entity.
    /// </summary>
    public class TenantConfiguration : IEntityTypeConfiguration<Tenant>
    {
        // Define static GUID for default tenant to ensure consistent references
        public static readonly Guid DefaultTenantId = Guid.Parse("11111111-1111-1111-1111-111111111111");

        // Fixed date for seeding to ensure consistency
        private static readonly DateTime SeedDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc);

        public void Configure(EntityTypeBuilder<Tenant> builder)
        {
            // Table Mapping with explicit schema
            builder.ToTable("tenants", "public");

            // Primary Key
            builder.HasKey(t => t.Id);
            builder.Property(t => t.Id).ValueGeneratedNever(); // For seeded entities with predefined IDs

            // --- Properties ---
            builder.Property(t => t.Name)
                .IsRequired()
                .HasMaxLength(200);

            builder.Property(t => t.Identifier)
                .IsRequired()
                .HasMaxLength(100);

            // PostgreSQL-specific syntax for unique index
            builder.HasIndex(t => t.Identifier)
                .IsUnique()
                .HasDatabaseName("IX_tenants_identifier_unique");

            // Subscription plan
            builder.Property(t => t.SubscriptionPlan)
                .HasMaxLength(50)
                .IsRequired(false);

            // Contact email
            builder.Property(t => t.ContactEmail)
                .HasMaxLength(254)
                .IsRequired(false);

            // Settings as JSON
            builder.Property(t => t.Settings)
                .HasColumnType("jsonb") // PostgreSQL JSONB for structured settings
                .IsRequired(false);

            // Address properties
            builder.Property(t => t.AddressLine1)
                .HasMaxLength(200)
                .IsRequired(false);

            builder.Property(t => t.City)
                .HasMaxLength(100)
                .IsRequired(false);

            builder.Property(t => t.PostalCode)
                .HasMaxLength(20)
                .IsRequired(false);

            builder.Property(t => t.Country)
                .HasMaxLength(100)
                .IsRequired(false);

            // IsActive flag
            builder.Property(t => t.IsActive)
                .IsRequired()
                .HasDefaultValue(true);

            // Audit fields from BaseEntity
            builder.Property("CreatedAt").IsRequired();
            builder.Property("ModifiedAt").IsRequired(false);

            // --- Seeding ---
            // Seed default tenant using anonymous type for HasData
            builder.HasData(
                new
                {
                    Id = DefaultTenantId,
                    Name = "Default System Tenant",
                    Identifier = "system",
                    SubscriptionPlan = "Standard",
                    ContactEmail = "<EMAIL>",
                    IsActive = true,
                    CreatedAt = SeedDate,
                    ModifiedAt = (DateTime?)null,
                    Settings = (string?)null,
                    AddressLine1 = (string?)null,
                    City = (string?)null,
                    PostalCode = (string?)null,
                    Country = (string?)null
                }
            );
        }
    }
}

