using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using ProcureToPay.Domain.Enums;       // Required for VendorProposalStatus
using ProcureToPay.Domain.ValueObjects; // Required for Money, potentially ComplianceCertification VO
using ProcureToPay.Domain.Events;       // Assuming VendorProposal domain events namespace exists
using ProcureToPay.Domain.Exceptions;   // Assuming DomainStateException exists

// Assuming BaseEntity<Guid>, Vendor, RequestForProposal entities exist
namespace ProcureToPay.Domain.Entities
{
    /// <summary>
    /// Represents a formal proposal submitted by a Vendor in response to a sourcing event (e.g., RFP, RFQ).
    /// Contains proposed terms, pricing, commitments, and compliance details.
    /// </summary>
    public class VendorProposal : BaseEntity<Guid> // Assuming BaseEntity<Guid>
    {
        /// <summary>
        /// Foreign Key for multi-tenancy.
        /// </summary>
        public Guid TenantId { get; private set; }

        /// <summary>
        /// Foreign key linking to the Vendor submitting the proposal.
        /// </summary>
        public Guid VendorId { get; private set; }

        /// <summary>
        /// Foreign key linking to the sourcing event (e.g., RequestForProposal) this proposal responds to.
        /// </summary>
        public Guid RequestForProposalId { get; private set; } // Assuming link to RFP, adjust if RFQ/RFI etc.

        /// <summary>
        /// Date and time the proposal was submitted.
        /// </summary>
        public DateTime SubmissionDate { get; private set; }

        /// <summary>
        /// Current status of the vendor proposal (e.g., Submitted, UnderReview, Awarded, Rejected).
        /// </summary>
        public VendorProposalStatus Status { get; private set; }

        /// <summary>
        /// The total proposed value or cost from the vendor. Uses Money VO.
        /// </summary>
        [System.ComponentModel.DataAnnotations.Schema.NotMapped]
        public Money ProposedTotalValue { get; private set; } = null!; // Changed from ProposedAmount

        /// <summary>
        /// The total proposed value as a decimal.
        /// </summary>
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? TotalProposedValue { get; private set; }

        /// <summary>
        /// Number of days the proposal remains valid from the submission date.
        /// </summary>
        public int ValidityPeriodDays { get; private set; }

        /// <summary>
        /// The date until which the proposal is valid.
        /// </summary>
        public DateTime? ValidityEndDate { get; private set; }

        // --- Proposal Details (from Requirements) ---

        /// <summary>
        /// Description of any alternate payment terms offered by the vendor.
        /// </summary>
        public string? AlternatePaymentTerms { get; private set; } // Added

        /// <summary>
        /// Description of any value-added services included in the proposal.
        /// </summary>
        public string? ValueAddedServices { get; private set; } // Added

        /// <summary>
        /// Text outlining the vendor's sustainability commitments related to this proposal.
        /// </summary>
        public string? SustainabilityCommitments { get; private set; } // Added

        /// <summary>
        /// Text describing any proposed risk-sharing clauses or mechanisms.
        /// </summary>
        public string? RiskSharingClauses { get; private set; } // Added

        /// <summary>
        /// Text describing any performance guarantees offered by the vendor.
        /// </summary>
        public string? PerformanceGuarantees { get; private set; } // Added

        /// <summary>
        /// Information disclosed about subcontractors involved in fulfilling the proposal.
        /// </summary>
        public string? SubcontractorDisclosures { get; private set; } // Added

        /// <summary>
        /// Stores details or references to compliance certifications as JSON. Requires external logic.
        /// Alternative: List<ComplianceCertification> with separate entity/VO and OwnsMany config.
        /// </summary>
        public string? ComplianceCertificationsJson { get; private set; } // Added

        /// <summary>
        /// Additional comments or notes about the proposal.
        /// </summary>
        public string? Comments { get; private set; }

        // --- Lifecycle & Versioning ---
        public int Version { get; private set; }       // Added: Versioning
        public bool IsDeleted { get; private set; }   // Added: Soft Delete

        // --- Navigation Properties ---
        public virtual Vendor Vendor { get; private set; } = null!;
        public virtual RequestForProposal RequestForProposal { get; private set; } = null!; // Assuming RFP entity
        // Optional: Link to resulting Contract if proposal is awarded
        // public Guid? ResultingContractId { get; private set; }
        // public virtual Contract? ResultingContract { get; private set; }


        /// <summary>
        /// Private constructor for EF Core.
        /// </summary>
        private VendorProposal() : base(Guid.NewGuid())
        {
            ProposedTotalValue = new Money(0, "XXX"); // Placeholder
        }

        /// <summary>
        /// Creates a new Vendor Proposal instance. Typically created in a 'Submitted' status.
        /// </summary>
        public VendorProposal(
            Guid id,
            Guid tenantId,
            Guid vendorId,
            Guid requestForProposalId,
            DateTime submissionDate,
            Money proposedTotalValue, // Use Money VO
            int validityPeriodDays,
            string? alternatePaymentTerms = null,
            string? valueAddedServices = null,
            string? sustainabilityCommitments = null,
            string? riskSharingClauses = null,
            string? performanceGuarantees = null,
            string? subcontractorDisclosures = null,
            string? complianceCertificationsJson = null,
            string? comments = null
            ) : base(id)
        {
            // Validation
            if (tenantId == Guid.Empty) throw new ArgumentException("TenantId cannot be empty.", nameof(tenantId));
            if (vendorId == Guid.Empty) throw new ArgumentException("VendorId cannot be empty.", nameof(vendorId));
            if (requestForProposalId == Guid.Empty) throw new ArgumentException("RequestForProposalId cannot be empty.", nameof(requestForProposalId));
            if (submissionDate == default) throw new ArgumentException("Submission date must be specified.", nameof(submissionDate));
            ArgumentNullException.ThrowIfNull(proposedTotalValue);
            if (proposedTotalValue.Amount < 0) throw new ArgumentOutOfRangeException(nameof(proposedTotalValue), "Proposed value cannot be negative.");
            if (validityPeriodDays <= 0) throw new ArgumentOutOfRangeException(nameof(validityPeriodDays), "Validity period must be positive.");

            TenantId = tenantId;
            VendorId = vendorId;
            RequestForProposalId = requestForProposalId;
            SubmissionDate = submissionDate.ToUniversalTime(); // Store as UTC
            ProposedTotalValue = proposedTotalValue;
            TotalProposedValue = proposedTotalValue.Amount; // Set the decimal property from the Money value object
            ValidityPeriodDays = validityPeriodDays;
            ValidityEndDate = submissionDate.AddDays(validityPeriodDays); // Calculate validity end date
            AlternatePaymentTerms = alternatePaymentTerms;
            ValueAddedServices = valueAddedServices;
            SustainabilityCommitments = sustainabilityCommitments;
            RiskSharingClauses = riskSharingClauses;
            PerformanceGuarantees = performanceGuarantees;
            SubcontractorDisclosures = subcontractorDisclosures;
            ComplianceCertificationsJson = complianceCertificationsJson;
            Comments = comments;

            Status = VendorProposalStatus.Submitted; // Initial status upon creation/submission
            Version = 1;
            IsDeleted = false;

            AddDomainEvent(new VendorProposalSubmittedEvent(this.Id, this.VendorId, this.RequestForProposalId)); // Example event
        }

        // --- Domain Methods ---

        public void UpdateStatus(VendorProposalStatus newStatus)
        {
            // Add validation for allowed status transitions if needed
            // e.g., if (Status == VendorProposalStatus.Awarded && newStatus == VendorProposalStatus.Rejected) throw ...
            if (Status == newStatus) return;

            var oldStatus = Status;
            Status = newStatus;
            IncrementVersion(); // Status change is a version change
            AddDomainEvent(new VendorProposalStatusChangedEvent(this.Id, oldStatus, newStatus));
        }

        public void UpdateValidity(int newValidityPeriodDays)
        {
            if (newValidityPeriodDays <= 0) throw new ArgumentOutOfRangeException(nameof(newValidityPeriodDays), "Validity period must be positive.");
            if (ValidityPeriodDays == newValidityPeriodDays) return;

            ValidityPeriodDays = newValidityPeriodDays;
            IncrementVersion();
            // AddDomainEvent(new VendorProposalValidityUpdatedEvent(this.Id, newValidityPeriodDays));
        }

        public void UpdateProposedValue(Money newValue)
        {
            // Add validation - can value be updated in current status? Currency match? Non-negative?
            if (Status != VendorProposalStatus.Submitted && Status != VendorProposalStatus.UnderReview) // Example
                throw new DomainStateException($"Cannot update value when proposal status is '{Status}'.");
            ArgumentNullException.ThrowIfNull(newValue);
            if (newValue.CurrencyCode != this.ProposedTotalValue.CurrencyCode)
                throw new ArgumentException("New value currency must match original proposal currency.", nameof(newValue));
            if (newValue.Amount < 0) throw new ArgumentOutOfRangeException(nameof(newValue), "Proposed value cannot be negative.");

            if (ProposedTotalValue == newValue) return;

            ProposedTotalValue = newValue;
            TotalProposedValue = newValue.Amount; // Update the decimal property as well
            IncrementVersion();
            AddDomainEvent(new VendorProposalValueUpdatedEvent(this.Id, newValue));
        }

        // Method to update other details - combine or keep separate?
        public void UpdateProposalDetails(
            string? alternatePaymentTerms,
            string? valueAddedServices,
            string? sustainabilityCommitments,
            string? riskSharingClauses,
            string? performanceGuarantees,
            string? subcontractorDisclosures,
            string? complianceCertificationsJson,
            string? comments)
        {
            // Add validation? Check status?
            if (Status != VendorProposalStatus.Submitted && Status != VendorProposalStatus.UnderReview) // Example
                throw new DomainStateException($"Cannot update details when proposal status is '{Status}'.");

            bool changed = false;
            if (AlternatePaymentTerms != alternatePaymentTerms) { AlternatePaymentTerms = alternatePaymentTerms; changed = true; }
            if (ValueAddedServices != valueAddedServices) { ValueAddedServices = valueAddedServices; changed = true; }
            if (SustainabilityCommitments != sustainabilityCommitments) { SustainabilityCommitments = sustainabilityCommitments; changed = true; }
            if (RiskSharingClauses != riskSharingClauses) { RiskSharingClauses = riskSharingClauses; changed = true; }
            if (PerformanceGuarantees != performanceGuarantees) { PerformanceGuarantees = performanceGuarantees; changed = true; }
            if (SubcontractorDisclosures != subcontractorDisclosures) { SubcontractorDisclosures = subcontractorDisclosures; changed = true; }
            if (ComplianceCertificationsJson != complianceCertificationsJson) { ComplianceCertificationsJson = complianceCertificationsJson; changed = true; }
            if (Comments != comments) { Comments = comments; changed = true; }

            if (changed)
            {
                IncrementVersion();
                AddDomainEvent(new VendorProposalDetailsUpdatedEvent(this.Id));
            }
        }

        public void MarkAsDeleted()
        {
            if (IsDeleted) return;
            IsDeleted = true;
            IncrementVersion();
            AddDomainEvent(new VendorProposalDeletedEvent(this.Id));
        }

        public void Restore()
        {
            if (!IsDeleted) return;
            IsDeleted = false;
            IncrementVersion();
            AddDomainEvent(new VendorProposalRestoredEvent(this.Id));
        }

        private void IncrementVersion()
        {
            Version++;
            // AddDomainEvent(new VendorProposalVersionIncrementedEvent(this.Id, this.Version));
        }
    }

    // --- Placeholder Value Object/Record Definitions (Place in Domain/ValueObjects) ---
    /*
    public record ComplianceCertification(string Name, string IssuingBody, DateTime ExpiryDate, string Reference);
    */

    // --- Placeholder Domain Event Definitions (Place in Domain/Events) ---
    /*
    namespace ProcureToPay.Domain.Events
    {
        public record VendorProposalSubmittedEvent(Guid ProposalId, Guid VendorId, Guid RfpId); // Or RFQ/RFI Id
        public record VendorProposalStatusChangedEvent(Guid ProposalId, VendorProposalStatus OldStatus, VendorProposalStatus NewStatus);
        public record VendorProposalValueUpdatedEvent(Guid ProposalId, Money NewValue);
        public record VendorProposalDetailsUpdatedEvent(Guid ProposalId);
        public record VendorProposalDeletedEvent(Guid ProposalId);
        public record VendorProposalRestoredEvent(Guid ProposalId);
        // public record VendorProposalVersionIncrementedEvent(Guid ProposalId, int NewVersion);
    }
    */
}
