# PostgreSQL Migration Guide for ProcureToPay

This guide provides best practices for working with Entity Framework Core migrations targeting PostgreSQL in the ProcureToPay solution.

## Table of Contents

1. [PostgreSQL Syntax Requirements](#postgresql-syntax-requirements)
2. [Entity Configuration Best Practices](#entity-configuration-best-practices)
3. [Migration Workflow](#migration-workflow)
4. [Troubleshooting](#troubleshooting)
5. [Scripts Reference](#scripts-reference)

## PostgreSQL Syntax Requirements

PostgreSQL has specific syntax requirements that differ from SQL Server:

### Column and Table Names

- PostgreSQL is case-sensitive and defaults to lowercase
- Use double quotes for identifiers that need case preservation: `"ColumnName"`
- Avoid using square brackets `[ColumnName]` which is SQL Server syntax

### Filtered Indexes

**SQL Server syntax (WRONG):**
```csharp
builder.HasIndex(c => c.Code)
    .IsUnique()
    .HasFilter("[Code] IS NOT NULL");
```

**PostgreSQL syntax (CORRECT):**
```csharp
builder.HasIndex(c => c.Code)
    .IsUnique()
    .HasFilter("\"Code\" IS NOT NULL");
```

### Schema References

**SQL Server syntax (WRONG):**
```csharp
builder.ToTable("Categories", "dbo");
```

**PostgreSQL syntax (CORRECT):**
```csharp
builder.ToTable("categories", "public");
```

### Data Types

| SQL Server | PostgreSQL | EF Core Configuration |
|------------|------------|----------------------|
| `nvarchar(max)` | `text` | `.HasColumnType("text")` |
| `datetime2` | `timestamp with time zone` | `.HasColumnType("timestamp with time zone")` |
| `decimal(18,2)` | `numeric(18,2)` | `.HasColumnType("numeric(18,2)")` |
| `uniqueidentifier` | `uuid` | (No special configuration needed) |

## Entity Configuration Best Practices

### Table and Column Naming

Use snake_case for table and column names in PostgreSQL:

```csharp
builder.ToTable("payment_transactions");
builder.Property(e => e.TransactionDate).HasColumnName("transaction_date");
```

### Filtered Unique Indexes

For nullable columns with unique constraints:

```csharp
builder.HasIndex(e => e.Code)
    .IsUnique()
    .HasFilter("\"Code\" IS NOT NULL")
    .HasDatabaseName("IX_entities_code_unique");
```

### Concurrency Tokens

Use PostgreSQL's `xmin` system column for optimistic concurrency:

```csharp
builder.Property<uint>("xmin")
    .HasColumnType("xid")
    .ValueGeneratedOnAddOrUpdate()
    .IsConcurrencyToken();
```

### JSON Data

Use PostgreSQL's native JSONB type:

```csharp
builder.Property(e => e.Metadata)
    .HasColumnType("jsonb");
```

### Tenant-Specific Unique Constraints

For multi-tenant applications:

```csharp
builder.HasIndex(e => new { e.TenantId, e.Code })
    .IsUnique()
    .HasDatabaseName("IX_entities_tenant_id_code_unique");
```

### Seeding Data

Always include all required properties, including audit fields:

```csharp
builder.HasData(
    new
    {
        Id = Guid.Parse("11111111-1111-1111-1111-111111111111"),
        Name = "Example",
        CreatedAt = SeedDate,
        ModifiedAt = (DateTime?)null
    }
);
```

## Migration Workflow

### Clean Migration Process

For a fresh start with correct PostgreSQL syntax:

1. **Archive existing migrations**
2. **Create a new initial migration**
3. **Verify PostgreSQL compatibility**
4. **Generate and apply SQL script**

Use the provided `clean-postgres-migrations.ps1` script:

```powershell
.\clean-postgres-migrations.ps1 -NewMigrationName "CleanPostgreSQLSchema"
```

### Adding New Migrations

When adding new migrations:

```powershell
.\create-ef-migration.ps1 -MigrationName "AddNewFeature" -OutputDir "Persistence\Migrations"
```

Always verify the generated migration files for PostgreSQL compatibility.

### Applying Migrations

For .NET Aspire projects:

```powershell
# Generate SQL script
.\generate-migration-sql.ps1

# Execute SQL script against container
.\execute-migration-sql.ps1
```

## Troubleshooting

### Common Issues

1. **Error: schema "common" does not exist**
   - Ensure schema names are correctly quoted: `"public"` not `[public]`

2. **Error: relation "Categories" does not exist**
   - PostgreSQL defaults to lowercase table names: use `categories` not `Categories`

3. **Error: column "Code" does not exist**
   - Check case sensitivity: use `"Code"` with quotes if preserving case

### Fixing Syntax Issues

If you encounter SQL Server syntax in migrations:

1. Fix the entity configurations (IEntityTypeConfiguration<T> classes)
2. Run `.\clean-postgres-migrations.ps1` to create a clean migration

## Scripts Reference

| Script | Purpose |
|--------|---------|
| `clean-postgres-migrations.ps1` | Create clean PostgreSQL-compatible migrations |
| `generate-migration-sql.ps1` | Generate SQL script from migrations |
| `execute-migration-sql.ps1` | Execute SQL script against PostgreSQL container |
| `create-ef-migration.ps1` | Add a new migration |
| `apply-aspire-migrations.ps1` | Apply migrations programmatically |

## Example: Complete Migration Reset

```powershell
# 1. Clean up and create new migration
.\clean-postgres-migrations.ps1 -NewMigrationName "CleanInitialSchema"

# 2. Start Aspire application
cd ProcureToPaySolution.AppHost
dotnet run

# 3. Execute migration SQL
cd ..
.\execute-migration-sql.ps1
```
