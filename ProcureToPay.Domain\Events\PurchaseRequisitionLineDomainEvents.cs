﻿using ProcureToPay.Domain.ValueObjects;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.NetworkInformation;
using System.Text;
using System.Threading.Tasks;
using ProcureToPay.Domain.Enums;       // Required for RequisitionStatus


namespace ProcureToPay.Domain.Events
{
    public record RequisitionLinePriceUpdatedEvent(Guid LineId, Money? NewPrice);
    public record RequisitionLineQuantityUpdatedEvent(Guid LineId, decimal NewQuantity);
    public record RequisitionLineDetailsUpdatedEvent(Guid LineId);
}