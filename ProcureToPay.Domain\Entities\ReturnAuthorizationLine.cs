﻿using System;
using ProcureToPay.Domain.Enums; // Potentially line-specific status/reason
using ProcureToPay.Domain.ValueObjects;

namespace ProcureToPay.Domain.Entities
{
    /// <summary>
    /// Represents a specific line item being returned as part of a Return Authorization.
    /// </summary>
    public class ReturnAuthorizationLine : BaseEntity<Guid>
    {
        /// <summary>
        /// Line number for ordering within the return authorization.
        /// </summary>
        public int LineNumber { get; private set; }

        /// <summary>
        /// Foreign Key to the parent Return Authorization header.
        /// </summary>
        public Guid ReturnAuthorizationId { get; private set; }

        /// <summary>
        /// Optional Foreign Key linking back to the original Sales Order Line being returned.
        /// </summary>
        public Guid? OriginalSalesOrderLineId { get; private set; }

        /// <summary>
        /// Optional Foreign Key linking to the Sales Order Line.
        /// </summary>
        [System.ComponentModel.DataAnnotations.Schema.NotMapped]
        public Guid? SalesOrderLineId { get; private set; }

        /// <summary>
        /// Optional Foreign Key to the Sales Order this line relates to.
        /// </summary>
        public Guid? SalesOrderId { get; private set; }

        /// <summary>
        /// Optional Foreign Key to the Sales Order Line Number this line relates to.
        /// </summary>
        public int? SalesOrderLineNumber { get; private set; }

        /// <summary>
        /// Optional Foreign Key linking back to the original Invoice Line being returned.
        /// </summary>
        [System.ComponentModel.DataAnnotations.Schema.NotMapped]
        public Guid? InvoiceLineId { get; private set; }

        /// <summary>
        /// Optional Foreign Key to the Invoice this line relates to.
        /// </summary>
        public Guid? InvoiceId { get; private set; }

        /// <summary>
        /// Optional Foreign Key to the Invoice Line Number this line relates to.
        /// </summary>
        public int? InvoiceLineNumber { get; private set; }

        /// <summary>
        /// The condition of the item being returned.
        /// </summary>
        public string? ItemCondition { get; private set; }

        // Link to the product being returned (either master or vendor-specific)
        public Guid? ProductDefinitionId { get; private set; }
        public Guid? VendorProductId { get; private set; }

        /// <summary>
        /// Foreign Key to the Product.
        /// </summary>
        public Guid ProductId { get; private set; }

        /// <summary>
        /// Snapshot of the SKU at the time of return authorization.
        /// </summary>
        public string SkuSnapshot { get; private set; } = null!;

        /// <summary>
        /// Snapshot of the description at the time of return authorization.
        /// </summary>
        public string DescriptionSnapshot { get; private set; } = null!;

        /// <summary>
        /// The quantity being authorized for return.
        /// </summary>
        public decimal QuantityAuthorized { get; private set; }

        /// <summary>
        /// The unit of measure for the quantity.
        /// </summary>
        public UnitOfMeasure UnitOfMeasure { get; private set; } // Snapshot from original line/product

        /// <summary>
        /// The actual quantity received back from the customer.
        /// </summary>
        public decimal QuantityReceived { get; private set; }

        /// <summary>
        /// The reason provided for returning this specific item.
        /// </summary>
        public string ReasonForReturn { get; private set; } = null!;

        /// <summary>
        /// The action requested or approved for this specific line item.
        /// </summary>
        public ReturnAction RequestedAction { get; private set; }


        // --- Navigation Properties ---
        public virtual ReturnAuthorization ReturnAuthorization { get; private set; } = null!;
        public virtual SalesOrderLine? OriginalSalesOrderLine { get; private set; }
        public virtual SalesOrderLine? SalesOrderLine { get; private set; }
        public virtual InvoiceLine? InvoiceLine { get; private set; }
        public virtual ProductDefinition? ProductDefinition { get; private set; }
        public virtual VendorProduct? VendorProduct { get; private set; }
        public virtual Product Product { get; private set; } = null!;


        private ReturnAuthorizationLine() : base(Guid.NewGuid()) { }

        /// <summary>
        /// Creates a new Return Authorization Line. Typically called via ReturnAuthorization aggregate.
        /// </summary>
        internal ReturnAuthorizationLine(
            Guid id,
            Guid returnAuthorizationId,
            int lineNumber,
            string skuSnapshot,
            string descriptionSnapshot,
            decimal quantityAuthorized,
            UnitOfMeasure unitOfMeasure,
            string reasonForReturn,
            ReturnAction requestedAction,
            Guid productId,
            Guid? originalSalesOrderLineId = null,
            Guid? salesOrderLineId = null,
            Guid? invoiceLineId = null,
            string? itemCondition = null,
            Guid? productDefinitionId = null,
            Guid? vendorProductId = null) : base(id)
        {
            // Validation
            if (returnAuthorizationId == Guid.Empty) throw new ArgumentException("ReturnAuthorizationId cannot be empty.", nameof(returnAuthorizationId));
            if (lineNumber <= 0) throw new ArgumentOutOfRangeException(nameof(lineNumber), "Line number must be positive.");
            ArgumentException.ThrowIfNullOrWhiteSpace(skuSnapshot);
            ArgumentException.ThrowIfNullOrWhiteSpace(descriptionSnapshot);
            if (quantityAuthorized <= 0) throw new ArgumentOutOfRangeException(nameof(quantityAuthorized), "Quantity authorized must be positive.");
            ArgumentException.ThrowIfNullOrWhiteSpace(reasonForReturn);
            // Add other validation as needed

            ReturnAuthorizationId = returnAuthorizationId;
            LineNumber = lineNumber;
            OriginalSalesOrderLineId = originalSalesOrderLineId;
            SalesOrderLineId = salesOrderLineId;
            // If SalesOrderLineId is provided, we need to parse it to get SalesOrderId and SalesOrderLineNumber
            if (salesOrderLineId.HasValue)
            {
                // This is a placeholder. In a real implementation, you would need to look up the SalesOrderId and LineNumber
                // based on the SalesOrderLineId, or change the API to accept SalesOrderId and SalesOrderLineNumber directly.
                SalesOrderId = null;
                SalesOrderLineNumber = null;
            }
            InvoiceLineId = invoiceLineId;
            // If InvoiceLineId is provided, we need to parse it to get InvoiceId and InvoiceLineNumber
            if (invoiceLineId.HasValue)
            {
                // This is a placeholder. In a real implementation, you would need to look up the InvoiceId and LineNumber
                // based on the InvoiceLineId, or change the API to accept InvoiceId and InvoiceLineNumber directly.
                InvoiceId = null;
                InvoiceLineNumber = null;
            }
            ItemCondition = itemCondition;
            ProductId = productId;
            ProductDefinitionId = productDefinitionId;
            VendorProductId = vendorProductId;
            SkuSnapshot = skuSnapshot;
            DescriptionSnapshot = descriptionSnapshot;
            QuantityAuthorized = quantityAuthorized;
            UnitOfMeasure = unitOfMeasure;
            ReasonForReturn = reasonForReturn;
            RequestedAction = requestedAction;
            QuantityReceived = 0m; // Initialize received quantity
        }

        /// <summary>
        /// Records the quantity received for this line item. Called via ReturnAuthorization aggregate.
        /// </summary>
        internal void ReceiveQuantity(decimal quantity)
        {
            if (quantity < 0) throw new ArgumentOutOfRangeException(nameof(quantity), "Received quantity cannot be negative.");
            // Potentially check against QuantityAuthorized? Allow over-receiving?
            // if (QuantityReceived + quantity > QuantityAuthorized) throw new InvalidOperationException("Received quantity exceeds authorized quantity.");

            QuantityReceived += quantity;
            // AddDomainEvent(...)
        }
    }
}
