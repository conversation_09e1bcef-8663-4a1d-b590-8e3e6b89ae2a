# BaseEntity Migration Guide

This document outlines the process for migrating the `BaseEntity<TId>` class to include audit fields and improved domain event handling.

## Migration Overview

The migration adds the following features to `BaseEntity<TId>`:

1. **Audit Fields**:
   - `CreatedAt`: DateTime when the entity was created
   - `ModifiedAt`: Nullable DateTime when the entity was last modified

2. **Improved Domain Event Handling**:
   - Strongly typed domain events using `IDomainEvent` interface
   - Backward compatibility for existing domain events

## Migration Steps

### 1. Code Changes

1. Create the `IDomainEvent` interface:
   ```csharp
   // ProcureToPay.Domain/Events/IDomainEvent.cs
   public interface IDomainEvent
   {
       DateTime OccurredOn { get; }
       Guid EventId { get; }
   }
   ```

2. Create the `DomainEvent` base class:
   ```csharp
   // ProcureToPay.Domain/Events/DomainEvent.cs
   public abstract class DomainEvent : IDomainEvent
   {
       public DateTime OccurredOn { get; } = DateTime.UtcNow;
       public Guid EventId { get; } = Guid.NewGuid();
   }
   ```

3. Update the `BaseEntity<TId>` class:
   ```csharp
   // ProcureToPay.Domain/Entities/BaseEntity.cs
   public abstract class BaseEntity<TId> where TId : struct, IEquatable<TId>
   {
       private readonly List<IDomainEvent> _domainEvents = new List<IDomainEvent>();

       public TId Id { get; protected set; }
       public DateTime CreatedAt { get; private set; }
       public DateTime? ModifiedAt { get; private set; }
       public IReadOnlyCollection<IDomainEvent> DomainEvents => _domainEvents.AsReadOnly();

       protected BaseEntity(TId id)
       {
           if (id.Equals(default))
           {
               throw new ArgumentException("Entity ID cannot be the default value.", nameof(id));
           }
           Id = id;
           CreatedAt = DateTime.UtcNow;
       }

       public void UpdateModifiedAt()
       {
           ModifiedAt = DateTime.UtcNow;
       }

       protected void AddDomainEvent(IDomainEvent domainEvent)
       {
           _domainEvents.Add(domainEvent ?? throw new ArgumentNullException(nameof(domainEvent)));
       }

       // Backward compatibility
       protected void AddDomainEvent(object domainEvent)
       {
           ArgumentNullException.ThrowIfNull(domainEvent);

           if (domainEvent is IDomainEvent typedEvent)
           {
               _domainEvents.Add(typedEvent);
           }
           else
           {
               _domainEvents.Add(new LegacyDomainEvent(domainEvent));
           }
       }

       // ... rest of the class
   }
   ```

### 2. Database Migration

1. Create the migration:
   ```powershell
   ./create-migration.ps1 AddAuditFieldsToBaseEntity
   ```

2. Review the migration file to ensure it adds the `CreatedAt` and `ModifiedAt` columns to all entity tables.

3. Apply the migration:
   ```powershell
   # For .NET Aspire projects, migrations are applied automatically at application startup
   # Run the application from the AppHost project
   dotnet run --project ProcureToPaySolution.AppHost
   ```

   Note: The migration is applied in the `Program.cs` file of the WebApp project:
   ```csharp
   // In WebApp/Program.cs
   try
   {
       Console.WriteLine("---> WebApp: Checking if database exists...");
       if (!dbContext.Database.CanConnect())
       {
           Console.WriteLine("---> WebApp: Cannot connect to database. Attempting to create it...");
           dbContext.Database.EnsureCreated();
           Console.WriteLine("---> WebApp: Database created successfully.");
       }
       else
       {
           Console.WriteLine("---> WebApp: Database exists and connection successful.");
       }

       // Now apply migrations
       Console.WriteLine("---> WebApp: Applying migrations...");
       dbContext.Database.Migrate();
       Console.WriteLine("---> WebApp: Migrations applied successfully.");
   }
   catch (Exception dbEx)
   {
       Console.WriteLine($"---> WebApp ERROR: Database operation failed: {dbEx.Message}");
       // Error handling...
   }
   ```

### 3. Testing

1. Verify that new entities have their `CreatedAt` field automatically set.
2. Verify that calling `UpdateModifiedAt()` correctly updates the `ModifiedAt` field.
3. Verify that existing domain events continue to work with the new implementation.
4. Verify that new strongly typed domain events work correctly.

## Rollback Plan

If issues are encountered, the following rollback steps can be taken:

1. Revert the code changes:
   ```powershell
   git checkout -- ProcureToPay.Domain/Entities/BaseEntity.cs
   ```

2. Remove the migration:
   ```powershell
   dotnet ef migrations remove --project ProcureToPay.Infrastructure --startup-project ProcureToPay.WebApp/ProcureToPay.WebApp
   ```

## Next Steps

After successfully migrating `BaseEntity<TId>`, consider the following next steps:

1. Update entity configuration files to properly configure the audit fields.
2. Create strongly typed domain events for important domain operations.
3. Implement a domain event dispatcher to handle the events.
4. Add automatic updating of `ModifiedAt` in the `ApplicationDbContext.SaveChanges` method.
