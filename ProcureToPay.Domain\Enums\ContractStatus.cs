﻿namespace ProcureToPay.Domain.Enums;

/// <summary>
/// Represents the status of a Contract.
/// </summary>
public enum ContractStatus
{
    /// <summary>
    /// Contract is being drafted or negotiated.
    /// </summary>
    Draft = 0,

    /// <summary>
    /// Contract has been agreed upon and is active.
    /// </summary>
    Active = 1,

    /// <summary>
    /// Contract period has ended.
    /// </summary>
    Expired = 2,

    /// <summary>
    /// Contract was terminated before its end date.
    /// </summary>
    Terminated = 3,

    /// <summary>
    /// Contract is pending final approval before becoming active.
    /// </summary>
    PendingApproval = 4
}
