using System.Threading;
using System.Threading.Tasks;

namespace ProcureToPay.Application.Interfaces
{
    /// <summary>
    /// Factory for creating application database contexts.
    /// </summary>
    public interface ICategoryDbContextFactory
    {
        /// <summary>
        /// Creates a new instance of an IApplicationDbContext.
        /// </summary>
        /// <returns>A new instance of an IApplicationDbContext.</returns>
        IApplicationDbContext CreateDbContext();

        /// <summary>
        /// Creates a new instance of an IApplicationDbContext asynchronously.
        /// </summary>
        /// <param name="cancellationToken">A token to cancel the operation.</param>
        /// <returns>A task representing the asynchronous operation. The task result contains a new instance of an IApplicationDbContext.</returns>
        Task<IApplicationDbContext> CreateDbContextAsync(CancellationToken cancellationToken = default);
    }
}
