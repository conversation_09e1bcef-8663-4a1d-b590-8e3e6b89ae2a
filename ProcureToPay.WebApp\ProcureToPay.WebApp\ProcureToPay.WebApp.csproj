﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>aspnet-ProcureToPay.WebApp-a9fc4a73-e57a-4dbf-bf7c-d404b9de1554</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\ProcureToPay.Application\ProcureToPay.Application.csproj" />
    <ProjectReference Include="..\..\ProcureToPay.Infrastructure\ProcureToPay.Infrastructure.csproj" />
    <ProjectReference Include="..\..\ProcureToPaySolution.ServiceDefaults\ProcureToPaySolution.ServiceDefaults.csproj" />
    <ProjectReference Include="..\ProcureToPay.WebApp.Client\ProcureToPay.WebApp.Client.csproj" />
    <PackageReference Include="Aspire.Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.Server" Version="9.0.3" />
    <PackageReference Include="Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore" Version="9.0.3" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.3" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.3" />
    <PackageReference Include="Npgsql" Version="9.0.3" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.3" />
  </ItemGroup>

</Project>
