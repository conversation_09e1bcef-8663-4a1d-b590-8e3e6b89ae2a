@page "/categories/add"
@using Microsoft.AspNetCore.Authorization
@using ProcureToPay.Application.Features.Categories.Services

@attribute [Authorize]
@inject NavigationManager NavigationManager
@inject IJSRuntime JSRuntime

<PageTitle>Add Category</PageTitle>

<div class="container">
    <h1>Add Category</h1>
    <p>Create a new product category for your organization.</p>

    <CategoryForm OnValidSubmitCallback="HandleCategoryAdded" />
</div>

@code {
    private async Task HandleCategoryAdded()
    {
        await JSRuntime.InvokeVoidAsync("alert", "Category created successfully.");
        NavigationManager.NavigateTo("/categories");
    }
}
