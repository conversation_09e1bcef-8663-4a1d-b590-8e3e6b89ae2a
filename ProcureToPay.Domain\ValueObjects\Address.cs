using System;
using System.Collections.Generic;
using System.Linq;

namespace ProcureToPay.Domain.ValueObjects
{
    /// <summary>
    /// Represents a physical address as a Value Object.
    /// Value Objects are compared by their values, not their reference.
    /// </summary>
    public class Address : ValueObject // Assuming a base ValueObject class exists for equality implementation
    {
        public string Street { get; private set; } = null!;
        public string City { get; private set; } = null!;
        public string State { get; private set; } = null!;
        public string Country { get; private set; } = null!;
        public string PostalCode { get; private set; } = null!;

        /// <summary>
        /// Private constructor for EF Core or deserialization.
        /// </summary>
        private Address() { }

        /// <summary>
        /// Creates a new Address instance.
        /// </summary>
        /// <param name="street">Street address line.</param>
        /// <param name="city">City name.</param>
        /// <param name="state">State or province.</param>
        /// <param name="country">Country name.</param>
        /// <param name="postalCode">Postal or ZIP code.</param>
        /// <exception cref="ArgumentNullException">Thrown if required fields are null or whitespace.</exception>
        public Address(string street, string city, string state, string country, string postalCode)
        {
            // Basic validation
            if (string.IsNullOrWhiteSpace(street)) throw new ArgumentNullException(nameof(street));
            if (string.IsNullOrWhiteSpace(city)) throw new ArgumentNullException(nameof(city));
            if (string.IsNullOrWhiteSpace(state)) throw new ArgumentNullException(nameof(state));
            if (string.IsNullOrWhiteSpace(country)) throw new ArgumentNullException(nameof(country));
            if (string.IsNullOrWhiteSpace(postalCode)) throw new ArgumentNullException(nameof(postalCode));

            Street = street;
            City = city;
            State = state;
            Country = country;
            PostalCode = postalCode;
        }

        // Required for Value Object semantics. Assumes a base class handles this.
        // If no base class, implement GetEqualityComponents, Equals, GetHashCode, ==, != here.
        protected override IEnumerable<object> GetEqualityComponents()
        {
            yield return Street;
            yield return City;
            yield return State;
            yield return Country;
            yield return PostalCode;
        }
    }

    // Note: ValueObject base class is defined in ValueObject.cs
}
