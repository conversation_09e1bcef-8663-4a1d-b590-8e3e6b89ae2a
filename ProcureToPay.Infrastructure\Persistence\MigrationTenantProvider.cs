using ProcureToPay.Domain.Interfaces;
using System;

namespace ProcureToPay.Infrastructure.Persistence
{
    /// <summary>
    /// Implementation of ITenantProvider for migration operations.
    /// Returns null for the tenant ID to ensure query filters are disabled during migrations.
    /// This matches the behavior of DesignTimeTenantProvider for consistency.
    /// </summary>
    public class MigrationTenantProvider : ITenantProvider
    {
        /// <summary>
        /// Gets the tenant ID for migration operations.
        /// </summary>
        /// <returns>Always returns null to disable tenant filtering during migrations.</returns>
        public Guid? GetCurrentTenantId()
        {
            return null;
        }
    }
}
