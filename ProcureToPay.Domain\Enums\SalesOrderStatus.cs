namespace ProcureToPay.Domain.Enums
{
    /// <summary>
    /// Represents the lifecycle status of a Sales Order.
    /// </summary>
    public enum SalesOrderStatus
    {
        /// <summary>
        /// Order is being created or modified, not yet submitted or confirmed.
        /// </summary>
        Draft = 0,

        /// <summary>
        /// Order has been submitted and is pending internal checks (e.g., credit, ATP).
        /// </summary>
        PendingApproval = 1,

        /// <summary>
        /// Order is on hold due to issues (e.g., credit failure, stock issue).
        /// </summary>
        OnHold = 2, // Added based on common workflows

        /// <summary>
        /// Order has passed checks and is confirmed, ready for fulfillment.
        /// </summary>
        Confirmed = 3,

        /// <summary>
        /// Order has been sent to the warehouse or fulfillment center for processing.
        /// </summary>
        ReleasedToFulfillment = 4, // Added based on entity logic

        /// <summary>
        /// Some items are out of stock and awaiting fulfillment.
        /// </summary>
        Backordered = 5, // Added based on common workflows

        /// <summary>
        /// Order has been partially or fully shipped to the customer.
        /// </summary>
        Shipped = 6,

        /// <summary>
        /// Order has been fully delivered to the customer.
        /// </summary>
        Delivered = 7, // Added based on common workflows

        /// <summary>
        /// An invoice has been generated for the order.
        /// </summary>
        Invoiced = 8,

        /// <summary>
        /// The order process is fully complete (shipped, delivered, invoiced, paid).
        /// </summary>
        Completed = 9,

        /// <summary>
        /// The order was cancelled before completion.
        /// </summary>
        Cancelled = 10
    }
}
