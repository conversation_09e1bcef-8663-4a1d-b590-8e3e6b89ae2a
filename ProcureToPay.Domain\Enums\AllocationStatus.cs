﻿namespace ProcureToPay.Domain.Enums
{
    /// <summary>
    /// Represents the status of a budget allocation.
    /// </summary>
    public enum AllocationStatus
    {
        /// <summary>
        /// The allocation is active and funds can be consumed against it.
        /// </summary>
        Active = 0,

        /// <summary>
        /// The allocation is inactive and cannot be consumed against (e.g., temporarily suspended).
        /// </summary>
        Inactive = 1,

        /// <summary>
        /// The allocation is closed, typically at the end of a period or project. No further consumption allowed.
        /// </summary>
        Closed = 2
    }
}
