using System;
using System.Collections.Generic;
using ProcureToPay.Domain.Exceptions; // For potential custom exceptions

namespace ProcureToPay.Domain.ValueObjects
{
    /// <summary>
    /// Represents a monetary value, encapsulating amount and currency code.
    /// Ensures type safety and prevents operations between different currencies.
    /// </summary>
    public class Money : ValueObject // Assumes base ValueObject class exists (from address_vo_01)
    {
        /// <summary>
        /// The monetary amount.
        /// </summary>
        public decimal Amount { get; private set; }

        /// <summary>
        /// The 3-letter ISO currency code (e.g., "USD", "EUR", "SAR").
        /// </summary>
        public string CurrencyCode { get; private set; } = null!;

        /// <summary>
        /// Private constructor for EF Core or deserialization.
        /// </summary>
        private Money() { }

        /// <summary>
        /// Creates a new Money instance.
        /// </summary>
        /// <param name="amount">The monetary amount.</param>
        /// <param name="currencyCode">The 3-letter ISO currency code.</param>
        /// <exception cref="ArgumentNullException">Thrown if currencyCode is null or whitespace.</exception>
        /// <exception cref="ArgumentException">Thrown if currencyCode is not 3 characters long.</exception>
        public Money(decimal amount, string currencyCode)
        {
            if (string.IsNullOrWhiteSpace(currencyCode))
                throw new ArgumentNullException(nameof(currencyCode), "Currency code cannot be empty.");
            if (currencyCode.Length != 3)
                throw new ArgumentException("Currency code must be 3 characters long.", nameof(currencyCode));

            // Consider adding validation for negative amounts if required by business rules
            // if (amount < 0) throw new ArgumentOutOfRangeException(nameof(amount), "Money amount cannot be negative.");

            Amount = amount;
            CurrencyCode = currencyCode.ToUpperInvariant(); // Store consistently
        }

        // --- Basic Arithmetic Operations (Examples) ---

        public static Money operator +(Money a, Money b)
        {
            if (a.CurrencyCode != b.CurrencyCode)
                throw new InvalidOperationException("Cannot add Money values with different currencies.");
            return new Money(a.Amount + b.Amount, a.CurrencyCode);
        }

        public static Money operator -(Money a, Money b)
        {
            if (a.CurrencyCode != b.CurrencyCode)
                throw new InvalidOperationException("Cannot subtract Money values with different currencies.");
            return new Money(a.Amount - b.Amount, a.CurrencyCode);
        }

        // Multiplication by a scalar (e.g., quantity)
        public static Money operator *(Money money, decimal multiplier)
        {
            return new Money(money.Amount * multiplier, money.CurrencyCode);
        }

        public static Money operator *(decimal multiplier, Money money)
        {
            return new Money(money.Amount * multiplier, money.CurrencyCode);
        }

        // Division by a scalar (less common, handle potential division by zero)
        public static Money operator /(Money money, decimal divisor)
        {
            if (divisor == 0) throw new DivideByZeroException("Cannot divide Money by zero.");
            return new Money(money.Amount / divisor, money.CurrencyCode);
        }


        // --- Comparison Operators (Examples) ---

        public static bool operator >(Money a, Money b)
        {
            if (a.CurrencyCode != b.CurrencyCode)
                throw new InvalidOperationException("Cannot compare Money values with different currencies.");
            return a.Amount > b.Amount;
        }

        public static bool operator <(Money a, Money b)
        {
            if (a.CurrencyCode != b.CurrencyCode)
                throw new InvalidOperationException("Cannot compare Money values with different currencies.");
            return a.Amount < b.Amount;
        }

        public static bool operator >=(Money a, Money b)
        {
            if (a.CurrencyCode != b.CurrencyCode)
                throw new InvalidOperationException("Cannot compare Money values with different currencies.");
            return a.Amount >= b.Amount;
        }

        public static bool operator <=(Money a, Money b)
        {
            if (a.CurrencyCode != b.CurrencyCode)
                throw new InvalidOperationException("Cannot compare Money values with different currencies.");
            return a.Amount <= b.Amount;
        }

        // Equality is handled by the ValueObject base class

        public override bool Equals(object? obj)
        {
            if (obj == null || obj.GetType() != GetType())
            {
                return false;
            }

            var other = (Money)obj;
            return GetEqualityComponents().SequenceEqual(other.GetEqualityComponents());
        }

        public override int GetHashCode()
        {
            return GetEqualityComponents()
                .Select(x => x != null ? x.GetHashCode() : 0)
                .Aggregate((x, y) => x ^ y);
        }


        // --- Value Object Equality Implementation ---
        protected override IEnumerable<object> GetEqualityComponents()
        {
            yield return Amount;
            yield return CurrencyCode;
        }

        public override string ToString()
        {
            // Basic representation, consider CultureInfo for formatting in presentation layer
            return $"{Amount:N2} {CurrencyCode}";
        }
    }
}
