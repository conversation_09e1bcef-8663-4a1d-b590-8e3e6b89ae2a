﻿using System;
using System.Collections.Generic;
using ProcureToPay.Domain.Enums;
using ProcureToPay.Domain.ValueObjects; // For Money
using ProcureToPay.Domain.Exceptions; // For DomainStateException

namespace ProcureToPay.Domain.Entities
{
    /// <summary>
    /// Represents a specific product or service offered by a particular Vendor,
    /// linking to a master ProductDefinition. Includes vendor-specific pricing, SKU, packaging etc.
    /// Uniqueness is typically defined by Vendor, ProductDefinition, UnitOfMeasure, and PackSize.
    /// </summary>
    public class VendorProduct : BaseEntity<Guid>
    {
        /// <summary>
        /// Foreign Key to the master Product Definition.
        /// </summary>
        public Guid ProductDefinitionId { get; private set; }

        /// <summary>
        /// Foreign Key to the Vendor offering this product.
        /// </summary>
        public Guid VendorId { get; private set; }

        /// <summary>
        /// Optional Vendor-specific Stock Keeping Unit (SKU), if different from the master SKU.
        /// </summary>
        public string? VendorSku { get; private set; }

        /// <summary>
        /// The price per unit offered by this vendor for this product.
        /// </summary>
        public Money UnitPrice { get; private set; } = null!;

        /// <summary>
        /// The unit of measure for the product as supplied by this vendor (e.g., Each, Box, Kg).
        /// </summary>
        public UnitOfMeasure UnitOfMeasure { get; private set; }

        /// <summary>
        /// Optional size of the package associated with the UnitOfMeasure (e.g., 12 for a Box of 12).
        /// Null if not applicable (e.g., for UoM 'Each').
        /// </summary>
        public decimal? PackSize { get; private set; } // Added Property

        /// <summary>
        /// Indicates if this vendor offering is currently active and available for ordering.
        /// </summary>
        public bool IsActive { get; private set; }

        /// <summary>
        /// Estimated number of days between ordering and availability/shipping from this vendor (optional).
        /// </summary>
        public int? LeadTimeDays { get; private set; }

        // --- Navigation Properties ---

        /// <summary>
        /// Navigation property to the master Product Definition. Virtual for lazy loading.
        /// </summary>
        public virtual ProductDefinition ProductDefinition { get; private set; } = null!;

        /// <summary>
        /// Navigation property to the Vendor. Virtual for lazy loading.
        /// </summary>
        public virtual Vendor Vendor { get; private set; } = null!;

        /// <summary>
        /// Navigation property for Purchase Order Lines referencing this specific vendor product.
        /// </summary>
        public virtual ICollection<PurchaseOrderLine> PurchaseOrderLines { get; private set; } = new List<PurchaseOrderLine>();


        /// <summary>
        /// Private parameterless constructor for EF Core hydration.
        /// </summary>
        private VendorProduct() : base(Guid.NewGuid()) { }

        /// <summary>
        /// Creates a new, active VendorProduct offering.
        /// </summary>
        /// <param name="id">Identifier.</param>
        /// <param name="productDefinitionId">ID of the master product definition.</param>
        /// <param name="vendorId">ID of the vendor offering the product.</param>
        /// <param name="unitPrice">Unit price (Money VO).</param>
        /// <param name="unitOfMeasure">Unit of measure (Enum).</param>
        /// <param name="packSize">Optional pack size associated with the UoM.</param> // Added Parameter
        /// <param name="vendorSku">Optional vendor-specific SKU.</param>
        /// <param name="leadTimeDays">Optional lead time.</param>
        /// <param name="isActive">Initial availability status (defaults to true).</param>
        public VendorProduct(
            Guid id,
            Guid productDefinitionId,
            Guid vendorId,
            Money unitPrice,
            UnitOfMeasure unitOfMeasure,
            decimal? packSize = null, // Added Parameter
            string? vendorSku = null,
            int? leadTimeDays = null,
            bool isActive = true
            ) : base(id)
        {
            if (productDefinitionId == Guid.Empty) throw new ArgumentException("ProductDefinitionId cannot be empty.", nameof(productDefinitionId));
            if (vendorId == Guid.Empty) throw new ArgumentException("VendorId cannot be empty.", nameof(vendorId));
            ArgumentNullException.ThrowIfNull(unitPrice);
            if (leadTimeDays.HasValue && leadTimeDays < 0) throw new ArgumentOutOfRangeException(nameof(leadTimeDays), "Lead time cannot be negative.");
            if (packSize.HasValue && packSize <= 0) throw new ArgumentOutOfRangeException(nameof(packSize), "Pack size must be positive if specified.");
            // Enum validation is implicit

            ProductDefinitionId = productDefinitionId;
            VendorId = vendorId;
            UnitPrice = unitPrice;
            UnitOfMeasure = unitOfMeasure;
            PackSize = packSize; // Assign PackSize
            VendorSku = vendorSku;
            LeadTimeDays = leadTimeDays;
            IsActive = isActive;

            // AddDomainEvent(new VendorProductCreatedEvent(this.Id));
        }

        // --- Domain Methods ---

        /// <summary>
        /// Updates the vendor-specific SKU.
        /// </summary>
        public void UpdateVendorSku(string? newSku)
        {
            VendorSku = newSku;
            // AddDomainEvent(...)
        }

        /// <summary>
        /// Updates the unit price for this vendor offering.
        /// </summary>
        public void UpdatePrice(Money newPrice)
        {
            ArgumentNullException.ThrowIfNull(newPrice);
            if (!IsActive)
            {
                throw new DomainStateException("Cannot update price for an inactive vendor product offering.");
            }
            if (newPrice.Amount < 0) throw new ArgumentOutOfRangeException(nameof(newPrice), "Unit price amount cannot be negative.");
            // Optional: Check if currency code matches expected context currency? (Better in App Service)

            if (UnitPrice == newPrice) return; // No change

            UnitPrice = newPrice;
            // AddDomainEvent(new VendorProductPriceChangedEvent(this.Id, newPrice));
        }

        /// <summary>
        /// Updates the estimated lead time.
        /// </summary>
        public void UpdateLeadTime(int? days)
        {
            if (days.HasValue && days < 0) throw new ArgumentOutOfRangeException(nameof(days), "Lead time cannot be negative.");
            if (LeadTimeDays == days) return;

            LeadTimeDays = days;
            // AddDomainEvent(...)
        }

        /// <summary>
        /// Updates the packaging details (Unit of Measure and Pack Size).
        /// Consider if these should be updatable or if a new VendorProduct should be created.
        /// Requires careful consideration of uniqueness constraints if updated.
        /// </summary>
        /// <param name="newUnitOfMeasure">The new Unit of Measure.</param>
        /// <param name="newPackSize">The new Pack Size (null if not applicable).</param>
        public void UpdatePackaging(UnitOfMeasure newUnitOfMeasure, decimal? newPackSize)
        {
            if (newPackSize.HasValue && newPackSize <= 0) throw new ArgumentOutOfRangeException(nameof(newPackSize), "Pack size must be positive if specified.");

            // IMPORTANT: If VendorId+ProductDefinitionId+UoM+PackSize has a unique constraint,
            // updating these might violate it if the new combination already exists.
            // This update logic might be better handled by deactivating the old and creating a new VendorProduct
            // in the Application Service layer.

            if (UnitOfMeasure != newUnitOfMeasure)
            {
                UnitOfMeasure = newUnitOfMeasure;
            }
            if (PackSize != newPackSize)
            {
                PackSize = newPackSize;
            }

            // TODO: Add domain event when implementing event handling
            // AddDomainEvent(new VendorProductPackagingUpdatedEvent(this.Id, UnitOfMeasure, PackSize));
        }


        /// <summary>
        /// Deactivates this vendor product offering.
        /// </summary>
        public void Deactivate()
        {
            if (!IsActive) return; // Already inactive
            IsActive = false;
            // AddDomainEvent(new VendorProductDeactivatedEvent(this.Id));
        }

        /// <summary>
        /// Activates this vendor product offering.
        /// </summary>
        public void Activate()
        {
            // As discussed, checks against ProductDefinition lifecycle state
            // are better placed in the Application Service layer before calling Activate.
            if (IsActive) return; // Already active
            IsActive = true;
            // AddDomainEvent(new VendorProductActivatedEvent(this.Id));
        }
    }
}
