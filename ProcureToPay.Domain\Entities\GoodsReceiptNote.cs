using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using ProcureToPay.Domain.Enums; // Required for GoodsReceiptNoteStatus
using ProcureToPay.Domain.Entities; // Required for BaseEntity and related entities

namespace ProcureToPay.Domain.Entities;

/// <summary>
/// Represents an internal Goods Receipt Note (GRN) confirming the receipt of items
/// against a Purchase Order, potentially referencing a Delivery Note.
/// Typically an Aggregate Root for the receiving process.
/// </summary>
public class GoodsReceiptNote : BaseEntity<Guid>
{
    /// <summary>
    /// Tenant identifier for multi-tenancy.
    /// </summary>
    [Required]
    public Guid TenantId { get; private set; }

    /// <summary>
    /// Unique internal identifier for the Goods Receipt Note.
    /// </summary>
    [Required]
    [MaxLength(50)]
    public string GoodsReceiptNoteNumber { get; private set; } = string.Empty;

    /// <summary>
    /// Legacy property for backward compatibility.
    /// </summary>
    [Required]
    [MaxLength(50)]
    public string GrnNumber
    {
        get => GoodsReceiptNoteNumber;
        private set => GoodsReceiptNoteNumber = value;
    }

    /// <summary>
    /// Foreign Key to the Purchase Order the received goods belong to.
    /// </summary>
    [Required]
    public Guid PurchaseOrderId { get; private set; }
    /// <summary>
    /// Navigation property to the related Purchase Order. Virtual for lazy loading.
    /// </summary>
    public virtual PurchaseOrder PurchaseOrder { get; private set; } = null!;

    /// <summary>
    /// Optional Foreign Key to the Vendor's Delivery Note that accompanied the shipment.
    /// </summary>
    public Guid? DeliveryNoteId { get; private set; }
    /// <summary>
    /// Optional navigation property to the related Delivery Note. Virtual for lazy loading.
    /// </summary>
    public virtual DeliveryNote? DeliveryNote { get; private set; }

    /// <summary>
    /// Foreign Key to the Vendor who supplied the goods.
    /// </summary>
    [Required]
    public Guid VendorId { get; private set; }
    /// <summary>
    /// Navigation property to the Vendor. Virtual for lazy loading.
    /// </summary>
    public virtual Vendor Vendor { get; private set; } = null!;

    /// <summary>
    /// The date and time when the goods were physically received.
    /// </summary>
    [Required]
    public DateTime ReceiptDate { get; private set; }

    /// <summary>
    /// The date and time when the goods were inspected.
    /// </summary>
    public DateTime? InspectionDate { get; private set; }

    /// <summary>
    /// The location where the goods were received.
    /// </summary>
    [MaxLength(255)]
    public string? ReceivingLocation { get; private set; }

    /// <summary>
    /// Optional ID linking the receiver to the ApplicationUser identity.
    /// </summary>
    [MaxLength(450)]
    public string? ReceivedByUserId { get; private set; }

    /// <summary>
    /// Name of the person who received the goods.
    /// </summary>
    [Required]
    [MaxLength(150)]
    public string ReceivedByName { get; private set; } = string.Empty;

    /// <summary>
    /// Current status of the Goods Receipt Note (e.g., pending inspection).
    /// </summary>
    [Required]
    public GoodsReceiptNoteStatus Status { get; private set; }

    /// <summary>
    /// General notes regarding the receipt (e.g., condition of packaging).
    /// </summary>
    public string? Notes { get; private set; }

    // Collection of line items detailing products and quantities received/inspected. Encapsulated list.
    private readonly List<GoodsReceiptNoteLine> _lines = new();
    public virtual IReadOnlyCollection<GoodsReceiptNoteLine> Lines => _lines.AsReadOnly();

    /// <summary>
    /// Private constructor for EF Core hydration.
    /// </summary>
    private GoodsReceiptNote() : base(Guid.Empty) { }

    /// <summary>
    /// Creates a new Goods Receipt Note in Pending Inspection status.
    /// </summary>
    public GoodsReceiptNote(
        Guid tenantId,
        string grnNumber,
        Guid purchaseOrderId,
        Guid vendorId,
        DateTime receiptDate,
        string receivedByName,
        Guid? deliveryNoteId = null,
        string? receivedByUserId = null,
        string? receivingLocation = null,
        string? notes = null) : base(Guid.NewGuid())
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(grnNumber);
        ArgumentException.ThrowIfNullOrWhiteSpace(receivedByName);
        if (tenantId == Guid.Empty) throw new ArgumentException("TenantId cannot be empty.", nameof(tenantId));
        if (purchaseOrderId == Guid.Empty) throw new ArgumentException("PurchaseOrderId cannot be empty.", nameof(purchaseOrderId));
        if (vendorId == Guid.Empty) throw new ArgumentException("VendorId cannot be empty.", nameof(vendorId));
        if (receiptDate > DateTime.UtcNow.AddDays(1)) // Basic sanity check
            throw new ArgumentOutOfRangeException(nameof(receiptDate), "Receipt date cannot be in the future.");

        TenantId = tenantId;
        GoodsReceiptNoteNumber = grnNumber;
        PurchaseOrderId = purchaseOrderId;
        DeliveryNoteId = deliveryNoteId; // Can be null
        VendorId = vendorId;
        ReceiptDate = receiptDate;
        ReceivedByUserId = receivedByUserId;
        ReceivedByName = receivedByName;
        ReceivingLocation = receivingLocation;
        Notes = notes;
        Status = GoodsReceiptNoteStatus.PendingInspection; // Initial status
    }

    // --- Domain Methods ---

    /// <summary>
    /// Adds a received line item to the GRN. Typically done during initial receipt recording.
    /// </summary>
    /// <param name="purchaseOrderLineId">ID of the corresponding PO line.</param>
    /// <param name="productId">ID of the product received.</param>
    /// <param name="productSkuSnapshot">Snapshot of SKU.</param>
    /// <param name="productDescriptionSnapshot">Snapshot of description.</param>
    /// <param name="quantityReceived">The quantity physically received.</param>
    /// <param name="lineNumber">The line number for ordering.</param>
    /// <param name="unitOfMeasure">The unit of measure for the quantity.</param>
    /// <param name="deliveryNoteLineId">Optional ID of the corresponding DN line.</param>
    /// <param name="batchNumber">Optional batch number of the received goods.</param>
    /// <param name="lotNumber">Optional lot number of the received goods.</param>
    /// <param name="expiryDate">Optional expiry date of the received goods.</param>
    /// <param name="putAwayLocation">Optional location where the goods were put away.</param>
    /// <param name="notes">Optional line item notes.</param>
    public void AddReceivedLine(
        Guid purchaseOrderLineId,
        Guid productId,
        string productSkuSnapshot,
        string productDescriptionSnapshot,
        decimal quantityReceived,
        int lineNumber,
        string unitOfMeasure,
        Guid? deliveryNoteLineId = null,
        string? batchNumber = null,
        string? lotNumber = null,
        DateTime? expiryDate = null,
        string? putAwayLocation = null,
        string? notes = null)
    {
        if (Status != GoodsReceiptNoteStatus.PendingInspection)
            throw new InvalidOperationException("Cannot add lines unless GRN status is Pending Inspection.");
        // TODO: Add validation - check if PO line exists, check if product matches PO line, etc.
        // TODO: Check if quantity received exceeds quantity remaining on PO line (requires loading PO data).

        var newLine = new GoodsReceiptNoteLine(
            Guid.NewGuid(),
            this.Id,
            purchaseOrderLineId,
            productId,
            productSkuSnapshot,
            productDescriptionSnapshot,
            quantityReceived,
            lineNumber,
            unitOfMeasure,
            deliveryNoteLineId,
            batchNumber,
            lotNumber,
            expiryDate,
            putAwayLocation,
            notes);
        _lines.Add(newLine);
    }

    /// <summary>
    /// Records the inspection result for a specific line item.
    /// </summary>
    /// <param name="goodsReceiptNoteLineId">The ID of the GRN line being inspected.</param>
    /// <param name="quantityAccepted">Quantity passing inspection.</param>
    /// <param name="quantityRejected">Quantity failing inspection.</param>
    /// <param name="rejectionReason">Reason if quantity was rejected.</param>
    /// <param name="inspectionNotes">Notes from the inspection.</param>
    public void RecordInspectionResult(Guid goodsReceiptNoteLineId, decimal quantityAccepted, decimal quantityRejected, string? rejectionReason = null, string? inspectionNotes = null)
    {
        if (Status != GoodsReceiptNoteStatus.PendingInspection)
            throw new InvalidOperationException("Cannot record inspection results unless GRN status is Pending Inspection.");

        var line = _lines.FirstOrDefault(l => l.Id == goodsReceiptNoteLineId);
        if (line == null)
            throw new KeyNotFoundException($"Goods Receipt Note Line with ID {goodsReceiptNoteLineId} not found on this GRN.");

        line.UpdateInspectionResult(quantityAccepted, quantityRejected, rejectionReason, inspectionNotes);
        // Note: The line itself validates quantities add up.
    }


    /// <summary>
    /// Marks the entire GRN inspection as complete.
    /// </summary>
    /// <exception cref="InvalidOperationException">Thrown if GRN is not Pending Inspection or if lines are not fully inspected.</exception>
    public void CompleteInspection()
    {
        if (Status != GoodsReceiptNoteStatus.PendingInspection)
            throw new InvalidOperationException($"Cannot complete inspection for GRN with status '{Status}'.");

        // Ensure all lines have had inspection results recorded
        if (_lines.Any(l => !l.InspectionCompleted))
        {
            throw new InvalidOperationException("Cannot complete GRN inspection until all lines have inspection results recorded.");
        }

        Status = GoodsReceiptNoteStatus.Completed;
        InspectionDate = DateTime.UtcNow;
        // TODO: Raise Domain Event? GoodsReceiptCompletedEvent(this.Id)
        // This event could trigger updates to PO status, inventory, or trigger AP process.
    }

    /// <summary>
    /// Cancels the GRN if it's still pending inspection.
    /// </summary>
    /// <param name="reason">Reason for cancellation.</param>
    public void Cancel(string reason)
    {
        if (Status != GoodsReceiptNoteStatus.PendingInspection)
            throw new InvalidOperationException($"Cannot cancel GRN with status '{Status}'.");

        ArgumentException.ThrowIfNullOrWhiteSpace(reason);
        Status = GoodsReceiptNoteStatus.Cancelled;
        Notes = $"Cancelled: {reason}\n---\n{Notes}";
        // TODO: Raise Domain Event? GoodsReceiptCancelledEvent(this.Id, reason)
    }

    public void AddNote(string note)
    {
        Notes = string.IsNullOrWhiteSpace(Notes) ? note : $"{Notes}\n{note}";
    }
}
