using System;
using ProcureToPay.Domain.Enums; // For VendorStatus
using ProcureToPay.Domain.ValueObjects; // For Address

namespace ProcureToPay.Domain.Events
{
    // --- Vendor Specific Events ---
    public record VendorCreatedEvent(Guid VendorId);
    public record VendorContactInfoUpdatedEvent(Guid VendorId);
    public record VendorIdentificationUpdatedEvent(Guid VendorId);
    public record VendorAddressUpdatedEvent(Guid VendorId, Address NewAddress); // Include relevant data
    public record VendorActivatedEvent(Guid VendorId);
    public record VendorDeactivatedEvent(Guid VendorId);
    public record VendorTerminatedEvent(Guid VendorId, string Reason); // Renamed from Blacklisted
}
