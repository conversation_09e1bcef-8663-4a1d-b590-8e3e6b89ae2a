namespace ProcureToPay.Domain.Enums;

/// <summary>
/// Represents the status of a Request for Proposal (RFP).
/// </summary>
public enum RfpStatus
{
    /// <summary>
    /// RFP is being prepared.
    /// </summary>
    Draft = 0,
    /// <summary>
    /// RFP has been issued to potential vendors.
    /// </summary>
    Issued = 1,
    /// <summary>
    /// Submission deadline passed; proposals are being evaluated.
    /// </summary>
    Evaluating = 2,
    /// <summary>
    /// Evaluation complete; vendor selected and contract awarded (or pending award).
    /// </summary>
    Awarded = 3,
    /// <summary>
    /// RFP process completed without awarding a contract.
    /// </summary>
    CompletedWithoutAward = 4,
    /// <summary>
    /// RFP process was cancelled.
    /// </summary>
    Cancelled = 5
}
