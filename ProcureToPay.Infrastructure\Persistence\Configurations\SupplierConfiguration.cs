using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProcureToPay.Domain.Entities; // Assuming Supplier, Vendor, ContactPerson entities/records exist here
using ProcureToPay.Domain.Enums;   // Assuming SupplierStatus, SupplierRiskRating enums exist here
using ProcureToPay.Domain.ValueObjects; // Assuming ContactPerson record might be here
using System.Collections.Generic; // For List<ContactPerson>
// using System.Text.Json; // No longer needed

namespace ProcureToPay.Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// EF Core Configuration for the Supplier entity.
    /// Configures properties related to supplier capabilities, risk, and compliance.
    /// Uses OwnsMany with ToJson() mapping for complex collections.
    /// </summary>
    public class SupplierConfiguration : IEntityTypeConfiguration<Supplier>
    {
        public void Configure(EntityTypeBuilder<Supplier> builder)
        {
            builder.ToTable("suppliers");

            // Assuming Supplier inherits from BaseEntity<Guid>
            builder.HasKey(s => s.Id);

            // --- Properties ---
            builder.Property(s => s.SupplierName) // May differ from Vendor name
                .IsRequired()
                .HasMaxLength(250);

            // Map Status Enum
            builder.Property(s => s.Status)
                .IsRequired()
                .HasConversion<string>() // Store enum as string
                .HasMaxLength(50)
                .HasDefaultValue(SupplierStatus.Active); // Example default

            // Risk Rating Classification (using Enum)
            builder.Property(s => s.RiskRating)
               .HasConversion<string>() // Store enum as string
               .HasMaxLength(50); // Nullable based on entity definition

            // Sustainable Sourcing Metrics (assuming a decimal score)
            builder.Property(s => s.SustainabilityScore)
               .HasPrecision(5, 2); // e.g., 0.00 to 100.00

            // Contract Manufacturer Flag
            builder.Property(s => s.IsContractManufacturer)
                .IsRequired()
                .HasDefaultValue(false);

            // Lead Time Performance Tracking (assuming average days, nullable int)
            builder.Property(s => s.AverageLeadTimeDays);

            // CSR Compliance Indicator Flag
            builder.Property(s => s.IsCsrCompliant)
                .IsRequired()
                .HasDefaultValue(false);

            // Capacity Utilization Reporting (assuming percentage, nullable decimal)
            builder.Property(s => s.CapacityUtilizationPercent)
               .HasPrecision(5, 2); // e.g., 0.00 to 100.00


            // Emergency Contact Hierarchy (Corrected JSON column mapping using OwnsMany + ToJson())
            // Assumes Supplier entity has: public List<ContactPerson> EmergencyContacts { get; private set; } = new List<ContactPerson>();
            // Assumes EF Core 7+ and Npgsql provider which supports ToJson() mapping on owned collections.
            builder.OwnsMany(s => s.EmergencyContacts, ownedBuilder =>
            {
                // Optional: Configure properties within the ContactPerson record if needed
                // ownedBuilder.Property(cp => cp.Name).IsRequired().HasMaxLength(150);
                // ownedBuilder.Property(cp => cp.Email).HasMaxLength(254);
                // ownedBuilder.Property(cp => cp.Phone).HasMaxLength(50);
                // ownedBuilder.Property(cp => cp.Role).HasMaxLength(100);
                // ownedBuilder.Property(cp => cp.HierarchyLevel);

                // Apply ToJson to the owned collection builder
                ownedBuilder.ToJson();
            });


            // --- Relationships ---

            // Relationship to Vendor (Assuming One-to-One with FK on Supplier)
            builder.HasOne(s => s.Vendor)
                   .WithOne(v => v.SupplierProfile) // Maps to the navigation property on Vendor
                   .HasForeignKey<Supplier>(s => s.VendorId) // The FK is defined in Supplier
                   .IsRequired() // A Supplier record must be linked to a Vendor
                   .OnDelete(DeleteBehavior.Cascade); // If Vendor is deleted, delete the associated Supplier profile


            // --- Indexes ---
            builder.HasIndex(s => s.VendorId).IsUnique(); // Enforce One-to-One via unique index on FK
            builder.HasIndex(s => s.RiskRating); // Index for filtering/sorting by risk (now enum)
            builder.HasIndex(s => s.IsContractManufacturer); // Index for filtering


            // --- Tenant Isolation ---
            // Assuming Supplier belongs to a Tenant (likely via Vendor relationship)
            // If direct TenantId needed:
            // builder.Property<Guid>("TenantId").IsRequired();
            // builder.HasIndex("TenantId");
            // builder.HasQueryFilter(s => EF.Property<Guid>(s, "TenantId") == _tenantId); // Requires _tenantId in DbContext

        }
    }

    // --- Helper Class Reminder (Define appropriately) ---
    /*
    namespace ProcureToPay.Domain.ValueObjects // Or appropriate namespace
    {
        public record ContactPerson(
            string Name,
            string Role,
            string Email,
            string Phone,
            int? HierarchyLevel // Example field for hierarchy
        );
    }
    */
}

