﻿using System;
using ProcureToPay.Domain.Enums;
using ProcureToPay.Domain.ValueObjects; // For DocumentLink

namespace ProcureToPay.Domain.Entities
{
    /// <summary>
    /// Represents a single review instance within a specific cycle of a Technical Submittal.
    /// </summary>
    public class SubmittalReview : BaseEntity<Guid> // Assuming BaseEntity<Guid> exists
    {
        /// <summary>
        /// FK to the parent TechnicalSubmittal.
        /// </summary>
        public Guid TechnicalSubmittalId { get; private set; }

        /// <summary>
        /// The review cycle number this review belongs to (e.g., 1, 2, 3...).
        /// </summary>
        public int ReviewCycle { get; private set; }

        /// <summary>
        /// User ID of the person who performed the review. Links to ApplicationUser Id.
        /// </summary>
        public string ReviewerId { get; private set; } = null!;

        /// <summary>
        /// Foreign Key to the reviewer.
        /// </summary>
        public Guid ReviewerGuid { get; private set; }

        /// <summary>
        /// Name of the reviewer (denormalized for easier display, but lookup via ID is primary).
        /// </summary>
        public string ReviewerName { get; private set; } = null!; // Consider making nullable or fetching on demand

        /// <summary>
        /// Timestamp when the review was completed/recorded.
        /// </summary>
        public DateTime ReviewDate { get; private set; }

        /// <summary>
        /// The disposition/outcome assigned by this specific reviewer.
        /// </summary>
        public SubmittalDisposition Disposition { get; private set; }

        /// <summary>
        /// Comments or feedback provided by the reviewer.
        /// </summary>
        public string? Comments { get; private set; }

        /// <summary>
        /// Optional link to a document containing markups or detailed review comments.
        /// Stored as JSONB.
        /// </summary>
        public DocumentLink? MarkupDocument { get; private set; }

        // --- Navigation Properties ---
        public virtual TechnicalSubmittal TechnicalSubmittal { get; private set; } = null!;
        


        /// <summary>
        /// Private constructor for EF Core.
        /// </summary>
        private SubmittalReview() : base(Guid.NewGuid()) { }

        /// <summary>
        /// Creates a new Submittal Review record.
        /// </summary>
        public SubmittalReview(
            Guid id,
            Guid technicalSubmittalId,
            int reviewCycle,
            string reviewerId,
            Guid reviewerGuid,
            string reviewerName, // Pass in during creation
            DateTime reviewDate,
            SubmittalDisposition disposition,
            string? comments = null,
            DocumentLink? markupDocument = null
            ) : base(id)
        {
            ArgumentOutOfRangeException.ThrowIfNegativeOrZero(reviewCycle);
            ArgumentException.ThrowIfNullOrWhiteSpace(reviewerId);
            ArgumentException.ThrowIfNullOrWhiteSpace(reviewerName); // Validate if passed in
            if (reviewerGuid == Guid.Empty) throw new ArgumentException("ReviewerGuid cannot be empty.", nameof(reviewerGuid));
            if (reviewDate == default) throw new ArgumentException("Review date must be specified.", nameof(reviewDate));

            TechnicalSubmittalId = technicalSubmittalId;
            ReviewCycle = reviewCycle;
            ReviewerId = reviewerId;
            ReviewerGuid = reviewerGuid;
            ReviewerName = reviewerName;
            ReviewDate = reviewDate.ToUniversalTime();
            Disposition = disposition;
            Comments = comments;
            MarkupDocument = markupDocument;

            // No domain events typically raised directly from child entities like this,
            // the aggregate root (TechnicalSubmittal) handles events related to review completion.
        }

        // Methods to update review details if allowed (less common, usually reviews are immutable once recorded)
        // public void UpdateReview(...) { ... }
    }
}
