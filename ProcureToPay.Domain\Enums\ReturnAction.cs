﻿namespace ProcureToPay.Domain.Enums
{
    /// <summary>
    /// Represents the action requested or taken for a return.
    /// </summary>
    public enum ReturnAction
    {
        /// <summary>
        /// The customer requests or will receive a monetary refund.
        /// </summary>
        Refund = 0,

        /// <summary>
        /// The customer requests or will receive store credit or account credit.
        /// </summary>
        Credit = 1,

        /// <summary>
        /// The customer requests or will receive a replacement item.
        /// </summary>
        Replacement = 2,

        /// <summary>
        /// The item will be repaired and returned.
        /// </summary>
        Repair = 3
    }
}

