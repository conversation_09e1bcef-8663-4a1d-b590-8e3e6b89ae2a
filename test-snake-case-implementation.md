# Testing Snake Case Implementation

## 1. Create a Test Migration
```bash
dotnet ef migrations add TestSnakeCaseImplementation --project ProcureToPay.Infrastructure --startup-project ProcureToPay.WebApp/ProcureToPay.WebApp
```

## 2. Review the Generated Migration
Check that:
- Properties without explicit `HasColumnName()` are converted to snake_case
- Properties with explicit column names remain unchanged
- Table names are converted appropriately
- No conflicts with owned entities (Money value objects)

## 3. Apply the Migration (when ready)
```bash
dotnet ef database update --project ProcureToPay.Infrastructure --startup-project ProcureToPay.WebApp/ProcureToPay.WebApp
```

## 4. Test Entity Operations
Create a simple test to verify entities work correctly:

```csharp
[Fact]
public async Task TestEntity_SnakeCaseNaming_WorksCorrectly()
{
    // Arrange
    var options = TestDbContextHelper.CreateNewContextOptions("SnakeCaseTest");
    var tenantProvider = TestDbContextHelper.CreateTenantProvider(Guid.NewGuid());

    // Act & Assert
    using (var context = new TestApplicationDbContext(options, tenantProvider))
    {
        var testEntity = new TestEntity
        {
            Id = Guid.NewGuid(),
            Name = "Test",
            Description = "Test Description",
            TestNumber = 123,
            CreatedDate = DateTime.UtcNow
        };

        context.TestEntities.Add(testEntity);
        await context.SaveChangesAsync();

        var retrieved = await context.TestEntities.FirstOrDefaultAsync(e => e.Id == testEntity.Id);
        Assert.NotNull(retrieved);
        Assert.Equal("Test", retrieved.Name);
        Assert.Equal(123, retrieved.TestNumber);
    }
}
```

## 5. Verify Database Schema
Connect to your PostgreSQL database and verify:
- Column names are in snake_case format
- Existing explicit configurations are preserved
- Foreign key relationships work correctly

## Expected Results
- `test_number` column for TestEntity.TestNumber
- `created_date` column for TestEntity.CreatedDate  
- `description` column for TestEntity.Description
- Existing explicit columns like `allocated_amount` remain unchanged
