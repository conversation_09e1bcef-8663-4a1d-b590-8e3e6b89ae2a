using System;
using ProcureToPay.Domain.Interfaces;

namespace ProcureToPay.Domain.Entities
{
    public class TestEntity : ITenantEntity
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public int TestNumber { get; set; }
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
        public Guid TenantId { get; set; }
    }
}
