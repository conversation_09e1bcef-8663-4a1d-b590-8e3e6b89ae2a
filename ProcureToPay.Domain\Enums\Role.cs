using System;

namespace ProcureToPay.Domain.Enums;

/// <summary>
/// Defines the user roles within the Procure-to-Pay system.
/// These roles are typically used for authorization purposes.
/// </summary>
[Flags] // Consider if Flags attribute is needed - usually not for standard Identity roles unless combining permissions. Standard roles are typically distinct. Let's remove Flags for now.
public enum Role
{
    /// <summary>
    /// No specific role assigned (or guest).
    /// </summary>
    None = 0,

    /// <summary>
    /// Full system administration capabilities.
    /// </summary>
    SystemAdministrator = 1,

    /// <summary>
    /// Standard user who can request purchases (create Purchase Requisitions).
    /// </summary>
    Requestor = 2,

    /// <summary>
    /// User responsible for approving requisitions, invoices, etc. 
    /// Consider multiple levels if needed (e.g., ApproverLevel1, ApproverLevel2).
    /// </summary>
    Approver = 4,

    /// <summary>
    /// User in the purchasing department responsible for creating POs, managing vendors, RFQs, etc.
    /// </summary>
    Buyer = 8, // Or PurchasingAgent

    /// <summary>
    /// User responsible for receiving goods and creating Goods Receipt Notes.
    /// </summary>
    Receiver = 16,

    /// <summary>
    /// User in Accounts Payable responsible for processing invoices and payments.
    /// </summary>
    AccountsPayable = 32, // Or APClerk

    /// <summary>
    /// User representing a Vendor organization, managing their profile, catalogue, proposals, invoices.
    /// </summary>
    VendorAdmin = 64,

    /// <summary>
    /// User representing a Supplier organization (if they have system access).
    /// </summary>
    SupplierAdmin = 128,

    /// <summary>
    /// User with financial oversight, budget management, and reporting capabilities.
    /// </summary>
    FinanceManager = 256,

    /// <summary>
    /// User with read-only access for auditing purposes.
    /// </summary>
    Auditor = 512,

    /// <summary>
    /// User responsible for managing contracts.
    /// </summary>
    ContractManager = 1024,

    /// <summary>
    /// User responsible for managing product catalogues.
    /// </summary>
    CatalogueManager = 2048

    // Add other specific roles as needed for your application's requirements.
}

// Helper class for working with Role enum, especially if using string representations with Identity
public static class Roles
{
    public const string SystemAdministrator = nameof(Role.SystemAdministrator);
    public const string Requestor = nameof(Role.Requestor);
    public const string Approver = nameof(Role.Approver);
    public const string Buyer = nameof(Role.Buyer);
    public const string Receiver = nameof(Role.Receiver);
    public const string AccountsPayable = nameof(Role.AccountsPayable);
    public const string VendorAdmin = nameof(Role.VendorAdmin);
    public const string SupplierAdmin = nameof(Role.SupplierAdmin);
    public const string FinanceManager = nameof(Role.FinanceManager);
    public const string Auditor = nameof(Role.Auditor);
    public const string ContractManager = nameof(Role.ContractManager);
    public const string CatalogueManager = nameof(Role.CatalogueManager);

    public static IEnumerable<string> GetAllRoles()
    {
        // Use reflection to get all string constants
        return typeof(Roles).GetFields(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Static | System.Reflection.BindingFlags.FlattenHierarchy)
            .Where(fi => fi.IsLiteral && !fi.IsInitOnly && fi.FieldType == typeof(string))
            .Select(x => (string)x.GetRawConstantValue()!)
            .ToList();
    }
}
