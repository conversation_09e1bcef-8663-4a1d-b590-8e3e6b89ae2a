# PowerShell script for applying EF Core migrations in a .NET Aspire environment
# This script handles the complexities of applying migrations to PostgreSQL containers
# managed by Aspire, including connection string resolution and retry logic.

param (
    [Parameter(Mandatory=$false)]
    [switch]$FromDashboard,
    
    [Parameter(Mandatory=$false)]
    [string]$ConnectionString,
    
    [Parameter(Mandatory=$false)]
    [int]$MaxRetries = 10,
    
    [Parameter(Mandatory=$false)]
    [int]$RetryDelaySeconds = 5
)

# Set script to stop on error
$ErrorActionPreference = "Stop"

# Define colors for output
$colorInfo = "Cyan"
$colorSuccess = "Green"
$colorWarning = "Yellow"
$colorError = "Red"

# Define paths
$infrastructureProjectPath = "ProcureToPay.Infrastructure"
$webAppProjectPath = "ProcureToPay.WebApp\ProcureToPay.WebApp"
$migrationScriptPath = "migration_script.sql"

# Display banner
Write-Host "=======================================================" -ForegroundColor $colorInfo
Write-Host "   EF Core Migration Script for .NET Aspire Projects   " -ForegroundColor $colorInfo
Write-Host "=======================================================" -ForegroundColor $colorInfo
Write-Host ""

# Step 1: Determine connection string
Write-Host "Step 1: Determining database connection string..." -ForegroundColor $colorInfo

if ($ConnectionString) {
    Write-Host "  Using provided connection string parameter" -ForegroundColor $colorInfo
    $pgConnectionString = $ConnectionString
}
elseif ($FromDashboard) {
    Write-Host "  Prompting for connection string from Aspire Dashboard..." -ForegroundColor $colorInfo
    Write-Host "  Please copy the PostgreSQL connection string from the Aspire Dashboard" -ForegroundColor $colorWarning
    Write-Host "  (Resources > PostgreSQL > Connection String)" -ForegroundColor $colorWarning
    $pgConnectionString = Read-Host "  Paste connection string"
    
    if ([string]::IsNullOrWhiteSpace($pgConnectionString)) {
        Write-Error "  No connection string provided. Exiting."
        exit 1
    }
}
else {
    Write-Host "  Using connection string from appsettings.json" -ForegroundColor $colorInfo
    # Extract connection string from appsettings.json
    $appSettingsPath = Join-Path $webAppProjectPath "appsettings.json"
    
    if (Test-Path $appSettingsPath) {
        $appSettings = Get-Content $appSettingsPath -Raw | ConvertFrom-Json
        $pgConnectionString = $appSettings.ConnectionStrings.postgresdb
        
        if ([string]::IsNullOrWhiteSpace($pgConnectionString)) {
            Write-Error "  Connection string 'postgresdb' not found in appsettings.json"
            exit 1
        }
    }
    else {
        Write-Error "  appsettings.json not found at: $appSettingsPath"
        exit 1
    }
}

Write-Host "  Connection string obtained successfully" -ForegroundColor $colorSuccess

# Step 2: Generate migration SQL script
Write-Host "Step 2: Generating migration SQL script..." -ForegroundColor $colorInfo

try {
    # Set environment variable for connection string
    $env:ConnectionStrings__postgresdb = $pgConnectionString
    
    # Execute the script generation command
    $scriptCmd = "dotnet ef migrations script --project $infrastructureProjectPath --startup-project $webAppProjectPath --output $migrationScriptPath --idempotent"
    Write-Host "  Running: $scriptCmd" -ForegroundColor $colorInfo
    
    Invoke-Expression $scriptCmd
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "  SQL script generated successfully at: $migrationScriptPath" -ForegroundColor $colorSuccess
    } else {
        Write-Error "  Failed to generate SQL script. See output above for details."
        exit 1
    }
}
catch {
    Write-Error "  An error occurred while generating the SQL script: $_"
    exit 1
}
finally {
    # Clear environment variable
    Remove-Item Env:\ConnectionStrings__postgresdb -ErrorAction SilentlyContinue
}

# Step 3: Parse connection string to get PostgreSQL details
Write-Host "Step 3: Parsing connection string..." -ForegroundColor $colorInfo

try {
    # Extract PostgreSQL connection details using regex
    $hostMatch = [regex]::Match($pgConnectionString, "Host=([^;]+)")
    $dbMatch = [regex]::Match($pgConnectionString, "Database=([^;]+)")
    $userMatch = [regex]::Match($pgConnectionString, "Username=([^;]+)")
    $passMatch = [regex]::Match($pgConnectionString, "Password=([^;]+)")
    $portMatch = [regex]::Match($pgConnectionString, "Port=([^;]+)")
    
    $pgHost = if ($hostMatch.Success) { $hostMatch.Groups[1].Value } else { "localhost" }
    $pgDatabase = if ($dbMatch.Success) { $dbMatch.Groups[1].Value } else { throw "Database name not found in connection string" }
    $pgUser = if ($userMatch.Success) { $userMatch.Groups[1].Value } else { throw "Username not found in connection string" }
    $pgPassword = if ($passMatch.Success) { $passMatch.Groups[1].Value } else { throw "Password not found in connection string" }
    $pgPort = if ($portMatch.Success) { $portMatch.Groups[1].Value } else { "5432" }
    
    Write-Host "  Connection details parsed successfully:" -ForegroundColor $colorSuccess
    Write-Host "    Host: $pgHost" -ForegroundColor $colorInfo
    Write-Host "    Database: $pgDatabase" -ForegroundColor $colorInfo
    Write-Host "    User: $pgUser" -ForegroundColor $colorInfo
    Write-Host "    Port: $pgPort" -ForegroundColor $colorInfo
}
catch {
    Write-Error "  Failed to parse connection string: $_"
    exit 1
}

# Step 4: Apply migrations with retry logic
Write-Host "Step 4: Applying migrations with retry logic..." -ForegroundColor $colorInfo

$retryCount = 0
$success = $false

while (-not $success -and $retryCount -lt $MaxRetries) {
    try {
        $retryCount++
        Write-Host "  Attempt $retryCount of $MaxRetries..." -ForegroundColor $colorInfo
        
        # Use psql to execute the migration script
        $env:PGPASSWORD = $pgPassword
        $psqlCmd = "psql -h $pgHost -p $pgPort -U $pgUser -d $pgDatabase -f $migrationScriptPath"
        
        # Execute the command
        $output = Invoke-Expression $psqlCmd
        
        # Check if the command was successful
        if ($LASTEXITCODE -eq 0) {
            $success = $true
            Write-Host "  Migrations applied successfully!" -ForegroundColor $colorSuccess
        } else {
            Write-Host "  Failed to apply migrations. Error code: $LASTEXITCODE" -ForegroundColor $colorWarning
            Write-Host "  Output: $output" -ForegroundColor $colorWarning
            
            if ($retryCount -lt $MaxRetries) {
                Write-Host "  Retrying in $RetryDelaySeconds seconds..." -ForegroundColor $colorWarning
                Start-Sleep -Seconds $RetryDelaySeconds
            }
        }
    }
    catch {
        Write-Host "  Error applying migrations: $_" -ForegroundColor $colorWarning
        
        if ($retryCount -lt $MaxRetries) {
            Write-Host "  Retrying in $RetryDelaySeconds seconds..." -ForegroundColor $colorWarning
            Start-Sleep -Seconds $RetryDelaySeconds
        }
    }
    finally {
        # Clear environment variable
        Remove-Item Env:\PGPASSWORD -ErrorAction SilentlyContinue
    }
}

if (-not $success) {
    Write-Error "Failed to apply migrations after $MaxRetries attempts."
    exit 1
}

# Step 5: Verify migrations were applied
Write-Host "Step 5: Verifying migrations were applied..." -ForegroundColor $colorInfo

try {
    $env:PGPASSWORD = $pgPassword
    $verifyCmd = "psql -h $pgHost -p $pgPort -U $pgUser -d $pgDatabase -c 'SELECT \"MigrationId\", \"ProductVersion\" FROM \"__EFMigrationsHistory\" ORDER BY \"MigrationId\";'"
    
    $migrations = Invoke-Expression $verifyCmd
    
    Write-Host "  Applied migrations:" -ForegroundColor $colorSuccess
    $migrations | ForEach-Object { Write-Host "    $_" -ForegroundColor $colorInfo }
}
catch {
    Write-Host "  Warning: Could not verify migrations: $_" -ForegroundColor $colorWarning
}
finally {
    # Clear environment variable
    Remove-Item Env:\PGPASSWORD -ErrorAction SilentlyContinue
}

Write-Host ""
Write-Host "Migration process completed successfully!" -ForegroundColor $colorSuccess
Write-Host ""
