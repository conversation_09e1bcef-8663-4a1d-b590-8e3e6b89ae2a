using System;

namespace ProcureToPay.Domain.Events;

// Note: Domain Events often inherit from a base class (e.g., DomainEvent) 
// or implement an interface (e.g., MediatR's INotification) 
// to facilitate dispatching and handling. For simplicity, this is a standalone class.

/// <summary>
/// Represents an event raised when an action attempts to exceed a budget allocation.
/// </summary>
public class BudgetExceededEvent // Consider : IDomainEvent or : INotification
{
    /// <summary>
    /// The ID of the specific Budget Allocation that was exceeded.
    /// </summary>
    public Guid BudgetAllocationId { get; }

    /// <summary>
    /// The ID of the document (e.g., Purchase Requisition, Purchase Order) 
    /// whose approval or processing triggered the budget check failure.
    /// </summary>
    public Guid TriggeringDocumentId { get; }

    /// <summary>
    /// The type of the document that triggered the event (e.g., "PurchaseRequisition", "PurchaseOrder").
    /// </summary>
    public string TriggeringDocumentType { get; } = string.Empty;

    /// <summary>
    /// The amount by which the budget allocation was exceeded.
    /// TODO: Consider using a Money Value Object here.
    /// </summary>
    public decimal AmountExceededBy { get; }

    /// <summary>
    /// The total amount allocated to the budget item.
    /// </summary>
    public decimal AllocatedAmount { get; }

    /// <summary>
    /// The total amount attempted to be spent/committed by the triggering document.
    /// </summary>
    public decimal AttemptedSpendingAmount { get; }

    /// <summary>
    /// The date and time (UTC) when the budget exceedance was detected.
    /// </summary>
    public DateTimeOffset EventOccurredUtc { get; }

    /// <summary>
    /// Optional ID of the user whose action triggered the event.
    /// </summary>
    public string? UserId { get; }

    /// <summary>
    /// Initializes a new instance of the <see cref="BudgetExceededEvent"/> class.
    /// </summary>
    /// <param name="budgetAllocationId">ID of the exceeded budget allocation.</param>
    /// <param name="triggeringDocumentId">ID of the triggering document (e.g., PO, PR).</param>
    /// <param name="triggeringDocumentType">Type of the triggering document.</param>
    /// <param name="allocatedAmount">The total allocated amount.</param>
    /// <param name="attemptedSpendingAmount">The amount that was attempted to be spent.</param>
    /// <param name="amountExceededBy">The amount by which the budget was exceeded.</param>
    /// <param name="userId">Optional ID of the user triggering the action.</param>
    public BudgetExceededEvent(
        Guid budgetAllocationId,
        Guid triggeringDocumentId,
        string triggeringDocumentType,
        decimal allocatedAmount,
        decimal attemptedSpendingAmount,
        decimal amountExceededBy,
        string? userId = null)
    {
        if (budgetAllocationId == Guid.Empty) throw new ArgumentException("BudgetAllocationId cannot be empty.", nameof(budgetAllocationId));
        if (triggeringDocumentId == Guid.Empty) throw new ArgumentException("TriggeringDocumentId cannot be empty.", nameof(triggeringDocumentId));
        ArgumentException.ThrowIfNullOrWhiteSpace(triggeringDocumentType);
        if (amountExceededBy <= 0) throw new ArgumentOutOfRangeException(nameof(amountExceededBy), "Amount exceeded must be positive.");
        if (allocatedAmount < 0) throw new ArgumentOutOfRangeException(nameof(allocatedAmount), "Allocated amount cannot be negative.");
        if (attemptedSpendingAmount < 0) throw new ArgumentOutOfRangeException(nameof(attemptedSpendingAmount), "Attempted spending amount cannot be negative.");


        BudgetAllocationId = budgetAllocationId;
        TriggeringDocumentId = triggeringDocumentId;
        TriggeringDocumentType = triggeringDocumentType;
        AllocatedAmount = allocatedAmount;
        AttemptedSpendingAmount = attemptedSpendingAmount;
        AmountExceededBy = amountExceededBy;
        UserId = userId;
        EventOccurredUtc = DateTimeOffset.UtcNow;
    }
}
