using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProcureToPay.Domain.Entities;
using ProcureToPay.Domain.Enums; // Assuming RFPStatus enum exists
using ProcureToPay.Infrastructure.Persistence.Extensions;

namespace ProcureToPay.Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// Configuration for the RequestForProposal entity for EF Core and PostgreSQL.
    /// </summary>
    public class RequestForProposalConfiguration : IEntityTypeConfiguration<RequestForProposal>
    {
        public void Configure(EntityTypeBuilder<RequestForProposal> builder)
        {
            // Table Mapping
            builder.ToTable("requests_for_proposal");

            // Primary Key
            builder.HasKey(rfp => rfp.Id);
            builder.Property(rfp => rfp.Id).ValueGeneratedOnAdd();

            // Properties
            builder.Property(rfp => rfp.RfpNumber)
                .IsRequired()
                .HasMaxLength(100); // Adjust length as needed

            builder.Property(rfp => rfp.Title)
                .IsRequired()
                .HasMaxLength(255);

            builder.Property(rfp => rfp.ScopeOfWork)
                .HasColumnType("text")
                .IsRequired(false);

            builder.Property(rfp => rfp.IssueDate)
                .IsRequired()
                .HasColumnType("timestamp without time zone"); // Or "date"

            builder.Property(rfp => rfp.SubmissionDeadline)
                .IsRequired()
                .HasColumnType("timestamp without time zone"); // Or "date"

            builder.Property(rfp => rfp.DecisionDate)
                .HasColumnType("timestamp without time zone") // Or "date"
                .IsRequired(false); // Decision date might be TBD

            builder.Property(rfp => rfp.Status)
                .IsRequired()
                .HasConversion<string>() // Or int
                .HasMaxLength(50);

            builder.Property(rfp => rfp.EvaluationCriteria)
                .HasColumnType("jsonb") // Store complex criteria as JSON
                .IsRequired(false);

            // Foreign Keys (Assuming Guid or appropriate types)
            builder.Property(rfp => rfp.ProjectId).IsRequired(false); // RFP might not be project-specific

            builder.Property(rfp => rfp.TenantId).IsRequired();


            // --- Concurrency Token ---
            builder.UseXminAsConcurrencyToken();


            // --- Relationships ---
            // RFP-Proposals (One-to-Many)
            builder.HasMany(rfp => rfp.VendorProposals)
                   .WithOne(vp => vp.RequestForProposal) // Assuming VendorProposal has RFP nav prop
                   .HasForeignKey(vp => vp.RequestForProposalId)
                   .IsRequired()
                   .OnDelete(DeleteBehavior.Cascade); // Deleting RFP deletes related proposals

            // Link to Project (Many-to-One, Optional)
            builder.HasOne(rfp => rfp.Project)
                   .WithMany(p => p.RequestsForProposal) // Assuming Project has collection
                   .HasForeignKey(rfp => rfp.ProjectId)
                   .IsRequired(false)
                   .OnDelete(DeleteBehavior.SetNull); // Nullify link if project deleted


            // --- Indexes ---
            builder.HasIndex(rfp => rfp.RfpNumber).IsUnique();
            builder.HasIndex(rfp => rfp.Status);
            builder.HasIndex(rfp => rfp.SubmissionDeadline);
            builder.HasIndex(rfp => rfp.ProjectId);
            builder.HasIndex(rfp => rfp.TenantId);
            // Consider GIN index for jsonb column if querying criteria is common
            // builder.HasIndex(rfp => rfp.EvaluationCriteria).HasMethod("gin");


            // --- TODO ---
            // TODO: Verify FK type for ProjectId matches Project entity.
            // TODO: Define RFPStatus enum.
            // TODO: Consider modeling RFP Sections/Requirements if needed.
        }
    }
}
