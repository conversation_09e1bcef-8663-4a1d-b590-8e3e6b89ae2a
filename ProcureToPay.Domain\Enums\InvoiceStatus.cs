﻿namespace ProcureToPay.Domain.Enums;

/// <summary>
/// Represents the status of a Vendor Invoice within the Procure-to-Pay process.
/// </summary>
public enum InvoiceStatus
{
    /// <summary>
    /// Invoice received but not yet processed or validated.
    /// </summary>
    Received = 0,
    /// <summary>
    /// Invoice is pending internal approval (e.g., matching, budget checks).
    /// </summary>
    PendingApproval = 1,
    /// <summary>
    /// Invoice is approved and scheduled for payment.
    /// </summary>
    ApprovedForPayment = 2,
    /// <summary>
    /// Invoice has been rejected due to discrepancies or other issues.
    /// </summary>
    Rejected = 3,
    /// <summary>
    /// Invoice has been partially paid.
    /// </summary>
    PartiallyPaid = 4,
    /// <summary>
    /// Invoice has been fully paid.
    /// </summary>
    Paid = 5,
    /// <summary>
    /// Invoice processing was cancelled.
    /// </summary>
    Cancelled = 6
}
