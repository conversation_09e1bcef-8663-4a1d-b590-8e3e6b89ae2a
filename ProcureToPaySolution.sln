﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.13.35825.156 d17.13
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ProcureToPay.WebApp", "ProcureToPay.WebApp\ProcureToPay.WebApp\ProcureToPay.WebApp.csproj", "{4BD81DFD-EDE8-9653-08BA-56D30DA6A2CD}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ProcureToPay.WebApp.Client", "ProcureToPay.WebApp\ProcureToPay.WebApp.Client\ProcureToPay.WebApp.Client.csproj", "{A7B3403F-2C54-7D4F-FE99-5ECEC90924F6}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ProcureToPaySolution.AppHost", "ProcureToPaySolution.AppHost\ProcureToPaySolution.AppHost.csproj", "{09131ABA-22AD-44C0-B35E-CF752BA68C9C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ProcureToPaySolution.ServiceDefaults", "ProcureToPaySolution.ServiceDefaults\ProcureToPaySolution.ServiceDefaults.csproj", "{DF3EAC38-52C4-B917-20FA-63653CAD2A59}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ProcureToPay.Domain", "ProcureToPay.Domain\ProcureToPay.Domain.csproj", "{F6538E7A-F3E3-4F80-B40E-8B3016E833C4}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ProcureToPay.Application", "ProcureToPay.Application\ProcureToPay.Application.csproj", "{73867A2D-80BE-4A0D-8DF2-F1C851D97EDF}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ProcureToPay.Infrastructure", "ProcureToPay.Infrastructure\ProcureToPay.Infrastructure.csproj", "{9E8EEBD0-E1A1-4871-8FAB-B390DF7DE215}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{4BD81DFD-EDE8-9653-08BA-56D30DA6A2CD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4BD81DFD-EDE8-9653-08BA-56D30DA6A2CD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4BD81DFD-EDE8-9653-08BA-56D30DA6A2CD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4BD81DFD-EDE8-9653-08BA-56D30DA6A2CD}.Release|Any CPU.Build.0 = Release|Any CPU
		{A7B3403F-2C54-7D4F-FE99-5ECEC90924F6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A7B3403F-2C54-7D4F-FE99-5ECEC90924F6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A7B3403F-2C54-7D4F-FE99-5ECEC90924F6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A7B3403F-2C54-7D4F-FE99-5ECEC90924F6}.Release|Any CPU.Build.0 = Release|Any CPU
		{09131ABA-22AD-44C0-B35E-CF752BA68C9C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{09131ABA-22AD-44C0-B35E-CF752BA68C9C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{09131ABA-22AD-44C0-B35E-CF752BA68C9C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{09131ABA-22AD-44C0-B35E-CF752BA68C9C}.Release|Any CPU.Build.0 = Release|Any CPU
		{DF3EAC38-52C4-B917-20FA-63653CAD2A59}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DF3EAC38-52C4-B917-20FA-63653CAD2A59}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DF3EAC38-52C4-B917-20FA-63653CAD2A59}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DF3EAC38-52C4-B917-20FA-63653CAD2A59}.Release|Any CPU.Build.0 = Release|Any CPU
		{F6538E7A-F3E3-4F80-B40E-8B3016E833C4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F6538E7A-F3E3-4F80-B40E-8B3016E833C4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F6538E7A-F3E3-4F80-B40E-8B3016E833C4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F6538E7A-F3E3-4F80-B40E-8B3016E833C4}.Release|Any CPU.Build.0 = Release|Any CPU
		{73867A2D-80BE-4A0D-8DF2-F1C851D97EDF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{73867A2D-80BE-4A0D-8DF2-F1C851D97EDF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{73867A2D-80BE-4A0D-8DF2-F1C851D97EDF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{73867A2D-80BE-4A0D-8DF2-F1C851D97EDF}.Release|Any CPU.Build.0 = Release|Any CPU
		{9E8EEBD0-E1A1-4871-8FAB-B390DF7DE215}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9E8EEBD0-E1A1-4871-8FAB-B390DF7DE215}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9E8EEBD0-E1A1-4871-8FAB-B390DF7DE215}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9E8EEBD0-E1A1-4871-8FAB-B390DF7DE215}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {94304A40-36F6-4956-BB87-8442AAACBAED}
	EndGlobalSection
EndGlobal
