﻿namespace ProcureToPay.Domain.Enums
{
    /// <summary>
    /// Represents the lifecycle status of a Return Authorization.
    /// </summary>
    public enum ReturnAuthorizationStatus
    {
        /// <summary>
        /// The return request has been submitted by the customer and is pending review.
        /// </summary>
        Requested = 0,

        /// <summary>
        /// The return request has been approved, and the customer can return the items.
        /// </summary>
        Approved = 1,

        /// <summary>
        /// The return request was rejected.
        /// </summary>
        Rejected = 2,

        /// <summary>
        /// The returned items have been physically received (partially or fully).
        /// </summary>
        ItemsReceived = 3,

        /// <summary>
        /// The return process is complete (e.g., refund/credit issued, replacement sent).
        /// </summary>
        Completed = 4,

        /// <summary>
        /// The return authorization was cancelled before completion.
        /// </summary>
        Cancelled = 5
    }
}
