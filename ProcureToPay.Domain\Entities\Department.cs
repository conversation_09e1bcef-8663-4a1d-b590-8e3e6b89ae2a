﻿using System;
using System.Collections.Generic;
// Assuming BaseEntity<Guid> and BudgetAllocation exist in this namespace
// using ProcureToPay.Domain.Entities;

namespace ProcureToPay.Domain.Entities
{
    /// <summary>
    /// Represents a Department within the organization, often used for budget allocation.
    /// </summary>
    public class Department : BaseEntity<Guid> // Assuming BaseEntity<Guid>
    {
        /// <summary>
        /// Foreign Key for multi-tenancy.
        /// </summary>
        public Guid TenantId { get; private set; }

        /// <summary>
        /// Name of the department (e.g., "IT", "Marketing", "Finance").
        /// </summary>
        public string Name { get; private set; } = null!;

        /// <summary>
        /// Optional code for the department.
        /// </summary>
        public string? Code { get; private set; }

        /// <summary>
        /// Optional description of the department's function.
        /// </summary>
        public string? Description { get; private set; }

        // --- Navigation Properties ---

        /// <summary>
        /// Budget allocations assigned to this department.
        /// </summary>
        public virtual ICollection<BudgetAllocation> BudgetAllocations { get; private set; } = new List<BudgetAllocation>();

        // Add other relevant properties like HeadOfDepartmentId, etc. if needed

        /// <summary>
        /// Private constructor for EF Core.
        /// </summary>
        private Department() : base(Guid.NewGuid()) { }

        /// <summary>
        /// Creates a new Department instance.
        /// </summary>
        /// <param name="id">Department identifier.</param>
        /// <param name="tenantId">Tenant identifier.</param>
        /// <param name="name">Name of the department.</param>
        /// <param name="code">Optional department code.</param>
        /// <param name="description">Optional description.</param>
        public Department(Guid id, Guid tenantId, string name, string? code = null, string? description = null) : base(id)
        {
            if (tenantId == Guid.Empty) throw new ArgumentException("TenantId cannot be empty.", nameof(tenantId));
            ArgumentException.ThrowIfNullOrWhiteSpace(name);

            TenantId = tenantId;
            Name = name;
            Code = code;
            Description = description;
        }

        // --- Domain Methods ---
        public void UpdateDetails(string name, string? code, string? description)
        {
            ArgumentException.ThrowIfNullOrWhiteSpace(name);
            // Add change tracking and domain events if needed
            Name = name;
            Code = code;
            Description = description;
            // AddDomainEvent(...)
        }
    }
}
