# Enhanced PowerShell script for applying migrations in .NET Aspire projects
# This script handles connection string extraction from the Aspire dashboard
# and applies migrations with proper error handling and verification

param(
    [Parameter(Mandatory=$false)]
    [string]$ConnectionString,
    
    [Parameter(Mandatory=$false)]
    [switch]$FromDashboard,
    
    [Parameter(Mandatory=$false)]
    [string]$DashboardUrl = "http://localhost:15888",
    
    [Parameter(Mandatory=$false)]
    [string]$DatabaseResourceName = "postgresdb",
    
    [Parameter(Mandatory=$false)]
    [int]$MaxRetries = 5,
    
    [Parameter(Mandatory=$false)]
    [string]$InfrastructureProject = "ProcureToPay.Infrastructure",
    
    [Parameter(Mandatory=$false)]
    [string]$WebAppProject = "ProcureToPay.WebApp\ProcureToPay.WebApp"
)

Write-Host "Enhanced Aspire Migration Script" -ForegroundColor Cyan
Write-Host "===============================" -ForegroundColor Cyan

# Set the working directory to the solution root
$solutionDir = $PSScriptRoot
$infrastructureProjectPath = Join-Path $solutionDir $InfrastructureProject
$webAppProjectPath = Join-Path $solutionDir $WebAppProject

# Function to get connection string from Aspire dashboard
function Get-AspireConnectionString {
    param (
        [string]$dashboardUrl,
        [string]$resourceName
    )
    
    Write-Host "Attempting to get connection string from Aspire dashboard..." -ForegroundColor Cyan
    
    try {
        # Check if the dashboard is accessible
        $dashboardResponse = Invoke-WebRequest -Uri $dashboardUrl -UseBasicParsing -ErrorAction Stop
        
        # If dashboard is accessible, try to get the resource details
        $resourceUrl = "$dashboardUrl/api/v1/resources/$resourceName"
        Write-Host "Querying resource at: $resourceUrl" -ForegroundColor Cyan
        
        $resourceResponse = Invoke-WebRequest -Uri $resourceUrl -UseBasicParsing -ErrorAction Stop
        $resourceData = $resourceResponse.Content | ConvertFrom-Json
        
        # Extract connection string from environment variables
        $envVars = $resourceData.environmentVariables
        if ($envVars -and $envVars.ConnectionStrings -and $envVars.ConnectionStrings.postgresdb) {
            return $envVars.ConnectionStrings.postgresdb
        }
        
        Write-Warning "Could not find connection string in resource data."
        return $null
    }
    catch {
        Write-Warning "Error accessing Aspire dashboard: $_"
        return $null
    }
}

# Resolve connection string
if (-not $ConnectionString) {
    if ($FromDashboard) {
        $ConnectionString = Get-AspireConnectionString -dashboardUrl $DashboardUrl -resourceName $DatabaseResourceName
        
        if (-not $ConnectionString) {
            Write-Error "Failed to get connection string from Aspire dashboard. Please ensure the dashboard is running and the resource name is correct."
            exit 1
        }
    }
    else {
        Write-Error "Connection string is required. Either provide it directly with -ConnectionString or use -FromDashboard to extract it from the Aspire dashboard."
        exit 1
    }
}

# Mask password in output for security
$maskedConnectionString = if ($ConnectionString -match "Password=([^;]+)") {
    $ConnectionString -replace "Password=$($matches[1])", "Password=***"
} else {
    $ConnectionString
}
Write-Host "Using Connection String: $maskedConnectionString" -ForegroundColor Cyan

# Step 1: Verify database connectivity
Write-Host "Step 1: Verifying database connectivity..." -ForegroundColor Cyan

$retry = 0
$connected = $false

while (-not $connected -and $retry -lt $MaxRetries) {
    try {
        # Extract connection parameters
        $connectionParams = @{}
        $ConnectionString -split ';' | ForEach-Object {
            $keyValue = $_ -split '='
            if ($keyValue.Length -eq 2) {
                $connectionParams[$keyValue[0].Trim()] = $keyValue[1].Trim()
            }
        }
        
        $pgHost = $connectionParams["Host"]
        $pgPort = $connectionParams["Port"]
        $pgDatabase = $connectionParams["Database"]
        $pgUser = $connectionParams["Username"]
        
        # Test connection using dotnet-ef
        Write-Host "  Testing database connection (attempt $($retry+1)/$MaxRetries)..." -ForegroundColor Cyan
        
        $testCmd = "dotnet ef database verify --project $infrastructureProjectPath --startup-project $webAppProjectPath --connection `"$ConnectionString`""
        $testOutput = Invoke-Expression $testCmd
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "  Database connection successful!" -ForegroundColor Green
            $connected = $true
        } else {
            $retry++
            $waitTime = [Math]::Pow(2, $retry)
            Write-Warning "  Database connection failed. Retrying in $waitTime seconds..."
            Start-Sleep -Seconds $waitTime
        }
    }
    catch {
        $retry++
        $waitTime = [Math]::Pow(2, $retry)
        Write-Warning "  Error connecting to database: $_"
        Write-Warning "  Retrying in $waitTime seconds..."
        Start-Sleep -Seconds $waitTime
    }
}

if (-not $connected) {
    Write-Error "Failed to connect to the database after $MaxRetries attempts. Please check your connection string and ensure the database is running."
    exit 1
}

# Step 2: Apply migrations
Write-Host "Step 2: Applying migrations..." -ForegroundColor Cyan

try {
    # Execute the migration command
    $migrationCmd = "dotnet ef database update --project $infrastructureProjectPath --startup-project $webAppProjectPath --connection `"$ConnectionString`" --verbose"
    Write-Host "  Running: $($migrationCmd -replace $ConnectionString, $maskedConnectionString)" -ForegroundColor Cyan
    
    $migrationOutput = Invoke-Expression $migrationCmd
    
    # Display the output
    $migrationOutput | ForEach-Object { Write-Host "    $_" }
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "  Migrations applied successfully!" -ForegroundColor Green
    } else {
        Write-Error "  Migration failed with exit code $LASTEXITCODE"
        exit 1
    }
}
catch {
    Write-Error "  An error occurred while applying migrations: $_"
    exit 1
}

# Step 3: Verify migrations were applied
Write-Host "Step 3: Verifying migrations were applied..." -ForegroundColor Cyan

try {
    # Check for __EFMigrationsHistory table
    $checkTableCmd = "dotnet ef database verify --project $infrastructureProjectPath --startup-project $webAppProjectPath --connection `"$ConnectionString`""
    $checkTableOutput = Invoke-Expression $checkTableCmd
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "  Database verification successful!" -ForegroundColor Green
        
        # Generate migration script to see what was applied
        $scriptPath = Join-Path $solutionDir "migration_script.sql"
        $scriptCmd = "dotnet ef migrations script --project $infrastructureProjectPath --startup-project $webAppProjectPath --output $scriptPath --idempotent"
        
        Write-Host "  Generating migration script for reference..." -ForegroundColor Cyan
        Invoke-Expression $scriptCmd
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "  Migration script generated at: $scriptPath" -ForegroundColor Green
        }
    } else {
        Write-Warning "  Database verification failed. Migrations may not have been applied correctly."
    }
}
catch {
    Write-Warning "  Error verifying migrations: $_"
}

Write-Host "Migration process completed." -ForegroundColor Green
