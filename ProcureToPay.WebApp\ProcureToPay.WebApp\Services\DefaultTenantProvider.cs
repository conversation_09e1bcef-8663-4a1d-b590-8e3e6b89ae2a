using ProcureToPay.Domain.Interfaces;
using ProcureToPay.Infrastructure.Persistence;
using System;

namespace ProcureToPay.WebApp.Services
{
    /// <summary>
    /// Default implementation of ITenantProvider that can be used in production.
    /// Uses the TenantResolutionStrategy to determine the current tenant ID.
    /// </summary>
    public class DefaultTenantProvider : ITenantProvider
    {
        private readonly TenantResolutionStrategy _tenantResolutionStrategy;

        /// <summary>
        /// Initializes a new instance of the <see cref="DefaultTenantProvider"/> class.
        /// </summary>
        /// <param name="tenantResolutionStrategy">The tenant resolution strategy.</param>
        public DefaultTenantProvider(TenantResolutionStrategy tenantResolutionStrategy)
        {
            _tenantResolutionStrategy = tenantResolutionStrategy ?? throw new ArgumentNullException(nameof(tenantResolutionStrategy));
        }

        /// <summary>
        /// Gets the current tenant ID using the tenant resolution strategy.
        /// </summary>
        /// <returns>The current tenant ID.</returns>
        public Guid? GetCurrentTenantId()
        {
            return _tenantResolutionStrategy.GetCurrentTenantId();
        }
    }
}
