@page "/categories-and-products"
@using Microsoft.AspNetCore.Authorization
@using ProcureToPay.Domain.Entities
@using ProcureToPay.Domain.Interfaces
@using Microsoft.EntityFrameworkCore

@attribute [Authorize]
@inject ITenantProvider TenantProvider
@inject ProcureToPay.Infrastructure.Persistence.ApplicationDbContext DbContext

<PageTitle>Categories and Products</PageTitle>

<h1>Categories and Products</h1>

<div class="alert alert-info">
    <p><strong>Current Tenant ID:</strong> @TenantProvider.GetCurrentTenantId()</p>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <h3>Categories</h3>
            </div>
            <div class="card-body">
                @if (isLoading)
                {
                    <p>Loading categories...</p>
                }
                else if (categories.Count == 0)
                {
                    <p>No categories found for the current tenant.</p>
                }
                else
                {
                    <ul class="list-group">
                        @foreach (var category in categories)
                        {
                            <li class="list-group-item">
                                <h5>@category.Name</h5>
                                <p>@category.Description</p>
                                <small>Code: @category.Code</small>
                            </li>
                        }
                    </ul>
                }
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <h3>Products</h3>
            </div>
            <div class="card-body">
                @if (isLoading)
                {
                    <p>Loading products...</p>
                }
                else if (products.Count == 0)
                {
                    <p>No products found for the current tenant.</p>
                }
                else
                {
                    <ul class="list-group">
                        @foreach (var product in products)
                        {
                            <li class="list-group-item">
                                <h5>@product.Name</h5>
                                <p>@product.Description</p>
                                <div class="d-flex justify-content-between">
                                    <small>Code: @product.ProductCode</small>
                                    <small>Unit: @product.UnitOfMeasure</small>
                                </div>
                            </li>
                        }
                    </ul>
                }
            </div>
        </div>
    </div>
</div>

@code {
    private bool isLoading = true;
    private List<Category> categories = new List<Category>();
    private List<Product> products = new List<Product>();

    protected override async Task OnInitializedAsync()
    {
        try
        {
            // Load categories
            categories = await DbContext.Set<Category>()
                .OrderBy(c => c.Name)
                .ToListAsync();

            // Load products
            products = await DbContext.Set<Product>()
                .Include(p => p.Category)
                .Where(p => !p.IsDeleted)
                .OrderBy(p => p.Name)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading data: {ex.Message}");
        }
        finally
        {
            isLoading = false;
        }
    }
}
