using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProcureToPay.Domain.Entities;
using ProcureToPay.Infrastructure.Identity; // Assuming ApplicationUser exists
// using ProcureToPay.Domain.Entities; // Assuming Role entity exists if using RoleId

namespace ProcureToPay.Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// Configuration for the ProcurementWorkflowStep entity for EF Core and PostgreSQL.
    /// </summary>
    public class ProcurementWorkflowStepConfiguration : IEntityTypeConfiguration<ProcurementWorkflowStep>
    {
        public void Configure(EntityTypeBuilder<ProcurementWorkflowStep> builder)
        {
            // Table Mapping
            builder.ToTable("procurement_workflow_steps");

            // Primary Key Strategy:
            // Option 1: Simple Identity PK (as chosen here)
            builder.HasKey(s => s.Id);
            builder.Property(s => s.Id).ValueGeneratedOnAdd();

            // Option 2: Composite Key (Uncomment if preferred)
            // builder.HasKey(s => new { s.ProcurementWorkflowId, s.StepOrder });
            // builder.Property(s => s.StepOrder).ValueGeneratedNever();

            // Properties
            builder.Property(s => s.StepOrder)
                .IsRequired();

            builder.Property(s => s.StepName)
                .IsRequired()
                .HasMaxLength(150);

            // Foreign Keys (Assuming Guid or appropriate types)
            builder.Property(s => s.ApproverRoleId).IsRequired(false); // Link to Role or User
            builder.Property(s => s.ApproverUserId).IsRequired(false); // Link to Role or User

            builder.Property(s => s.ConditionExpression)
                .HasColumnType("text") // Store condition logic (e.g., amount > 1000)
                .IsRequired(false);

            // Map SlaDuration using Npgsql support for TimeSpan <-> interval
            builder.Property(s => s.SlaDuration)
                .HasColumnType("interval") // Maps to PostgreSQL interval type
                .IsRequired(false);

            // Foreign Key to Workflow
            builder.Property(s => s.ProcurementWorkflowId).IsRequired();


            // --- Relationships ---
            // Step-Workflow (Many-to-One)
            builder.HasOne(s => s.ProcurementWorkflow)
                   .WithMany(w => w.Steps)
                   .HasForeignKey(s => s.ProcurementWorkflowId)
                   .IsRequired() // Required as per relationship definition
                   .OnDelete(DeleteBehavior.Cascade); // Matches workflow config

            // Link to Role/User (Many-to-One, Optional)
            // Assuming Role entity exists
            // builder.HasOne(s => s.ApproverRole)
            //        .WithMany()
            //        .HasForeignKey(s => s.ApproverRoleId)
            //        .IsRequired(false) // Step might not require specific role
            //        .OnDelete(DeleteBehavior.Restrict); // Prevent deleting role if used in steps

            // Assuming ApplicationUser entity exists
            builder.HasOne<ProcureToPay.Infrastructure.Identity.ApplicationUser>() // Or your specific user entity
                   .WithMany()
                   .HasForeignKey(s => s.ApproverUserId)
                   .IsRequired(false) // Step might not require specific user
                   .OnDelete(DeleteBehavior.Restrict); // Prevent deleting user if assigned as approver


            // --- Indexes ---
            builder.HasIndex(s => s.ProcurementWorkflowId);
            // If using simple Id PK, add unique constraint on WorkflowId + StepOrder
            builder.HasIndex(s => new { s.ProcurementWorkflowId, s.StepOrder }).IsUnique();
            builder.HasIndex(s => s.ApproverRoleId);
            builder.HasIndex(s => s.ApproverUserId);


            // --- TODO ---
            // TODO: Define Role entity if using ApproverRoleId.
            // TODO: Verify FK types match related entities (Role, User).
            // TODO: Decide on PK strategy (Identity or Composite).
        }
    }
}
