using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProcureToPay.Domain.Entities; // Assuming SalesOrder, SalesOrderLine, Customer, ReturnAuthorization, SalesTerritory entities exist
using ProcureToPay.Domain.Enums;   // Assuming SalesOrderStatus enum exists
using ProcureToPay.Domain.ValueObjects; // Assuming Address, Money VOs exist
using System;

namespace ProcureToPay.Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// EF Core Configuration for the SalesOrder entity targeting PostgreSQL.
    /// </summary>
    public class SalesOrderConfiguration : IEntityTypeConfiguration<SalesOrder>
    {
        // Placeholder for Tenant ID - Inject or retrieve appropriately in DbContext
        private readonly Guid? _tenantId;
        private const string DefaultSchema = "public"; // Example schema

        /// <summary>
        /// Initializes a new instance of the <see cref="SalesOrderConfiguration"/> class.
        /// </summary>
        public SalesOrderConfiguration()
        {
            // Default constructor - _tenantId remains null
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="SalesOrderConfiguration"/> class with a specific tenant ID.
        /// </summary>
        /// <param name="tenantId">The tenant ID to use for filtering.</param>
        public SalesOrderConfiguration(Guid tenantId)
        {
            _tenantId = tenantId;
        }

        public void Configure(EntityTypeBuilder<SalesOrder> builder)
        {
            // --- Table Mapping ---
            builder.ToTable("sales_orders", DefaultSchema); // Use snake_case and schema

            // --- Primary Key ---
            builder.HasKey(so => so.Id);

            // --- Soft Delete Configuration ---
            builder.Property(so => so.IsDeleted)
                   .HasDefaultValue(false)
                   .IsRequired();
            builder.HasIndex(so => so.IsDeleted);

            // --- Concurrency Control (PostgreSQL xmin - EF Core 7+) ---
            builder.Property<uint>("xmin")
                   .HasColumnType("xid")
                   .ValueGeneratedOnAddOrUpdate()
                   .IsConcurrencyToken();

            // --- Property Mappings & Constraints ---
            builder.Property(so => so.TenantId)
                   .IsRequired();
            builder.HasIndex(so => so.TenantId);

            builder.Property(so => so.OrderNumber)
                   .IsRequired()
                   .HasMaxLength(50);
            // Unique index within a tenant
            builder.HasIndex(so => new { so.TenantId, so.OrderNumber }).IsUnique();

            builder.Property(so => so.OrderDate)
                   .IsRequired()
                   .HasColumnType("timestamp without time zone");
            builder.HasIndex(so => so.OrderDate);

            // Map Status Enum
            builder.Property(so => so.Status)
                   .IsRequired()
                   .HasConversion<string>()
                   .HasMaxLength(50);
            builder.HasIndex(so => so.Status);

            // Currency Code (Also part of Money VO, stored directly for easier querying)
            builder.Property(so => so.CurrencyCode)
                  .IsRequired()
                  .HasMaxLength(3);

            // Flags and Indicators
            builder.Property(so => so.IsCreditApproved).IsRequired().HasDefaultValue(false);
            builder.Property(so => so.AtpCheckDate).HasColumnType("timestamp without time zone"); // Nullable
            builder.Property(so => so.IsAtpConfirmed).IsRequired().HasDefaultValue(false);
            builder.Property(so => so.IsDropShipment).IsRequired().HasDefaultValue(false);

            // Sales & Commission
            builder.Property(so => so.SalespersonId).HasMaxLength(450); // Match Identity User Id length
            builder.HasIndex(so => so.SalespersonId);
            builder.Property(so => so.CommissionRate).HasPrecision(5, 4); // e.g., 0.0000 to 1.0000 for rate

            // Related Documents / Integration
            builder.Property(so => so.RelatedReturnAuthorizationId); // Nullable Guid
            builder.HasIndex(so => so.RelatedReturnAuthorizationId);
            builder.Property(so => so.SalesTerritoryId); // Nullable Guid
            builder.HasIndex(so => so.SalesTerritoryId);
            builder.Property(so => so.EdiTransactionReference).HasMaxLength(100);
            builder.HasIndex(so => so.EdiTransactionReference);

            // Configure CustomerId FK (relationship configured below)
            builder.Property(so => so.CustomerId); // Nullable Guid
            builder.HasIndex(so => so.CustomerId);


            // --- Value Object Mapping ---

            // BillingAddress (Address VO)
            builder.OwnsOne(so => so.BillingAddress, addr =>
            {
                addr.Property(a => a.Street).HasColumnName("billing_street").HasMaxLength(200).IsRequired();
                addr.Property(a => a.City).HasColumnName("billing_city").HasMaxLength(100).IsRequired();
                addr.Property(a => a.State).HasColumnName("billing_state").HasMaxLength(100).IsRequired();
                addr.Property(a => a.Country).HasColumnName("billing_country").HasMaxLength(100).IsRequired();
                addr.Property(a => a.PostalCode).HasColumnName("billing_postal_code").HasMaxLength(20).IsRequired();
            });
            builder.Navigation(so => so.BillingAddress).IsRequired();

            // ShippingAddress (Address VO)
            builder.OwnsOne(so => so.ShippingAddress, addr =>
            {
                addr.Property(a => a.Street).HasColumnName("shipping_street").HasMaxLength(200).IsRequired();
                addr.Property(a => a.City).HasColumnName("shipping_city").HasMaxLength(100).IsRequired();
                addr.Property(a => a.State).HasColumnName("shipping_state").HasMaxLength(100).IsRequired();
                addr.Property(a => a.Country).HasColumnName("shipping_country").HasMaxLength(100).IsRequired();
                addr.Property(a => a.PostalCode).HasColumnName("shipping_postal_code").HasMaxLength(20).IsRequired();
            });
            builder.Navigation(so => so.ShippingAddress).IsRequired();

            // TotalAmount (Money VO)
            builder.OwnsOne(so => so.TotalAmount, total =>
            {
                total.Property(m => m.Amount)
                    .HasColumnName("total_amount") // snake_case
                    .HasPrecision(18, 4) // Consistent precision
                    .IsRequired();
                // Ignore CurrencyCode mapping here as it's a direct property on SalesOrder
                total.Ignore(m => m.CurrencyCode);
            });
            builder.Navigation(so => so.TotalAmount).IsRequired();


            // --- Relationships ---

            // To SalesOrderLines (One SO to Many Lines)
            builder.HasMany(so => so.Lines)
                   .WithOne(l => l.SalesOrder) // Assumes SalesOrderLine has SalesOrder nav prop
                   .HasForeignKey(l => l.SalesOrderId) // Assumes SalesOrderLine has FK
                   .IsRequired()
                   .OnDelete(DeleteBehavior.Cascade); // Deleting SO deletes its lines

            // To Customer (Optional Many SOs to One Customer)
            builder.HasOne(so => so.Customer)
                   .WithMany(c => c.SalesOrders) // Assumes Customer has ICollection<SalesOrder> SalesOrders
                   .HasForeignKey(so => so.CustomerId)
                   .IsRequired(false) // Customer might be optional
                   .OnDelete(DeleteBehavior.Restrict); // Prevent deleting Customer if they have Sales Orders

            // To ReturnAuthorization (Optional One SO to One RA - or Many?)
            builder.HasOne(so => so.ReturnAuthorization)
                   .WithMany() // Assuming RA doesn't need direct collection of SOs
                   .HasForeignKey(so => so.RelatedReturnAuthorizationId)
                   .IsRequired(false)
                   .OnDelete(DeleteBehavior.SetNull); // If RA deleted, nullify link

            // To SalesTerritory (Optional Many SOs to One Territory)
            builder.HasOne(so => so.SalesTerritory)
                   .WithMany() // Assuming Territory doesn't need direct collection of SOs
                   .HasForeignKey(so => so.SalesTerritoryId)
                   .IsRequired(false)
                   .OnDelete(DeleteBehavior.SetNull); // If Territory deleted, nullify link


            // --- Tenant Isolation & Soft Delete Query Filter ---
            if (_tenantId.HasValue)
            {
                builder.HasQueryFilter(so => !so.IsDeleted && so.TenantId == _tenantId);
            }
            else
            {
                builder.HasQueryFilter(so => !so.IsDeleted);
            }
        }
    }
}

