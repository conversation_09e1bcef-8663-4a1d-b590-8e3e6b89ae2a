﻿using System;
using System.Collections.Generic;
using ProcureToPay.Domain.Events;

namespace ProcureToPay.Domain.Entities
{
    /// <summary>
    /// Abstract base class for domain entities, providing an identifier, domain event handling,
    /// and audit properties.
    /// </summary>
    /// <typeparam name="TId">The type of the entity's identifier.</typeparam>
    public abstract class BaseEntity<TId> where TId : struct, IEquatable<TId>
    {
        private readonly List<IDomainEvent> _domainEvents = new List<IDomainEvent>();

        /// <summary>
        /// The unique identifier for the entity.
        /// </summary>
        public TId Id { get; protected set; }

        /// <summary>
        /// Date and time when the entity was created.
        /// </summary>
        public DateTime CreatedAt { get; private set; }

        /// <summary>
        /// Date and time when the entity was last modified.
        /// </summary>
        public DateTime? ModifiedAt { get; private set; }

        /// <summary>
        /// Collection of domain events raised by this entity.
        /// </summary>
        public IReadOnlyCollection<IDomainEvent> DomainEvents => _domainEvents.AsReadOnly();

        /// <summary>
        /// Protected constructor for derived classes and EF Core.
        /// </summary>
        /// <param name="id">The identifier for the entity.</param>
        protected BaseEntity(TId id)
        {
            // Basic check, though struct constraint helps
            if (id.Equals(default))
            {
                throw new ArgumentException("Entity ID cannot be the default value.", nameof(id));
            }
            Id = id;
            CreatedAt = DateTime.UtcNow;
        }

        /// <summary>
        /// Updates the ModifiedAt timestamp to the current UTC time.
        /// </summary>
        public void UpdateModifiedAt()
        {
            ModifiedAt = DateTime.UtcNow;
        }

        /// <summary>
        /// Adds a domain event to the entity's collection.
        /// </summary>
        /// <param name="domainEvent">The domain event to add.</param>
        protected void AddDomainEvent(IDomainEvent domainEvent)
        {
            _domainEvents.Add(domainEvent ?? throw new ArgumentNullException(nameof(domainEvent)));
        }

        /// <summary>
        /// Adds a domain event to the entity's collection.
        /// This overload maintains backward compatibility with existing code.
        /// </summary>
        /// <param name="domainEvent">The domain event to add.</param>
        protected void AddDomainEvent(object domainEvent)
        {
            ArgumentNullException.ThrowIfNull(domainEvent);

            if (domainEvent is IDomainEvent typedEvent)
            {
                _domainEvents.Add(typedEvent);
            }
            else
            {
                _domainEvents.Add(new LegacyDomainEvent(domainEvent));
            }
        }

        /// <summary>
        /// Wrapper for legacy domain events.
        /// </summary>
        private class LegacyDomainEvent(object originalEvent) : IDomainEvent
        {
            public DateTime OccurredOn { get; } = DateTime.UtcNow;
            public Guid EventId { get; } = Guid.NewGuid();
            public object OriginalEvent { get; } = originalEvent;
        }

        /// <summary>
        /// Clears all domain events from the entity's collection.
        /// Typically called after events have been dispatched.
        /// </summary>
        public void ClearDomainEvents()
        {
            _domainEvents.Clear();
        }

        // --- Equality Implementation (Optional but recommended for Entities) ---

        public override bool Equals(object? obj)
        {
            if (obj == null || obj is not BaseEntity<TId> other)
            {
                return false;
            }

            // Handles transient entities (Id is default) and compares by reference.
            // Compares persistent entities by Id.
            if (IsTransient() || other.IsTransient())
            {
                return ReferenceEquals(this, other);
            }

            return Id.Equals(other.Id);
        }

        /// <summary>
        /// Checks if the entity is transient (has not been assigned a persistent Id).
        /// </summary>
        /// <returns>True if the entity's Id is the default value for its type.</returns>
        public bool IsTransient()
        {
            return Id.Equals(default);
        }

        public override int GetHashCode()
        {
            // Consistent hash code for persistent entities based on Id.
            // Uses base hash code for transient entities.
            return IsTransient() ? base.GetHashCode() : Id.GetHashCode();
        }

        public static bool operator ==(BaseEntity<TId>? left, BaseEntity<TId>? right)
        {
            if (left is null && right is null)
                return true;
            if (left is null || right is null)
                return false;

            return left.Equals(right);
        }

        public static bool operator !=(BaseEntity<TId>? left, BaseEntity<TId>? right)
        {
            return !(left == right);
        }
    }
}
