﻿using System;
using ProcureToPay.Domain.Enums;       // Required for RequisitionStatus
using ProcureToPay.Domain.ValueObjects; // Required for Address (if included in events)

// Define events within the appropriate namespace
namespace ProcureToPay.Domain.Events
{
    // Using records for immutable event data carriers

    /// <summary>
    /// Event raised when a new Purchase Requisition is created.
    /// </summary>
    /// <param name="RequisitionId">The ID of the newly created requisition.</param>
    /// <param name="TenantId">The Tenant ID associated with the requisition.</param>
    public record RequisitionCreatedEvent(Guid RequisitionId, Guid TenantId);

    /// <summary>
    /// Event raised when a line item is added to a Purchase Requisition.
    /// </summary>
    /// <param name="RequisitionId">The ID of the requisition.</param>
    /// <param name="LineId">The ID of the added line item.</param>
    public record RequisitionLineAddedEvent(Guid RequisitionId, Guid LineId);

    /// <summary>
    /// Event raised when a line item is removed from a Purchase Requisition.
    /// </summary>
    /// <param name="RequisitionId">The ID of the requisition.</param>
    /// <param name="LineId">The ID of the removed line item.</param>
    public record RequisitionLineRemovedEvent(Guid RequisitionId, Guid LineId);

    /// <summary>
    /// Event raised when core details (justification, department, etc.) of a Purchase Requisition are updated.
    /// </summary>
    /// <param name="RequisitionId">The ID of the updated requisition.</param>
    public record RequisitionDetailsUpdatedEvent(Guid RequisitionId);

    /// <summary>
    /// Event raised when the shipping address of a Purchase Requisition is updated.
    /// </summary>
    /// <param name="RequisitionId">The ID of the updated requisition.</param>
    // Optional: Include the new Address? value if needed by handlers
    // public record RequisitionShippingAddressUpdatedEvent(Guid RequisitionId, Address? NewAddress);
    public record RequisitionShippingAddressUpdatedEvent(Guid RequisitionId);


    /// <summary>
    /// Event raised when a Purchase Requisition is submitted for approval.
    /// </summary>
    /// <param name="RequisitionId">The ID of the submitted requisition.</param>
    public record RequisitionSubmittedEvent(Guid RequisitionId);

    /// <summary>
    /// Event raised when a Purchase Requisition is approved.
    /// </summary>
    /// <param name="RequisitionId">The ID of the approved requisition.</param>
    public record RequisitionApprovedEvent(Guid RequisitionId);

    /// <summary>
    /// Event raised when a Purchase Requisition is rejected.
    /// </summary>
    /// <param name="RequisitionId">The ID of the rejected requisition.</param>
    /// <param name="Reason">The reason provided for rejection.</param>
    public record RequisitionRejectedEvent(Guid RequisitionId, string Reason);

    /// <summary>
    /// Event raised when a Purchase Requisition is marked as (partially or fully) ordered.
    /// </summary>
    /// <param name="RequisitionId">The ID of the requisition.</param>
    /// <param name="PurchaseOrderId">The ID of the associated Purchase Order.</param>
    /// <param name="IsFullyOrdered">Flag indicating if the requisition is now fully ordered.</param>
    public record RequisitionOrderedEvent(Guid RequisitionId, Guid PurchaseOrderId, bool IsFullyOrdered);

    /// <summary>
    /// Event raised when a Purchase Requisition is cancelled.
    /// </summary>
    /// <param name="RequisitionId">The ID of the cancelled requisition.</param>
    /// <param name="Reason">The reason provided for cancellation.</param>
    public record RequisitionCancelledEvent(Guid RequisitionId, string Reason);

    /// <summary>
    /// Generic event raised whenever the status of a Purchase Requisition changes.
    /// </summary>
    /// <param name="RequisitionId">The ID of the requisition whose status changed.</param>
    /// <param name="OldStatus">The previous status.</param>
    /// <param name="NewStatus">The new current status.</param>
    public record RequisitionStatusChangedEvent(Guid RequisitionId, RequisitionStatus OldStatus, RequisitionStatus NewStatus);

}
