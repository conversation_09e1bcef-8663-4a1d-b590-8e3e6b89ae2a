using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using ProcureToPay.Domain.Enums; // Required for RfpStatus
using ProcureToPay.Domain.Entities; // Required for BaseEntity and related entities

namespace ProcureToPay.Domain.Entities;

/// <summary>
/// Represents a Request for Proposal (RFP) document used to solicit detailed proposals
/// from vendors for specific projects, goods, or services.
/// Often an Aggregate Root.
/// </summary>
public class RequestForProposal : BaseEntity<Guid>
{
    /// <summary>
    /// Unique, human-readable identifier for the RFP.
    /// </summary>
    [Required]
    [MaxLength(50)]
    public string RfpNumber { get; private set; } = string.Empty;

    /// <summary>
    /// The title or subject of the RFP.
    /// </summary>
    [Required]
    [MaxLength(250)]
    public string Title { get; private set; } = string.Empty;

    /// <summary>
    /// Detailed description of the scope of work, requirements, or specifications.
    /// </summary>
    [Required]
    public string ScopeOfWork { get; private set; } = string.Empty;

    /// <summary>
    /// Criteria that will be used to evaluate submitted proposals.
    /// </summary>
    [Required]
    public string EvaluationCriteria { get; private set; } = string.Empty;

    /// <summary>
    /// Tenant identifier for multi-tenancy.
    /// </summary>
    [Required]
    public Guid TenantId { get; private set; }

    /// <summary>
    /// The date the RFP was officially issued to vendors. Null if in Draft.
    /// </summary>
    public DateTime? IssuedDate { get; private set; }

    /// <summary>
    /// The issue date of the RFP.
    /// </summary>
    [Required]
    public DateTime IssueDate { get; private set; }

    /// <summary>
    /// The date when the decision was made.
    /// </summary>
    public DateTime? DecisionDate { get; private set; }

    /// <summary>
    /// Foreign Key to the Project this RFP is associated with.
    /// </summary>
    public Guid? ProjectId { get; private set; }

    /// <summary>
    /// The deadline for vendors to submit questions regarding the RFP (optional).
    /// </summary>
    public DateTime? QuestionDeadline { get; private set; }

    /// <summary>
    /// The deadline for vendors to submit their proposals.
    /// </summary>
    [Required]
    public DateTime SubmissionDeadline { get; private set; }

    /// <summary>
    /// Current status of the RFP process.
    /// </summary>
    [Required]
    public RfpStatus Status { get; private set; }

    /// <summary>
    /// Optional ID linking the issuer to the ApplicationUser identity.
    /// </summary>
    [MaxLength(450)]
    public string? IssuedByUserId { get; private set; }

    /// <summary>
    /// Name of the person or department issuing the RFP.
    /// </summary>
    [MaxLength(150)]
    public string? IssuedByName { get; private set; }

    /// <summary>
    /// Department associated with the RFP.
    /// </summary>
    [MaxLength(100)]
    public string? Department { get; private set; }

    /// <summary>
    /// Expected start date for the resulting contract or project (optional).
    /// </summary>
    public DateTime? ExpectedContractStartDate { get; private set; }

    /// <summary>
    /// Expected duration of the resulting contract or project (optional, e.g., "1 year").
    /// </summary>
    [MaxLength(50)]
    public string? ExpectedContractDuration { get; private set; }

    /// <summary>
    /// Optional Foreign Key to the Vendor awarded the contract resulting from this RFP.
    /// </summary>
    public Guid? AwardedVendorId { get; private set; }
    /// <summary>
    /// Optional navigation property to the awarded Vendor.
    /// </summary>
    public virtual Vendor? AwardedVendor { get; private set; }

    /// <summary>
    /// Optional Foreign Key to the Contract created as a result of this RFP.
    /// </summary>
    public Guid? AwardedContractId { get; private set; }
    /// <summary>
    /// Optional navigation property to the resulting Contract.
    /// </summary>
    public virtual Contract? AwardedContract { get; private set; }

    /// <summary>
    /// Navigation property to the Project this RFP is associated with.
    /// </summary>
    public virtual Project? Project { get; private set; }

    /// <summary>
    /// Date and time the RFP process reached a final state (Awarded, CompletedWithoutAward, Cancelled).
    /// </summary>
    public DateTime? CompletedDate { get; private set; } // Included CompletedDate

    // Collection of proposals received from vendors in response to this RFP. Encapsulated list.
    private readonly List<VendorProposal> _vendorProposals = new();
    public virtual IReadOnlyCollection<VendorProposal> VendorProposals => _vendorProposals.AsReadOnly();

    /// <summary>
    /// Private constructor for EF Core hydration.
    /// </summary>
    private RequestForProposal() : base(Guid.NewGuid()) { }

    /// <summary>
    /// Creates a new Request for Proposal in Draft status.
    /// </summary>
    public RequestForProposal(
        string rfpNumber,
        string title,
        string scopeOfWork,
        string evaluationCriteria,
        DateTime submissionDeadline,
        DateTime? questionDeadline = null,
        string? issuedByUserId = null,
        string? issuedByName = null,
        string? department = null,
        DateTime? expectedContractStartDate = null,
        string? expectedContractDuration = null
        ) : base(Guid.NewGuid())
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(rfpNumber);
        ArgumentException.ThrowIfNullOrWhiteSpace(title);
        ArgumentException.ThrowIfNullOrWhiteSpace(scopeOfWork);
        ArgumentException.ThrowIfNullOrWhiteSpace(evaluationCriteria);
        if (submissionDeadline.Date < DateTime.UtcNow.Date) // Basic check
            throw new ArgumentOutOfRangeException(nameof(submissionDeadline), "Submission deadline must be in the future.");
        if (questionDeadline.HasValue && questionDeadline.Value.Date >= submissionDeadline.Date)
            throw new ArgumentOutOfRangeException(nameof(questionDeadline), "Question deadline must be before the submission deadline.");

        RfpNumber = rfpNumber;
        Title = title;
        ScopeOfWork = scopeOfWork;
        EvaluationCriteria = evaluationCriteria;
        SubmissionDeadline = submissionDeadline.Date;
        QuestionDeadline = questionDeadline?.Date;
        IssuedByUserId = issuedByUserId;
        IssuedByName = issuedByName;
        Department = department;
        ExpectedContractStartDate = expectedContractStartDate;
        ExpectedContractDuration = expectedContractDuration;
        Status = RfpStatus.Draft; // Initial status
        IssuedDate = null;
        CompletedDate = null; // Not completed initially
    }

    // --- Domain Methods ---

    /// <summary>
    /// Issues the RFP, changing its status and setting the issued date.
    /// </summary>
    public void Issue()
    {
        if (Status != RfpStatus.Draft)
            throw new InvalidOperationException($"Cannot issue an RFP with status '{Status}'. RFP must be in Draft status.");

        Status = RfpStatus.Issued;
        IssuedDate = DateTime.UtcNow;
        // TODO: Raise Domain Event? RfpIssuedEvent(this.Id)
    }

    /// <summary>
    /// Moves the RFP to the Evaluating status, typically after the submission deadline.
    /// </summary>
    public void StartEvaluation()
    {
        if (Status != RfpStatus.Issued)
            throw new InvalidOperationException($"Cannot start evaluation for RFP with status '{Status}'. RFP must be Issued.");

        // Optional: Check if SubmissionDeadline has passed?
        // if (DateTime.UtcNow.Date <= SubmissionDeadline) { ... }

        Status = RfpStatus.Evaluating;
        // TODO: Raise Domain Event? RfpEvaluationStartedEvent(this.Id)
    }

    /// <summary>
    /// Awards the RFP to a specific vendor, optionally linking the resulting contract. Sets CompletedDate.
    /// </summary>
    /// <param name="awardedVendorId">The ID of the vendor who won the proposal.</param>
    /// <param name="awardedContractId">Optional ID of the contract created from the award.</param>
    public void Award(Guid awardedVendorId, Guid? awardedContractId = null)
    {
        if (Status != RfpStatus.Evaluating)
            throw new InvalidOperationException($"Cannot award an RFP with status '{Status}'. Evaluation must be complete.");
        if (awardedVendorId == Guid.Empty)
            throw new ArgumentException("Awarded VendorId cannot be empty.", nameof(awardedVendorId));
        // TODO: Validate that the awardedVendorId corresponds to a submitted VendorProposal?

        Status = RfpStatus.Awarded;
        AwardedVendorId = awardedVendorId;
        AwardedContractId = awardedContractId; // Link to contract if created
        CompletedDate = DateTime.UtcNow; // Set completion date
                                         // TODO: Raise Domain Event? RfpAwardedEvent(this.Id, awardedVendorId, awardedContractId)
    }

    /// <summary>
    /// Marks the RFP as completed without awarding a contract. Sets CompletedDate.
    /// </summary>
    /// <param name="reason">Reason for not selecting a quote.</param>
    public void CompleteWithoutAward(string reason)
    {
        if (Status != RfpStatus.Evaluating)
            throw new InvalidOperationException($"Cannot complete without award an RFP with status '{Status}'. Must be Evaluating.");

        ArgumentException.ThrowIfNullOrWhiteSpace(reason);
        Status = RfpStatus.CompletedWithoutAward;
        CompletedDate = DateTime.UtcNow; // Set completion date
                                         // Store reason? Add Notes property?
                                         // TODO: Raise Domain Event? RfpCompletedWithoutAwardEvent(this.Id, reason)
    }

    /// <summary>
    /// Cancels the RFP process. Sets CompletedDate.
    /// </summary>
    /// <param name="reason">Reason for cancellation.</param>
    public void Cancel(string reason)
    {
        if (Status == RfpStatus.Awarded || Status == RfpStatus.CompletedWithoutAward || Status == RfpStatus.Cancelled)
            throw new InvalidOperationException($"Cannot cancel an RFP with status '{Status}'.");

        ArgumentException.ThrowIfNullOrWhiteSpace(reason);
        Status = RfpStatus.Cancelled;
        CompletedDate = DateTime.UtcNow; // Set completion date
                                         // Store reason? Add Notes property?
                                         // TODO: Raise Domain Event? RfpCancelledEvent(this.Id, reason)
    }

    // Note: Adding VendorProposals would likely happen via a separate method or service.
    // public void AddProposal(VendorProposal proposal) { ... }

    /// <summary>
    /// Updates the details of the RFP. Only allowed when in Draft status.
    /// </summary>
    public void UpdateDetails(string title, string scopeOfWork, string evaluationCriteria, DateTime submissionDeadline, DateTime? questionDeadline, DateTime? expectedContractStartDate, string? expectedContractDuration)
    {
        if (Status != RfpStatus.Draft)
            throw new InvalidOperationException("Cannot update details unless RFP is in Draft status.");

        ArgumentException.ThrowIfNullOrWhiteSpace(title);
        ArgumentException.ThrowIfNullOrWhiteSpace(scopeOfWork);
        ArgumentException.ThrowIfNullOrWhiteSpace(evaluationCriteria);
        if (submissionDeadline.Date < DateTime.UtcNow.Date)
            throw new ArgumentOutOfRangeException(nameof(submissionDeadline), "Submission deadline must be in the future.");
        if (questionDeadline.HasValue && questionDeadline.Value.Date >= submissionDeadline.Date)
            throw new ArgumentOutOfRangeException(nameof(questionDeadline), "Question deadline must be before the submission deadline.");

        Title = title;
        ScopeOfWork = scopeOfWork;
        EvaluationCriteria = evaluationCriteria;
        SubmissionDeadline = submissionDeadline.Date;
        QuestionDeadline = questionDeadline?.Date;
        ExpectedContractStartDate = expectedContractStartDate;
        ExpectedContractDuration = expectedContractDuration;
    }
}
