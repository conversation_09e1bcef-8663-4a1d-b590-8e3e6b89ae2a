﻿﻿using System;
using System.Collections.Generic;

namespace ProcureToPay.Domain.Entities // Ensure this namespace matches your existing BaseEntity
{
    /// <summary>
    /// Abstract base class for domain entities, providing an identifier and domain event handling.
    /// </summary>
    /// <typeparam name="TId">The type of the entity's identifier.</typeparam>
    public abstract class BaseEntity<TId> where TId : struct, IEquatable<TId> // Example constraint: ID is a value type
    {
        private readonly List<object> _domainEvents = new();

        /// <summary>
        /// The unique identifier for the entity.
        /// </summary>
        public TId Id { get; protected set; }

        /// <summary>
        /// Collection of domain events raised by this entity.
        /// </summary>
        public IReadOnlyCollection<object> DomainEvents => _domainEvents.AsReadOnly();

        /// <summary>
        /// Protected constructor for derived classes and EF Core.
        /// </summary>
        /// <param name="id">The identifier for the entity.</param>
        protected BaseEntity(TId id)
        {
            // Basic check, though struct constraint helps
            if (id.Equals(default(TId)))
            {
                throw new ArgumentException("Entity ID cannot be the default value.", nameof(id));
            }
            Id = id;
        }

        /// <summary>
        /// Adds a domain event to the entity's collection.
        /// </summary>
        /// <param name="domainEvent">The domain event to add.</param>
        protected void AddDomainEvent(object domainEvent)
        {
            _domainEvents.Add(domainEvent ?? throw new ArgumentNullException(nameof(domainEvent)));
        }

        /// <summary>
        /// Clears all domain events from the entity's collection.
        /// Typically called after events have been dispatched.
        /// </summary>
        public void ClearDomainEvents()
        {
            _domainEvents.Clear();
        }

        // --- Equality Implementation (Optional but recommended for Entities) ---

        public override bool Equals(object? obj)
        {
            if (obj == null || obj is not BaseEntity<TId> other)
            {
                return false;
            }

            // Handles transient entities (Id is default) and compares by reference.
            // Compares persistent entities by Id.
            if (IsTransient() || other.IsTransient())
            {
                return ReferenceEquals(this, other);
            }

            return Id.Equals(other.Id);
        }

        /// <summary>
        /// Checks if the entity is transient (has not been assigned a persistent Id).
        /// </summary>
        /// <returns>True if the entity's Id is the default value for its type.</returns>
        public bool IsTransient()
        {
            return Id.Equals(default(TId));
        }

        public override int GetHashCode()
        {
            // Consistent hash code for persistent entities based on Id.
            // Uses base hash code for transient entities.
            return IsTransient() ? base.GetHashCode() : Id.GetHashCode();
        }

        public static bool operator ==(BaseEntity<TId>? left, BaseEntity<TId>? right)
        {
            if (ReferenceEquals(left, null) && ReferenceEquals(right, null))
                return true;
            if (ReferenceEquals(left, null) || ReferenceEquals(right, null))
                return false;

            return left.Equals(right);
        }

        public static bool operator !=(BaseEntity<TId>? left, BaseEntity<TId>? right)
        {
            return !(left == right);
        }
    }
}
