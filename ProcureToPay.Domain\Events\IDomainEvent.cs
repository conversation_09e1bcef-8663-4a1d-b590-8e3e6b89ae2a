using System;

namespace ProcureToPay.Domain.Events
{
    /// <summary>
    /// Marker interface for domain events.
    /// </summary>
    public interface IDomainEvent
    {
        /// <summary>
        /// Gets the date and time when the event occurred.
        /// </summary>
        DateTime OccurredOn { get; }
        
        /// <summary>
        /// Gets the unique identifier for the event.
        /// </summary>
        Guid EventId { get; }
    }
}
