@page "/categories"
@using Microsoft.AspNetCore.Authorization
@using ProcureToPay.Application.Features.Categories.DTOs
@using ProcureToPay.Application.Features.Categories.Services
@using Microsoft.JSInterop

@attribute [Authorize]
@inject ICategoryService CategoryService
@inject NavigationManager NavigationManager
@inject IJSRuntime JSRuntime

<PageTitle>Categories</PageTitle>

<div class="container">
    <h1>Categories</h1>
    <p>Manage product categories for your organization.</p>

    <div class="mb-4">
        <a href="/categories/add" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i> Add Category
        </a>
    </div>

    @if (_isLoading)
    {
        <div class="d-flex justify-content-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>
    }
    else if (_categories == null || !_categories.Any())
    {
        <div class="alert alert-info">
            <p>No categories found. Click the "Add Category" button to create your first category.</p>
        </div>
    }
    else
    {
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Code</th>
                        <th>Parent Category</th>
                        <th>Products</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var category in _categories)
                    {
                        <tr>
                            <td>@category.Name</td>
                            <td>@category.Code</td>
                            <td>@(category.ParentCategoryName ?? "-")</td>
                            <td>@category.ProductCount</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="@($"/categories/edit/{category.Id}")" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-pencil"></i> Edit
                                    </a>
                                    <button @onclick="() => DeleteCategory(category.Id, category.Name)" class="btn btn-sm btn-outline-danger">
                                        <i class="bi bi-trash"></i> Delete
                                    </button>
                                </div>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    }
</div>

@code {
    private IEnumerable<CategoryDto>? _categories;
    private bool _isLoading = true;
    private string? _errorMessage;

    protected override async Task OnInitializedAsync()
    {
        await LoadCategories();
    }

    private async Task LoadCategories()
    {
        try
        {
            _isLoading = true;
            _categories = await CategoryService.GetCategoriesAsync();
        }
        catch (Exception ex)
        {
            _errorMessage = $"Error loading categories: {ex.Message}";
            await JSRuntime.InvokeVoidAsync("console.error", _errorMessage);
        }
        finally
        {
            _isLoading = false;
            StateHasChanged();
        }
    }

    private async Task DeleteCategory(Guid categoryId, string categoryName)
    {
        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", $"Are you sure you want to delete category '{categoryName}'?");
        if (confirmed)
        {
            try
            {
                var success = await CategoryService.DeleteCategoryAsync(categoryId);
                if (success)
                {
                    await LoadCategories();
                    await JSRuntime.InvokeVoidAsync("alert", $"Category '{categoryName}' deleted successfully.");
                }
                else
                {
                    await JSRuntime.InvokeVoidAsync("alert", "Unable to delete category. It may have child categories or products assigned to it.");
                }
            }
            catch (Exception ex)
            {
                _errorMessage = $"Error deleting category: {ex.Message}";
                await JSRuntime.InvokeVoidAsync("alert", _errorMessage);
            }
        }
    }
}
