public enum TechnicalSubmittalStatus
{
    Draft = 0,          // Being prepared by the submitter
    Submitted = 1,      // Submitted, awaiting review assignment/start
    InReview = 2,       // Currently under review
    ReviseAndResubmit = 3, // Review complete, requires vendor revision and resubmission
    Approved = 4,       // Review complete, approved without comments
    ApprovedAsNoted = 5, // Review complete, approved with comments/minor changes noted
    Rejected = 6,       // Review complete, rejected, potentially linked to NCR
    Closed = 7,         // Final disposition reached (Approved/ApprovedAsNoted/Rejected) and process complete
    Superseded = 8,     // Replaced by a newer submittal
    Cancelled = 9       // Submittal process cancelled
}