using System;
using System.ComponentModel.DataAnnotations;
using ProcureToPay.Domain.Interfaces;

namespace ProcureToPay.Domain.Entities
{
    /// <summary>
    /// Represents a product specific to a tenant in the system.
    /// </summary>
    public class TenantProduct : BaseEntity<Guid>, ITenantEntity
    {
        /// <summary>
        /// The name of the tenant product.
        /// </summary>
        [Required]
        [MaxLength(200)]
        public string Name { get; private set; } = string.Empty;

        /// <summary>
        /// The SKU (Stock Keeping Unit) of the tenant product.
        /// </summary>
        [MaxLength(50)]
        public string? SKU { get; private set; }

        /// <summary>
        /// A description of the tenant product.
        /// </summary>
        [MaxLength(1000)]
        public string? Description { get; private set; }

        /// <summary>
        /// The price of the tenant product.
        /// </summary>
        public decimal Price { get; private set; }

        /// <summary>
        /// The tenant identifier this entity belongs to.
        /// </summary>
        public Guid TenantId { get; set; }

        /// <summary>
        /// Private constructor for EF Core.
        /// </summary>
        private TenantProduct() : base(Guid.Empty) { }

        /// <summary>
        /// Creates a new tenant product.
        /// </summary>
        /// <param name="id">The unique identifier for the tenant product.</param>
        /// <param name="name">The name of the tenant product.</param>
        /// <param name="price">The price of the tenant product.</param>
        /// <param name="sku">Optional SKU for the tenant product.</param>
        /// <param name="description">Optional description for the tenant product.</param>
        public TenantProduct(
            Guid id,
            string name,
            decimal price,
            string? sku = null,
            string? description = null) : base(id)
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new ArgumentException("Name cannot be empty", nameof(name));

            if (price < 0)
                throw new ArgumentException("Price cannot be negative", nameof(price));

            Name = name;
            Price = price;
            SKU = sku;
            Description = description;
        }

        /// <summary>
        /// Updates the tenant product details.
        /// </summary>
        /// <param name="name">The updated name.</param>
        /// <param name="price">The updated price.</param>
        /// <param name="sku">The updated SKU.</param>
        /// <param name="description">The updated description.</param>
        public void Update(string name, decimal price, string? sku = null, string? description = null)
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new ArgumentException("Name cannot be empty", nameof(name));

            if (price < 0)
                throw new ArgumentException("Price cannot be negative", nameof(price));

            Name = name;
            Price = price;
            SKU = sku;
            Description = description;
        }
    }
}
