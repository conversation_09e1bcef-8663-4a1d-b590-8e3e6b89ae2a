using System;
using ProcureToPay.Domain.Enums; // For PurchaseOrderStatus

namespace ProcureToPay.Domain.Events
{
    // --- PurchaseOrder Specific Events ---
    public record PurchaseOrderCreatedEvent(Guid PurchaseOrderId);
    public record PurchaseOrderSubmittedEvent(Guid PurchaseOrderId); // Example if SubmitForApproval raises event
    public record PurchaseOrderApprovedEvent(Guid PurchaseOrderId);
    public record PurchaseOrderRejectedEvent(Guid PurchaseOrderId, string? Reason);
    public record PurchaseOrderCompletedEvent(Guid PurchaseOrderId);
    public record PurchaseOrderCancelledEvent(Guid PurchaseOrderId, string? Reason);
    public record PurchaseOrderStatusChangedEvent(Guid PurchaseOrderId, PurchaseOrderStatus OldStatus, PurchaseOrderStatus NewStatus); // Generic status change
    public record PurchaseOrderLineAddedEvent(Guid PurchaseOrderId, Guid PurchaseOrderLineId);
    public record PurchaseOrderLineRemovedEvent(Guid PurchaseOrderId, Guid PurchaseOrderLineId);
    // Add other events like PurchaseOrderTotalAmountChangedEvent if needed
}
