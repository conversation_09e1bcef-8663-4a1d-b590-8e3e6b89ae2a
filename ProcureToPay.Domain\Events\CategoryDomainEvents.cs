﻿using System;

namespace ProcureToPay.Domain.Events
{
    // --- Placeholder: Define a base marker interface or class if desired ---
    // public interface IDomainEvent { }

    // --- Category Specific Events ---

    // Using records for immutable event data carriers

    /// <summary>
    /// Raised when a new category is created.
    /// </summary>
    /// <param name="CategoryId">The ID of the newly created category.</param>
    /// <param name="Name">The name of the newly created category.</param>
    public record CategoryCreatedEvent(Guid CategoryId, string Name);

    /// <summary>
    /// Raised when a category's descriptive details (Name, Description, Code, UnspscCode) are updated.
    /// </summary>
    /// <param name="CategoryId">The ID of the updated category.</param>
    public record CategoryDetailsUpdatedEvent(Guid CategoryId);

    /// <summary>
    /// Raised when a category's parent is changed (moved in the hierarchy).
    /// </summary>
    /// <param name="CategoryId">The ID of the category that was moved.</param>
    /// <param name="NewParentId">The ID of the new parent category (null if moved to root).</param>
    public record CategoryParentChangedEvent(Guid CategoryId, Guid? NewParentId);

    // Add other events as needed (e.g., CategoryDeletedEvent)

}
