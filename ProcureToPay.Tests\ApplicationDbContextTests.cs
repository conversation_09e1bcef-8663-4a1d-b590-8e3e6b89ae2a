using Microsoft.EntityFrameworkCore;
using Moq;
using ProcureToPay.Domain.Entities;
using ProcureToPay.Domain.Interfaces;
using Xunit;

namespace ProcureToPay.Tests
{
    public class ApplicationDbContextTests
    {
        private static readonly Guid TenantId1 = Guid.Parse("11111111-1111-1111-1111-111111111111");
        private static readonly Guid TenantId2 = Guid.Parse("*************-2222-2222-************");

        [Fact]
        public void SaveChanges_AddsCorrectTenantId_ToNewEntities()
        {
            // Arrange
            var options = TestDbContextHelper.CreateNewContextOptions("SaveChanges_AddsCorrectTenantId");
            var tenantProvider = TestDbContextHelper.CreateTenantProvider(TenantId1);

            // Act
            using (var context = new TestApplicationDbContext(options, tenantProvider))
            {
                var testEntity = new TestEntity
                {
                    Id = Guid.NewGuid(),
                    Name = "Test Entity"
                };

                context.TestEntities.Add(testEntity);
                context.SaveChanges();

                // Assert
                Assert.Equal(TenantId1, testEntity.TenantId);
            }
        }

        [Fact]
        public void SaveChanges_HandlesNullTenantId_GracefullyDuringMigrations()
        {
            // Arrange
            var options = TestDbContextHelper.CreateNewContextOptions("SaveChanges_HandlesNullTenantId");
            var tenantProvider = TestDbContextHelper.CreateTenantProvider(null);

            // Act
            using (var context = new TestApplicationDbContext(options, tenantProvider))
            {
                var testEntity = new TestEntity
                {
                    Id = Guid.NewGuid(),
                    Name = "Test Entity for Migration"
                };

                context.TestEntities.Add(testEntity);
                context.SaveChanges();

                // Assert
                Assert.NotEqual(Guid.Empty, testEntity.TenantId);
                Assert.Equal(Guid.Parse("11111111-1111-1111-1111-111111111111"), testEntity.TenantId);
            }
        }
    }
}
