using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Reflection;

namespace ProcureToPay.Domain.Enums;

/// <summary>
/// Represents standard units of measure for products and services in the procurement system.
/// This enum provides a standardized set of measurement units for consistent data entry and reporting.
/// </summary>
[Serializable]
public enum UnitOfMeasure
{
    /// <summary>
    /// Default unit for countable items (e.g., individual products, pieces).
    /// </summary>
    [Display(Name = "Each", Description = "Individual units or pieces", Order = 1)]
    [Description("Individual units or pieces")]
    Each = 0,

    /// <summary>
    /// Kilograms for weight measurement.
    /// </summary>
    [Display(Name = "Kilogram", Description = "Unit of mass equal to 1000 grams", Order = 2)]
    [Description("Unit of mass equal to 1000 grams")]
    Kilogram = 1,

    /// <summary>
    /// Grams for precise weight measurement.
    /// </summary>
    [Display(Name = "Gram", Description = "Basic unit of mass in the metric system", Order = 3)]
    [Description("Basic unit of mass in the metric system")]
    Gram = 2,

    /// <summary>
    /// Liters for volume measurement.
    /// </summary>
    [Display(Name = "Liter", Description = "Unit of volume equal to 1000 milliliters", Order = 4)]
    [Description("Unit of volume equal to 1000 milliliters")]
    Liter = 3,

    /// <summary>
    /// Milliliters for precise volume measurement.
    /// </summary>
    [Display(Name = "Milliliter", Description = "One thousandth of a liter", Order = 5)]
    [Description("One thousandth of a liter")]
    Milliliter = 4,

    /// <summary>
    /// Meters for length measurement.
    /// </summary>
    [Display(Name = "Meter", Description = "Basic unit of length in the metric system", Order = 6)]
    [Description("Basic unit of length in the metric system")]
    Meter = 5,

    /// <summary>
    /// Centimeters for precise length measurement.
    /// </summary>
    [Display(Name = "Centimeter", Description = "One hundredth of a meter", Order = 7)]
    [Description("One hundredth of a meter")]
    Centimeter = 6,

    /// <summary>
    /// Square meters for area measurement.
    /// </summary>
    [Display(Name = "Square Meter", Description = "Unit of area measurement", Order = 8)]
    [Description("Unit of area measurement")]
    SquareMeter = 7,

    /// <summary>
    /// Cubic meters for volume measurement.
    /// </summary>
    [Display(Name = "Cubic Meter", Description = "Unit of volume measurement", Order = 9)]
    [Description("Unit of volume measurement")]
    CubicMeter = 8,

    /// <summary>
    /// Hours for time-based services.
    /// </summary>
    [Display(Name = "Hour", Description = "Unit of time (60 minutes)", Order = 10)]
    [Description("Unit of time (60 minutes)")]
    Hour = 9,

    /// <summary>
    /// Days for time-based services.
    /// </summary>
    [Display(Name = "Day", Description = "Unit of time (24 hours)", Order = 11)]
    [Description("Unit of time (24 hours)")]
    Day = 10,

    /// <summary>
    /// Weeks for time-based services.
    /// </summary>
    [Display(Name = "Week", Description = "Unit of time (7 days)", Order = 12)]
    [Description("Unit of time (7 days)")]
    Week = 11,

    /// <summary>
    /// Months for time-based services or subscriptions.
    /// </summary>
    [Display(Name = "Month", Description = "Unit of time (calendar month)", Order = 13)]
    [Description("Unit of time (calendar month)")]
    Month = 12,

    /// <summary>
    /// Years for time-based services or subscriptions.
    /// </summary>
    [Display(Name = "Year", Description = "Unit of time (12 months)", Order = 14)]
    [Description("Unit of time (12 months)")]
    Year = 13,

    /// <summary>
    /// Boxes for packaged items.
    /// </summary>
    [Display(Name = "Box", Description = "Container for multiple items", Order = 15)]
    [Description("Container for multiple items")]
    Box = 14,

    /// <summary>
    /// Pairs for items sold in pairs.
    /// </summary>
    [Display(Name = "Pair", Description = "Set of two matching items", Order = 16)]
    [Description("Set of two matching items")]
    Pair = 15,

    /// <summary>
    /// Sets for grouped items.
    /// </summary>
    [Display(Name = "Set", Description = "Collection of related items", Order = 17)]
    [Description("Collection of related items")]
    Set = 16,

    /// <summary>
    /// Packages for bundled items.
    /// </summary>
    [Display(Name = "Package", Description = "Bundled collection of items", Order = 18)]
    [Description("Bundled collection of items")]
    Package = 17,

    /// <summary>
    /// Rolls for items sold in roll form (e.g., fabric, paper).
    /// </summary>
    [Display(Name = "Roll", Description = "Cylindrical package of material", Order = 19)]
    [Description("Cylindrical package of material")]
    Roll = 18,

    /// <summary>
    /// Units for generic measurement.
    /// </summary>
    [Display(Name = "Unit", Description = "Generic unit of measurement", Order = 20)]
    [Description("Generic unit of measurement")]
    Unit = 19,

    /// <summary>
    /// Service units for service-based items.
    /// </summary>
    [Display(Name = "Service Unit", Description = "Unit of service delivery", Order = 21)]
    [Description("Unit of service delivery")]
    ServiceUnit = 20,

    /// <summary>
    /// Other units not covered by standard options.
    /// </summary>
    [Display(Name = "Other", Description = "Custom unit of measure", Order = 99)]
    [Description("Custom unit of measure")]
    Other = 99
}

/// <summary>
/// Extension methods for the UnitOfMeasure enum to provide additional functionality.
/// </summary>
public static class UnitOfMeasureExtensions
{
    /// <summary>
    /// Gets the display name for a UnitOfMeasure enum value.
    /// </summary>
    /// <param name="unitOfMeasure">The UnitOfMeasure enum value.</param>
    /// <returns>The display name from the Display attribute, or the enum name if not found.</returns>
    public static string GetDisplayName(this UnitOfMeasure unitOfMeasure)
    {
        var memberInfo = typeof(UnitOfMeasure).GetMember(unitOfMeasure.ToString()).FirstOrDefault();
        if (memberInfo != null)
        {
            var displayAttribute = memberInfo.GetCustomAttribute<DisplayAttribute>();
            if (displayAttribute != null)
            {
                return displayAttribute.Name ?? unitOfMeasure.ToString();
            }
        }
        return unitOfMeasure.ToString();
    }

    /// <summary>
    /// Gets the description for a UnitOfMeasure enum value.
    /// </summary>
    /// <param name="unitOfMeasure">The UnitOfMeasure enum value.</param>
    /// <returns>The description from the Description attribute, or an empty string if not found.</returns>
    public static string GetDescription(this UnitOfMeasure unitOfMeasure)
    {
        var memberInfo = typeof(UnitOfMeasure).GetMember(unitOfMeasure.ToString()).FirstOrDefault();
        if (memberInfo != null)
        {
            var descriptionAttribute = memberInfo.GetCustomAttribute<DescriptionAttribute>();
            if (descriptionAttribute != null)
            {
                return descriptionAttribute.Description;
            }
        }
        return string.Empty;
    }

    /// <summary>
    /// Determines if the unit of measure is a weight-based unit.
    /// </summary>
    /// <param name="unitOfMeasure">The UnitOfMeasure enum value.</param>
    /// <returns>True if the unit is weight-based, otherwise false.</returns>
    public static bool IsWeightBased(this UnitOfMeasure unitOfMeasure)
    {
        return unitOfMeasure == UnitOfMeasure.Kilogram || unitOfMeasure == UnitOfMeasure.Gram;
    }

    /// <summary>
    /// Determines if the unit of measure is a volume-based unit.
    /// </summary>
    /// <param name="unitOfMeasure">The UnitOfMeasure enum value.</param>
    /// <returns>True if the unit is volume-based, otherwise false.</returns>
    public static bool IsVolumeBased(this UnitOfMeasure unitOfMeasure)
    {
        return unitOfMeasure == UnitOfMeasure.Liter || unitOfMeasure == UnitOfMeasure.Milliliter || unitOfMeasure == UnitOfMeasure.CubicMeter;
    }

    /// <summary>
    /// Determines if the unit of measure is a length-based unit.
    /// </summary>
    /// <param name="unitOfMeasure">The UnitOfMeasure enum value.</param>
    /// <returns>True if the unit is length-based, otherwise false.</returns>
    public static bool IsLengthBased(this UnitOfMeasure unitOfMeasure)
    {
        return unitOfMeasure == UnitOfMeasure.Meter || unitOfMeasure == UnitOfMeasure.Centimeter;
    }

    /// <summary>
    /// Determines if the unit of measure is a time-based unit.
    /// </summary>
    /// <param name="unitOfMeasure">The UnitOfMeasure enum value.</param>
    /// <returns>True if the unit is time-based, otherwise false.</returns>
    public static bool IsTimeBased(this UnitOfMeasure unitOfMeasure)
    {
        return unitOfMeasure == UnitOfMeasure.Hour || unitOfMeasure == UnitOfMeasure.Day ||
               unitOfMeasure == UnitOfMeasure.Week || unitOfMeasure == UnitOfMeasure.Month ||
               unitOfMeasure == UnitOfMeasure.Year;
    }

    /// <summary>
    /// Determines if the unit of measure is a countable unit.
    /// </summary>
    /// <param name="unitOfMeasure">The UnitOfMeasure enum value.</param>
    /// <returns>True if the unit is countable, otherwise false.</returns>
    public static bool IsCountable(this UnitOfMeasure unitOfMeasure)
    {
        return unitOfMeasure == UnitOfMeasure.Each || unitOfMeasure == UnitOfMeasure.Box ||
               unitOfMeasure == UnitOfMeasure.Pair || unitOfMeasure == UnitOfMeasure.Set ||
               unitOfMeasure == UnitOfMeasure.Package || unitOfMeasure == UnitOfMeasure.Roll ||
               unitOfMeasure == UnitOfMeasure.Unit || unitOfMeasure == UnitOfMeasure.ServiceUnit;
    }

    /// <summary>
    /// Gets the appropriate decimal precision for the unit of measure.
    /// </summary>
    /// <param name="unitOfMeasure">The UnitOfMeasure enum value.</param>
    /// <returns>The recommended decimal precision for the unit.</returns>
    public static int GetRecommendedPrecision(this UnitOfMeasure unitOfMeasure)
    {
        if (unitOfMeasure == UnitOfMeasure.Each || unitOfMeasure == UnitOfMeasure.Box ||
            unitOfMeasure == UnitOfMeasure.Pair || unitOfMeasure == UnitOfMeasure.Set ||
            unitOfMeasure == UnitOfMeasure.Package || unitOfMeasure == UnitOfMeasure.Roll ||
            unitOfMeasure == UnitOfMeasure.Day || unitOfMeasure == UnitOfMeasure.Week ||
            unitOfMeasure == UnitOfMeasure.Month || unitOfMeasure == UnitOfMeasure.Year)
        {
            return 0; // Whole numbers typically
        }
        else if (unitOfMeasure == UnitOfMeasure.Hour || unitOfMeasure == UnitOfMeasure.Meter ||
                 unitOfMeasure == UnitOfMeasure.Kilogram || unitOfMeasure == UnitOfMeasure.Liter ||
                 unitOfMeasure == UnitOfMeasure.SquareMeter || unitOfMeasure == UnitOfMeasure.CubicMeter)
        {
            return 2; // Two decimal places typically
        }
        else
        {
            return 4; // Higher precision for small units
        }
    }

    /// <summary>
    /// Attempts to parse a string into a UnitOfMeasure enum value.
    /// </summary>
    /// <param name="value">The string value to parse.</param>
    /// <param name="unitOfMeasure">The resulting UnitOfMeasure if successful.</param>
    /// <returns>True if parsing was successful, otherwise false.</returns>
    public static bool TryParse(string value, out UnitOfMeasure unitOfMeasure)
    {
        unitOfMeasure = UnitOfMeasure.Each; // Default

        if (string.IsNullOrWhiteSpace(value))
        {
            return false;
        }

        // Try direct enum parsing
        if (Enum.TryParse(value, true, out UnitOfMeasure result))
        {
            unitOfMeasure = result;
            return true;
        }

        // Try matching by display name
        foreach (UnitOfMeasure unit in Enum.GetValues(typeof(UnitOfMeasure)))
        {
            if (string.Equals(unit.GetDisplayName(), value, StringComparison.OrdinalIgnoreCase))
            {
                unitOfMeasure = unit;
                return true;
            }
        }

        // Handle common abbreviations and alternative names
        value = value.ToLowerInvariant().Trim();
        switch (value)
        {
            case "ea":
            case "pc":
            case "piece":
            case "pieces":
                unitOfMeasure = UnitOfMeasure.Each;
                return true;

            case "kg":
                unitOfMeasure = UnitOfMeasure.Kilogram;
                return true;

            case "g":
                unitOfMeasure = UnitOfMeasure.Gram;
                return true;

            case "l":
                unitOfMeasure = UnitOfMeasure.Liter;
                return true;

            case "ml":
                unitOfMeasure = UnitOfMeasure.Milliliter;
                return true;

            case "m":
                unitOfMeasure = UnitOfMeasure.Meter;
                return true;

            case "cm":
                unitOfMeasure = UnitOfMeasure.Centimeter;
                return true;

            case "m2":
            case "sq m":
            case "sqm":
                unitOfMeasure = UnitOfMeasure.SquareMeter;
                return true;

            case "m3":
            case "cu m":
            case "cbm":
                unitOfMeasure = UnitOfMeasure.CubicMeter;
                return true;

            case "hr":
            case "hrs":
                unitOfMeasure = UnitOfMeasure.Hour;
                return true;

            case "dy":
            case "days":
                unitOfMeasure = UnitOfMeasure.Day;
                return true;

            case "wk":
            case "weeks":
                unitOfMeasure = UnitOfMeasure.Week;
                return true;

            case "mo":
            case "months":
                unitOfMeasure = UnitOfMeasure.Month;
                return true;

            case "yr":
            case "years":
                unitOfMeasure = UnitOfMeasure.Year;
                return true;

            case "bx":
            case "boxes":
                unitOfMeasure = UnitOfMeasure.Box;
                return true;

            case "pr":
            case "pairs":
                unitOfMeasure = UnitOfMeasure.Pair;
                return true;

            case "sets":
                unitOfMeasure = UnitOfMeasure.Set;
                return true;

            case "pkg":
            case "packages":
                unitOfMeasure = UnitOfMeasure.Package;
                return true;

            case "rolls":
                unitOfMeasure = UnitOfMeasure.Roll;
                return true;

            case "units":
                unitOfMeasure = UnitOfMeasure.Unit;
                return true;

            case "service":
            case "services":
            case "srv":
                unitOfMeasure = UnitOfMeasure.ServiceUnit;
                return true;

            default:
                return false;
        }
    }
}