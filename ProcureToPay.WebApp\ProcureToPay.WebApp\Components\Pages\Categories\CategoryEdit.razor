@page "/categories/edit/{CategoryId:guid}"
@using Microsoft.AspNetCore.Authorization
@using ProcureToPay.Application.Features.Categories.Services

@attribute [Authorize]
@inject NavigationManager NavigationManager
@inject IJSRuntime JSRuntime

<PageTitle>Edit Category</PageTitle>

<div class="container">
    <h1>Edit Category</h1>
    <p>Update an existing product category.</p>

    <CategoryForm CategoryIdToEdit="CategoryId" OnValidSubmitCallback="HandleCategoryUpdated" />
</div>

@code {
    [Parameter]
    public Guid CategoryId { get; set; }

    private async Task HandleCategoryUpdated()
    {
        await JSRuntime.InvokeVoidAsync("alert", "Category updated successfully.");
        NavigationManager.NavigateTo("/categories");
    }
}
