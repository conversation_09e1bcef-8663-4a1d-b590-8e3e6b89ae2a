namespace ProcureToPay.Domain.Enums;

/// <summary>
/// Represents the status of a Delivery Note.
/// </summary>
public enum DeliveryNoteStatus
{
    /// <summary>
    /// Delivery note has been created but goods have not yet been shipped.
    /// </summary>
    Pending = 0,

    /// <summary>
    /// Goods have been shipped but not yet received.
    /// </summary>
    Shipped = 1,

    /// <summary>
    /// Goods have been partially received.
    /// </summary>
    PartiallyReceived = 2,

    /// <summary>
    /// Goods have been fully received.
    /// </summary>
    Received = 3,

    /// <summary>
    /// Delivery note has been cancelled.
    /// </summary>
    Cancelled = 4
}
