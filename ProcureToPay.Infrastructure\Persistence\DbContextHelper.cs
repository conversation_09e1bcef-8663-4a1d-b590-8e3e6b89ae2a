using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using ProcureToPay.Domain.Interfaces;
using System;

namespace ProcureToPay.Infrastructure.Persistence
{
    /// <summary>
    /// Helper class for working with DbContext in singleton contexts.
    /// </summary>
    public static class DbContextHelper
    {
        /// <summary>
        /// Creates a new instance of ApplicationDbContext with a properly scoped tenant provider.
        /// This method should be used when creating DbContext instances in a singleton context.
        /// </summary>
        /// <param name="serviceProvider">The service provider to resolve services.</param>
        /// <returns>A new instance of ApplicationDbContext.</returns>
        public static ApplicationDbContext CreateDbContext(IServiceProvider serviceProvider)
        {
            // Create a new scope to resolve the scoped services
            using var scope = serviceProvider.CreateScope();
            
            // Get the DbContext options from the service provider
            var options = scope.ServiceProvider.GetRequiredService<DbContextOptions<ApplicationDbContext>>();
            
            // Get the tenant provider from the service provider
            var tenantProvider = scope.ServiceProvider.GetRequiredService<ITenantProvider>();
            
            // Create and return the DbContext with the scoped tenant provider
            return new ApplicationDbContext(options, tenantProvider);
        }

        /// <summary>
        /// Executes an action with a new instance of ApplicationDbContext with proper scoping.
        /// This method should be used when working with DbContext in a singleton context.
        /// </summary>
        /// <param name="serviceProvider">The service provider to resolve services.</param>
        /// <param name="action">The action to execute with the DbContext.</param>
        public static void ExecuteWithDbContext(IServiceProvider serviceProvider, Action<ApplicationDbContext> action)
        {
            // Create a new scope to resolve the scoped services
            using var scope = serviceProvider.CreateScope();
            
            // Get the DbContext from the service provider
            var dbContext = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            
            // Execute the action with the DbContext
            action(dbContext);
        }

        /// <summary>
        /// Executes a function with a new instance of ApplicationDbContext with proper scoping and returns the result.
        /// This method should be used when working with DbContext in a singleton context.
        /// </summary>
        /// <typeparam name="TResult">The type of the result.</typeparam>
        /// <param name="serviceProvider">The service provider to resolve services.</param>
        /// <param name="func">The function to execute with the DbContext.</param>
        /// <returns>The result of the function.</returns>
        public static TResult ExecuteWithDbContext<TResult>(IServiceProvider serviceProvider, Func<ApplicationDbContext, TResult> func)
        {
            // Create a new scope to resolve the scoped services
            using var scope = serviceProvider.CreateScope();
            
            // Get the DbContext from the service provider
            var dbContext = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            
            // Execute the function with the DbContext and return the result
            return func(dbContext);
        }

        /// <summary>
        /// Executes an async function with a new instance of ApplicationDbContext with proper scoping and returns the result.
        /// This method should be used when working with DbContext in a singleton context.
        /// </summary>
        /// <typeparam name="TResult">The type of the result.</typeparam>
        /// <param name="serviceProvider">The service provider to resolve services.</param>
        /// <param name="func">The async function to execute with the DbContext.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the result of the function.</returns>
        public static async Task<TResult> ExecuteWithDbContextAsync<TResult>(IServiceProvider serviceProvider, Func<ApplicationDbContext, Task<TResult>> func)
        {
            // Create a new scope to resolve the scoped services
            using var scope = serviceProvider.CreateScope();
            
            // Get the DbContext from the service provider
            var dbContext = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            
            // Execute the async function with the DbContext and return the result
            return await func(dbContext);
        }
    }
}
