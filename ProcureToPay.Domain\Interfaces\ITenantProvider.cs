namespace ProcureToPay.Domain.Interfaces
{
    /// <summary>
    /// Service interface for resolving the current tenant ID in a multi-tenant system.
    /// </summary>
    public interface ITenantProvider
    {
        /// <summary>
        /// Gets the current tenant ID from the execution context.
        /// </summary>
        /// <returns>The current tenant ID, or null if no tenant context is available.</returns>
        Guid? GetCurrentTenantId();
    }
}
