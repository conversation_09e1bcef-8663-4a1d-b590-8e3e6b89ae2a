using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProcureToPay.Domain.Entities;
using ProcureToPay.Domain.Enums; // Assuming DeliveryNoteStatus enum exists
using ProcureToPay.Domain.ValueObjects; // Assuming Address value object exists
using ProcureToPay.Infrastructure.Persistence.Extensions;

namespace ProcureToPay.Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// Configuration for the DeliveryNote entity for EF Core and PostgreSQL.
    /// </summary>
    public class DeliveryNoteConfiguration : IEntityTypeConfiguration<DeliveryNote>
    {
        public void Configure(EntityTypeBuilder<DeliveryNote> builder)
        {
            // Table Mapping
            builder.ToTable("delivery_notes");

            // Primary Key
            builder.HasKey(dn => dn.Id);
            builder.Property(dn => dn.Id).ValueGeneratedOnAdd();

            // Properties
            builder.Property(dn => dn.DeliveryNoteNumber)
                .IsRequired()
                .HasMaxLength(100); // Adjust length as needed

            builder.Property(dn => dn.DeliveryDate)
                .IsRequired()
                .HasColumnType("timestamp without time zone");

            builder.Property(dn => dn.ShipmentDate)
                .HasColumnType("timestamp without time zone")
                .IsRequired(false); // Shipment date might be optional

            builder.Property(dn => dn.Status)
                .IsRequired()
                .HasConversion<string>() // Or HasConversion<int>()
                .HasMaxLength(50); // Adjust size if string

            // Configure CarrierInfo Value Object
            builder.OwnsOne(dn => dn.CarrierInfo, carrierInfo =>
            {
                carrierInfo.Property(c => c.Name).HasColumnName("carrier_name").HasMaxLength(100).IsRequired(false);
                carrierInfo.Property(c => c.TrackingNumber).HasColumnName("tracking_number").HasMaxLength(100).IsRequired(false);
            });

            builder.Property(dn => dn.ReceivedBy) // Name or Id of receiver?
                .HasMaxLength(150)
                .IsRequired(false);

            // Foreign Keys (Assuming Guid or appropriate types)
            builder.Property(dn => dn.SalesOrderId).IsRequired(false); // Link to SO or PO
            builder.Property(dn => dn.PurchaseOrderId).IsRequired(false); // Link to SO or PO

            builder.Property(dn => dn.TenantId).IsRequired();


            // Owned Entities (Value Objects)
            builder.OwnsOne(dn => dn.ShippingAddress, address =>
            {
                address.Property(a => a.Street).HasColumnName("shipping_street").HasMaxLength(200).IsRequired(false);
                address.Property(a => a.City).HasColumnName("shipping_city").HasMaxLength(100).IsRequired(false);
                address.Property(a => a.State).HasColumnName("shipping_state").HasMaxLength(100).IsRequired(false);
                address.Property(a => a.PostalCode).HasColumnName("shipping_postal_code").HasMaxLength(20).IsRequired(false);
                address.Property(a => a.Country).HasColumnName("shipping_country").HasMaxLength(100).IsRequired(false);
                // address.WithOwner().Navigation(dn => dn.ShippingAddress).IsRequired(); // Uncomment if address is mandatory
            });

            builder.OwnsOne(dn => dn.BillingAddress, address => // Assuming BillingAddress property exists
            {
                address.Property(a => a.Street).HasColumnName("billing_street").HasMaxLength(200).IsRequired(false);
                address.Property(a => a.City).HasColumnName("billing_city").HasMaxLength(100).IsRequired(false);
                address.Property(a => a.State).HasColumnName("billing_state").HasMaxLength(100).IsRequired(false);
                address.Property(a => a.PostalCode).HasColumnName("billing_postal_code").HasMaxLength(20).IsRequired(false);
                address.Property(a => a.Country).HasColumnName("billing_country").HasMaxLength(100).IsRequired(false);
                // address.WithOwner().Navigation(dn => dn.BillingAddress).IsRequired(); // Uncomment if address is mandatory
            });


            // --- Concurrency Token ---
            builder.UseXminAsConcurrencyToken();


            // --- Relationships ---
            // Header-Lines (One-to-Many)
            builder.HasMany(dn => dn.Lines)
                   .WithOne(l => l.DeliveryNote) // Assuming DeliveryNoteLine has DeliveryNote navigation prop
                   .HasForeignKey(l => l.DeliveryNoteId)
                   .IsRequired() // A line must belong to a header
                   .OnDelete(DeleteBehavior.Cascade); // Deleting header deletes lines

            // Link to Origin (Many-to-One) - Choose SO or PO, or allow both but likely one is primary
            builder.HasOne(dn => dn.SalesOrder)
                   .WithMany() // Assuming SalesOrder doesn't have direct nav back
                   .HasForeignKey(dn => dn.SalesOrderId)
                   .IsRequired(false) // Not required if linked to PO instead
                   .OnDelete(DeleteBehavior.Restrict); // Prevent deleting SO if linked DN exists

            builder.HasOne(dn => dn.PurchaseOrder)
                  .WithMany(po => po.DeliveryNotes) // Assuming PO has collection
                  .HasForeignKey(dn => dn.PurchaseOrderId)
                  .IsRequired(false) // Not required if linked to SO instead
                  .OnDelete(DeleteBehavior.Restrict); // Prevent deleting PO if linked DN exists


            // --- Indexes ---
            builder.HasIndex(dn => dn.DeliveryNoteNumber).IsUnique();
            builder.HasIndex(dn => dn.Status);
            builder.HasIndex(dn => dn.DeliveryDate);
            builder.HasIndex(dn => dn.SalesOrderId);
            builder.HasIndex(dn => dn.PurchaseOrderId);
            builder.HasIndex(dn => dn.TenantId);


            // --- TODO ---
            // TODO: Confirm if link is to SalesOrder OR PurchaseOrder, adjust IsRequired accordingly.
            // TODO: Define BillingAddress property on DeliveryNote entity if needed.
            // TODO: Verify FK types match related entities.
        }
    }
}

