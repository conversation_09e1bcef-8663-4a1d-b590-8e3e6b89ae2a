using System;

namespace ProcureToPay.Domain.Events
{
    /// <summary>
    /// Base class for domain events.
    /// </summary>
    public abstract class DomainEvent : IDomainEvent
    {
        /// <summary>
        /// Gets the date and time when the event occurred.
        /// </summary>
        public DateTime OccurredOn { get; }
        
        /// <summary>
        /// Gets the unique identifier for the event.
        /// </summary>
        public Guid EventId { get; }

        /// <summary>
        /// Initializes a new instance of the <see cref="DomainEvent"/> class.
        /// </summary>
        protected DomainEvent()
        {
            EventId = Guid.NewGuid();
            OccurredOn = DateTime.UtcNow;
        }
    }
}
