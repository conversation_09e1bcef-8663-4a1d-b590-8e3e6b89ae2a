using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace ProcureToPay.Infrastructure.Persistence
{
    /// <summary>
    /// Helper class for applying database migrations programmatically at application startup.
    /// This is especially important for containerized applications like those using .NET Aspire,
    /// where containers are ephemeral and migrations need to be applied when the application starts.
    /// </summary>
    public static class MigrationHelper
    {
        /// <summary>
        /// Applies pending migrations to the database.
        /// </summary>
        /// <param name="serviceProvider">The service provider to resolve services from.</param>
        /// <param name="retryCount">Number of times to retry if migration fails.</param>
        public static void ApplyMigrations(IServiceProvider serviceProvider, int retryCount = 5)
        {
            using (var scope = serviceProvider.CreateScope())
            {
                var services = scope.ServiceProvider;
                var logger = services.GetRequiredService<ILogger<ApplicationDbContext>>();

                try
                {
                    logger.LogInformation("Applying database migrations...");
                    var context = services.GetRequiredService<ApplicationDbContext>();

                    // Use a retry policy for database operations
                    var retry = 0;
                    var migrated = false;

                    while (!migrated && retry < retryCount)
                    {
                        try
                        {
                            context.Database.Migrate();
                            migrated = true;
                            logger.LogInformation("Database migrations applied successfully.");
                        }
                        catch (Exception ex)
                        {
                            retry++;
                            logger.LogWarning(ex, "Error applying migrations (attempt {Retry}/{RetryCount}): {Message}",
                                retry, retryCount, ex.Message);

                            if (retry >= retryCount)
                            {
                                logger.LogError(ex, "Failed to apply migrations after {RetryCount} attempts.", retryCount);
                                throw;
                            }

                            // Wait before retrying
                            Thread.Sleep(1000 * retry);
                        }
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "An error occurred while applying migrations.");
                    throw;
                }
            }
        }

        /// <summary>
        /// Applies pending migrations to the database asynchronously.
        /// </summary>
        /// <param name="serviceProvider">The service provider to resolve services from.</param>
        /// <param name="retryCount">Number of times to retry if migration fails.</param>
        public static async Task ApplyMigrationsAsync(IServiceProvider serviceProvider, int retryCount = 5)
        {
            using (var scope = serviceProvider.CreateScope())
            {
                var services = scope.ServiceProvider;
                var logger = services.GetRequiredService<ILogger<ApplicationDbContext>>();

                try
                {
                    logger.LogInformation("Applying database migrations asynchronously...");
                    var context = services.GetRequiredService<ApplicationDbContext>();

                    // Use a retry policy for database operations
                    var retry = 0;
                    var migrated = false;

                    while (!migrated && retry < retryCount)
                    {
                        try
                        {
                            await context.Database.MigrateAsync();
                            migrated = true;
                            logger.LogInformation("Database migrations applied successfully.");
                        }
                        catch (Exception ex)
                        {
                            retry++;
                            logger.LogWarning(ex, "Error applying migrations (attempt {Retry}/{RetryCount}): {Message}",
                                retry, retryCount, ex.Message);

                            if (retry >= retryCount)
                            {
                                logger.LogError(ex, "Failed to apply migrations after {RetryCount} attempts.", retryCount);
                                throw;
                            }

                            // Wait before retrying
                            await Task.Delay(1000 * retry);
                        }
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "An error occurred while applying migrations.");
                    throw;
                }
            }
        }
    }
}
