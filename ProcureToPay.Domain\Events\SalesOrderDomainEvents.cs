﻿using System;
using ProcureToPay.Domain.Enums; // For SalesOrderStatus

namespace ProcureToPay.Domain.Events
{
    // --- Placeholder: Define a base marker interface or class if desired ---
    // public interface IDomainEvent { }

    // --- SalesOrder Specific Events ---

    /// <summary>
    /// Raised when a new Sales Order is created.
    /// </summary>
    /// <param name="SalesOrderId">The ID of the newly created sales order.</param>
    /// <param name="TenantId">The Tenant ID associated with the sales order.</param>
    /// <param name="CustomerId">The optional ID of the customer.</param>
    public record SalesOrderCreatedEvent(Guid SalesOrderId, Guid TenantId, Guid? CustomerId);

    /// <summary>
    /// Raised when a line item is added to a Sales Order.
    /// Typically raised by the SalesOrder aggregate after adding the line.
    /// </summary>
    /// <param name="SalesOrderId">The ID of the parent sales order.</param>
    /// <param name="LineId">The ID of the newly added line item.</param>
    public record SalesOrderLineAddedEvent(Guid SalesOrderId, Guid LineId);

    /// <summary>
    /// Raised when a line item is removed from a Sales Order.
    /// Typically raised by the SalesOrder aggregate after removing the line.
    /// </summary>
    /// <param name="SalesOrderId">The ID of the parent sales order.</param>
    /// <param name="LineId">The ID of the removed line item.</param>
    public record SalesOrderLineRemovedEvent(Guid SalesOrderId, Guid LineId);

    /// <summary>
    /// Raised after a credit check has been performed for a Sales Order.
    /// </summary>
    /// <param name="SalesOrderId">The ID of the sales order.</param>
    /// <param name="IsApproved">Indicates whether the credit check passed.</param>
    public record SalesOrderCreditCheckedEvent(Guid SalesOrderId, bool IsApproved);

    /// <summary>
    /// Raised after an Available-To-Promise (ATP) check has been performed.
    /// </summary>
    /// <param name="SalesOrderId">The ID of the sales order.</param>
    /// <param name="IsConfirmed">Indicates whether ATP confirmed availability.</param>
    public record SalesOrderAtpConfirmedEvent(Guid SalesOrderId, bool IsConfirmed);

    /// <summary>
    /// Raised when a Sales Order is confirmed (e.g., after checks pass).
    /// </summary>
    /// <param name="SalesOrderId">The ID of the confirmed sales order.</param>
    public record SalesOrderConfirmedEvent(Guid SalesOrderId);

    /// <summary>
    /// Generic event raised whenever the Status of a Sales Order changes.
    /// </summary>
    /// <param name="SalesOrderId">The ID of the sales order whose status changed.</param>
    /// <param name="OldStatus">The previous status.</param>
    /// <param name="NewStatus">The new current status.</param>
    public record SalesOrderStatusChangedEvent(Guid SalesOrderId, SalesOrderStatus OldStatus, SalesOrderStatus NewStatus);

    /// <summary>
    /// Raised when a Sales Order is marked as shipped.
    /// </summary>
    /// <param name="SalesOrderId">The ID of the shipped sales order.</param>
    /// <param name="ShipDate">The date the order was shipped.</param>
    // Optional: Add tracking info if available at time of event
    // public record SalesOrderShippedEvent(Guid SalesOrderId, DateTime ShipDate, string? TrackingNumber);
    public record SalesOrderShippedEvent(Guid SalesOrderId, DateTime ShipDate);


    /// <summary>
    /// Raised when a Sales Order is soft-deleted.
    /// </summary>
    /// <param name="SalesOrderId">The ID of the deleted sales order.</param>
    public record SalesOrderDeletedEvent(Guid SalesOrderId);

    /// <summary>
    /// Raised when a soft-deleted Sales Order is restored.
    /// </summary>
    /// <param name="SalesOrderId">The ID of the restored sales order.</param>
    public record SalesOrderRestoredEvent(Guid SalesOrderId);

    // Add other events as needed:
    // - SalesOrderReleasedToFulfillmentEvent
    // - SalesOrderInvoicedEvent
    // - SalesOrderCompletedEvent
    // - SalesOrderCancelledEvent
    // - SalesOrderTotalAmountChangedEvent
    // - SalesOrderReturnLinkedEvent
    // - SalesOrderTerritoryAssignedEvent
    // - SalesOrderSalespersonAssignedEvent

}
