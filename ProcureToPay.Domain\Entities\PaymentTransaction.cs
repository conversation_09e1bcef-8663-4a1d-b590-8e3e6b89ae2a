using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using ProcureToPay.Domain.Enums; // Required for PaymentStatus, PaymentMethod
using ProcureToPay.Domain.Entities; // Required for BaseEntity and related entities

namespace ProcureToPay.Domain.Entities;

/// <summary>
/// Represents a single payment transaction made towards one or more invoices for a vendor.
/// </summary>
public class PaymentTransaction : BaseEntity<Guid>
{
    /// <summary>
    /// Tenant identifier for multi-tenancy.
    /// </summary>
    [Required]
    public Guid TenantId { get; private set; }

    /// <summary>
    /// Unique reference number for the transaction (e.g., from payment gateway, bank statement).
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string TransactionReference { get; private set; } = string.Empty;

    /// <summary>
    /// The date and time the payment transaction was executed or initiated.
    /// </summary>
    [Required]
    public DateTime PaymentDate { get; private set; }

    /// <summary>
    /// The amount of the payment.
    /// TODO: Consider using Money Value Object (Amount + Currency).
    /// </summary>
    [Required]
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Amount { get; private set; }

    /// <summary>
    /// Currency code for the payment amount (e.g., SAR, AED, QAR).
    /// </summary>
    [Required]
    [MaxLength(3)]
    public string CurrencyCode { get; private set; } = string.Empty;

    /// <summary>
    /// The method used for the payment.
    /// </summary>
    [Required]
    public PaymentMethod PaymentMethod { get; private set; }

    /// <summary>
    /// The current status of the payment transaction.
    /// </summary>
    [Required]
    public PaymentStatus Status { get; private set; }

    /// <summary>
    /// Foreign Key to the Vendor who received the payment.
    /// </summary>
    [Required]
    public Guid VendorId { get; private set; }
    /// <summary>
    /// Navigation property to the Vendor. Virtual for lazy loading.
    /// </summary>
    public virtual Vendor Vendor { get; private set; } = null!;

    /// <summary>
    /// Optional Foreign Key to a single Invoice being paid by this transaction.
    /// Note: For payments covering multiple invoices, a separate join entity
    /// (e.g., InvoicePayment) linking PaymentTransaction and Invoice is recommended.
    /// </summary>
    public Guid? InvoiceId { get; private set; }
    /// <summary>
    /// Optional navigation property to the related Invoice. Virtual for lazy loading.
    /// </summary>
    public virtual Invoice? Invoice { get; private set; }

    /// <summary>
    /// Optional notes regarding the payment (e.g., purpose if not linked to invoice, processing details).
    /// </summary>
    public string? Notes { get; private set; }

    /// <summary>
    /// Bank reference for the payment transaction.
    /// </summary>
    public string? BankReference { get; private set; }

    // TODO: Consider adding properties related to bank details if using transfers
    // public string? PayerBankAccount { get; private set; }
    // public string? PayeeBankAccountIban { get; private set; }

    /// <summary>
    /// Private constructor for EF Core hydration.
    /// </summary>
    private PaymentTransaction() : base(Guid.Empty) { }

    /// <summary>
    /// Creates a new Payment Transaction, typically starting in Pending or Processing status.
    /// </summary>
    public PaymentTransaction(
        Guid tenantId,
        string transactionReference,
        DateTime paymentDate,
        decimal amount,
        string currencyCode,
        PaymentMethod paymentMethod,
        Guid vendorId,
        Guid? invoiceId = null, // Optional link to single invoice
        string? bankReference = null,
        string? notes = null
        ) : base(Guid.NewGuid())
    {
        if (tenantId == Guid.Empty) throw new ArgumentException("TenantId cannot be empty.", nameof(tenantId));
        ArgumentException.ThrowIfNullOrWhiteSpace(transactionReference);
        if (amount <= 0) throw new ArgumentOutOfRangeException(nameof(amount), "Payment amount must be positive.");
        ArgumentException.ThrowIfNullOrWhiteSpace(currencyCode);
        if (currencyCode.Length != 3) throw new ArgumentException("Currency code must be 3 characters.", nameof(currencyCode));
        if (vendorId == Guid.Empty) throw new ArgumentException("VendorId cannot be empty.", nameof(vendorId));
        // Consider validating paymentMethod is not Unknown?

        TenantId = tenantId;
        TransactionReference = transactionReference;
        PaymentDate = paymentDate;
        Amount = amount;
        CurrencyCode = currencyCode.ToUpperInvariant();
        PaymentMethod = paymentMethod;
        VendorId = vendorId;
        InvoiceId = invoiceId;
        BankReference = bankReference;
        Notes = notes;
        Status = PaymentStatus.Pending; // Initial status, update via methods
    }

    // --- Domain Methods for Status Transitions ---

    /// <summary>
    /// Marks the payment as currently processing.
    /// </summary>
    public void MarkAsProcessing()
    {
        if (Status == PaymentStatus.Pending || Status == PaymentStatus.RequiresAction) // Allow retry?
        {
            Status = PaymentStatus.Processing;
            // TODO: Raise Domain Event? PaymentProcessingEvent(this.Id)
        }
        else
        {
            throw new InvalidOperationException($"Cannot mark payment as Processing when status is '{Status}'.");
        }
    }

    /// <summary>
    /// Marks the payment as succeeded.
    /// </summary>
    /// <param name="actualPaymentDate">Optional: Update payment date if confirmation provides a more accurate one.</param>
    public void MarkAsSucceeded(DateTime? actualPaymentDate = null)
    {
        // Allow succeeding from Pending or Processing? Or RequiresAction?
        if (Status == PaymentStatus.Processing || Status == PaymentStatus.Pending || Status == PaymentStatus.RequiresAction)
        {
            Status = PaymentStatus.Succeeded;
            if (actualPaymentDate.HasValue)
            {
                PaymentDate = actualPaymentDate.Value;
            }
            // TODO: Raise Domain Event? PaymentSucceededEvent(this.Id, this.InvoiceId, this.Amount)
            // This event would trigger updating the Invoice's AmountPaid and Status.
        }
        else
        {
            throw new InvalidOperationException($"Cannot mark payment as Succeeded when status is '{Status}'.");
        }
    }

    /// <summary>
    /// Marks the payment as failed.
    /// </summary>
    /// <param name="failureReason">Reason for failure.</param>
    public void MarkAsFailed(string failureReason)
    {
        // Allow failing from Pending or Processing? Or RequiresAction?
        if (Status == PaymentStatus.Processing || Status == PaymentStatus.Pending || Status == PaymentStatus.RequiresAction)
        {
            ArgumentException.ThrowIfNullOrWhiteSpace(failureReason);
            Status = PaymentStatus.Failed;
            Notes = $"Failed: {failureReason}\n---\n{Notes}";
            // TODO: Raise Domain Event? PaymentFailedEvent(this.Id, failureReason)
        }
        else
        {
            throw new InvalidOperationException($"Cannot mark payment as Failed when status is '{Status}'.");
        }
    }

    /// <summary>
    /// Cancels the payment if possible (e.g., if still Pending).
    /// </summary>
    /// <param name="reason">Reason for cancellation.</param>
    public void Cancel(string reason)
    {
        if (Status == PaymentStatus.Pending || Status == PaymentStatus.RequiresAction)
        {
            ArgumentException.ThrowIfNullOrWhiteSpace(reason);
            Status = PaymentStatus.Cancelled;
            Notes = $"Cancelled: {reason}\n---\n{Notes}";
            // TODO: Raise Domain Event? PaymentCancelledEvent(this.Id, reason)
        }
        else
        {
            throw new InvalidOperationException($"Cannot cancel payment when status is '{Status}'.");
        }
    }

    public void AddNote(string note)
    {
        Notes = string.IsNullOrWhiteSpace(Notes) ? note : $"{Notes}\n{note}";
    }
}
