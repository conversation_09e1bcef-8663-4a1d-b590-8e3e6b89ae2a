﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using ProcureToPay.Domain.Entities; // Required for BaseEntity and related entities

namespace ProcureToPay.Domain.Entities;

/// <summary>
/// Represents a single line item on a Goods Receipt Note, detailing the quantity
/// received and inspection results for a specific product against a PO line.
/// </summary>
public class GoodsReceiptNoteLine : BaseEntity<Guid>
{
    /// <summary>
    /// Line number for ordering within the goods receipt note.
    /// </summary>
    [Required]
    public int LineNumber { get; private set; }

    /// <summary>
    /// Foreign Key to the parent Goods Receipt Note.
    /// </summary>
    [Required]
    public Guid GoodsReceiptNoteId { get; private set; }
    /// <summary>
    /// Navigation property to the parent Goods Receipt Note. Virtual for lazy loading.
    /// </summary>
    public virtual GoodsReceiptNote GoodsReceiptNote { get; private set; } = null!;

    /// <summary>
    /// Foreign Key to the original Purchase Order Line this receipt corresponds to.
    /// </summary>
    [Required]
    public Guid PurchaseOrderLineId { get; private set; }
    /// <summary>
    /// Navigation property to the original Purchase Order Line. Virtual for lazy loading.
    /// </summary>
    public virtual PurchaseOrderLine PurchaseOrderLine { get; private set; } = null!;

    /// <summary>
    /// Optional Foreign Key to the Delivery Note Line this receipt corresponds to.
    /// </summary>
    public Guid? DeliveryNoteLineId { get; private set; }
    /// <summary>
    /// Optional navigation property to the related Delivery Note Line. Virtual for lazy loading.
    /// </summary>
    public virtual DeliveryNoteLine? DeliveryNoteLine { get; private set; }

    /// <summary>
    /// Foreign Key to the Product received.
    /// </summary>
    [Required]
    public Guid ProductId { get; private set; }
    /// <summary>
    /// Navigation property to the Product received. Virtual for lazy loading.
    /// </summary>
    public virtual ProductDefinition Product { get; private set; } = null!;

    // Store snapshots in case product details change
    [Required]
    [MaxLength(100)]
    public string ProductSkuSnapshot { get; private set; } = string.Empty;
    [Required]
    [MaxLength(500)]
    public string ProductDescriptionSnapshot { get; private set; } = string.Empty;

    /// <summary>
    /// The quantity physically received for this item in this GRN.
    /// Using decimal for flexibility.
    /// </summary>
    [Required]
    [Column(TypeName = "decimal(18, 4)")]
    public decimal QuantityReceived { get; private set; }

    /// <summary>
    /// The unit of measure for the quantity.
    /// </summary>
    [Required]
    [MaxLength(20)]
    public string UnitOfMeasure { get; private set; } = string.Empty;

    /// <summary>
    /// The quality control status of the line item.
    /// </summary>
    [MaxLength(50)]
    public string? QualityControlStatus { get; private set; }

    /// <summary>
    /// The location where the goods were put away.
    /// </summary>
    [MaxLength(100)]
    public string? PutAwayLocation { get; private set; }

    /// <summary>
    /// The batch number of the received goods.
    /// </summary>
    public string? BatchNumber { get; private set; }

    /// <summary>
    /// The lot number of the received goods.
    /// </summary>
    public string? LotNumber { get; private set; }

    /// <summary>
    /// The expiry date of the received goods.
    /// </summary>
    public DateTime? ExpiryDate { get; private set; }

    // --- Inspection Results ---

    /// <summary>
    /// Flag indicating if inspection results have been recorded for this line.
    /// </summary>
    [Required]
    public bool InspectionCompleted { get; private set; } = false;

    /// <summary>
    /// Quantity accepted after inspection. Null until inspection is completed.
    /// </summary>
    [Column(TypeName = "decimal(18, 4)")]
    public decimal? QuantityAccepted { get; private set; }

    /// <summary>
    /// Quantity rejected after inspection. Null until inspection is completed.
    /// </summary>
    [Column(TypeName = "decimal(18, 4)")]
    public decimal? QuantityRejected { get; private set; }

    /// <summary>
    /// Reason provided if quantity was rejected during inspection.
    /// </summary>
    [MaxLength(500)]
    public string? RejectionReason { get; private set; }

    /// <summary>
    /// General notes specific to this line item (receiving or inspection).
    /// </summary>
    // *** RENAMED this property from InspectionNotes to Notes ***
    public string? Notes { get; private set; }

    /// <summary>
    /// Private constructor for EF Core hydration.
    /// </summary>
    private GoodsReceiptNoteLine() : base(Guid.Empty) { }

    /// <summary>
    /// Internal constructor, called from GoodsReceiptNote.AddReceivedLine.
    /// Initializes received quantity, inspection status is pending.
    /// </summary>
    internal GoodsReceiptNoteLine(
        Guid id,
        Guid goodsReceiptNoteId,
        Guid purchaseOrderLineId,
        Guid productId,
        string productSkuSnapshot,
        string productDescriptionSnapshot,
        decimal quantityReceived,
        int lineNumber,
        string unitOfMeasure,
        Guid? deliveryNoteLineId = null,
        string? batchNumber = null,
        string? lotNumber = null,
        DateTime? expiryDate = null,
        string? putAwayLocation = null,
        string? notes = null // Parameter for initial receiving notes
        ): base(Guid.NewGuid())
    {
        // Basic Validation
        if (id == Guid.Empty) throw new ArgumentException("Id cannot be empty.", nameof(id));
        if (goodsReceiptNoteId == Guid.Empty) throw new ArgumentException("GoodsReceiptNoteId cannot be empty.", nameof(goodsReceiptNoteId));
        if (purchaseOrderLineId == Guid.Empty) throw new ArgumentException("PurchaseOrderLineId cannot be empty.", nameof(purchaseOrderLineId));
        if (productId == Guid.Empty) throw new ArgumentException("ProductId cannot be empty.", nameof(productId));
        ArgumentException.ThrowIfNullOrWhiteSpace(productSkuSnapshot);
        ArgumentException.ThrowIfNullOrWhiteSpace(productDescriptionSnapshot);
        ArgumentException.ThrowIfNullOrWhiteSpace(unitOfMeasure);
        if (lineNumber <= 0) throw new ArgumentOutOfRangeException(nameof(lineNumber), "Line number must be positive.");
        if (quantityReceived <= 0) throw new ArgumentOutOfRangeException(nameof(quantityReceived), "Quantity received must be positive.");

        GoodsReceiptNoteId = goodsReceiptNoteId;
        PurchaseOrderLineId = purchaseOrderLineId;
        DeliveryNoteLineId = deliveryNoteLineId;
        ProductId = productId;
        ProductSkuSnapshot = productSkuSnapshot;
        ProductDescriptionSnapshot = productDescriptionSnapshot;
        QuantityReceived = quantityReceived;
        LineNumber = lineNumber;
        UnitOfMeasure = unitOfMeasure;
        BatchNumber = batchNumber;
        LotNumber = lotNumber;
        ExpiryDate = expiryDate;
        PutAwayLocation = putAwayLocation;
        Notes = notes; // Assign constructor parameter to the RENAMED Notes property

        // Call base constructor with the provided id
        base.Id = id;

        // Initial state - inspection not done
        InspectionCompleted = false;
        QuantityAccepted = null;
        QuantityRejected = null;
        RejectionReason = null;
        QualityControlStatus = null;
        // InspectionNotes property removed, notes related to inspection go in UpdateInspectionResult
    }

    /// <summary>
    /// Internal method to record the outcome of inspection for this line.
    /// Should only be called from within the GoodsReceiptNote aggregate root.
    /// </summary>
    internal void UpdateInspectionResult(decimal quantityAccepted, decimal quantityRejected, string? rejectionReason = null, string? inspectionNotes = null)
    {
        if (InspectionCompleted)
            throw new InvalidOperationException("Inspection results have already been recorded for this line.");

        if (quantityAccepted < 0) throw new ArgumentOutOfRangeException(nameof(quantityAccepted), "Quantity accepted cannot be negative.");
        if (quantityRejected < 0) throw new ArgumentOutOfRangeException(nameof(quantityRejected), "Quantity rejected cannot be negative.");
        // Use Math.Round or tolerance if decimal precision issues occur
        if (Math.Abs((quantityAccepted + quantityRejected) - QuantityReceived) > 0.0001m) // Check within tolerance
            throw new InvalidOperationException($"Accepted quantity ({quantityAccepted}) + Rejected quantity ({quantityRejected}) must equal Received quantity ({QuantityReceived}).");
        if (quantityRejected > 0 && string.IsNullOrWhiteSpace(rejectionReason))
            throw new ArgumentException("A rejection reason must be provided if quantity is rejected.", nameof(rejectionReason));

        QuantityAccepted = quantityAccepted;
        QuantityRejected = quantityRejected;
        RejectionReason = quantityRejected > 0 ? rejectionReason : null; // Only store reason if rejected
        Notes = string.IsNullOrWhiteSpace(Notes) ? inspectionNotes : $"{Notes}\nInspection: {inspectionNotes}"; // Append inspection notes to general notes
        InspectionCompleted = true;

        // Set quality control status based on inspection results
        if (quantityRejected == 0)
        {
            QualityControlStatus = "Accepted";
        }
        else if (quantityAccepted == 0)
        {
            QualityControlStatus = "Rejected";
        }
        else
        {
            QualityControlStatus = "PartiallyAccepted";
        }

        // TODO: Raise Domain Event? GoodsReceiptLineInspectedEvent(this.Id)
    }
}
