# Migration Troubleshooting Guide for .NET Aspire Projects

This guide provides solutions for common migration issues in .NET Aspire projects, particularly those with multi-tenant architectures.

## Understanding the Migration Process

In .NET Aspire projects, database migrations are typically applied in two ways:

1. **Programmatically at application startup** - Using `EnhancedMigrationHelper` in `Program.cs`
2. **Manually using EF Core tools** - Using `dotnet ef database update` command

## Common Issues and Solutions

### 1. Connection String Issues

**Symptoms:**
- "Unable to connect to database" errors
- Connection timeouts
- "Connection string not found" errors

**Solutions:**
- Ensure the Aspire PostgreSQL container is running
- Use the connection string from the Aspire dashboard
- Use the `apply-aspire-migrations.ps1` script with the `-FromDashboard` parameter

**Example:**
```powershell
.\apply-aspire-migrations.ps1 -FromDashboard
```

### 2. Multi-Tenancy and Query Filters

**Symptoms:**
- Migrations fail with tenant-related errors
- Query filters interfere with migration operations

**Solutions:**
- Ensure the `MigrationTenantProvider` and `DesignTimeTenantProvider` both return `null` for tenant ID
- Verify that `ApplicationDbContext.ConfigureTenantQueryFilters` correctly disables filters when tenant ID is null
- Check that `ApplicationDbContextFactory` uses `DesignTimeTenantProvider`

### 3. Database Container Not Ready

**Symptoms:**
- Intermittent connection failures
- Migrations fail on first attempt but succeed on retry

**Solutions:**
- Use the enhanced retry logic in `EnhancedMigrationHelper`
- Increase the `maxRetries` parameter in `app.Services.ApplyMigrationsWithRetry(maxRetries: 10)`
- Use the `apply-aspire-migrations.ps1` script which includes retry logic

### 4. PostgreSQL Syntax Issues

**Symptoms:**
- SQL errors in migration operations
- Errors mentioning column names or filters

**Solutions:**
- Ensure column names in filter conditions use double quotes (`"ColumnName"`) rather than square brackets (`[ColumnName]`)
- Check entity configurations for PostgreSQL compatibility

### 5. Missing Schema Errors

**Symptoms:**
- Errors like `schema "common" does not exist`
- Migrations fail with `3F000` error code
- Errors when trying to move tables between schemas

**Example Error:**
```
Failed executing DbCommand: ALTER TABLE common.departments SET SCHEMA public;
Error: 3F000: schema "common" does not exist
```

**Solutions:**
- Use the `skip-problematic-migrations.ps1` script to skip migrations that reference non-existent schemas
- Reset migrations using the `reset-migrations.ps1` script to create a clean initial migration
- Manually insert migration records into `__EFMigrationsHistory` to skip problematic migrations

**Example:**
```powershell
# Skip specific problematic migrations
.\skip-problematic-migrations.ps1 -ConnectionString "your_connection_string"

# Or reset migrations completely
.\reset-migrations.ps1 -NewMigrationName "CleanInitialSchema"
```

## Debugging Migrations

### Checking Migration Status

To check which migrations have been applied:

```powershell
# Connect to your PostgreSQL database and run:
SELECT * FROM "__EFMigrationsHistory" ORDER BY "MigrationId";
```

### Generating SQL Scripts

To generate SQL scripts for migrations without applying them:

```powershell
# From solution root:
.\apply-aspire-migrations.ps1 -FromDashboard -GenerateScriptOnly
```

Or manually:

```powershell
dotnet ef migrations script --project ProcureToPay.Infrastructure --startup-project ProcureToPay.WebApp\ProcureToPay.WebApp --output migration_script.sql --idempotent
```

### Verbose Logging

To enable verbose logging for migrations:

1. Add to `appsettings.Development.json`:
```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.EntityFrameworkCore.Database.Command": "Information",
      "Microsoft.EntityFrameworkCore.Migrations": "Debug"
    }
  }
}
```

## Advanced Troubleshooting

### Manually Creating Migration History Records

If you need to manually insert migration records:

```sql
INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250404014913_InitialIdentitySchema', '9.0.3');
```

### Resetting the Migration Process

If you need to start fresh:

1. Drop all tables in the database
2. Delete all migration files in the `Migrations` folder
3. Create a new initial migration:
```powershell
dotnet ef migrations add InitialMigration --project ProcureToPay.Infrastructure --startup-project ProcureToPay.WebApp\ProcureToPay.WebApp
```

## Best Practices for Aspire Projects

1. **Always use programmatic migrations** at application startup for containerized environments
2. **Implement robust retry logic** to handle database container startup delays
3. **Properly handle multi-tenancy** during migrations by disabling tenant filters
4. **Use explicit connection strings** from the Aspire dashboard for manual operations
5. **Keep migration scripts idempotent** to allow safe re-runs

## Useful Commands

### Creating a New Migration

```powershell
dotnet ef migrations add MigrationName --project ProcureToPay.Infrastructure --startup-project ProcureToPay.WebApp\ProcureToPay.WebApp
```

### Applying Migrations

```powershell
# Using the enhanced script:
.\apply-aspire-migrations.ps1 -FromDashboard

# Or manually:
dotnet ef database update --project ProcureToPay.Infrastructure --startup-project ProcureToPay.WebApp\ProcureToPay.WebApp --connection "connection-string-here"
```

### Removing the Last Migration

```powershell
dotnet ef migrations remove --project ProcureToPay.Infrastructure --startup-project ProcureToPay.WebApp\ProcureToPay.WebApp
```
