﻿using System;
using ProcureToPay.Domain.Enums; // For RFQStatus

namespace ProcureToPay.Domain.Events
{
    // --- Placeholder: Define a base marker interface or class if desired ---
    // public interface IDomainEvent { }

    // --- RequestForQuote Specific Events ---

    /// <summary>
    /// Raised when a new Request for Quote is created.
    /// </summary>
    /// <param name="RfqId">The ID of the newly created RFQ.</param>
    /// <param name="TenantId">The Tenant ID associated with the RFQ.</param>
    public record RfqCreatedEvent(Guid RfqId, Guid TenantId);

    /// <summary>
    /// Raised when core details of an RFQ are updated (e.g., Title, Deadline, Address).
    /// </summary>
    /// <param name="RfqId">The ID of the updated RFQ.</param>
    public record RfqDetailsUpdatedEvent(Guid RfqId);

    /// <summary>
    /// Raised when an RFQ is sent to vendors (status changed to Open).
    /// Could potentially be handled solely by RfqStatusChangedEvent.
    /// </summary>
    /// <param name="RfqId">The ID of the sent RFQ.</param>
    public record RfqSentEvent(Guid RfqId);

    /// <summary>
    /// Generic event raised whenever the Status of an RFQ changes.
    /// </summary>
    /// <param name="RfqId">The ID of the RFQ whose status changed.</param>
    /// <param name="OldStatus">The previous status.</param>
    /// <param name="NewStatus">The new current status.</param>
    public record RfqStatusChangedEvent(Guid RfqId, RFQStatus OldStatus, RFQStatus NewStatus);

    /// <summary>
    /// Raised when an RFQ is awarded to a specific vendor.
    /// </summary>
    /// <param name="RfqId">The ID of the awarded RFQ.</param>
    /// <param name="AwardedVendorId">The ID of the vendor who was awarded the quote.</param>
    public record RfqAwardedEvent(Guid RfqId, Guid AwardedVendorId);

    /// <summary>
    /// Raised when an RFQ is cancelled.
    /// </summary>
    /// <param name="RfqId">The ID of the cancelled RFQ.</param>
    /// <param name="Reason">The reason provided for cancellation.</param>
    public record RfqCancelledEvent(Guid RfqId, string Reason);

    /// <summary>
    /// Raised when an RFQ is soft-deleted.
    /// </summary>
    /// <param name="RfqId">The ID of the deleted RFQ.</param>
    public record RfqDeletedEvent(Guid RfqId);

    /// <summary>
    /// Raised when a soft-deleted RFQ is restored.
    /// </summary>
    /// <param name="RfqId">The ID of the restored RFQ.</param>
    public record RfqRestoredEvent(Guid RfqId);

    // Optional: Events for line item changes if needed directly,
    // though often changes are managed via the RFQ aggregate root.
    // public record RfqLineAddedEvent(Guid RfqId, Guid LineId);
    // public record RfqLineRemovedEvent(Guid RfqId, Guid LineId);

}
