using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProcureToPay.Domain.Entities;
using ProcureToPay.Domain.Enums; // Assuming RFIStatus enum exists
using ProcureToPay.Infrastructure.Persistence.Extensions;

namespace ProcureToPay.Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// Configuration for the RequestForInformation entity for EF Core and PostgreSQL.
    /// </summary>
    public class RequestForInformationConfiguration : IEntityTypeConfiguration<RequestForInformation>
    {
        public void Configure(EntityTypeBuilder<RequestForInformation> builder)
        {
            // Table Mapping
            builder.ToTable("requests_for_information");

            // Primary Key
            builder.HasKey(rfi => rfi.Id);
            builder.Property(rfi => rfi.Id).ValueGeneratedOnAdd();

            // Properties
            builder.Property(rfi => rfi.RfiNumber)
                .IsRequired()
                .HasMaxLength(100); // Adjust length as needed

            builder.Property(rfi => rfi.Title)
                 .IsRequired()
                 .HasMaxLength(255);

            builder.Property(rfi => rfi.Description)
                .HasColumnType("text")
                .IsRequired(false);

            builder.Property(rfi => rfi.IssueDate)
                .IsRequired()
                .HasColumnType("timestamp without time zone"); // Or "date"

            builder.Property(rfi => rfi.ResponseDeadline)
                .IsRequired()
                .HasColumnType("timestamp without time zone"); // Or "date"

            builder.Property(rfi => rfi.Status)
                .IsRequired()
                .HasConversion<string>() // Or int
                .HasMaxLength(50);

            // Foreign Keys (Assuming Guid or appropriate types)
            builder.Property(rfi => rfi.ProjectId).IsRequired(false); // RFI might not be project-specific

            builder.Property(rfi => rfi.TenantId).IsRequired();


            // --- Concurrency Token ---
            builder.UseXminAsConcurrencyToken();


            // --- Relationships ---
            // Link to Project (Many-to-One, Optional)
            builder.HasOne(rfi => rfi.Project)
                   .WithMany(p => p.RequestsForInformation) // Assuming Project has collection
                   .HasForeignKey(rfi => rfi.ProjectId)
                   .IsRequired(false)
                   .OnDelete(DeleteBehavior.SetNull); // Nullify link if project deleted

            // Consider relationships to RFI Questions, Vendor Responses if modeled as separate entities
            // builder.HasMany(rfi => rfi.Questions)...
            // builder.HasMany(rfi => rfi.VendorResponses)...


            // --- Indexes ---
            builder.HasIndex(rfi => rfi.RfiNumber).IsUnique();
            builder.HasIndex(rfi => rfi.Status);
            builder.HasIndex(rfi => rfi.ResponseDeadline);
            builder.HasIndex(rfi => rfi.ProjectId);
            builder.HasIndex(rfi => rfi.TenantId);


            // --- TODO ---
            // TODO: Verify FK type for ProjectId matches Project entity.
            // TODO: Define RFIStatus enum.
            // TODO: Model and configure RFI Questions/Responses if needed.
        }
    }
}
