using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProcureToPay.Domain.Entities;
using ProcureToPay.Domain.Enums;
using ProcureToPay.Infrastructure.Persistence.Configurations; // For CategoryConfiguration references
using System;
using static ProcureToPay.Infrastructure.Persistence.Configurations.CategoryConfiguration; // Import static category IDs
using TenantConfiguration = ProcureToPay.Infrastructure.Persistence.Configurations.TenantConfiguration;

namespace ProcureToPay.Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// Configuration for the Product entity for EF Core and PostgreSQL.
    /// </summary>
    public class ProductConfiguration : IEntityTypeConfiguration<Product>
    {
        // Define static GUIDs for products to ensure consistent references
        public static readonly Guid A4CopyPaperId = Guid.Parse("*************-3333-3333-************");
        public static readonly Guid SpiralNotebookId = Guid.Parse("*************-3333-3333-************");
        public static readonly Guid BallpointPenId = Guid.Parse("*************-3333-3333-************");
        public static readonly Guid LaptopComputerId = Guid.Parse("*************-3333-3333-************");
        public static readonly Guid MonitorId = Guid.Parse("*************-3333-3333-************");
        public static readonly Guid WirelessMouseId = Guid.Parse("*************-3333-3333-************");

        // Fixed date for seeding to ensure consistency
        private static readonly DateTime SeedDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc);

        public void Configure(EntityTypeBuilder<Product> builder)
        {
            // Table Mapping with explicit schema
            builder.ToTable("products", "public");

            // Primary Key
            builder.HasKey(p => p.Id);
            builder.Property(p => p.Id).ValueGeneratedNever(); // For seeded entities with predefined IDs

            // --- Properties ---
            builder.Property(p => p.Name)
                .IsRequired()
                .HasMaxLength(255);

            builder.Property(p => p.ProductCode)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(p => p.Description)
                .HasColumnType("text") // Use PostgreSQL text type for longer descriptions
                .IsRequired(false);

            builder.Property(p => p.UnitOfMeasure)
                .IsRequired()
                .HasConversion<string>() // Store enum as string
                .HasMaxLength(20);

            builder.Property(p => p.IsActive)
                .IsRequired()
                .HasDefaultValue(true);

            builder.Property(p => p.IsDeleted)
                .IsRequired()
                .HasDefaultValue(false);

            builder.Property(p => p.TenantId)
                .IsRequired();

            // Audit fields from BaseEntity
            builder.Property("CreatedAt").IsRequired();
            builder.Property("ModifiedAt").IsRequired(false);

            // --- Relationships ---
            builder.HasOne(p => p.Category)
                .WithMany(c => c.Products)
                .HasForeignKey(p => p.CategoryId)
                .OnDelete(DeleteBehavior.SetNull);

            // --- Indexes ---
            builder.HasIndex(p => p.Name)
                .HasDatabaseName("IX_products_name");

            builder.HasIndex(p => p.IsActive)
                .HasDatabaseName("IX_products_is_active");

            builder.HasIndex(p => p.IsDeleted)
                .HasDatabaseName("IX_products_is_deleted");

            builder.HasIndex(p => p.TenantId)
                .HasDatabaseName("IX_products_tenant_id");

            builder.HasIndex(p => p.CategoryId)
                .HasDatabaseName("IX_products_category_id");

            // PostgreSQL-specific syntax for composite unique index
            builder.HasIndex(p => new { p.TenantId, p.ProductCode })
                .IsUnique()
                .HasDatabaseName("IX_products_tenant_id_product_code_unique");

            // --- Seeding ---
            // Seed sample products using anonymous types for HasData
            builder.HasData(
                // Paper Products
                new
                {
                    Id = A4CopyPaperId,
                    Name = "A4 Copy Paper",
                    ProductCode = "PAPER-001",
                    UnitOfMeasure = UnitOfMeasure.Box,
                    TenantId = TenantConfiguration.DefaultTenantId,
                    CategoryId = PaperProductsId, // Using static reference from CategoryConfiguration
                    Description = "Premium A4 copy paper, 80gsm, 500 sheets per ream, 5 reams per box",
                    IsActive = true,
                    IsDeleted = false,
                    CreatedAt = SeedDate,
                    ModifiedAt = (DateTime?)null
                },
                new
                {
                    Id = SpiralNotebookId,
                    Name = "Spiral Notebook",
                    ProductCode = "PAPER-002",
                    UnitOfMeasure = UnitOfMeasure.Each,
                    TenantId = TenantConfiguration.DefaultTenantId,
                    CategoryId = PaperProductsId,
                    Description = "A5 spiral-bound notebook, 100 pages, ruled",
                    IsActive = true,
                    IsDeleted = false,
                    CreatedAt = SeedDate,
                    ModifiedAt = (DateTime?)null
                },

                // Writing Instruments
                new
                {
                    Id = BallpointPenId,
                    Name = "Ballpoint Pen",
                    ProductCode = "WRITE-001",
                    UnitOfMeasure = UnitOfMeasure.Box,
                    TenantId = TenantConfiguration.DefaultTenantId,
                    CategoryId = WritingInstrumentsId,
                    Description = "Blue ballpoint pens, medium point, 12 pens per box",
                    IsActive = true,
                    IsDeleted = false,
                    CreatedAt = SeedDate,
                    ModifiedAt = (DateTime?)null
                },

                // Computers
                new
                {
                    Id = LaptopComputerId,
                    Name = "Laptop Computer",
                    ProductCode = "COMP-001",
                    UnitOfMeasure = UnitOfMeasure.Each,
                    TenantId = TenantConfiguration.DefaultTenantId,
                    CategoryId = ComputersId,
                    Description = "15.6\" business laptop, 16GB RAM, 512GB SSD, Intel Core i7",
                    IsActive = true,
                    IsDeleted = false,
                    CreatedAt = SeedDate,
                    ModifiedAt = (DateTime?)null
                },

                // Peripherals
                new
                {
                    Id = MonitorId,
                    Name = "24\" Monitor",
                    ProductCode = "PERIPH-001",
                    UnitOfMeasure = UnitOfMeasure.Each,
                    TenantId = TenantConfiguration.DefaultTenantId,
                    CategoryId = PeripheralsId,
                    Description = "24-inch LED monitor, 1080p, HDMI and DisplayPort",
                    IsActive = true,
                    IsDeleted = false,
                    CreatedAt = SeedDate,
                    ModifiedAt = (DateTime?)null
                },
                new
                {
                    Id = WirelessMouseId,
                    Name = "Wireless Mouse",
                    ProductCode = "PERIPH-002",
                    UnitOfMeasure = UnitOfMeasure.Each,
                    TenantId = TenantConfiguration.DefaultTenantId,
                    CategoryId = PeripheralsId,
                    Description = "Ergonomic wireless mouse, 2.4GHz, USB receiver",
                    IsActive = true,
                    IsDeleted = false,
                    CreatedAt = SeedDate,
                    ModifiedAt = (DateTime?)null
                }
            );
        }
    }
}

