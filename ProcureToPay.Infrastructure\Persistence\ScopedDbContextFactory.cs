using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using ProcureToPay.Domain.Interfaces;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace ProcureToPay.Infrastructure.Persistence
{
    /// <summary>
    /// A custom implementation of IDbContextFactory that properly handles scoped dependencies.
    /// This factory creates a new scope for each DbContext instance to ensure proper resolution
    /// of scoped dependencies like ITenantProvider.
    /// </summary>
    /// <typeparam name="TContext">The type of DbContext to create.</typeparam>
    public class ScopedDbContextFactory<TContext> : IDbContextFactory<TContext> where TContext : DbContext
    {
        private readonly IServiceProvider _serviceProvider;

        /// <summary>
        /// Initializes a new instance of the <see cref="ScopedDbContextFactory{TContext}"/> class.
        /// </summary>
        /// <param name="serviceProvider">The service provider to resolve services.</param>
        public ScopedDbContextFactory(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        }

        /// <summary>
        /// Creates a new instance of a derived DbContext with properly scoped dependencies.
        /// </summary>
        /// <returns>A new instance of TContext.</returns>
        public TContext CreateDbContext()
        {
            // Create a new scope to resolve scoped dependencies
            var scope = _serviceProvider.CreateScope();

            try
            {
                // Get the DbContextOptions from the service provider
                var options = scope.ServiceProvider.GetRequiredService<DbContextOptions<ApplicationDbContext>>();

                // Get the tenant provider from the service provider
                var tenantProvider = scope.ServiceProvider.GetRequiredService<ITenantProvider>();

                // Create and return the DbContext with the scoped tenant provider
                // Note: This assumes TContext is ApplicationDbContext or a derived type
                return (TContext)(object)new ApplicationDbContext(options, tenantProvider);
            }
            catch
            {
                // If an exception occurs, dispose the scope to prevent leaks
                scope.Dispose();
                throw;
            }
        }

        /// <summary>
        /// Creates a new instance of a derived DbContext with properly scoped dependencies asynchronously.
        /// </summary>
        /// <param name="cancellationToken">A <see cref="CancellationToken" /> to observe while waiting for the task to complete.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains a new instance of TContext.</returns>
        public Task<TContext> CreateDbContextAsync(CancellationToken cancellationToken = default)
        {
            // For simplicity, we're just calling the synchronous method
            // In a real-world scenario, you might want to implement true async initialization if needed
            return Task.FromResult(CreateDbContext());
        }
    }
}
