# Data Seeding Strategy for ProcureToPay

This document outlines the comprehensive data seeding strategy for the ProcureToPay application, which uses Entity Framework Core with PostgreSQL in a .NET 9 Blazor application.

## Overview

The application employs a dual-approach seeding strategy:

1. **EF Core HasData for Static Reference Data**: Used for seeding foundational, static reference data directly within entity configurations.
2. **Custom ApplicationDbSeeder for Dynamic/Complex Data**: Used for data requiring application services or conditional logic.

## Key Principles

### 1. Static GUIDs for Predictable References

Define static GUIDs for seeded entities to ensure consistent references across configurations:

```csharp
public static readonly Guid DefaultTenantId = Guid.Parse("11111111-1111-1111-1111-111111111111");
public static readonly Guid ITDepartmentId = Guid.Parse("*************-4444-4444-************");
```

### 2. Anonymous Types for HasData

Use anonymous types with HasData for cleaner seeding:

```csharp
builder.HasData(
    new
    {
        Id = DefaultTenantId,
        Name = "Default System Tenant",
        Identifier = "system",
        IsActive = true,
        CreatedAt = SeedDate,
        ModifiedAt = (DateTime?)null
    }
);
```

### 3. Fixed DateTime Values for Idempotent Migrations

Use static DateTime values for audit fields to ensure idempotent migrations:

```csharp
private static readonly DateTime SeedDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc);
```

### 4. Comprehensive Property Initialization

Ensure all non-nullable properties (including inherited ones) have values:

```csharp
new
{
    Id = CategoryId,
    Name = "Office Supplies",
    Description = "General office supplies",
    TenantId = DefaultTenantId,
    CreatedAt = SeedDate,
    ModifiedAt = (DateTime?)null
}
```

### 5. Explicit Null Values for Nullable Properties

Explicitly set nullable properties to null for clarity:

```csharp
ModifiedAt = (DateTime?)null,
ParentCategoryId = (Guid?)null
```

## Implementation Details

### Phase 1: HasData in Entity Configurations

#### TenantConfiguration.cs

```csharp
public static readonly Guid DefaultTenantId = Guid.Parse("11111111-1111-1111-1111-111111111111");
private static readonly DateTime SeedDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc);

// In Configure method:
builder.HasData(
    new
    {
        Id = DefaultTenantId,
        Name = "Default System Tenant",
        Identifier = "system",
        SubscriptionPlan = "Standard",
        ContactEmail = "<EMAIL>",
        IsActive = true,
        CreatedAt = SeedDate,
        ModifiedAt = (DateTime?)null,
        Settings = (string?)null,
        AddressLine1 = (string?)null,
        City = (string?)null,
        PostalCode = (string?)null,
        Country = (string?)null
    }
);
```

#### CategoryConfiguration.cs

```csharp
public static readonly Guid OfficeSuppliesId = Guid.Parse("*************-2222-2222-************");
public static readonly Guid ItEquipmentId = Guid.Parse("*************-2222-2222-************");
// More category IDs...

// In Configure method:
builder.HasData(
    new
    {
        Id = OfficeSuppliesId,
        Name = "Office Supplies",
        Description = "General office supplies and stationery",
        Code = "OFF-SUP",
        UnspscCode = "14111500",
        ParentCategoryId = (Guid?)null,
        CreatedAt = SeedDate,
        ModifiedAt = (DateTime?)null
    },
    // More categories...
);
```

#### ProductConfiguration.cs

```csharp
public static readonly Guid A4CopyPaperId = Guid.Parse("*************-3333-3333-************");
// More product IDs...

// In Configure method:
builder.HasData(
    new
    {
        Id = A4CopyPaperId,
        Name = "A4 Copy Paper",
        ProductCode = "PAPER-001",
        UnitOfMeasure = UnitOfMeasure.Box,
        TenantId = DefaultTenantId,
        CategoryId = PaperProductsId, // Using static reference from CategoryConfiguration
        Description = "Premium A4 copy paper, 80gsm, 500 sheets per ream, 5 reams per box",
        IsActive = true,
        IsDeleted = false,
        CreatedAt = SeedDate,
        ModifiedAt = (DateTime?)null
    },
    // More products...
);
```

#### DepartmentConfiguration.cs

```csharp
public static readonly Guid ITDepartmentId = Guid.Parse("*************-4444-4444-************");
public static readonly Guid FinanceDepartmentId = Guid.Parse("*************-4444-4444-************");
// More department IDs...

// In Configure method:
builder.HasData(
    new
    {
        Id = ITDepartmentId,
        TenantId = DefaultTenantId,
        Name = "Information Technology",
        Code = "IT",
        Description = "Handles IT infrastructure, software development, and technical support.",
        CreatedAt = SeedDate,
        ModifiedAt = (DateTime?)null
    },
    // More departments...
);
```

### Phase 2: ApplicationDbSeeder for Complex Data

The `ApplicationDbSeeder` class handles seeding data that requires application services or conditional logic:

```csharp
public class ApplicationDbSeeder
{
    // Constructor with injected services
    public ApplicationDbSeeder(
        ILogger<ApplicationDbSeeder> logger,
        ApplicationDbContext dbContext,
        UserManager<ApplicationUser> userManager,
        RoleManager<IdentityRole> roleManager,
        IServiceScopeFactory scopeFactory)
    {
        // Initialize fields
    }

    // Main seeding method
    public async Task SeedAllAsync()
    {
        try
        {
            // Ensure default tenant exists
            await EnsureDefaultTenantExistsAsync();

            // Seed roles
            await SeedRolesAsync();

            // Seed users with roles
            await SeedUsersAsync();

            // Seed departments (fallback)
            await EnsureDepartmentsExistAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An error occurred while seeding application data.");
            throw;
        }
    }

    // Individual seeding methods
    private async Task SeedRolesAsync() { /* ... */ }
    private async Task SeedUsersAsync() { /* ... */ }
    private async Task EnsureDepartmentsExistAsync() { /* ... */ }
}
```

## Integration with Application Startup

The seeding process is integrated into the application startup in `Program.cs`:

```csharp
// In Program.cs
// Register the ApplicationDbSeeder
builder.Services.AddScoped<ApplicationDbSeeder>();

// Build the app
var app = builder.Build();

// Apply migrations programmatically
bool migrationSuccess = app.Services.ApplyMigrationsWithRetry(maxRetries: 10);

if (migrationSuccess)
{
    // Seed application data after successful migration
    using (var scope = app.Services.CreateScope())
    {
        var seeder = scope.ServiceProvider.GetRequiredService<ApplicationDbSeeder>();
        await seeder.SeedAllAsync();
    }
}
```

## Execution Flow

1. **Update Entity Configurations**: Modify IEntityTypeConfiguration<T> classes with HasData calls.
2. **Add Migration**: Generate a new EF Core migration to capture HasData changes.
3. **Apply Migrations**: Use your established migration process to apply the migration.
4. **Run Application**: Start your Aspire application, which will run ApplicationDbSeeder.
5. **Verify Data**: Check the database to ensure all expected data is present.

## Best Practices

1. **Tenant Association**: Ensure tenant-specific entities include the correct TenantId.
2. **Enum Conversions**: When using enums with HasConversion<string>(), ensure string values match enum names.
3. **Idempotent Seeder**: Make ApplicationDbSeeder idempotent to prevent duplicate data.
4. **Error Handling**: Log errors during seeding and decide whether to fail application startup.
5. **Consistent IDs**: Use the same static GUIDs across all environments to ensure consistency.
