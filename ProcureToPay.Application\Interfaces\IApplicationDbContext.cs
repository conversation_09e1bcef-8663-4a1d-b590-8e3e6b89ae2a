using Microsoft.EntityFrameworkCore;
using ProcureToPay.Domain.Entities;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace ProcureToPay.Application.Interfaces
{
    /// <summary>
    /// Interface for the application database context.
    /// </summary>
    public interface IApplicationDbContext : IAsyncDisposable
    {
        /// <summary>
        /// Gets or sets the categories DbSet.
        /// </summary>
        DbSet<Category> Categories { get; }

        /// <summary>
        /// Saves changes to the database.
        /// </summary>
        /// <param name="cancellationToken">A cancellation token.</param>
        /// <returns>The number of state entries written to the database.</returns>
        Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
    }
}
