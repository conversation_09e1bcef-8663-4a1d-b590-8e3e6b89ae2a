﻿namespace ProcureToPay.Domain.Enums
{
    /// <summary>
    /// Represents the classification or type of a customer.
    /// </summary>
    public enum CustomerType
    {
        /// <summary>
        /// Default or unspecified type.
        /// </summary>
        Unspecified = 0,

        /// <summary>
        /// A business entity (e.g., corporation, LLC).
        /// </summary>
        Business = 1,

        /// <summary>
        /// An individual consumer.
        /// </summary>
        Individual = 2,

        /// <summary>
        /// A government entity.
        /// </summary>
        Government = 3,

        /// <summary>
        /// A non-profit organization.
        /// </summary>
        NonProfit = 4
        // Add other types as needed
    }
}
