using System;
using System.Collections.Generic;
using ProcureToPay.Domain.Enums;
using ProcureToPay.Domain.Interfaces;

namespace ProcureToPay.Domain.Entities
{
    /// <summary>
    /// Represents a product entity in the system.
    /// </summary>
    public class Product : BaseEntity<Guid>, ITenantEntity
    {
        /// <summary>
        /// The name of the product.
        /// </summary>
        public string Name { get; private set; } = null!;

        /// <summary>
        /// The product code or SKU.
        /// </summary>
        public string ProductCode { get; private set; } = null!;

        /// <summary>
        /// A description of the product.
        /// </summary>
        public string? Description { get; private set; }

        /// <summary>
        /// The unit of measure for the product.
        /// </summary>
        public UnitOfMeasure UnitOfMeasure { get; private set; }

        /// <summary>
        /// Flag indicating if the product is active.
        /// </summary>
        public bool IsActive { get; private set; }

        /// <summary>
        /// Flag indicating if the product has been soft-deleted.
        /// </summary>
        public bool IsDeleted { get; private set; }

        /// <summary>
        /// Gets or sets the category ID of the product.
        /// </summary>
        public Guid? CategoryId { get; private set; }

        /// <summary>
        /// Gets or sets the category of the product.
        /// </summary>
        public virtual Category? Category { get; private set; }

        /// <summary>
        /// Gets or sets the tenant ID for multi-tenancy support.
        /// </summary>
        public Guid TenantId { get; set; }

        /// <summary>
        /// Private constructor for EF Core.
        /// </summary>
        private Product() : base(Guid.NewGuid()) { }

        /// <summary>
        /// Creates a new Product instance.
        /// </summary>
        public Product(
            Guid id,
            string name,
            string productCode,
            UnitOfMeasure unitOfMeasure,
            string? description = null,
            bool isActive = true) : base(id)
        {
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
            if (string.IsNullOrWhiteSpace(productCode)) throw new ArgumentNullException(nameof(productCode));

            Name = name;
            ProductCode = productCode;
            UnitOfMeasure = unitOfMeasure;
            Description = description;
            IsActive = isActive;
            IsDeleted = false;
        }

        /// <summary>
        /// Updates the product details.
        /// </summary>
        public void UpdateDetails(string name, string productCode, string? description, UnitOfMeasure unitOfMeasure)
        {
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
            if (string.IsNullOrWhiteSpace(productCode)) throw new ArgumentNullException(nameof(productCode));

            Name = name;
            ProductCode = productCode;
            Description = description;
            UnitOfMeasure = unitOfMeasure;
        }

        /// <summary>
        /// Activates the product.
        /// </summary>
        public void Activate()
        {
            if (IsActive) return;
            IsActive = true;
        }

        /// <summary>
        /// Deactivates the product.
        /// </summary>
        public void Deactivate()
        {
            if (!IsActive) return;
            IsActive = false;
        }

        /// <summary>
        /// Marks the product as deleted (soft delete).
        /// </summary>
        public void MarkAsDeleted()
        {
            if (IsDeleted) return;
            IsDeleted = true;
            IsActive = false;
        }

        /// <summary>
        /// Restores a soft-deleted product.
        /// </summary>
        public void Restore()
        {
            if (!IsDeleted) return;
            IsDeleted = false;
            // Note: This doesn't automatically reactivate the product
        }

        /// <summary>
        /// Updates the category of the product.
        /// </summary>
        /// <param name="categoryId">The ID of the category to assign to the product, or null to remove the category.</param>
        public void UpdateCategory(Guid? categoryId)
        {
            if (CategoryId == categoryId) return; // No change
            CategoryId = categoryId;
            // Domain event could be added here if needed
        }
    }
}
