﻿using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking; // Required for ChangeTracker
using Microsoft.EntityFrameworkCore.Metadata; // Required for StoreObjectIdentifier and ConfigurationSource
using Microsoft.EntityFrameworkCore.Migrations; // Required for HistoryRepository
using Microsoft.Extensions.DependencyInjection; // Required for IServiceProvider
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using ProcureToPay.Application.Interfaces;
using ProcureToPay.Domain.Entities; // Import your domain entities namespace
using ProcureToPay.Domain.Interfaces; // Assuming ITenantEntity and ITenantProvider interfaces are here
using ProcureToPay.Infrastructure.Identity; // Import your ApplicationUser if it's custom
using System.Linq.Expressions; // Required for LambdaExpression
using System.Reflection; // Required for ApplyConfigurationsFromAssembly
using System.Text; // Required for StringBuilder
using System.Text.RegularExpressions; // Required for enhanced snake_case conversion

namespace ProcureToPay.Infrastructure.Persistence
{
    /// <summary>
    /// The main database context for the application, integrating Identity and Domain entities.
    /// Implements multi-tenancy using a shared database, row-level security approach.
    /// </summary>
    public class ApplicationDbContext : IdentityDbContext<ApplicationUser>, IApplicationDbContext // Use your specific ApplicationUser class
    {
        private readonly ITenantProvider _tenantProvider;
        private readonly Guid? _currentTenantId; // Store tenant ID resolved during construction for filtering
        private readonly IServiceProvider? _serviceProvider;
        private readonly NamingConventionOptions _namingOptions;
        private readonly ILogger<ApplicationDbContext>? _logger;

        // --- Domain Entity DbSets ---
        // (Keep all existing DbSet properties as they were)
        public DbSet<Budget> Budgets { get; set; } = null!;
        public DbSet<BudgetAllocation> BudgetAllocations { get; set; } = null!;
        public DbSet<Category> Categories { get; set; } = null!;
        public DbSet<Contract> Contracts { get; set; } = null!;
        public DbSet<Customer> Customers { get; set; } = null!;
        public DbSet<DeliveryNote> DeliveryNotes { get; set; } = null!;
        public DbSet<DeliveryNoteLine> DeliveryNoteLines { get; set; } = null!;
        public DbSet<Department> Departments { get; set; } = null!;
        public DbSet<GoodsReceiptNote> GoodsReceiptNotes { get; set; } = null!;
        public DbSet<GoodsReceiptNoteLine> GoodsReceiptNoteLines { get; set; } = null!;
        public DbSet<Invoice> Invoices { get; set; } = null!;
        public DbSet<InvoiceLine> InvoiceLines { get; set; } = null!;
        public DbSet<PaymentTransaction> PaymentTransactions { get; set; } = null!;
        public DbSet<ProcurementWorkflow> ProcurementWorkflows { get; set; } = null!;
        public DbSet<ProcurementWorkflowStep> ProcurementWorkflowSteps { get; set; } = null!;
        public DbSet<ProductDefinition> ProductDefinitions { get; set; } = null!;
        public DbSet<Product> Products { get; set; } = null!;
        public DbSet<Project> Projects { get; set; } = null!;
        public DbSet<PurchaseOrder> PurchaseOrders { get; set; } = null!;
        public DbSet<PurchaseOrderLine> PurchaseOrderLines { get; set; } = null!;
        public DbSet<PurchaseRequisition> PurchaseRequisitions { get; set; } = null!;
        public DbSet<PurchaseRequisitionLine> PurchaseRequisitionLines { get; set; } = null!;
        public DbSet<RequestForInformation> RequestsForInformation { get; set; } = null!;
        public DbSet<RequestForProposal> RequestsForProposal { get; set; } = null!;
        public DbSet<RequestForQuote> RequestsForQuote { get; set; } = null!;
        public DbSet<RequestForQuoteLine> RequestForQuoteLines { get; set; } = null!;
        public DbSet<ReturnAuthorization> ReturnAuthorizations { get; set; } = null!;
        public DbSet<ReturnAuthorizationLine> ReturnAuthorizationLines { get; set; } = null!;
        public DbSet<SalesOrder> SalesOrders { get; set; } = null!;
        public DbSet<SalesOrderLine> SalesOrderLines { get; set; } = null!;
        public DbSet<SalesTerritory> SalesTerritories { get; set; } = null!;
        public DbSet<SubmittalReview> SubmittalReviews { get; set; } = null!;
        public DbSet<Supplier> Suppliers { get; set; } = null!;
        public DbSet<TechnicalSubmittal> TechnicalSubmittals { get; set; } = null!;
        public DbSet<Tenant> Tenants { get; set; } = null!; // Tenant entity itself might not implement ITenantEntity
        public DbSet<TenantProduct> TenantProducts { get; set; } = null!;
        public DbSet<Vendor> Vendors { get; set; } = null!;
        public DbSet<VendorProduct> VendorProducts { get; set; } = null!;
        public DbSet<VendorProposal> VendorProposals { get; set; } = null!;
        public DbSet<TestEntity> TestEntities { get; set; } = null!;
        // Add other DbSets as needed...


        /// <summary>
        /// Initializes a new instance of the <see cref="ApplicationDbContext"/> class.
        /// </summary>
        /// <param name="options">The options to be used by a <see cref="DbContext"/>.</param>
        /// <param name="tenantProvider">Service to resolve the current tenant ID.</param>
        /// <param name="namingOptions">Optional naming convention options. If null, default options are used.</param>
        /// <param name="logger">Optional logger for detailed naming convention logging.</param>
        public ApplicationDbContext(
            DbContextOptions<ApplicationDbContext> options,
            ITenantProvider tenantProvider,
            IOptions<NamingConventionOptions>? namingOptions = null,
            ILogger<ApplicationDbContext>? logger = null) // Inject the tenant provider
            : base(options)
        {
            _tenantProvider = tenantProvider ?? throw new ArgumentNullException(nameof(tenantProvider));
            _currentTenantId = _tenantProvider.GetCurrentTenantId(); // Resolve tenant ID once per context instance
            _namingOptions = namingOptions?.Value ?? new NamingConventionOptions();
            _logger = logger;
        }

        /// <summary>
        /// Configures the schema needed for the identity framework, applies entity configurations,
        /// and sets up global query filters for multi-tenancy.
        /// </summary>
        /// <param name="modelBuilder">The builder being used to construct the model for this context.</param>
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            // --- Apply Identity Configurations FIRST ---
            base.OnModelCreating(modelBuilder);

            // --- Apply Domain Entity Configurations from Infrastructure Assembly ---
            modelBuilder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());

            // --- Apply Snake Case Naming Convention with Enhanced Features ---
            if (_namingOptions.EnableSnakeCaseConversion)
            {
                if (_namingOptions.EnableValidation)
                {
                    ValidateNamingConventionChanges(modelBuilder);
                }
                ApplySnakeCaseNamingConvention(modelBuilder);
            }

            // --- Global Query Filter for Tenant Isolation ---
            ConfigureTenantQueryFilters(modelBuilder);

            // --- Default Schema (Optional) ---
            // modelBuilder.HasDefaultSchema("p2p");

            // --- PostgreSQL Extensions (Optional) ---
            // modelBuilder.HasPostgresExtension("uuid-ossp");
        }

        /// <summary>
        /// Configures global query filters for entities implementing ITenantEntity.
        /// </summary>
        /// <param name="modelBuilder">The model builder.</param>
        private void ConfigureTenantQueryFilters(ModelBuilder modelBuilder)
        {
            // Iterate through all entity types in the model
            foreach (var entityType in modelBuilder.Model.GetEntityTypes())
            {
                // Check if the entity implements the ITenantEntity interface
                if (typeof(ITenantEntity).IsAssignableFrom(entityType.ClrType))
                {
                    // Build the lambda expression for the query filter dynamically
                    var parameter = Expression.Parameter(entityType.ClrType, "e");

                    // IMPORTANT: For migrations and design-time operations, we need to allow all entities
                    // to be visible regardless of tenant. This is critical for EF Core to properly
                    // generate and apply migrations.

                    // Check if we're in a migration or design-time context
                    bool isMigrationOrDesignTime = _currentTenantId == null;

                    if (isMigrationOrDesignTime)
                    {
                        // During migrations or design-time operations, don't apply tenant filtering
                        // This allows EF Core to see all entities regardless of tenant
                        var trueExpression = Expression.Constant(true);
                        var lambda = Expression.Lambda(trueExpression, parameter);
                        modelBuilder.Entity(entityType.ClrType).HasQueryFilter(lambda);
                    }
                    else
                    {
                        // In normal operation with a valid tenant, apply tenant filtering
                        var property = Expression.Property(parameter, nameof(ITenantEntity.TenantId));
                        var tenantIdValue = Expression.Constant(_currentTenantId);
                        var equal = Expression.Equal(property, tenantIdValue);
                        var lambda = Expression.Lambda(equal, parameter);
                        modelBuilder.Entity(entityType.ClrType).HasQueryFilter(lambda);
                    }
                }
            }
        }

        /// <summary>
        /// Applies snake_case naming convention to all entity properties that don't already have explicit column names.
        /// This replaces the EFCore.NamingConventions library to avoid compatibility issues with .NET 9 preview.
        /// Enhanced with configurable options, detailed logging, and proper EF Core migrations history table handling.
        /// </summary>
        /// <param name="modelBuilder">The model builder.</param>
        private void ApplySnakeCaseNamingConvention(ModelBuilder modelBuilder)
        {
            if (!_namingOptions.EnableSnakeCaseConversion) return;

            if (_namingOptions.EnableLogging && _logger != null)
                _logger.LogInformation("[SNAKE_CASE_CONVENTION] Applying custom snake_case naming convention...");

            foreach (var entityType in modelBuilder.Model.GetEntityTypes())
            {
                // Skip owned entities to avoid conflicts with their owner's configuration
                if (entityType.IsOwned())
                {
                    if (_namingOptions.EnableLogging && _logger != null)
                        _logger.LogDebug("[SNAKE_CASE_CONVENTION] Skipping owned entity type: {EntityType}", entityType.DisplayName());
                    continue;
                }

                string? currentTableName = entityType.GetTableName();
                string? schema = entityType.GetSchema();

                // Special handling for EF Core migrations history table
                if (currentTableName == HistoryRepository.DefaultTableName)
                {
                    if (_namingOptions.EnableLogging && _logger != null)
                        _logger.LogDebug("[SNAKE_CASE_CONVENTION] Skipping EF Core History Table: {TableName}. Ensuring its columns (MigrationId, ProductVersion) remain PascalCase.", currentTableName);

                    // Ensure PascalCase for history table columns
                    foreach (var property in entityType.GetProperties())
                    {
                        if (property.Name == nameof(HistoryRow.MigrationId))
                        {
                            var storeObjectId = StoreObjectIdentifier.Table(currentTableName, schema);
                            if (property.GetColumnName(storeObjectId) != nameof(HistoryRow.MigrationId))
                                property.SetColumnName(nameof(HistoryRow.MigrationId));
                        }
                        if (property.Name == nameof(HistoryRow.ProductVersion))
                        {
                            var storeObjectId = StoreObjectIdentifier.Table(currentTableName, schema);
                            if (property.GetColumnName(storeObjectId) != nameof(HistoryRow.ProductVersion))
                                property.SetColumnName(nameof(HistoryRow.ProductVersion));
                        }
                    }
                    continue;
                }

                var finalTableName = currentTableName; // Will be updated if table name changes

                // Apply snake_case to table names
                if (_namingOptions.ConvertTableNames && currentTableName != null)
                {
                    string desiredSnakeCaseTableName = ConvertToSnakeCase(currentTableName);
                    if (currentTableName != desiredSnakeCaseTableName)
                    {
                        entityType.SetTableName(desiredSnakeCaseTableName);
                        finalTableName = desiredSnakeCaseTableName; // Use new name for subsequent operations
                        if (_namingOptions.EnableLogging && _logger != null)
                            _logger.LogDebug("[SNAKE_CASE_CONVENTION] Table: {Original} -> {Converted}", currentTableName, desiredSnakeCaseTableName);
                    }
                }

                var storeObjectIdentifier = StoreObjectIdentifier.Table(finalTableName, schema);

                // Apply snake_case to column names
                if (_namingOptions.ConvertColumnNames)
                {
                    foreach (var property in entityType.GetProperties())
                    {
                        string clrPropertyName = property.Name;
                        string? currentColumnName = property.GetColumnName(storeObjectIdentifier);
                        string desiredSnakeCaseColumnName = ConvertToSnakeCase(clrPropertyName);
                        var columnNameConfigSource = property.GetColumnNameConfigurationSource(storeObjectIdentifier);
                        bool wasExplicitlyNamedByUser = columnNameConfigSource == ConfigurationSource.Explicit ||
                                                       columnNameConfigSource == ConfigurationSource.DataAnnotation;

                        if (!wasExplicitlyNamedByUser && currentColumnName != desiredSnakeCaseColumnName)
                        {
                            property.SetColumnName(desiredSnakeCaseColumnName);
                            if (_namingOptions.EnableLogging && _logger != null)
                                _logger.LogDebug("[SNAKE_CASE_CONVENTION] Column: {Entity}.{Property} -> {NewName} on table {Table}",
                                    entityType.DisplayName(), clrPropertyName, desiredSnakeCaseColumnName, finalTableName);
                        }
                        else if (wasExplicitlyNamedByUser && _namingOptions.EnableLogging && _logger != null && currentColumnName != desiredSnakeCaseColumnName)
                        {
                            _logger.LogDebug("[SNAKE_CASE_CONVENTION] Column: {Entity}.{Property} explicitly named '{CurrentName}', skipping conversion to '{DesiredName}'.",
                                entityType.DisplayName(), clrPropertyName, currentColumnName, desiredSnakeCaseColumnName);
                        }
                    }
                }

                // Apply snake_case to index names
                if (_namingOptions.ConvertIndexNames)
                {
                    foreach (var index in entityType.GetIndexes())
                    {
                        string? currentIndexName = index.GetDatabaseName();
                        if (currentIndexName != null && !currentIndexName.StartsWith("ix_", StringComparison.OrdinalIgnoreCase))
                        {
                            string newIndexName = $"ix_{ConvertToSnakeCase(finalTableName ?? "table")}_{string.Join("_", index.Properties.Select(p => ConvertToSnakeCase(p.Name)))}";
                            if (newIndexName.Length > 63) newIndexName = newIndexName.Substring(0, 63); // PostgreSQL limit
                            if (currentIndexName != newIndexName)
                            {
                                index.SetDatabaseName(newIndexName);
                                if (_namingOptions.EnableLogging && _logger != null)
                                    _logger.LogDebug("[SNAKE_CASE_CONVENTION] Index: {Original} -> {Converted} on table {Table}", currentIndexName, newIndexName, finalTableName);
                            }
                        }
                    }
                }

                // Apply snake_case to primary key names
                if (_namingOptions.ConvertKeyNames)
                {
                    foreach (var key in entityType.GetKeys())
                    {
                        string? keyName = key.GetName();
                        if (keyName != null && !keyName.StartsWith("pk_", StringComparison.OrdinalIgnoreCase))
                        {
                            string newKeyName = $"pk_{ConvertToSnakeCase(finalTableName ?? "table")}";
                            if (newKeyName.Length > 63) newKeyName = newKeyName.Substring(0, 63); // PostgreSQL limit
                            if (keyName != newKeyName)
                            {
                                key.SetName(newKeyName);
                                if (_namingOptions.EnableLogging && _logger != null)
                                    _logger.LogDebug("[SNAKE_CASE_CONVENTION] Primary Key: {Original} -> {Converted} on table {Table}", keyName, newKeyName, finalTableName);
                            }
                        }
                    }
                }

                // Apply snake_case to foreign key constraint names
                if (_namingOptions.ConvertForeignKeyConstraintNames)
                {
                    foreach (var foreignKey in entityType.GetForeignKeys())
                    {
                        string? fkName = foreignKey.GetConstraintName();
                        if (fkName != null && !fkName.StartsWith("fk_", StringComparison.OrdinalIgnoreCase))
                        {
                            string newFkName = $"fk_{ConvertToSnakeCase(finalTableName ?? "table")}_{string.Join("_", foreignKey.Properties.Select(p => ConvertToSnakeCase(p.Name)))}";
                            if (newFkName.Length > 63) newFkName = newFkName.Substring(0, 63); // PostgreSQL limit
                            if (fkName != newFkName)
                            {
                                foreignKey.SetConstraintName(newFkName);
                                if (_namingOptions.EnableLogging && _logger != null)
                                    _logger.LogDebug("[SNAKE_CASE_CONVENTION] Foreign Key: {Original} -> {Converted} on table {Table}", fkName, newFkName, finalTableName);
                            }
                        }
                    }
                }
            }
        }

        /// <summary>
        /// Converts a PascalCase or camelCase string to snake_case.
        /// Enhanced to handle edge cases better, including acronyms.
        /// </summary>
        /// <param name="input">The input string to convert.</param>
        /// <returns>The snake_case version of the input string.</returns>
        private static string ConvertToSnakeCase(string input)
        {
            if (string.IsNullOrEmpty(input)) return input;

            // Enhanced regex pattern to handle acronyms and edge cases better
            var step1 = Regex.Replace(input, @"(?<=[a-z0-9])(?<following>[A-Z])|(?<=[A-Z])(?<following>[A-Z][a-z])", "_${following}");
            return step1.ToLowerInvariant();
        }

        /// <summary>
        /// Validates naming convention changes to prevent conflicts with existing explicit column names.
        /// This helps prevent dangerous migrations that could cause data loss or conflicts.
        /// </summary>
        /// <param name="modelBuilder">The model builder.</param>
        /// <exception cref="InvalidOperationException">Thrown when naming conflicts are detected.</exception>
        private void ValidateNamingConventionChanges(ModelBuilder modelBuilder)
        {
            // Check if any existing explicit column names would conflict
            // with generated snake_case names
            var conflicts = new List<string>();

            foreach (var entityType in modelBuilder.Model.GetEntityTypes())
            {
                if (entityType.IsOwned())
                    continue;

                var explicitColumns = entityType.GetProperties()
                    .Where(p => p.GetColumnName() != p.Name)
                    .Select(p => p.GetColumnName())
                    .ToHashSet();

                var generatedColumns = entityType.GetProperties()
                    .Where(p => p.GetColumnName() == p.Name)
                    .Select(p => ConvertToSnakeCase(p.Name))
                    .ToHashSet();

                var conflictingColumns = explicitColumns.Intersect(generatedColumns);
                conflicts.AddRange(conflictingColumns);
            }

            if (conflicts.Any())
            {
                throw new InvalidOperationException(
                    $"Snake case conversion would create conflicts with existing explicit column names: {string.Join(", ", conflicts)}");
            }
        }

        /// <summary>
        /// Overrides SaveChanges to automatically set the TenantId for added/modified tenant entities.
        /// </summary>
        /// <returns>The number of state entries written to the database.</returns>
        public override int SaveChanges()
        {
            SetTenantIdBeforeSaving();
            return base.SaveChanges();
        }

        /// <summary>
        /// Overrides SaveChangesAsync to automatically set the TenantId for added/modified tenant entities.
        /// </summary>
        /// <param name="cancellationToken">A <see cref="CancellationToken"/> to observe while waiting for the task to complete.</param>
        /// <returns>A task that represents the asynchronous save operation. The task result contains the number of state entries written to the database.</returns>
        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            SetTenantIdBeforeSaving();
            return await base.SaveChangesAsync(cancellationToken);
        }

        /// <summary>
        /// Sets the TenantId property for entities implementing ITenantEntity before saving changes.
        /// </summary>
        /// <exception cref="InvalidOperationException">Thrown if TenantId is required but not available from the provider.</exception>
        private void SetTenantIdBeforeSaving()
        {
            // Get all Added or Modified entries that implement ITenantEntity
            var entries = ChangeTracker.Entries<ITenantEntity>()
                .Where(e => e.State == EntityState.Added || e.State == EntityState.Modified);

            // If there are no tenant entities being modified, we don't need to do anything
            if (!entries.Any())
            {
                return;
            }

            // Check if we're in a migration or design-time context
            bool isMigrationOrDesignTime = _currentTenantId == null;

            if (isMigrationOrDesignTime)
            {
                // During migrations or design-time operations, use a default tenant ID
                // This is necessary for creating the initial schema and applying migrations
                Guid defaultTenantId = Guid.Parse("11111111-1111-1111-1111-111111111111");

                foreach (var entry in entries)
                {
                    // Only set the TenantId if it's not already set
                    if (entry.Entity.TenantId == Guid.Empty)
                    {
                        entry.Entity.TenantId = defaultTenantId;
                    }
                }
            }
            else
            {
                // In normal operation with a valid tenant, set the tenant ID
                // Make sure _currentTenantId is not null before accessing .Value
                if (_currentTenantId.HasValue)
                {
                    foreach (var entry in entries)
                    {
                        entry.Entity.TenantId = _currentTenantId.Value;
                    }
                }
                else
                {
                    // This should not happen in normal operation, but handle it gracefully
                    throw new InvalidOperationException("No tenant ID is available, but tenant entities are being saved. This indicates a configuration issue with the tenant provider.");
                }
            }
        }

        // --- Optional: Override SaveChangesAsync for Auditing ---
        // public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        // {
        //     SetTenantIdBeforeSaving(); // Ensure TenantId is set first
        //     UpdateAuditProperties(); // Then update audit properties
        //     return await base.SaveChangesAsync(cancellationToken);
        // }
        // private void UpdateAuditProperties() { /* ... logic ... */ }
    }
}