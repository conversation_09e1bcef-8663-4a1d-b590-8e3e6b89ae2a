using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProcureToPay.Domain.Entities;
using ProcureToPay.Domain.Enums; // Assuming SubmittalDisposition enum exists
using ProcureToPay.Infrastructure.Identity; // Assuming ApplicationUser exists

namespace ProcureToPay.Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// Configuration for the SubmittalReview entity for EF Core and PostgreSQL.
    /// </summary>
    public class SubmittalReviewConfiguration : IEntityTypeConfiguration<SubmittalReview>
    {
        public void Configure(EntityTypeBuilder<SubmittalReview> builder)
        {
            // Table Mapping
            builder.ToTable("submittal_reviews");

            // Primary Key
            builder.HasKey(sr => sr.Id);
            builder.Property(sr => sr.Id).ValueGeneratedOnAdd();

            // Properties
            builder.Property(sr => sr.ReviewDate)
                .IsRequired()
                .HasColumnType("timestamp without time zone")
                .HasDefaultValueSql("CURRENT_TIMESTAMP"); // Set default to current time on insert

            builder.Property(sr => sr.Disposition) // Renamed from Status for clarity based on enum name
                .IsRequired()
                .HasConversion<string>() // Or int
                .HasMaxLength(50); // Adjust size if string (e.g., Approved, Rejected, ReviseAndResubmit)

            builder.Property(sr => sr.Comments)
                .HasColumnType("text")
                .IsRequired(false);

            // Configure MarkupDocument as JSON
            builder.Property(sr => sr.MarkupDocument)
                .HasColumnType("jsonb")
                .IsRequired(false);

            // Foreign Keys (Assuming Guid or appropriate types)
            builder.Property(sr => sr.TechnicalSubmittalId).IsRequired();
            builder.Property(sr => sr.ReviewerId).IsRequired(); // Assuming FK to ApplicationUser

            // TenantId might be inherited via TechnicalSubmittal


            // --- Relationships ---
            // Link to Submittal (Many-to-One)
            builder.HasOne(sr => sr.TechnicalSubmittal)
                   .WithMany(ts => ts.Reviews) // Matches TechnicalSubmittal config
                   .HasForeignKey(sr => sr.TechnicalSubmittalId)
                   .IsRequired()
                   .OnDelete(DeleteBehavior.Cascade); // Deleting submittal deletes its reviews

            // Link to Reviewer (Many-to-One)
            builder.HasOne<ProcureToPay.Infrastructure.Identity.ApplicationUser>() // Assuming Reviewer navigation prop exists
                   .WithMany() // Assuming User doesn't have direct nav back
                   .HasForeignKey(sr => sr.ReviewerId)
                   .IsRequired()
                   .OnDelete(DeleteBehavior.Restrict); // Prevent deleting user if they performed reviews


            // --- Indexes ---
            builder.HasIndex(sr => sr.TechnicalSubmittalId);
            builder.HasIndex(sr => sr.ReviewerId);
            builder.HasIndex(sr => sr.Disposition); // Indexing status/disposition is common
            builder.HasIndex(sr => sr.ReviewDate);


            // --- TODO ---
            // TODO: Verify FK types (TechnicalSubmittalId, ReviewerId) match related entities.
            // TODO: Define SubmittalDisposition enum (e.g., Approved, Rejected, ReviseAndResubmit).
            // TODO: Define Reviewer navigation property on SubmittalReview entity.
        }
    }
}

