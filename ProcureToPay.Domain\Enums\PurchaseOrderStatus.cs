namespace ProcureToPay.Domain.Enums
{
    /// <summary>
    /// Defines the possible statuses for a Purchase Order.
    /// </summary>
    public enum PurchaseOrderStatus
    {
        /// <summary>
        /// The Purchase Order is being created or modified and is not yet submitted.
        /// </summary>
        Draft = 0, // Added

        /// <summary>
        /// The Purchase Order has been submitted and is awaiting approval.
        /// </summary>
        PendingApproval = 1, // Added (Renamed 'Pending' if that was its original intent)

        /// <summary>
        /// The Purchase Order has been approved and is ready for processing/fulfillment.
        /// </summary>
        Approved = 2,

        /// <summary>
        /// The Purchase Order was rejected during the approval process.
        /// </summary>
        Rejected = 3,

        /// <summary>
        /// The Purchase Order has been fully fulfilled, received, and invoiced.
        /// </summary>
        Completed = 4,

        /// <summary>
        /// The Purchase Order has been cancelled before completion.
        /// </summary>
        Cancelled = 5,

        /// <summary>
        /// Optional: Goods/services have been partially received against the PO.
        /// </summary>
        PartiallyReceived = 6 // Added (Optional, based on your process)
    }
}
