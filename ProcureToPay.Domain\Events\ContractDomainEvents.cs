﻿using System;
using ProcureToPay.Domain.Enums;       // For ContractStatus
using ProcureToPay.Domain.ValueObjects; // For Money

namespace ProcureToPay.Domain.Events
{
    // --- Placeholder: Define a base marker interface or class if desired ---
    // public interface IDomainEvent { }

    // --- Contract Specific Events ---

    public record ContractCreatedEvent(Guid ContractId, Guid? TenantId);

    public record ContractDetailsUpdatedEvent(Guid ContractId);

    public record ContractValueUpdatedEvent(Guid ContractId, Money? NewValue);

    public record ContractStatusChangedEvent(Guid ContractId, ContractStatus NewStatus);

    // Events for JSON field updates - could pass the whole JSON string or just signal an update
    public record ContractMilestonesUpdatedEvent(Guid ContractId /*, string? NewMilestonesJson */ );

    public record ContractSlaUpdatedEvent(Guid ContractId /*, string? NewSlaDetailsJson */ );

    public record ContractComplianceLinksUpdatedEvent(Guid ContractId /*, string? NewLinksJson */ );

    public record ContractPerformanceScoreRecordedEvent(Guid ContractId, decimal? Score);

    public record ContractTerminatedEvent(Guid ContractId, string Reason);

    public record ContractDeletedEvent(Guid ContractId); // For soft delete

    public record ContractRestoredEvent(Guid ContractId); // For restoring soft delete

    public record ContractVersionIncrementedEvent(Guid ContractId, int NewVersion); // Optional specific event

}
