using System;
using System.Collections.Generic;
using System.Linq;
using ProcureToPay.Domain.Enums;       // Required for RequisitionStatus, UnitOfMeasure
using ProcureToPay.Domain.ValueObjects; // Required for Address, Money
using ProcureToPay.Domain.Exceptions;   // Assuming DomainStateException exists
using ProcureToPay.Domain.Events;       // Assuming Requisition domain events namespace exists

namespace ProcureToPay.Domain.Entities
{
    /// <summary>
    /// Represents an internal request to purchase goods or services.
    /// Often requires approval before becoming a Purchase Order.
    /// Typically an Aggregate Root containing PurchaseRequisitionLines.
    /// </summary>
    public class PurchaseRequisition : BaseEntity<Guid> // Assuming BaseEntity<Guid>
    {
        /// <summary>
        /// Foreign Key for multi-tenancy. Links this requisition to a specific Tenant.
        /// </summary>
        public Guid TenantId { get; private set; }
        // Optional Navigation Property to Tenant (configure relationship in Infrastructure if added):
        // public virtual Tenant Tenant { get; private set; } = null!;

        /// <summary>
        /// Unique, human-readable identifier for the requisition. Should be unique within a tenant.
        /// </summary>
        public string RequisitionNumber { get; private set; } = string.Empty;

        /// <summary>
        /// Name of the employee or person requesting the purchase.
        /// </summary>
        public string RequestorName { get; private set; } = string.Empty;

        /// <summary>
        /// Email address of the requestor.
        /// </summary>
        public string RequestorEmail { get; private set; } = string.Empty;

        /// <summary>
        /// Optional ID linking the requestor to the ApplicationUser identity.
        /// </summary>
        public string? RequestorUserId { get; private set; }

        /// <summary>
        /// Department making the request.
        /// </summary>
        public string Department { get; private set; } = string.Empty;

        /// <summary>
        /// Date the requisition was created.
        /// </summary>
        public DateTime RequestDate { get; private set; }

        /// <summary>
        /// Optional date by which the items/services are needed.
        /// </summary>
        public DateTime? DateNeeded { get; private set; }

        /// <summary>
        /// Justification or reason for the purchase request.
        /// </summary>
        public string Justification { get; private set; } = string.Empty;

        /// <summary>
        /// Current status of the requisition.
        /// </summary>
        public RequisitionStatus Status { get; private set; }

        /// <summary>
        /// The currency code for the estimated costs (e.g., SAR, AED, QAR).
        /// </summary>
        public string CurrencyCode { get; private set; } = string.Empty;

        /// <summary>
        /// The total estimated cost of all lines on the requisition, represented as Money VO.
        /// </summary>
        public Money TotalEstimatedCost { get; private set; } = null!; // Changed to Money VO

        /// <summary>
        /// Optional shipping address for the requested items. Uses Address VO.
        /// </summary>
        public Address? ShippingAddress { get; private set; } // Changed to Address VO (nullable)

        /// <summary>
        /// Optional notes or comments.
        /// </summary>
        public string? Notes { get; private set; }

        /// <summary>
        /// Optional ID of the primary Purchase Order created from this requisition.
        /// </summary>
        public Guid? AssociatedPurchaseOrderId { get; private set; }


        // --- Navigation Properties ---

        /// <summary>
        /// Optional Navigation property to the primary resulting Purchase Order. Virtual for lazy loading.
        /// </summary>
        public virtual PurchaseOrder? AssociatedPurchaseOrder { get; private set; }

        /// <summary>
        /// Collection of line items for this requisition. Encapsulated list.
        /// </summary>
        private readonly List<PurchaseRequisitionLine> _lines = new();
        public virtual IReadOnlyCollection<PurchaseRequisitionLine> Lines => _lines.AsReadOnly();

        /// <summary>
        /// Collection navigation property for Purchase Orders generated from this Requisition.
        /// Required for the relationship defined in PurchaseOrderConfiguration.
        /// </summary>
        public virtual ICollection<PurchaseOrder> PurchaseOrders { get; private set; } = new List<PurchaseOrder>(); // Added collection


        // Removed: Concurrency token - handled by xmin shadow property in configuration
        // public uint Version { get; private set; }


        /// <summary>
        /// Private constructor for EF Core hydration.
        /// </summary>
        private PurchaseRequisition() : base(Guid.NewGuid())
        {
            // Required for EF Core, ensure required non-nullable fields are initialized if needed
            // Initialize Money VO to prevent null reference issues
            TotalEstimatedCost = new Money(0, "XXX"); // Use placeholder
        }

        /// <summary>
        /// Creates a new Purchase Requisition in Draft status.
        /// </summary>
        public PurchaseRequisition(
            Guid id, // Added ID parameter
            Guid tenantId,
            string requisitionNumber,
            string requestorName,
            string requestorEmail,
            string department,
            string justification,
            string currencyCode,
            string? requestorUserId = null,
            DateTime? dateNeeded = null,
            Address? shippingAddress = null, // Changed to Address VO
            string? notes = null) : base(id) // Pass ID to base
        {
            // Basic validation
            if (tenantId == Guid.Empty) throw new ArgumentException("TenantId cannot be empty.", nameof(tenantId));
            ArgumentException.ThrowIfNullOrWhiteSpace(requisitionNumber);
            ArgumentException.ThrowIfNullOrWhiteSpace(requestorName);
            ArgumentException.ThrowIfNullOrWhiteSpace(requestorEmail); // TODO: Add email format validation externally
            ArgumentException.ThrowIfNullOrWhiteSpace(department);
            ArgumentException.ThrowIfNullOrWhiteSpace(justification);
            ArgumentException.ThrowIfNullOrWhiteSpace(currencyCode);
            if (currencyCode.Length != 3) throw new ArgumentException("Currency code must be 3 characters.", nameof(currencyCode));

            TenantId = tenantId;
            RequisitionNumber = requisitionNumber;
            RequestorName = requestorName;
            RequestorEmail = requestorEmail;
            RequestorUserId = requestorUserId;
            Department = department;
            Justification = justification;
            CurrencyCode = currencyCode.ToUpperInvariant();
            RequestDate = DateTime.UtcNow; // Set creation date
            DateNeeded = dateNeeded;
            ShippingAddress = shippingAddress; // Assign Address VO
            Notes = notes;
            Status = RequisitionStatus.Draft; // Initial status
            TotalEstimatedCost = new Money(0m, this.CurrencyCode); // Initialize with correct currency

            AddDomainEvent(new RequisitionCreatedEvent(this.Id, this.TenantId));
        }

        // --- Methods ---

        /// <summary>
        /// Adds a line item. Note: Parameters depend on the final PurchaseRequisitionLine definition.
        /// </summary>
        /// <param name="line">The PurchaseRequisitionLine entity to add.</param>
        public void AddLine(PurchaseRequisitionLine line) // Signature changed to accept line entity
        {
            if (Status != RequisitionStatus.Draft)
                throw new DomainStateException("Cannot add lines to a requisition that is not in Draft status.");

            ArgumentNullException.ThrowIfNull(line);
            // Ensure line belongs to this requisition
            if (line.PurchaseRequisitionId != this.Id)
                throw new ArgumentException("Line item does not belong to this requisition.", nameof(line));
            // Ensure currency matches if line has monetary values
            if (line.EstimatedLineCost != null && line.EstimatedLineCost.CurrencyCode != this.CurrencyCode)
                throw new ArgumentException("Line item currency does not match requisition currency.", nameof(line));

            // Prevent adding duplicates if necessary (e.g., based on ProductId/Description)
            // if (_lines.Any(l => l.ProductId == line.ProductId && l.Description == line.Description)) { ... }

            _lines.Add(line);
            RecalculateTotalEstimatedCost();
            AddDomainEvent(new RequisitionLineAddedEvent(this.Id, line.Id));
        }

        /// <summary>
        /// Removes a line item.
        /// </summary>
        /// <param name="requisitionLineId">The ID of the line to remove.</param>
        public void RemoveLine(Guid requisitionLineId)
        {
            if (Status != RequisitionStatus.Draft)
                throw new DomainStateException("Cannot remove lines from a requisition that is not in Draft status.");

            var line = _lines.FirstOrDefault(l => l.Id == requisitionLineId);
            if (line != null)
            {
                _lines.Remove(line);
                RecalculateTotalEstimatedCost();
                AddDomainEvent(new RequisitionLineRemovedEvent(this.Id, requisitionLineId));
            }
            // Else: line not found, potentially log or ignore
        }

        /// <summary>
        /// Updates the shipping address using the Address Value Object.
        /// </summary>
        /// <param name="newAddress">The new shipping address (can be null).</param>
        public void UpdateShippingAddress(Address? newAddress)
        {
            if (Status != RequisitionStatus.Draft)
                throw new DomainStateException("Cannot update address once requisition is submitted.");

            if (ShippingAddress == newAddress) return; // Assumes Address VO equality implemented

            ShippingAddress = newAddress;
            // AddDomainEvent(new RequisitionShippingAddressUpdatedEvent(this.Id));
        }

        /// <summary>
        /// Updates core details of the requisition.
        /// </summary>
        public void UpdateDetails(string justification, string department, DateTime? dateNeeded, string? notes)
        {
            if (Status != RequisitionStatus.Draft)
                throw new DomainStateException("Cannot update details once requisition is submitted.");
            ArgumentException.ThrowIfNullOrWhiteSpace(justification);
            ArgumentException.ThrowIfNullOrWhiteSpace(department);

            bool changed = (Justification != justification || Department != department || DateNeeded != dateNeeded || Notes != notes);

            Justification = justification;
            Department = department;
            DateNeeded = dateNeeded;
            Notes = notes;

            if (changed) AddDomainEvent(new RequisitionDetailsUpdatedEvent(this.Id));
        }

        /// <summary>
        /// Submits the requisition for approval.
        /// </summary>
        public void SubmitForApproval()
        {
            if (Status != RequisitionStatus.Draft)
                throw new DomainStateException($"Requisition in status {Status} cannot be submitted.");
            if (!Lines.Any())
                throw new DomainStateException("Requisition cannot be submitted with no line items.");

            RecalculateTotalEstimatedCost(); // Ensure totals are up-to-date
            SetStatus(RequisitionStatus.PendingApproval);
            AddDomainEvent(new RequisitionSubmittedEvent(this.Id));
        }

        /// <summary>
        /// Approves the requisition. Budget checks should happen externally or via domain service.
        /// </summary>
        public void Approve() // Potentially add approverUserId parameter
        {
            if (Status != RequisitionStatus.PendingApproval)
                throw new DomainStateException($"Requisition in status {Status} cannot be approved.");

            // Budget check logic ideally resides outside the entity
            // if (budgetCheckFails) { throw new BudgetExceededException(...) }

            SetStatus(RequisitionStatus.Approved);
            AddDomainEvent(new RequisitionApprovedEvent(this.Id));
        }

        /// <summary>
        /// Rejects the requisition.
        /// </summary>
        /// <param name="reason">Reason for rejection.</param>
        public void Reject(string reason) // Potentially add rejectorUserId parameter
        {
            if (Status != RequisitionStatus.PendingApproval)
                throw new DomainStateException($"Requisition in status {Status} cannot be rejected.");

            ArgumentException.ThrowIfNullOrWhiteSpace(reason);
            SetStatus(RequisitionStatus.Rejected);
            // Consider how to store the reason - append to Notes or dedicated field?
            Notes = $"Rejected: {reason}\n---\n{Notes}";
            AddDomainEvent(new RequisitionRejectedEvent(this.Id, reason));
        }

        /// <summary>
        /// Marks the requisition as (partially or fully) ordered.
        /// </summary>
        /// <param name="purchaseOrderId">The ID of the resulting Purchase Order.</param>
        /// <param name="isFullyOrdered">Indicates if this PO covers all remaining items.</param>
        public void MarkAsOrdered(Guid purchaseOrderId, bool isFullyOrdered)
        {
            // Allow marking as ordered if Approved or already Partially Ordered
            if (Status != RequisitionStatus.Approved && Status != RequisitionStatus.PartiallyOrdered)
                throw new DomainStateException($"Requisition in status {Status} cannot be marked as ordered.");
            if (purchaseOrderId == Guid.Empty)
                throw new ArgumentException("Associated Purchase Order ID cannot be empty.", nameof(purchaseOrderId));

            // Consider logic if multiple POs can be associated? This assumes one primary PO link.
            AssociatedPurchaseOrderId = purchaseOrderId;
            SetStatus(isFullyOrdered ? RequisitionStatus.FullyOrdered : RequisitionStatus.PartiallyOrdered);
            AddDomainEvent(new RequisitionOrderedEvent(this.Id, purchaseOrderId, isFullyOrdered));
        }

        /// <summary>
        /// Cancels the requisition.
        /// </summary>
        /// <param name="reason">Reason for cancellation.</param>
        public void Cancel(string reason)
        {
            // Allow cancellation if Draft or Pending Approval
            if (Status != RequisitionStatus.Draft && Status != RequisitionStatus.PendingApproval)
                throw new DomainStateException($"Requisition in status {Status} cannot be cancelled.");

            ArgumentException.ThrowIfNullOrWhiteSpace(reason);
            SetStatus(RequisitionStatus.Cancelled);
            Notes = $"Cancelled: {reason}\n---\n{Notes}";
            AddDomainEvent(new RequisitionCancelledEvent(this.Id, reason));
        }

        /// <summary>
        /// Recalculates total estimated cost using Money VO from lines.
        /// </summary>
        private void RecalculateTotalEstimatedCost()
        {
            // Ensure all lines have the same currency as the requisition before summing
            if (_lines.Any(l => l.EstimatedLineCost != null && l.EstimatedLineCost.CurrencyCode != this.CurrencyCode))
            {
                // This should ideally not happen if AddLine validates currency
                throw new DomainStateException("Cannot calculate total estimated cost due to currency mismatch in requisition lines.");
            }

            decimal total = _lines.Sum(l => l.EstimatedLineCost?.Amount ?? 0m); // Sum Amounts, handle null costs
            TotalEstimatedCost = new Money(total, this.CurrencyCode); // Create new Money VO
        }

        /// <summary>
        /// Central method to set status and raise event.
        /// </summary>
        private void SetStatus(RequisitionStatus newStatus)
        {
            if (Status == newStatus) return;
            var oldStatus = Status;
            Status = newStatus;
            AddDomainEvent(new RequisitionStatusChangedEvent(this.Id, oldStatus, newStatus));
        }
    }

    // --- Placeholder Domain Event Definitions (Place in Domain/Events) ---
    /*
    namespace ProcureToPay.Domain.Events
    {
        public record RequisitionCreatedEvent(Guid RequisitionId, Guid TenantId);
        public record RequisitionLineAddedEvent(Guid RequisitionId, Guid LineId);
        public record RequisitionLineRemovedEvent(Guid RequisitionId, Guid LineId);
        public record RequisitionDetailsUpdatedEvent(Guid RequisitionId);
        public record RequisitionShippingAddressUpdatedEvent(Guid RequisitionId);
        public record RequisitionSubmittedEvent(Guid RequisitionId);
        public record RequisitionApprovedEvent(Guid RequisitionId);
        public record RequisitionRejectedEvent(Guid RequisitionId, string Reason);
        public record RequisitionOrderedEvent(Guid RequisitionId, Guid PurchaseOrderId, bool IsFullyOrdered);
        public record RequisitionCancelledEvent(Guid RequisitionId, string Reason);
        public record RequisitionStatusChangedEvent(Guid RequisitionId, RequisitionStatus OldStatus, RequisitionStatus NewStatus);
    }
    */
}
