﻿using System;
using System.Collections.Generic;
using System.Linq;
using ProcureToPay.Domain.Enums;       // For ReturnAuthorizationStatus, ReturnAction
using ProcureToPay.Domain.ValueObjects; // For Address
using ProcureToPay.Domain.Exceptions;   // Assuming DomainStateException exists
using ProcureToPay.Domain.Events;       // Assuming RMA domain events namespace exists

// Assuming BaseEntity<Guid>, ReturnAuthorizationLine, SalesOrder, Customer entities exist
namespace ProcureToPay.Domain.Entities
{
    /// <summary>
    /// Represents a Return Merchandise Authorization (RMA), authorizing the return of goods.
    /// Acts as the Aggregate Root for ReturnAuthorizationLines.
    /// </summary>
    public class ReturnAuthorization : BaseEntity<Guid>
    {
        /// <summary>
        /// Foreign Key for multi-tenancy.
        /// </summary>
        public Guid TenantId { get; private set; }

        /// <summary>
        /// Unique, human-readable identifier for the RMA (e.g., RMA-2025-001).
        /// </summary>
        public string RmaNumber { get; private set; } = null!;

        /// <summary>
        /// Date the return was requested or the RMA was created.
        /// </summary>
        public DateTime RequestDate { get; private set; }

        /// <summary>
        /// Current status of the RMA process.
        /// </summary>
        public ReturnAuthorizationStatus Status { get; private set; }

        /// <summary>
        /// Foreign Key linking to the Customer requesting the return.
        /// </summary>
        public Guid CustomerId { get; private set; }

        /// <summary>
        /// Foreign Key linking to the original Sales Order containing the items being returned.
        /// </summary>
        public Guid OriginalSalesOrderId { get; private set; }

        /// <summary>
        /// Foreign Key linking to the Sales Order.
        /// </summary>
        public Guid? SalesOrderId { get; private set; }

        /// <summary>
        /// Foreign Key linking to the Invoice related to this return.
        /// </summary>
        public Guid? InvoiceId { get; private set; }

        /// <summary>
        /// Date when the return was authorized.
        /// </summary>
        public DateTime? AuthorizationDate { get; private set; }

        /// <summary>
        /// Date when the return authorization expires.
        /// </summary>
        public DateTime? ExpiryDate { get; private set; }

        /// <summary>
        /// Overall reason provided by the customer for the return request.
        /// </summary>
        public string ReasonForReturn { get; private set; } = null!;

        /// <summary>
        /// The primary action requested by the customer (e.g., Refund, Credit, Replacement).
        /// Specific lines might have different actions if needed.
        /// </summary>
        public ReturnAction RequestedAction { get; private set; }

        /// <summary>
        /// Instructions provided to the customer for shipping the items back.
        /// </summary>
        public string? ShippingInstructions { get; private set; }

        /// <summary>
        /// Internal notes regarding the RMA.
        /// </summary>
        public string? Notes { get; private set; }

        /// <summary>
        /// Flag indicating if the RMA has been soft-deleted.
        /// </summary>
        public bool IsDeleted { get; private set; }

        // --- Navigation Properties ---
        private readonly List<ReturnAuthorizationLine> _lines = new();
        /// <summary>
        /// Line items included in this Return Authorization.
        /// </summary>
        public virtual IReadOnlyCollection<ReturnAuthorizationLine> Lines => _lines.AsReadOnly();

        /// <summary>
        /// Navigation property to the Customer.
        /// </summary>
        public virtual Customer Customer { get; private set; } = null!;

        /// <summary>
        /// Navigation property to the original Sales Order.
        /// </summary>
        [System.ComponentModel.DataAnnotations.Schema.NotMapped]
        public virtual SalesOrder OriginalSalesOrder { get; private set; } = null!;

        /// <summary>
        /// Navigation property to the related Invoice.
        /// </summary>
        public virtual Invoice? Invoice { get; private set; }

        /// <summary>
        /// Navigation property to the related Sales Order.
        /// </summary>
        public virtual SalesOrder? SalesOrder { get; private set; }


        /// <summary>
        /// Private constructor for EF Core.
        /// </summary>
        private ReturnAuthorization() : base(Guid.NewGuid()) { }

        /// <summary>
        /// Creates a new Return Authorization in Requested status.
        /// </summary>
        public ReturnAuthorization(
            Guid id,
            Guid tenantId,
            string rmaNumber,
            DateTime requestDate,
            Guid customerId,
            Guid originalSalesOrderId,
            string reasonForReturn,
            ReturnAction requestedAction,
            Guid? invoiceId = null,
            DateTime? authorizationDate = null,
            DateTime? expiryDate = null,
            string? shippingInstructions = null,
            string? notes = null
            ) : base(id)
        {
            // Validation
            if (tenantId == Guid.Empty) throw new ArgumentException("TenantId cannot be empty.", nameof(tenantId));
            ArgumentException.ThrowIfNullOrWhiteSpace(rmaNumber);
            if (requestDate == default) throw new ArgumentException("Request date must be specified.", nameof(requestDate));
            if (customerId == Guid.Empty) throw new ArgumentException("CustomerId cannot be empty.", nameof(customerId));
            if (originalSalesOrderId == Guid.Empty) throw new ArgumentException("OriginalSalesOrderId cannot be empty.", nameof(originalSalesOrderId));
            ArgumentException.ThrowIfNullOrWhiteSpace(reasonForReturn);

            TenantId = tenantId;
            RmaNumber = rmaNumber;
            RequestDate = requestDate.ToUniversalTime(); // Store as UTC
            CustomerId = customerId;
            OriginalSalesOrderId = originalSalesOrderId;
            InvoiceId = invoiceId;
            AuthorizationDate = authorizationDate?.ToUniversalTime() ?? DateTime.UtcNow;
            ExpiryDate = expiryDate?.ToUniversalTime();
            ReasonForReturn = reasonForReturn;
            RequestedAction = requestedAction;
            ShippingInstructions = shippingInstructions;
            Notes = notes;
            Status = ReturnAuthorizationStatus.Requested; // Initial Status
            IsDeleted = false;

            AddDomainEvent(new ReturnAuthorizationCreatedEvent(this.Id, this.TenantId, this.CustomerId, this.OriginalSalesOrderId));
        }

        // --- Domain Methods ---

        public void AddLine(ReturnAuthorizationLine line)
        {
            if (Status != ReturnAuthorizationStatus.Requested && Status != ReturnAuthorizationStatus.Approved) // Allow adding lines only when Requested or Approved?
                throw new DomainStateException($"Cannot add lines when RMA status is '{Status}'.");

            ArgumentNullException.ThrowIfNull(line);
            if (line.ReturnAuthorizationId != this.Id)
                throw new ArgumentException("Line does not belong to this RMA.", nameof(line));
            // Add duplicate check if needed

            _lines.Add(line);
            // AddDomainEvent(new ReturnAuthorizationLineAddedEvent(this.Id, line.Id));
        }

        public void RemoveLine(Guid lineId)
        {
            if (Status != ReturnAuthorizationStatus.Requested && Status != ReturnAuthorizationStatus.Approved)
                throw new DomainStateException($"Cannot remove lines when RMA status is '{Status}'.");

            var line = _lines.FirstOrDefault(l => l.Id == lineId);
            if (line != null)
            {
                _lines.Remove(line);
                // AddDomainEvent(new ReturnAuthorizationLineRemovedEvent(this.Id, lineId));
            }
        }

        // --- Status Transitions ---

        public void Approve(string? shippingInstructions = null, DateTime? expiryDate = null)
        {
            if (Status != ReturnAuthorizationStatus.Requested) throw new DomainStateException($"Cannot approve RMA with status '{Status}'.");
            if (!_lines.Any()) throw new DomainStateException("Cannot approve RMA with no lines.");

            ShippingInstructions = shippingInstructions ?? ShippingInstructions; // Update if provided
            AuthorizationDate = DateTime.UtcNow; // Set authorization date to now
            ExpiryDate = expiryDate?.ToUniversalTime(); // Set expiry date if provided
            SetStatus(ReturnAuthorizationStatus.Approved);
            // AddDomainEvent(new ReturnAuthorizationApprovedEvent(this.Id)); // More specific event
        }

        public void Reject(string reason)
        {
            if (Status != ReturnAuthorizationStatus.Requested) throw new DomainStateException($"Cannot reject RMA with status '{Status}'.");
            ArgumentException.ThrowIfNullOrWhiteSpace(reason);

            SetStatus(ReturnAuthorizationStatus.Rejected);
            Notes = $"Rejected: {reason}\n---\n{Notes}"; // Example: Append reason
            AddDomainEvent(new ReturnAuthorizationRejectedEvent(this.Id, reason)); // Specific event
        }

        public void MarkItemsReceived() // Potentially pass received line details
        {
            if (Status != ReturnAuthorizationStatus.Approved) throw new DomainStateException($"Cannot mark items received for RMA with status '{Status}'.");
            // Logic to update line item received quantities might happen here or via line methods
            // For simplicity, just update header status. More complex logic would check if all lines are received.
            SetStatus(ReturnAuthorizationStatus.ItemsReceived);
        }

        public void Complete(string? completionNotes = null)
        {
            // Typically completed after items received and action (refund/credit/replacement) performed
            if (Status != ReturnAuthorizationStatus.ItemsReceived) throw new DomainStateException($"Cannot complete RMA with status '{Status}'.");

            SetStatus(ReturnAuthorizationStatus.Completed);
            Notes = $"Completed: {completionNotes}\n---\n{Notes}"; // Example
                                                                   // AddDomainEvent(new ReturnAuthorizationCompletedEvent(this.Id)); // Specific event
        }

        public void Cancel(string reason)
        {
            if (Status == ReturnAuthorizationStatus.Completed || Status == ReturnAuthorizationStatus.Cancelled) throw new DomainStateException($"Cannot cancel RMA with status '{Status}'.");
            ArgumentException.ThrowIfNullOrWhiteSpace(reason);

            SetStatus(ReturnAuthorizationStatus.Cancelled);
            Notes = $"Cancelled: {reason}\n---\n{Notes}"; // Example
            AddDomainEvent(new ReturnAuthorizationCancelledEvent(this.Id, reason)); // Specific event
        }


        // --- Soft Delete ---
        public void MarkAsDeleted()
        {
            if (IsDeleted) return;
            IsDeleted = true;
            AddDomainEvent(new ReturnAuthorizationDeletedEvent(this.Id));
        }

        public void Restore()
        {
            if (!IsDeleted) return;
            IsDeleted = false;
            AddDomainEvent(new ReturnAuthorizationRestoredEvent(this.Id));
        }

        // --- Helper ---
        private void SetStatus(ReturnAuthorizationStatus newStatus)
        {
            if (Status == newStatus) return;
            var oldStatus = Status;
            Status = newStatus;
            AddDomainEvent(new ReturnAuthorizationStatusChangedEvent(this.Id, oldStatus, newStatus));
        }
    }

    // --- Placeholder Domain Event Definitions (Place in Domain/Events) ---
    /*
    namespace ProcureToPay.Domain.Events
    {
        public record ReturnAuthorizationCreatedEvent(Guid RmaId, Guid TenantId, Guid CustomerId, Guid OriginalSalesOrderId);
        public record ReturnAuthorizationStatusChangedEvent(Guid RmaId, ReturnAuthorizationStatus OldStatus, ReturnAuthorizationStatus NewStatus);
        public record ReturnAuthorizationApprovedEvent(Guid RmaId);
        public record ReturnAuthorizationRejectedEvent(Guid RmaId, string Reason);
        public record ReturnAuthorizationCompletedEvent(Guid RmaId);
        public record ReturnAuthorizationCancelledEvent(Guid RmaId, string Reason);
        public record ReturnAuthorizationDeletedEvent(Guid RmaId);
        public record ReturnAuthorizationRestoredEvent(Guid RmaId);
        // public record ReturnAuthorizationLineAddedEvent(Guid RmaId, Guid LineId);
        // public record ReturnAuthorizationLineRemovedEvent(Guid RmaId, Guid LineId);
    }
    */
}
