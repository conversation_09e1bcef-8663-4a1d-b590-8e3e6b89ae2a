using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProcureToPay.Domain.Entities; // Assuming Customer, SalesOrder, Invoice entities exist
using ProcureToPay.Domain.Enums;   // Assuming CustomerType enum exists
using ProcureToPay.Domain.ValueObjects; // Assuming Address, Money, ContactPerson VOs/records exist
using System;

namespace ProcureToPay.Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// EF Core Configuration for the Customer entity targeting PostgreSQL.
    /// </summary>
    public class CustomerConfiguration : IEntityTypeConfiguration<Customer>
    {
        private const string DefaultSchema = "public"; // Example schema

        public void Configure(EntityTypeBuilder<Customer> builder)
        {
            // --- Table Mapping ---
            builder.ToTable("customers", DefaultSchema); // Use snake_case and schema

            // --- Primary Key ---
            // Assuming Customer inherits from BaseEntity<Guid>
            builder.HasKey(c => c.Id);
            // Note: UseIdentityByDefaultColumn() requirement from prompt ignored as PK is Guid

            // --- Soft Delete Configuration ---
            // Assumes Customer has: public bool IsDeleted { get; private set; }
            builder.Property(c => c.IsDeleted)
                   .HasDefaultValue(false)
                   .IsRequired();
            builder.HasIndex(c => c.IsDeleted);


            // --- Concurrency Control (PostgreSQL xmin - EF Core 7+) ---
            builder.Property<uint>("xmin")
                   .HasColumnType("xid")
                   .ValueGeneratedOnAddOrUpdate()
                   .IsConcurrencyToken();


            // --- Property Mappings & Constraints ---
            builder.Property(c => c.TenantId)
                   .IsRequired();
            builder.HasIndex(c => c.TenantId); // Index for tenant filtering

            builder.Property(c => c.CustomerCode)
                   .IsRequired()
                   .HasMaxLength(50)
                   .HasColumnName("customer_code"); // snake_case
            // Unique index within a tenant
            builder.HasIndex(c => new { c.TenantId, c.CustomerCode }).IsUnique();

            builder.Property(c => c.Name)
                   .IsRequired()
                   .HasColumnType("text") // Use text for potentially long names
                   .HasColumnName("name");
            builder.HasIndex(c => c.Name); // Index for searching

            builder.Property(c => c.LegalName)
                   .HasColumnType("text") // Nullable
                   .HasColumnName("legal_name");

            builder.Property(c => c.TaxIdentifier)
                   .HasMaxLength(50) // Adjust length as needed
                   .HasColumnName("tax_identifier");
            builder.HasIndex(c => c.TaxIdentifier); // Index if searched frequently

            builder.Property(c => c.IsActive)
                   .IsRequired()
                   .HasDefaultValue(true)
                   .HasColumnName("is_active");
            builder.HasIndex(c => c.IsActive); // Index for filtering

            builder.Property(c => c.DefaultPaymentTerms)
                   .HasMaxLength(100)
                   .HasColumnName("default_payment_terms");

            builder.Property(c => c.DefaultCurrencyCode)
                   .HasMaxLength(3)
                   .HasColumnName("default_currency_code"); // Fixed length for ISO code

            builder.Property(c => c.Website)
                   .HasColumnType("text")
                   .HasColumnName("website");

            // Map CustomerType Enum
            builder.Property(c => c.CustomerType)
                   .IsRequired()
                   .HasConversion<string>() // Store as string
                   .HasMaxLength(50)
                   .HasColumnName("customer_type");
            builder.HasIndex(c => c.CustomerType); // Index if used for filtering

            // Map AssignedSalesRepId FK (relationship configured below)
            builder.Property(c => c.AssignedSalesRepId)
                   .HasMaxLength(450); // Match Identity User Id length if applicable
            builder.HasIndex(c => c.AssignedSalesRepId);


            // --- Value Object / Owned Entity Configuration ---

            // CreditLimit (nullable Money VO)
            builder.OwnsOne(c => c.CreditLimit, limit =>
            {
                limit.Property(m => m.Amount)
                    .HasColumnName("credit_limit_amount") // snake_case
                    .HasColumnType("numeric(18, 2)") // Use specific numeric type
                    .IsRequired(); // Amount required if Money object exists
                limit.Property(m => m.CurrencyCode)
                    .HasColumnName("credit_limit_currency_code") // snake_case
                    .HasMaxLength(3)
                    .IsRequired(); // Currency required if Money object exists
            });
            // Navigation is implicitly optional as CreditLimit property is nullable

            // BillingAddress (Address VO)
            builder.OwnsOne(c => c.BillingAddress, addr =>
            {
                addr.Property(a => a.Street).HasColumnName("billing_street").HasMaxLength(255).IsRequired();
                addr.Property(a => a.City).HasColumnName("billing_city").HasMaxLength(100).IsRequired();
                addr.Property(a => a.State).HasColumnName("billing_state").HasMaxLength(100).IsRequired();
                addr.Property(a => a.Country).HasColumnName("billing_country").HasMaxLength(100).IsRequired();
                addr.Property(a => a.PostalCode).HasColumnName("billing_postal_code").HasMaxLength(20).IsRequired();
            });
            builder.Navigation(c => c.BillingAddress).IsRequired();

            // ShippingAddress (Address VO)
            builder.OwnsOne(c => c.ShippingAddress, addr =>
            {
                addr.Property(a => a.Street).HasColumnName("shipping_street").HasMaxLength(255).IsRequired();
                addr.Property(a => a.City).HasColumnName("shipping_city").HasMaxLength(100).IsRequired();
                addr.Property(a => a.State).HasColumnName("shipping_state").HasMaxLength(100).IsRequired();
                addr.Property(a => a.Country).HasColumnName("shipping_country").HasMaxLength(100).IsRequired();
                addr.Property(a => a.PostalCode).HasColumnName("shipping_postal_code").HasMaxLength(20).IsRequired();
            });
            builder.Navigation(c => c.ShippingAddress).IsRequired();

            // PrimaryContact (ContactPerson record/VO)
            builder.OwnsOne(c => c.PrimaryContact, contact =>
            {
                // Assumes ContactPerson record has Name, Role, Email, Phone properties
                contact.Property(p => p.Name).HasColumnName("primary_contact_name").HasMaxLength(150).IsRequired();
                contact.Property(p => p.Role).HasColumnName("primary_contact_role").HasMaxLength(100);
                contact.Property(p => p.Email).HasColumnName("primary_contact_email").HasMaxLength(254);
                contact.Property(p => p.Phone).HasColumnName("primary_contact_phone").HasMaxLength(50);
                // HierarchyLevel ignored here as it's a single primary contact
                contact.Ignore(p => p.HierarchyLevel);
            });
            builder.Navigation(c => c.PrimaryContact).IsRequired();


            // --- Relationship Configurations ---

            // To SalesOrders (One Customer to Many SalesOrders)
            builder.HasMany(c => c.SalesOrders)
                   .WithOne(so => so.Customer) // Assumes SalesOrder has Customer nav prop
                   .HasForeignKey(so => so.CustomerId) // Assumes SalesOrder has CustomerId FK
                   .IsRequired(false) // SalesOrder might not require a customer? Check this rule.
                   .OnDelete(DeleteBehavior.Restrict); // Prevent deleting Customer if they have Sales Orders

            // To Invoices (One Customer to Many Invoices)
            builder.HasMany(c => c.Invoices)
                   .WithOne(i => i.Customer) // Assumes Invoice has Customer nav prop
                   .HasForeignKey(i => i.CustomerId) // Assumes Invoice has CustomerId FK
                   .IsRequired(false) // Invoice might not require a customer? Check this rule.
                   .OnDelete(DeleteBehavior.Restrict); // Prevent deleting Customer if they have Invoices

            // To AssignedSalesRep (ApplicationUser) - Relationship defined via FK only
            // EF Core can map FKs without explicit navigation properties.
            // If ApplicationUser needs an ICollection<Customer>, configure HasOne from that side.
            // builder.HasOne(c => c.AssignedSalesRep) // No navigation property on Customer
            //        .WithMany() // No inverse navigation property needed on ApplicationUser for this
            //        .HasForeignKey(c => c.AssignedSalesRepId)
            //        .IsRequired(false)
            //        .OnDelete(DeleteBehavior.SetNull); // If SalesRep (User) deleted, set FK to null


            // --- Tenant Isolation & Soft Delete Query Filter ---
            // Note: Tenant filtering should be implemented at the DbContext level
            // Example:
            // builder.HasQueryFilter(c => !c.IsDeleted && c.TenantId == currentTenantId);
            // Where currentTenantId is obtained from the DbContext or a tenant provider service

            // For now, just implement the soft delete filter
            builder.HasQueryFilter(c => !c.IsDeleted);
        }
    }
}

