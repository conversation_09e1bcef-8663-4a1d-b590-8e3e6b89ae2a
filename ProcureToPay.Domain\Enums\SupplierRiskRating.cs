namespace ProcureToPay.Domain.Enums;

/// <summary>
/// Represents the risk classification assigned to a supplier after due‑diligence / scoring.
/// Values are ordered from least to most risky so higher numeric value means higher risk.
/// </summary>
public enum SupplierRiskRating
{
    /// <summary>
    /// Minimal risk observed – stable financials, strong compliance.
    /// </summary>
    Low = 0,

    /// <summary>
    /// Acceptable risk – minor concerns but generally meets requirements.
    /// </summary>
    Medium = 1,

    /// <summary>
    /// Significant risk – requires mitigation plans or closer monitoring.
    /// </summary>
    High = 2,

    /// <summary>
    /// Critical risk – supplier may pose severe operational/financial/CSR concerns.
    /// </summary>
    Critical = 3
} 