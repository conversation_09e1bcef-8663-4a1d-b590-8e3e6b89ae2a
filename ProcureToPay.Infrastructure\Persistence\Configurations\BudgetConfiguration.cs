using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProcureToPay.Domain.Entities; // Assuming Budget, BudgetAllocation entities exist
using ProcureToPay.Domain.Enums;   // Assuming BudgetStatus enum exists
using ProcureToPay.Domain.ValueObjects; // Assuming Money VO exists
using System;

namespace ProcureToPay.Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// EF Core Configuration for the Budget entity targeting PostgreSQL.
    /// Configures versioning, baseline/forecast, JSON fields, relationships, concurrency, soft delete.
    /// </summary>
    public class BudgetConfiguration : IEntityTypeConfiguration<Budget>
    {
        private const string DefaultSchema = "public"; // Example schema

        public void Configure(EntityTypeBuilder<Budget> builder)
        {
            // --- Table Mapping ---
            builder.ToTable("budgets", DefaultSchema); // Use snake_case and schema

            // --- Primary Key ---
            // Assuming Budget inherits from BaseEntity<Guid>
            builder.HasKey(b => b.Id);

            // --- Soft Delete Configuration ---
            // Assumes Budget has: public bool IsDeleted { get; private set; }
            builder.Property(b => b.IsDeleted)
                   .HasDefaultValue(false)
                   .IsRequired();
            // Global query filter combined with Tenant filter later
            builder.HasIndex(b => b.IsDeleted);


            // --- Concurrency Control (PostgreSQL xmin - EF Core 7+) ---
            builder.Property<uint>("xmin")
                   .HasColumnType("xid")
                   .ValueGeneratedOnAddOrUpdate()
                   .IsConcurrencyToken();


            // --- Property Mappings & Constraints ---
            builder.Property(b => b.TenantId)
                   .IsRequired();
            builder.HasIndex(b => b.TenantId); // Index for tenant filtering

            builder.Property(b => b.Name)
                   .IsRequired()
                   .HasMaxLength(200);
            builder.HasIndex(b => b.Name);

            builder.Property(b => b.Description)
                   .HasColumnType("text"); // Use 'text' for potentially long descriptions

            builder.Property(b => b.FiscalYear)
                   .IsRequired();
            builder.HasIndex(b => b.FiscalYear);

            builder.Property(b => b.StartDate)
                   .HasColumnType("timestamp without time zone")
                   .IsRequired();
            builder.HasIndex(b => b.StartDate);

            builder.Property(b => b.EndDate)
                   .HasColumnType("timestamp without time zone")
                   .IsRequired();
            builder.HasIndex(b => b.EndDate);

            // Map Status Enum
            builder.Property(b => b.Status)
                   .IsRequired()
                   .HasConversion<string>()
                   .HasMaxLength(50);
            builder.HasIndex(b => b.Status);

            // Versioning
            builder.Property(b => b.Version)
                   .IsRequired()
                   .HasDefaultValue(1);

            // Rolling Forecast
            builder.Property(b => b.IsRollingForecast)
                   .IsRequired()
                   .HasDefaultValue(false);
            builder.Property(b => b.ForecastPeriodsJson)
                   .HasColumnType("jsonb"); // PostgreSQL JSONB type

            // Allocation Rules
            builder.Property(b => b.AllocationRulesJson)
                   .HasColumnType("jsonb"); // PostgreSQL JSONB type

            // Workflow Integration
            builder.Property(b => b.WorkflowInstanceId); // Nullable Guid
            builder.HasIndex(b => b.WorkflowInstanceId);

            // User Foreign Keys (assuming string IDs from Identity)
            builder.Property(b => b.CreatedById)
                   .IsRequired()
                   .HasMaxLength(450); // Match Identity User Id length if applicable
            builder.HasIndex(b => b.CreatedById);

            builder.Property(b => b.ApprovedById)
                   .HasMaxLength(450); // Match Identity User Id length if applicable
            builder.HasIndex(b => b.ApprovedById);

            // Currency Code (Stored directly on Budget)
            builder.Property(b => b.CurrencyCode)
                  .IsRequired()
                  .HasMaxLength(3);


            // --- Value Object Mapping (Money) ---

            // BaselineAmount (Money VO)
            builder.OwnsOne(b => b.BaselineAmount, baseline =>
            {
                baseline.Property(m => m.Amount)
                    .HasColumnName("baseline_amount") // snake_case
                    .HasColumnType("numeric(18, 4)")  // Precision/scale
                    .IsRequired();
                // Ignore CurrencyCode mapping here as it's a direct property on Budget
                baseline.Ignore(m => m.CurrencyCode);
            });
            builder.Navigation(b => b.BaselineAmount).IsRequired();

            // ForecastAmount (nullable Money VO)
            builder.OwnsOne(b => b.ForecastAmount, forecast =>
            {
                forecast.Property(m => m.Amount)
                    .HasColumnName("forecast_amount") // snake_case
                    .HasColumnType("numeric(18, 4)")   // Precision/scale
                    .IsRequired(); // Amount required if Money object exists
                // Ignore CurrencyCode mapping here
                forecast.Ignore(m => m.CurrencyCode);
            });
            // Navigation is implicitly optional as ForecastAmount property is nullable


            // --- Relationships ---

            // To BudgetAllocations (One Budget to Many Allocations)
            builder.HasMany(b => b.BudgetAllocations)
                   .WithOne(ba => ba.Budget) // Assumes BudgetAllocation has Budget nav prop
                   .HasForeignKey(ba => ba.BudgetId) // Assumes BudgetAllocation has BudgetId FK
                   .IsRequired()
                   .OnDelete(DeleteBehavior.Restrict); // Prevent deleting Budget if Allocations exist

            // Relationships to ApplicationUser (CreatedBy, ApprovedBy) are typically configured
            // implicitly via the FKs or potentially from the ApplicationUser configuration side,
            // especially since navigation properties were removed from Budget entity.


            // --- Tenant Isolation & Soft Delete Query Filter ---
            // Note: Tenant filtering should be implemented at the DbContext level
            // Example:
            // builder.HasQueryFilter(b => !b.IsDeleted && b.TenantId == currentTenantId);
            // Where currentTenantId is obtained from the DbContext or a tenant provider service

            // For now, just implement the soft delete filter
            builder.HasQueryFilter(b => !b.IsDeleted);

        }
    }
}

