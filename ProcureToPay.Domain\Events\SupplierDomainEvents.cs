﻿using System;
using ProcureToPay.Domain.Entities;
using ProcureToPay.Domain.Enums; // For SupplierStatus, SupplierRiskRating
using ProcureToPay.Domain.ValueObjects; // For ContactPerson

namespace ProcureToPay.Domain.Events
{
    // --- Placeholder: Define a base marker interface or class if desired ---
    // public interface IDomainEvent { }

    // --- Supplier Specific Events ---

    // Using records for immutable event data carriers

    public record SupplierProfileCreatedEvent(Guid SupplierId, Guid VendorId); // Example

    public record SupplierStatusUpdatedEvent(Guid SupplierId, SupplierStatus NewStatus);

    public record SupplierRiskRatingUpdatedEvent(Guid SupplierId, SupplierRiskRating? NewRating);

    public record SupplierPerformanceMetricsUpdatedEvent(Guid SupplierId, int? LeadTime, decimal? SustainabilityScore, decimal? CapacityUtilization);

    public record SupplierComplianceUpdatedEvent(Guid SupplierId, bool IsCsrCompliant);

    public record SupplierContractManufacturerStatusSetEvent(Guid SupplierId, bool IsContractManufacturer);

    public record SupplierEmergencyContactAddedEvent(Guid SupplierId, ProcureToPay.Domain.ValueObjects.ContactPerson Contact);

    public record SupplierEmergencyContactRemovedEvent(Guid SupplierId, ProcureToPay.Domain.ValueObjects.ContactPerson Contact);

    public record SupplierEmergencyContactsReplacedEvent(Guid SupplierId);

}
