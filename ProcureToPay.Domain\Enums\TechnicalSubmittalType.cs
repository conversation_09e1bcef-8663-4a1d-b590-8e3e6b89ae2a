﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

public enum TechnicalSubmittalType
{
    Other = 0,
    ProductData = 1,        // Datasheets, specifications
    ShopDrawing = 2,        // Fabrication/installation drawings
    Sample = 3,             // Physical material sample
    TestReport = 4,         // Results of tests (factory or site)
    Certification = 5,      // Compliance certificates
    Calculation = 6,        // Engineering calculations
    Procedure = 7,          // Method statements, installation procedures
    AsDesigned = 8,         // Documentation reflecting the design intent
    AsBuilt = 9,            // Documentation reflecting the final installed/constructed state
    OandMManual = 10,       // Operation and Maintenance Manuals
    Warranty = 11,          // Warranty documentation
    InspectionTestPlan = 12 // ITP Document itself
                            // Add others as needed
}