using System;
using System.Collections.Generic;
using System.Linq;
using System.ComponentModel.DataAnnotations;
using ProcureToPay.Domain.Enums;
using ProcureToPay.Domain.ValueObjects; // For DocumentLink
using ProcureToPay.Domain.Exceptions;
using ProcureToPay.Domain.Events;       // Assuming events namespace exists

namespace ProcureToPay.Domain.Entities
{
    /// <summary>
    /// Represents a package of technical documentation submitted by a vendor for review and approval.
    /// Acts as the Aggregate Root for SubmittalReviews.
    /// </summary>
    public class TechnicalSubmittal : BaseEntity<Guid>
    {
        /// <summary>
        /// Foreign Key for multi-tenancy.
        /// </summary>
        public Guid TenantId { get; private set; }

        /// <summary>
        /// Unique Document Control Number for the submittal package (e.g., PROJ-SUB-MECH-001-R0).
        /// Uniqueness might be enforced per project/tenant via index.
        /// </summary>
        public string SubmittalNumber { get; private set; } = null!;

        /// <summary>
        /// The revision number or identifier for the submittal.
        /// </summary>
        public string? Revision { get; private set; }

        /// <summary>
        /// Human-readable title of the submittal package.
        /// </summary>
        public string Title { get; private set; } = null!;

        /// <summary>
        /// Optional detailed description of the submittal's content or purpose.
        /// </summary>
        public string? Description { get; private set; }

        /// <summary>
        /// Type or category of the submittal (e.g., ShopDrawing, AsBuilt).
        /// </summary>
        public TechnicalSubmittalType SubmittalType { get; private set; }

        /// <summary>
        /// Current overall status of the submittal package.
        /// </summary>
        public TechnicalSubmittalStatus Status { get; private set; }

        /// <summary>
        /// The current review cycle number (starts at 1 for initial submission).
        /// Incremented upon vendor resubmission after 'ReviseAndResubmit'.
        /// </summary>
        public int CurrentReviewCycle { get; private set; }

        /// <summary>
        /// Date the submittal is due for review completion (for the current cycle).
        /// </summary>
        public DateTime? ReviewDueDate { get; private set; }

        /// <summary>
        /// Date by which the submittal is required.
        /// </summary>
        public DateTime? RequiredDate { get; private set; }

        /// <summary>
        /// Date the review process for the current cycle actually started.
        /// </summary>
        public DateTime? ReviewStartDate { get; private set; }

        /// <summary>
        /// Date the review for the current cycle was fully completed (all reviewers finished).
        /// </summary>
        public DateTime? ReviewCompletionDate { get; private set; }

        /// <summary>
        /// The final, consolidated disposition for the *current* review cycle.
        /// Determined based on individual SubmittalReview dispositions (logic likely in Application Service).
        /// </summary>
        public SubmittalDisposition CurrentOverallDisposition { get; private set; }

        // --- Context / Linking ---
        /// <summary>
        /// Optional FK to the specific Purchase Order Line this submittal relates to.
        /// </summary>
        public Guid? PurchaseOrderLineId { get; private set; }

        /// <summary>
        /// Optional FK to the Contract this submittal fulfills a requirement for.
        /// </summary>
        public Guid? ContractId { get; private set; }

        /// <summary>
        /// Optional FK to the Project this submittal belongs to.
        /// </summary>
        public Guid? ProjectId { get; private set; }

        /// <summary>
        /// Reference to the relevant specification section (e.g., "09 91 00 - Painting").
        /// </summary>
        public string? SpecificationSection { get; private set; }

        /// <summary>
        /// Optional Foreign Key to the specification.
        /// </summary>
        public Guid? SpecificationId { get; private set; }

        /// <summary>
        /// Optional reference to a related Inspection and Test Plan (ITP).
        /// </summary>
        public string? RelatedITPReference { get; private set; }

        /// <summary>
        /// Optional Foreign Key to the Test Plan.
        /// </summary>
        public Guid? TestPlanId { get; private set; }

        /// <summary>
        /// Optional reference to a Non-Conformance Report (NCR) if the submittal was rejected.
        /// </summary>
        public string? RelatedNCRReference { get; private set; }

        /// <summary>
        /// Optional Foreign Key to the Non-Conformance Report.
        /// </summary>
        public Guid? NonConformanceReportId { get; private set; }

        // --- Submission Details ---
        /// <summary>
        /// FK to the Vendor/Supplier who submitted the package.
        /// </summary>
        public Guid? VendorId { get; private set; }

        /// <summary>
        /// User ID of the person who performed the submission action. Links to ApplicationUser Id.
        /// </summary>
        public string SubmittedById { get; private set; } = null!;

        /// <summary>
        /// Foreign Key to the user who submitted the submittal.
        /// </summary>
        [MaxLength(450)]
        public string SubmittedByUserId { get; private set; } = null!;

        /// <summary>
        /// Foreign Key to the current reviewer.
        /// </summary>
        public Guid? CurrentReviewerId { get; private set; }

        /// <summary>
        /// The count of review cycles.
        /// </summary>
        public int CycleCount { get; private set; }

        /// <summary>
        /// Flag indicating if this is an as-built submittal.
        /// </summary>
        public bool IsAsBuilt { get; private set; }

        /// <summary>
        /// Flag indicating if this is final documentation.
        /// </summary>
        public bool IsFinalDocumentation { get; private set; }

        /// <summary>
        /// Timestamp when the submittal was last submitted (or resubmitted).
        /// </summary>
        public DateTime SubmittedDate { get; private set; }

        // --- Final Sign-off ---
        /// <summary>
        /// User ID of the person who gave the final sign-off when status moves to Closed. Links to ApplicationUser Id.
        /// </summary>
        public string? FinalSignOffById { get; private set; }

        /// <summary>
        /// Foreign Key to the user who signed off the submittal.
        /// </summary>
        public Guid? SignedOffById { get; private set; }

        /// <summary>
        /// Timestamp of the final sign-off.
        /// </summary>
        public DateTime? FinalSignOffDate { get; private set; }

        /// <summary>
        /// Timestamp of the sign-off.
        /// </summary>
        public DateTime? SignedOffDate { get; private set; }

        /// <summary>
        /// The count of resubmissions.
        /// </summary>
        public int ResubmissionCount { get; private set; }

        /// <summary>
        /// The maximum number of allowed resubmissions.
        /// </summary>
        public int? MaxResubmissions { get; private set; }

        /// <summary>
        /// Flag indicating if the submittal has been soft-deleted.
        /// </summary>
        public bool IsDeleted { get; private set; }

        // --- Document Storage ---
        /// <summary>
        /// List of documents included in this submittal package version.
        /// Stored as JSONB. Uses DocumentLink Value Object.
        /// </summary>
        public List<DocumentLink> SubmittedDocuments { get; private set; } = new();


        // --- Navigation Properties ---
        private readonly List<SubmittalReview> _reviewHistory = new();
        /// <summary>
        /// History of all reviews performed across all cycles for this submittal.
        /// </summary>
        public virtual IReadOnlyCollection<SubmittalReview> ReviewHistory => _reviewHistory.AsReadOnly();

        // Optional navigation properties if FKs are set
        public virtual PurchaseOrderLine? PurchaseOrderLine { get; private set; }
        public virtual Contract? Contract { get; private set; }
        public virtual Project? Project { get; private set; }
        public virtual Vendor? Vendor { get; private set; }

        // Collection navigation properties
        public virtual ICollection<SubmittalReview> Reviews { get; private set; } = new List<SubmittalReview>();





        /// <summary>
        /// Private constructor for EF Core.
        /// </summary>
        private TechnicalSubmittal() : base(Guid.NewGuid()) { }

        /// <summary>
        /// Creates a new Technical Submittal in Draft status.
        /// </summary>
        public TechnicalSubmittal(
            Guid id,
            Guid tenantId,
            string submittalNumber,
            string title,
            TechnicalSubmittalType submittalType,
            Guid? vendorId,
            string submittedByUserId,
            string? revision = null,
            DateTime? requiredDate = null,
            Guid? specificationId = null,
            Guid? projectId = null, // Project context often useful
            Guid? purchaseOrderLineId = null,
            Guid? contractId = null,
            string? specificationSection = null,
            string? description = null,
            bool isAsBuilt = false,
            bool isFinalDocumentation = false,
            int? maxResubmissions = null
            ) : base(id)
        {
            // Validation
            if (tenantId == Guid.Empty) throw new ArgumentException("TenantId cannot be empty.", nameof(tenantId));
            ArgumentException.ThrowIfNullOrWhiteSpace(submittalNumber);
            ArgumentException.ThrowIfNullOrWhiteSpace(title);
            if (vendorId.HasValue && vendorId.Value == Guid.Empty) throw new ArgumentException("VendorId cannot be empty if provided.", nameof(vendorId));
            ArgumentException.ThrowIfNullOrWhiteSpace(submittedByUserId);

            TenantId = tenantId;
            SubmittalNumber = submittalNumber; // Consider logic for generating/validating this number format
            Title = title;
            SubmittalType = submittalType;
            VendorId = vendorId;
            SubmittedByUserId = submittedByUserId;
            Revision = revision;
            RequiredDate = requiredDate?.ToUniversalTime();
            SpecificationId = specificationId;
            ProjectId = projectId;
            PurchaseOrderLineId = purchaseOrderLineId;
            ContractId = contractId;
            SpecificationSection = specificationSection;
            Description = description;
            IsAsBuilt = isAsBuilt;
            IsFinalDocumentation = isFinalDocumentation;
            MaxResubmissions = maxResubmissions;

            Status = TechnicalSubmittalStatus.Draft;
            CurrentReviewCycle = 0; // Will become 1 on first submission
            CycleCount = 0;
            ResubmissionCount = 0;
            CurrentOverallDisposition = SubmittalDisposition.NotReviewed;
            IsDeleted = false;
            SubmittedDocuments = new List<DocumentLink>(); // Initialize empty

            AddDomainEvent(new TechnicalSubmittalCreatedEvent(this.Id, this.TenantId, this.SubmittalNumber));
        }

        // --- Domain Methods ---

        /// <summary>
        /// Submits the package for review. Moves from Draft or ReviseAndResubmit.
        /// </summary>
        /// <param name="submittedById">User ID of the submitter.</param>
        /// <param name="submittedDocuments">The list of documents being submitted.</param>
        /// <param name="reviewDueDate">Optional due date for the review cycle.</param>
        /// <param name="resubmissionLimit">Optional: The max allowed cycles (checked externally).</param>
        public void Submit(string submittedById, List<DocumentLink> submittedDocuments, DateTime? reviewDueDate = null, int? resubmissionLimit = null)
        {
            if (Status != TechnicalSubmittalStatus.Draft && Status != TechnicalSubmittalStatus.ReviseAndResubmit)
                throw new DomainStateException($"Cannot submit submittal with status '{Status}'. Must be Draft or ReviseAndResubmit.");
            ArgumentException.ThrowIfNullOrWhiteSpace(submittedById);
            ArgumentNullException.ThrowIfNull(submittedDocuments);
            if (!submittedDocuments.Any())
                throw new DomainStateException("Cannot submit with no documents.");

            // Increment cycle number on submission/resubmission
            CurrentReviewCycle++;

            // Check Resubmission Limit (Example - actual enforcement might be better in Application Service)
            if (resubmissionLimit.HasValue && CurrentReviewCycle > resubmissionLimit.Value)
            {
                // Option 1: Throw exception
                throw new DomainStateException($"Resubmission limit ({resubmissionLimit.Value}) exceeded. Current cycle: {CurrentReviewCycle}.");
                // Option 2: Set status to Rejected/Closed automatically (less common for entity to do this)
                // SetStatus(TechnicalSubmittalStatus.Rejected);
                // AddDomainEvent(...)
                // return;
            }


            SubmittedById = submittedById;
            SubmittedDate = DateTime.UtcNow;
            SubmittedDocuments = submittedDocuments; // Replace documents with the new submission
            ReviewDueDate = reviewDueDate?.ToUniversalTime();
            ReviewStartDate = null; // Reset for the new cycle
            ReviewCompletionDate = null; // Reset for the new cycle
            CurrentOverallDisposition = SubmittalDisposition.NotReviewed; // Reset for the new cycle

            SetStatus(TechnicalSubmittalStatus.Submitted);
            AddDomainEvent(new TechnicalSubmittalSubmittedEvent(this.Id, this.CurrentReviewCycle, this.SubmittedById));
        }

        /// <summary>
        /// Marks the submittal as being actively reviewed.
        /// </summary>
        public void StartReview()
        {
            if (Status != TechnicalSubmittalStatus.Submitted)
                throw new DomainStateException($"Cannot start review for submittal with status '{Status}'. Must be Submitted.");

            ReviewStartDate = DateTime.UtcNow;
            SetStatus(TechnicalSubmittalStatus.InReview);
            // AddDomainEvent(...)
        }

        /// <summary>
        /// Records an individual review outcome for the current cycle.
        /// Note: Determining the *overall* disposition usually happens externally.
        /// </summary>
        /// <param name="review">The SubmittalReview record.</param>
        public void AddReviewRecord(SubmittalReview review)
        {
            if (Status != TechnicalSubmittalStatus.InReview)
                throw new DomainStateException($"Cannot add review record for submittal with status '{Status}'. Must be InReview.");
            ArgumentNullException.ThrowIfNull(review);
            if (review.TechnicalSubmittalId != this.Id)
                throw new ArgumentException("Review record does not belong to this submittal.", nameof(review));
            if (review.ReviewCycle != this.CurrentReviewCycle)
                throw new DomainStateException($"Review record cycle ({review.ReviewCycle}) does not match current submittal cycle ({this.CurrentReviewCycle}).");

            // Prevent adding duplicate reviews for the same reviewer in the same cycle?
            if (_reviewHistory.Any(r => r.ReviewCycle == this.CurrentReviewCycle && r.ReviewerId == review.ReviewerId))
            {
                throw new DomainStateException($"Reviewer '{review.ReviewerName}' has already submitted a review for cycle {this.CurrentReviewCycle}.");
            }

            _reviewHistory.Add(review);
            // Domain event might be raised when *all* expected reviews for the cycle are in,
            // or perhaps an event per individual review recorded.
            AddDomainEvent(new TechnicalSubmittalReviewRecordedEvent(this.Id, review.Id, review.ReviewerId, review.Disposition));
        }

        /// <summary>
        /// Completes the current review cycle, setting the overall disposition and updating status.
        /// This method assumes the overall disposition has been determined externally (e.g., in Application Service).
        /// </summary>
        /// <param name="overallDisposition">The consolidated outcome for this cycle.</param>
        /// <param name="finalSignOffById">User ID if this is the final closing sign-off.</param>
        /// <param name="relatedNCRReference">Optional NCR reference if rejected.</param>
        public void CompleteReviewCycle(SubmittalDisposition overallDisposition, string? finalSignOffById = null, string? relatedNCRReference = null)
        {
            if (Status != TechnicalSubmittalStatus.InReview)
                throw new DomainStateException($"Cannot complete review cycle for submittal with status '{Status}'. Must be InReview.");
            // Add check: Ensure expected reviews for the cycle are actually present in _reviewHistory?

            ReviewCompletionDate = DateTime.UtcNow;
            CurrentOverallDisposition = overallDisposition;

            TechnicalSubmittalStatus nextStatus;
            bool isFinal = false;

            switch (overallDisposition)
            {
                case SubmittalDisposition.Approved:
                case SubmittalDisposition.ApprovedAsNoted:
                case SubmittalDisposition.InformationOnly: // Treat InfoOnly as effectively approved/closed
                    nextStatus = TechnicalSubmittalStatus.Closed;
                    isFinal = true;
                    break;
                case SubmittalDisposition.ReviseAndResubmit:
                    nextStatus = TechnicalSubmittalStatus.ReviseAndResubmit;
                    break;
                case SubmittalDisposition.Rejected:
                    nextStatus = TechnicalSubmittalStatus.Closed; // Typically rejection is final unless policy allows appeal/resubmit
                    RelatedNCRReference = relatedNCRReference; // Link NCR on rejection
                    isFinal = true;
                    break;
                case SubmittalDisposition.NotReviewed: // Should not happen if completing cycle
                default:
                    throw new DomainStateException($"Invalid overall disposition '{overallDisposition}' provided to complete review cycle.");
            }

            SetStatus(nextStatus);

            if (isFinal)
            {
                FinalSignOffById = finalSignOffById ?? "System"; // Or require explicit ID?
                FinalSignOffDate = DateTime.UtcNow;
                AddDomainEvent(new TechnicalSubmittalClosedEvent(this.Id, this.CurrentOverallDisposition, this.FinalSignOffById));
            }
            else // ReviseAndResubmit
            {
                AddDomainEvent(new TechnicalSubmittalRequiresRevisionEvent(this.Id, this.CurrentReviewCycle));
            }
        }

        /// <summary>
        /// Updates context links or references.
        /// </summary>
        public void UpdateContext(Guid? purchaseOrderLineId, Guid? contractId, Guid? projectId, string? specificationSection, string? relatedITPReference)
        {
            if (Status == TechnicalSubmittalStatus.Closed || Status == TechnicalSubmittalStatus.Cancelled || Status == TechnicalSubmittalStatus.Superseded)
                throw new DomainStateException($"Cannot update context for submittal with status '{Status}'.");

            PurchaseOrderLineId = purchaseOrderLineId;
            ContractId = contractId;
            ProjectId = projectId;
            SpecificationSection = specificationSection;
            RelatedITPReference = relatedITPReference;
            // AddDomainEvent(...) if needed
        }

        /// <summary>
        /// Marks the submittal as superseded by another.
        /// </summary>
        /// <param name="supersedingSubmittalId">Optional ID of the submittal that replaces this one.</param>
        public void MarkAsSuperseded(Guid? supersedingSubmittalId = null)
        {
            if (Status == TechnicalSubmittalStatus.Superseded) return;
            // Consider adding checks - can only supersede if Closed?
            SetStatus(TechnicalSubmittalStatus.Superseded);
            // Store supersedingSubmittalId if needed
            AddDomainEvent(new TechnicalSubmittalSupersededEvent(this.Id, supersedingSubmittalId));
        }

        /// <summary>
        /// Cancels the submittal process.
        /// </summary>
        public void Cancel()
        {
            if (Status == TechnicalSubmittalStatus.Closed || Status == TechnicalSubmittalStatus.Cancelled || Status == TechnicalSubmittalStatus.Superseded)
                throw new DomainStateException($"Cannot cancel submittal with status '{Status}'.");

            SetStatus(TechnicalSubmittalStatus.Cancelled);
            AddDomainEvent(new TechnicalSubmittalCancelledEvent(this.Id));
        }


        // --- Soft Delete ---
        public void MarkAsDeleted()
        {
            if (IsDeleted) return;
            // Add checks? Cannot delete if InReview or Closed? Depends on policy.
            IsDeleted = true;
            AddDomainEvent(new TechnicalSubmittalDeletedEvent(this.Id));
        }

        public void Restore()
        {
            if (!IsDeleted) return;
            IsDeleted = false;
            AddDomainEvent(new TechnicalSubmittalRestoredEvent(this.Id));
        }

        // --- Helper ---
        private void SetStatus(TechnicalSubmittalStatus newStatus)
        {
            if (Status == newStatus) return;
            var oldStatus = Status;
            Status = newStatus;
            AddDomainEvent(new TechnicalSubmittalStatusChangedEvent(this.Id, oldStatus, newStatus));
        }
    }

    // --- Placeholder Domain Event Definitions (Place in Domain/Events) ---
    /*
    namespace ProcureToPay.Domain.Events
    {
        public record TechnicalSubmittalCreatedEvent(Guid SubmittalId, Guid TenantId, string SubmittalNumber);
        public record TechnicalSubmittalSubmittedEvent(Guid SubmittalId, int ReviewCycle, string SubmittedById);
        public record TechnicalSubmittalReviewRecordedEvent(Guid SubmittalId, Guid ReviewId, string ReviewerId, SubmittalDisposition Disposition);
        public record TechnicalSubmittalRequiresRevisionEvent(Guid SubmittalId, int CompletedCycle);
        public record TechnicalSubmittalClosedEvent(Guid SubmittalId, SubmittalDisposition FinalDisposition, string? FinalSignOffById);
        public record TechnicalSubmittalStatusChangedEvent(Guid SubmittalId, TechnicalSubmittalStatus OldStatus, TechnicalSubmittalStatus NewStatus);
        public record TechnicalSubmittalSupersededEvent(Guid SubmittalId, Guid? SupersedingSubmittalId);
        public record TechnicalSubmittalCancelledEvent(Guid SubmittalId);
        public record TechnicalSubmittalDeletedEvent(Guid SubmittalId);
        public record TechnicalSubmittalRestoredEvent(Guid SubmittalId);
    }
    */
}
