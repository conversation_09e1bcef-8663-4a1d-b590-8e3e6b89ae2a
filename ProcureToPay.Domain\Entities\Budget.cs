﻿using System;
using System.Collections.Generic;
using System.Linq;
using ProcureToPay.Domain.Enums;       // Required for BudgetStatus
using ProcureToPay.Domain.ValueObjects; // Required for Money
using ProcureToPay.Domain.Exceptions;   // Assuming DomainStateException exists
using ProcureToPay.Domain.Events;       // Assuming Budget domain events namespace exists

// Assuming BaseEntity<Guid>, BudgetAllocation entities exist
// NOTE: Removed direct dependency on ApplicationUser (Infrastructure concern)
namespace ProcureToPay.Domain.Entities
{
    /// <summary>
    /// Represents a budget for a specific period, department, or project.
    /// Tracks baseline, forecast, allocations, and status. Includes versioning and soft delete.
    /// </summary>
    public class Budget : BaseEntity<Guid> // Assuming BaseEntity<Guid> provides Id and event handling
    {
        /// <summary>
        /// Foreign Key for multi-tenancy.
        /// </summary>
        public Guid TenantId { get; private set; }

        /// <summary>
        /// User-friendly name for the budget (e.g., "Marketing Q3 2025", "Project Phoenix IT Budget").
        /// </summary>
        public string Name { get; private set; } = null!;

        /// <summary>
        /// Detailed description or purpose of the budget.
        /// </summary>
        public string? Description { get; private set; }

        /// <summary>
        /// The fiscal year this budget primarily applies to (e.g., 2025). For alignment and reporting.
        /// </summary>
        public int FiscalYear { get; private set; }

        /// <summary>
        /// The start date of the budget period.
        /// </summary>
        public DateTime StartDate { get; private set; }

        /// <summary>
        /// The end date of the budget period.
        /// </summary>
        public DateTime EndDate { get; private set; }

        /// <summary>
        /// The original, approved budget amount (baseline). Uses Money VO.
        /// </summary>
        public Money BaselineAmount { get; private set; } = null!;

        /// <summary>
        /// The latest forecasted budget amount (can differ from baseline). Uses Money VO.
        /// </summary>
        public Money? ForecastAmount { get; private set; }

        /// <summary>
        /// The currency code for the budget amounts (derived from BaselineAmount).
        /// </summary>
        public string CurrencyCode { get; private set; } = null!;

        /// <summary>
        /// Current status of the budget (e.g., Draft, Submitted, Approved, Closed).
        /// </summary>
        public BudgetStatus Status { get; private set; }

        /// <summary>
        /// Version number to track changes to the budget definition or amounts. Starts at 1.
        /// </summary>
        public int Version { get; private set; }

        /// <summary>
        /// Flag indicating if this budget uses rolling forecast capabilities.
        /// </summary>
        public bool IsRollingForecast { get; private set; }

        /// <summary>
        /// Stores details for rolling forecast periods/amounts as JSON. Requires external logic for management.
        /// </summary>
        public string? ForecastPeriodsJson { get; private set; }

        /// <summary>
        /// Stores departmental allocation rules or restrictions as JSON. Requires external logic for enforcement.
        /// </summary>
        public string? AllocationRulesJson { get; private set; }

        /// <summary>
        /// Optional identifier linking this budget to a specific approval workflow instance.
        /// </summary>
        public Guid? WorkflowInstanceId { get; private set; }

        /// <summary>
        /// Foreign Key (string representation) of the user who created the budget. Links to ApplicationUser Id.
        /// </summary>
        public string CreatedById { get; private set; } = null!;

        /// <summary>
        /// Foreign Key (string representation) of the user who last approved the budget. Links to ApplicationUser Id.
        /// </summary>
        public string? ApprovedById { get; private set; }


        /// <summary>
        /// Flag indicating if the budget has been soft-deleted.
        /// </summary>
        public bool IsDeleted { get; private set; }


        // --- Navigation Properties ---
        public virtual ICollection<BudgetAllocation> BudgetAllocations { get; private set; } = new List<BudgetAllocation>();

        // Removed Navigation Properties to ApplicationUser to maintain Domain layer independence
        // public virtual ApplicationUser CreatedBy { get; private set; } = null!;
        // public virtual ApplicationUser? ApprovedBy { get; private set; }


        /// <summary>
        /// Private constructor for EF Core.
        /// </summary>
        private Budget() : base(Guid.NewGuid())
        {
            BaselineAmount = new Money(0, "XXX");
            ForecastAmount = null;
            CurrencyCode = "XXX";
        }

        /// <summary>
        /// Creates a new Budget in Draft status.
        /// </summary>
        public Budget(
            Guid id,
            Guid tenantId,
            string name,
            int fiscalYear,
            DateTime startDate,
            DateTime endDate,
            Money baselineAmount,
            string createdById, // Accepts string ID
            string? description = null,
            Money? forecastAmount = null,
            bool isRollingForecast = false,
            string? allocationRulesJson = null,
            string? forecastPeriodsJson = null
            ) : base(id)
        {
            if (tenantId == Guid.Empty) throw new ArgumentException("TenantId cannot be empty.", nameof(tenantId));
            ArgumentException.ThrowIfNullOrWhiteSpace(name);
            ArgumentNullException.ThrowIfNull(baselineAmount);
            if (baselineAmount.Amount < 0) throw new ArgumentOutOfRangeException(nameof(baselineAmount), "Baseline amount cannot be negative.");
            if (fiscalYear <= 1900) throw new ArgumentOutOfRangeException(nameof(fiscalYear), "Fiscal year seems invalid.");
            if (endDate < startDate) throw new ArgumentException("End date cannot be before start date.", nameof(endDate));
            ArgumentException.ThrowIfNullOrWhiteSpace(createdById);
            if (forecastAmount != null && forecastAmount.CurrencyCode != baselineAmount.CurrencyCode)
                throw new ArgumentException("Forecast currency must match baseline currency.", nameof(forecastAmount));


            TenantId = tenantId;
            Name = name;
            FiscalYear = fiscalYear;
            StartDate = startDate.Date;
            EndDate = endDate.Date;
            BaselineAmount = baselineAmount;
            CurrencyCode = baselineAmount.CurrencyCode;
            ForecastAmount = forecastAmount ?? new Money(baselineAmount.Amount, baselineAmount.CurrencyCode);
            CreatedById = createdById; // Store string ID
            Description = description;
            IsRollingForecast = isRollingForecast;
            AllocationRulesJson = allocationRulesJson;
            ForecastPeriodsJson = forecastPeriodsJson;

            Status = BudgetStatus.Draft;
            Version = 1;
            IsDeleted = false;
            ApprovedById = null; // Explicitly null on creation

            AddDomainEvent(new BudgetCreatedEvent(this.Id, this.TenantId));
        }

        // --- Domain Methods ---

        public void UpdateDetails(string name, string? description, int fiscalYear, DateTime startDate, DateTime endDate)
        {
            if (Status == BudgetStatus.Closed) throw new DomainStateException("Cannot update details of a closed budget.");
            ArgumentException.ThrowIfNullOrWhiteSpace(name);
            if (fiscalYear <= 1900) throw new ArgumentOutOfRangeException(nameof(fiscalYear), "Fiscal year seems invalid.");
            if (endDate < startDate) throw new ArgumentException("End date cannot be before start date.", nameof(endDate));

            bool changed = (Name != name || Description != description || FiscalYear != fiscalYear || StartDate != startDate.Date || EndDate != endDate.Date);

            Name = name;
            Description = description;
            FiscalYear = fiscalYear;
            StartDate = startDate.Date;
            EndDate = endDate.Date;

            if (changed)
            {
                IncrementVersion();
                AddDomainEvent(new BudgetDetailsUpdatedEvent(this.Id));
            }
        }

        public void UpdateBaseline(Money newBaseline)
        {
            if (Status != BudgetStatus.Draft && Status != BudgetStatus.Submitted) throw new DomainStateException($"Cannot update baseline when budget status is '{Status}'.");
            ArgumentNullException.ThrowIfNull(newBaseline);
            if (newBaseline.CurrencyCode != this.CurrencyCode) throw new ArgumentException("New baseline currency must match budget currency.", nameof(newBaseline));
            if (newBaseline.Amount < 0) throw new ArgumentOutOfRangeException(nameof(newBaseline), "Baseline amount cannot be negative.");

            if (BaselineAmount == newBaseline) return;

            BaselineAmount = newBaseline;
            if (Status == BudgetStatus.Draft || Status == BudgetStatus.Submitted)
            {
                ForecastAmount = new Money(newBaseline.Amount, newBaseline.CurrencyCode);
            }

            IncrementVersion();
            AddDomainEvent(new BudgetBaselineUpdatedEvent(this.Id, newBaseline));
        }

        public void UpdateForecast(Money newForecast)
        {
            if (Status == BudgetStatus.Closed) throw new DomainStateException("Cannot update forecast for a closed budget.");
            ArgumentNullException.ThrowIfNull(newForecast);
            if (newForecast.CurrencyCode != this.CurrencyCode) throw new ArgumentException("New forecast currency must match budget currency.", nameof(newForecast));
            if (newForecast.Amount < 0) throw new ArgumentOutOfRangeException(nameof(newForecast), "Forecast amount cannot be negative.");

            if (ForecastAmount == newForecast) return;

            ForecastAmount = newForecast;
            IncrementVersion();
            AddDomainEvent(new BudgetForecastUpdatedEvent(this.Id, newForecast));
        }

        public void UpdateAllocationRules(string? rulesJson)
        {
            if (AllocationRulesJson == rulesJson) return;
            AllocationRulesJson = rulesJson;
            IncrementVersion();
            AddDomainEvent(new BudgetRulesUpdatedEvent(this.Id));
        }

        public void UpdateRollingForecastData(string? forecastPeriodsJson)
        {
            if (!IsRollingForecast) throw new DomainStateException("Cannot update rolling forecast data for a non-rolling budget.");
            if (ForecastPeriodsJson == forecastPeriodsJson) return;
            ForecastPeriodsJson = forecastPeriodsJson;
            IncrementVersion();
            AddDomainEvent(new BudgetForecastPeriodsUpdatedEvent(this.Id));
        }

        public void LinkToWorkflow(Guid? workflowInstanceId)
        {
            if (WorkflowInstanceId == workflowInstanceId) return;
            WorkflowInstanceId = workflowInstanceId;
            // AddDomainEvent(...)
        }

        public void Approve(string approvedById) // Accepts string ID
        {
            if (Status != BudgetStatus.Submitted)
                throw new DomainStateException($"Cannot approve budget with status '{Status}'. Must be '{BudgetStatus.Submitted}'.");
            ArgumentException.ThrowIfNullOrWhiteSpace(approvedById);

            SetStatus(BudgetStatus.Approved);
            ApprovedById = approvedById; // Store string ID
            IncrementVersion();
            // Event raised in SetStatus, but maybe add ApprovedById to event?
            // AddDomainEvent(new BudgetApprovedEvent(this.Id, approvedById)); // Already raised via SetStatus
        }

        public void SubmitForApproval()
        {
            if (Status != BudgetStatus.Draft)
                throw new DomainStateException($"Cannot submit budget with status '{Status}'. Must be '{BudgetStatus.Draft}'.");

            SetStatus(BudgetStatus.Submitted);
            // Event raised in SetStatus
        }

        public void Close()
        {
            if (Status != BudgetStatus.Approved)
                throw new DomainStateException($"Cannot close budget with status '{Status}'. Must be '{BudgetStatus.Approved}'.");

            SetStatus(BudgetStatus.Closed);
            // Event raised in SetStatus
        }

        public void Reject(string reason, string rejectedById) // Accepts string ID
        {
            if (Status != BudgetStatus.Submitted)
                throw new DomainStateException($"Cannot reject budget with status '{Status}'. Must be '{BudgetStatus.Submitted}'.");
            ArgumentException.ThrowIfNullOrWhiteSpace(reason);
            ArgumentException.ThrowIfNullOrWhiteSpace(rejectedById);

            SetStatus(BudgetStatus.Rejected);
            IncrementVersion();
            AddDomainEvent(new BudgetRejectedEvent(this.Id, rejectedById, reason)); // Raise specific event with ID
        }

        public void MarkAsDeleted()
        {
            if (IsDeleted) return;
            IsDeleted = true;
            IncrementVersion();
            AddDomainEvent(new BudgetDeletedEvent(this.Id));
        }

        public void Restore()
        {
            if (!IsDeleted) return;
            IsDeleted = false;
            IncrementVersion();
            AddDomainEvent(new BudgetRestoredEvent(this.Id));
        }

        private void IncrementVersion()
        {
            Version++;
            AddDomainEvent(new BudgetVersionIncrementedEvent(this.Id, this.Version));
        }

        private void SetStatus(BudgetStatus newStatus)
        {
            if (Status == newStatus) return;
            var oldStatus = Status;
            Status = newStatus;
            IncrementVersion();
            AddDomainEvent(new BudgetStatusChangedEvent(this.Id, oldStatus, newStatus));
        }
    }

    // --- Placeholder Domain Event Definitions (Place in Domain/Events) ---
    /* see budget_domain_events_01 artifact */

    // --- Placeholder Department Entity (Place in Domain/Entities) ---
    /* see budget_allocation_entity_01 artifact */
}
