# Script to clean the database and apply the migration

Write-Host "Clean Database and Apply Migration Utility" -ForegroundColor Cyan
Write-Host "=========================================" -ForegroundColor Cyan

# Step 1: Start the Aspire application
Write-Host "Step 1: Starting the Aspire application..." -ForegroundColor Cyan
Write-Host "  Please start the Aspire application manually and wait for the PostgreSQL container to be ready." -ForegroundColor Yellow
Write-Host "  Press Enter when the PostgreSQL container is running..." -ForegroundColor Yellow
Read-Host

# Step 2: Check if Dock<PERSON> is running and find the PostgreSQL container
Write-Host "Step 2: Checking Docker and finding PostgreSQL container..." -ForegroundColor Cyan
try {
    $dockerStatus = docker ps
    Write-Host "  Docker is running." -ForegroundColor Green
} catch {
    Write-Error "  Docker does not appear to be running. Please start Docker and try again."
    exit 1
}

# Get the PostgreSQL container ID
$containerName = "postgresdb-*"
try {
    $containerId = docker ps --filter "name=$containerName" --format "{{.ID}}"
    if (-not $containerId) {
        Write-Error "  PostgreSQL container not found. Make sure the Aspire application is running."
        exit 1
    }
    Write-Host "  Found PostgreSQL container: $containerId" -ForegroundColor Green
} catch {
    Write-Error "  Error finding PostgreSQL container: $_"
    exit 1
}

# Step 3: Get connection string from appsettings.json
Write-Host "Step 3: Getting database connection information..." -ForegroundColor Cyan
try {
    $connectionString = Get-Content -Path "appsettings.json" | ConvertFrom-Json | Select-Object -ExpandProperty ConnectionStrings | Select-Object -ExpandProperty postgresdb

    # Parse the connection string to get the components
    $connectionParts = $connectionString -split ";"
    $pgHost = ($connectionParts | Where-Object { $_ -like "Host=*" }) -replace "Host=", ""
    $pgPort = ($connectionParts | Where-Object { $_ -like "Port=*" }) -replace "Port=", ""
    $pgDatabase = ($connectionParts | Where-Object { $_ -like "Database=*" }) -replace "Database=", ""
    $pgUser = ($connectionParts | Where-Object { $_ -like "Username=*" }) -replace "Username=", ""
    $pgPassword = ($connectionParts | Where-Object { $_ -like "Password=*" }) -replace "Password=", ""

    # For security, mask the password in logs
    $maskedConnectionString = $connectionString -replace "Password=.*?;", "Password=***;"
    Write-Host "  Using connection: $maskedConnectionString" -ForegroundColor Cyan
} catch {
    Write-Error "  Error getting connection string: $_"
    exit 1
}

# Step 4: Drop all tables in the database
Write-Host "Step 4: Dropping all tables in database '$pgDatabase'..." -ForegroundColor Yellow
try {
    # Use the SQL file
    Get-Content "drop-and-recreate-database.sql" | docker exec -i $containerId psql -h $pgHost -U $pgUser -d $pgDatabase
    
    Write-Host "  All tables have been dropped successfully!" -ForegroundColor Green
} catch {
    Write-Error "  Error dropping tables: $_"
    exit 1
}

# Step 5: Apply the migration
Write-Host "Step 5: Applying the migration..." -ForegroundColor Cyan

# Set the working directory to the solution root
$solutionDir = $PSScriptRoot

# Set the full project paths
$infrastructureProjectPath = Join-Path $solutionDir "ProcureToPay.Infrastructure"
$webAppProjectPath = Join-Path $solutionDir "ProcureToPay.WebApp\ProcureToPay.WebApp"

# Apply migrations with verbose output
Write-Host "  Running: dotnet ef database update --project $infrastructureProjectPath --startup-project $webAppProjectPath --context ApplicationDbContext --connection $maskedConnectionString --verbose" -ForegroundColor Cyan

try {
    # Execute the migration command
    $migrationOutput = dotnet ef database update --project $infrastructureProjectPath --startup-project $webAppProjectPath --context ApplicationDbContext --connection $connectionString --verbose
    
    # Display the output
    $migrationOutput | ForEach-Object { Write-Host "    $_" }
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "  Migrations applied successfully!" -ForegroundColor Green
    } else {
        Write-Error "  Migration failed with exit code $LASTEXITCODE"
        exit 1
    }
} catch {
    Write-Error "  An error occurred while applying migrations: $_"
    exit 1
}

# Step 6: Verify migrations were applied
Write-Host "Step 6: Verifying migrations were applied..." -ForegroundColor Cyan

# SQL to check applied migrations
$verifyMigrationsSql = @"
SELECT "MigrationId" FROM "__EFMigrationsHistory" ORDER BY "MigrationId";
"@

try {
    $appliedMigrations = $verifyMigrationsSql | docker exec -i $containerId psql -h $pgHost -U $pgUser -d $pgDatabase -t
    
    if ($appliedMigrations) {
        Write-Host "  Applied migrations:" -ForegroundColor Green
        $appliedMigrations -split "`n" | Where-Object { $_.Trim() } | ForEach-Object {
            Write-Host "    - $($_.Trim())" -ForegroundColor Green
        }
    } else {
        Write-Warning "  No migrations found in the database after applying migrations!"
    }
} catch {
    Write-Warning "  Error verifying applied migrations: $_"
}

# Step 7: Verify tables were created
Write-Host "Step 7: Verifying tables were created..." -ForegroundColor Cyan

# SQL to check created tables
$verifyTablesSql = @"
SELECT table_schema, table_name 
FROM information_schema.tables 
WHERE table_schema NOT IN ('pg_catalog', 'information_schema') 
ORDER BY table_schema, table_name;
"@

try {
    $createdTables = $verifyTablesSql | docker exec -i $containerId psql -h $pgHost -U $pgUser -d $pgDatabase -t
    
    if ($createdTables) {
        Write-Host "  Created tables:" -ForegroundColor Green
        $createdTables -split "`n" | Where-Object { $_.Trim() } | ForEach-Object {
            Write-Host "    - $($_.Trim())" -ForegroundColor Green
        }
    } else {
        Write-Warning "  No tables found in the database after applying migrations!"
    }
} catch {
    Write-Warning "  Error verifying created tables: $_"
}

Write-Host "Migration process completed successfully!" -ForegroundColor Green
