﻿using System;
using ProcureToPay.Domain.ValueObjects; // Assuming Money VO exists here

namespace ProcureToPay.Domain.ValueObjects // Or a dedicated sub-namespace like Domain.ValueObjects.ContractSpecific
{
    /// <summary>
    /// Represents a milestone within a contract's payment schedule.
    /// </summary>
    /// <param name="Name">Name or description of the milestone.</param>
    /// <param name="DueDate">Target completion date for the milestone.</param>
    /// <param name="Amount">Payment amount associated with completing this milestone.</param>
    /// <param name="Description">Optional further details about the milestone criteria.</param>
    public record ContractMilestone(
        string Name,
        DateTime DueDate,
        Money Amount, // Use Money VO
        string? Description
    );

    /// <summary>
    /// Represents a specific detail or metric within a Service Level Agreement (SLA).
    /// </summary>
    /// <param name="MetricName">Name of the SLA metric (e.g., "Uptime", "Response Time").</param>
    /// <param name="Target">The target value or description (e.g., "99.9%", "< 4 hours").</param>
    /// <param name="MeasurementPeriod">How often the metric is measured (e.g., "Monthly", "Quarterly").</param>
    /// <param name="Penalty">Optional description of penalty for non-compliance.</param>
    public record SlaDetail(
        string MetricName,
        string Target,
        string MeasurementPeriod,
        string? Penalty
    );

    /// <summary>
    /// Represents a reference or link to a compliance document related to the contract.
    /// </summary>
    /// <param name="Name">Display name of the document.</param>
    /// <param name="Url">URL or path to the document.</param>
    /// <param name="DocumentType">Type of document (e.g., "Certificate", "Audit Report").</param>
    /// <param name="Description">Optional description.</param>
    public record DocumentLink(
        string Name,
        string Url, // Or could be an ID referencing a document management system
        string DocumentType,
        string? Description
    );
}
