using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProcureToPay.Domain.Entities;
using ProcureToPay.Domain.Enums; // Assuming TechnicalSubmittalStatus enum exists
using ProcureToPay.Infrastructure.Identity; // Assuming ApplicationUser exists
using ProcureToPay.Infrastructure.Persistence.Extensions;

namespace ProcureToPay.Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// Configuration for the TechnicalSubmittal entity for EF Core and PostgreSQL.
    /// </summary>
    public class TechnicalSubmittalConfiguration : IEntityTypeConfiguration<TechnicalSubmittal>
    {
        public void Configure(EntityTypeBuilder<TechnicalSubmittal> builder)
        {
            // Table Mapping
            builder.ToTable("technical_submittals");

            // Primary Key
            builder.HasKey(ts => ts.Id);
            builder.Property(ts => ts.Id).ValueGeneratedOnAdd();

            // Properties
            builder.Property(ts => ts.SubmittalNumber) // Renamed from DocumentNumber for consistency
                .IsRequired()
                .HasMaxLength(100); // Adjust length as needed

            builder.Property(ts => ts.Title)
                .IsRequired()
                .HasMaxLength(255);

            builder.Property(ts => ts.Revision)
                .HasMaxLength(20) // Or use int
                .IsRequired(false);

            builder.Property(ts => ts.SubmittedDate)
                .IsRequired()
                .HasColumnType("timestamp without time zone");

            builder.Property(ts => ts.RequiredDate) // Due date
                .HasColumnType("timestamp without time zone")
                .IsRequired(false);

            builder.Property(ts => ts.Status)
                .IsRequired()
                .HasConversion<string>() // Or int
                .HasMaxLength(50);

            // Foreign Keys (Assuming Guid or appropriate types)
            builder.Property(ts => ts.ProjectId).IsRequired();
            builder.Property(ts => ts.SpecificationId).IsRequired(false); // Link to Spec/Product/Document
            builder.Property(ts => ts.SubmittedByUserId).IsRequired(); // Link to submitting user

            // Properties from previous version (keep if needed)
            builder.Property(ts => ts.SubmittalType)
                .IsRequired()
                .HasConversion<string>()
                .HasMaxLength(50);
            builder.Property(ts => ts.CurrentReviewerId).IsRequired(false);
            builder.Property(ts => ts.CycleCount).IsRequired().HasDefaultValue(1);
            builder.Property(ts => ts.IsAsBuilt).IsRequired().HasDefaultValue(false);
            builder.Property(ts => ts.IsFinalDocumentation).IsRequired().HasDefaultValue(false);
            builder.Property(ts => ts.TestPlanId).IsRequired(false);
            builder.Property(ts => ts.NonConformanceReportId).IsRequired(false);
            builder.Property(ts => ts.ResubmissionCount).IsRequired().HasDefaultValue(0);
            builder.Property(ts => ts.MaxResubmissions).IsRequired(false);
            builder.Property(ts => ts.SignedOffById).IsRequired(false);
            builder.Property(ts => ts.SignedOffDate).HasColumnType("timestamp without time zone").IsRequired(false);
            builder.Property(ts => ts.VendorId).IsRequired(false); // Submitter might be internal user or vendor

            builder.Property(ts => ts.TenantId).IsRequired();

            // Configure SubmittedDocuments as JSON
            builder.Property(ts => ts.SubmittedDocuments)
                .HasColumnType("jsonb");


            // --- Concurrency Token ---
            builder.UseXminAsConcurrencyToken();


            // --- Relationships ---
            // Submittal-Reviews (One-to-Many)
            builder.HasMany(ts => ts.Reviews)
                   .WithOne(sr => sr.TechnicalSubmittal) // Matches SubmittalReview config
                   .HasForeignKey(sr => sr.TechnicalSubmittalId)
                   .IsRequired()
                   .OnDelete(DeleteBehavior.Cascade); // Deleting submittal deletes its reviews

            // Link to Project (Many-to-One)
            builder.HasOne(ts => ts.Project)
                   .WithMany(p => p.TechnicalSubmittals) // Assuming Project has collection
                   .HasForeignKey(ts => ts.ProjectId)
                   .IsRequired()
                   .OnDelete(DeleteBehavior.Restrict); // Prevent deleting project if submittals exist (or Cascade?)

            // Link to Specification/Product (Many-to-One, Optional)
            // Assuming Specification entity exists (adjust name/type if needed)
            // builder.HasOne(ts => ts.Specification)
            //        .WithMany()
            //        .HasForeignKey(ts => ts.SpecificationId)
            //        .IsRequired(false)
            //        .OnDelete(DeleteBehavior.Restrict); // Or SetNull?

             // Link to Vendor (Many-to-One, Optional)
            builder.HasOne(ts => ts.Vendor)
                .WithMany() // Assuming Vendor doesn't have direct nav back
                .HasForeignKey(ts => ts.VendorId)
                .IsRequired(false) // Submitter might be internal
                .OnDelete(DeleteBehavior.Restrict);


            // Link to Submitter (Many-to-One)
            builder.HasOne<ProcureToPay.Infrastructure.Identity.ApplicationUser>() // Assuming SubmittedByUser nav prop exists
                   .WithMany() // Assuming User doesn't have direct nav back
                   .HasForeignKey(ts => ts.SubmittedByUserId)
                   .IsRequired()
                   .OnDelete(DeleteBehavior.Restrict); // Prevent deleting user if they submitted items


            // --- Indexes ---
            builder.HasIndex(ts => ts.SubmittalNumber).IsUnique(); // Unique per project/tenant?
            builder.HasIndex(ts => new { ts.ProjectId, ts.SubmittalNumber, ts.Revision }).IsUnique(); // Common unique key
            builder.HasIndex(ts => ts.Status);
            builder.HasIndex(ts => ts.SubmittedDate);
            builder.HasIndex(ts => ts.RequiredDate);
            builder.HasIndex(ts => ts.ProjectId);
            builder.HasIndex(ts => ts.SpecificationId);
            builder.HasIndex(ts => ts.SubmittedByUserId);
            builder.HasIndex(ts => ts.VendorId);
            builder.HasIndex(ts => ts.TenantId);


            // --- TODO ---
            // TODO: Verify FK types (ProjectId, SpecificationId, SubmittedByUserId, VendorId) match related entities.
            // TODO: Define TechnicalSubmittalStatus enum.
            // TODO: Define Specification entity and relationship if needed.
            // TODO: Define SubmittedByUser navigation property on TechnicalSubmittal entity.
            // TODO: Confirm uniqueness constraint for SubmittalNumber (per project or global?).
            // TODO: Re-evaluate deferred TODOs from previous version (document numbering, review cycles).
        }
    }
}
